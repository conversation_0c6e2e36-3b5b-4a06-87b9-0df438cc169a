"""
SQL转换器模块

提供SQL语句转换功能，将SQL语句从一种数据库方言转换为另一种。
"""
import re
from typing import Dict, List, Any, Optional, Tuple, Set, Union

from app.core.sql_parser import SQLParser, SQLNode, SQLClauseType
from app.models.logger import logger


class SQLTransformer:
    """SQL转换器"""

    def __init__(self, source_db: str, target_db: str):
        """
        初始化SQL转换器

        Args:
            source_db: 源数据库类型
            target_db: 目标数据库类型
        """
        self.source_db = source_db.lower()
        self.target_db = target_db.lower()

        # 初始化SQL解析器
        self.parser = SQLParser(source_db)

        # 缓存已转换的函数调用
        self.function_cache = {}

        # 预编译正则表达式
        self._compile_regex_patterns()

        # 初始化转换规则
        self._init_transformation_rules()

    def _compile_regex_patterns(self):
        """预编译常用的正则表达式模式"""
        # 函数调用模式
        self.func_pattern = re.compile(r'(\w+)\s*\(', re.IGNORECASE)

        # 子查询模式
        self.subquery_pattern = re.compile(r'\(\s*SELECT\s+', re.IGNORECASE)

        # 窗口函数模式
        self.window_pattern = re.compile(r'OVER\s*\(', re.IGNORECASE)

        # 常用函数模式
        self.date_func_pattern = re.compile(r'(TO_CHAR|TO_DATE|DATE_FORMAT|STR_TO_DATE|CONVERT|FORMAT|VARCHAR_FORMAT|TRUNC|EXTRACT|DATEADD|DATEDIFF|GETDATE|SYSDATE|CURRENT_DATE|CURRENT_TIMESTAMP)\s*\(', re.IGNORECASE)
        self.string_func_pattern = re.compile(r'(SUBSTRING|SUBSTR|LENGTH|LEN|INSTR|CHARINDEX|LOCATE|TRIM|LTRIM|RTRIM|UPPER|LOWER|REPLACE|TRANSLATE|REGEXP_REPLACE|REGEXP_SUBSTR|REGEXP_LIKE|REGEXP_COUNT|CONCAT|CONCAT_WS|LPAD|RPAD)\s*\(', re.IGNORECASE)
        self.special_func_pattern = re.compile(r'(COALESCE|NVL|ISNULL|IFNULL|NULLIF|VALUE|DECODE|CASE|IIF|GREATEST|LEAST|DENSE_RANK|RANK|ROW_NUMBER|LEAD|LAG|FIRST_VALUE|LAST_VALUE|NTH_VALUE|PERCENT_RANK|CUME_DIST)\s*\(', re.IGNORECASE)
        self.numeric_func_pattern = re.compile(r'(ABS|CEIL|CEILING|FLOOR|ROUND|TRUNC|MOD|POWER|SQRT|EXP|LN|LOG|LOG10|SIN|COS|TAN|ASIN|ACOS|ATAN|ATAN2|SIGN|RAND|RANDOM)\s*\(', re.IGNORECASE)
        self.aggregate_func_pattern = re.compile(r'(SUM|AVG|MIN|MAX|COUNT|STDDEV|VARIANCE|LISTAGG|STRING_AGG|GROUP_CONCAT|ARRAY_AGG|JSON_AGG|XMLAGG)\s*\(', re.IGNORECASE)
        self.json_func_pattern = re.compile(r'(JSON_VALUE|JSON_QUERY|JSON_EXTRACT|JSON_OBJECT|JSON_ARRAY|JSON_TABLE|JSONB_EXTRACT_PATH|JSONB_EXTRACT_PATH_TEXT|JSON_CONTAINS|JSON_SEARCH)\s*\(', re.IGNORECASE)

        # 参数类型识别模式
        self.number_pattern = re.compile(r'^[-+]?(\d+(\.\d*)?|\.\d+)([eE][-+]?\d+)?$')
        self.date_pattern = re.compile(r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}(\s\d{1,2}:\d{1,2}(:\d{1,2})?)?$')
        self.string_pattern = re.compile(r'^[\'\"].*[\'\"]$')
        self.boolean_pattern = re.compile(r'^(true|false|yes|no|y|n|1|0|t|f)$', re.IGNORECASE)
        self.null_pattern = re.compile(r'^(null|nil|none)$', re.IGNORECASE)

        # 复杂参数模式
        self.complex_param_pattern = re.compile(r'([^,()]+(?:\([^()]*(?:\([^()]*\)[^()]*)*\)[^,()]*)*)')
        self.param_list_pattern = re.compile(r'\s*,\s*')

        # 函数参数模式
        self.func_args_pattern = re.compile(r'(\w+)\s*\(\s*(.*?)\s*\)', re.IGNORECASE | re.DOTALL)

    def _init_transformation_rules(self):
        """初始化转换规则"""
        # 分页转换规则
        self.pagination_rules = {
            # DB2 -> 其他数据库
            "db2": {
                "oracle": self._db2_to_oracle_pagination,
                "postgres": self._db2_to_postgres_pagination,
                "gaussdb": self._db2_to_postgres_pagination,
                "sqlserver": self._db2_to_sqlserver_pagination,
                "mysql": self._db2_to_mysql_pagination
            },
            # Oracle -> 其他数据库
            "oracle": {
                "db2": self._oracle_to_db2_pagination,
                "postgres": self._oracle_to_postgres_pagination,
                "gaussdb": self._oracle_to_postgres_pagination,
                "sqlserver": self._oracle_to_sqlserver_pagination,
                "mysql": self._oracle_to_mysql_pagination
            },
            # PostgreSQL/GaussDB -> 其他数据库
            "postgres": {
                "db2": self._postgres_to_db2_pagination,
                "oracle": self._postgres_to_oracle_pagination,
                "sqlserver": self._postgres_to_sqlserver_pagination,
                "mysql": self._postgres_to_mysql_pagination
            },
            "gaussdb": {
                "db2": self._postgres_to_db2_pagination,
                "oracle": self._postgres_to_oracle_pagination,
                "sqlserver": self._postgres_to_sqlserver_pagination,
                "mysql": self._postgres_to_mysql_pagination
            },
            # SQL Server -> 其他数据库
            "sqlserver": {
                "db2": self._sqlserver_to_db2_pagination,
                "oracle": self._sqlserver_to_oracle_pagination,
                "postgres": self._sqlserver_to_postgres_pagination,
                "gaussdb": self._sqlserver_to_postgres_pagination,
                "mysql": self._sqlserver_to_mysql_pagination
            },
            # MySQL -> 其他数据库
            "mysql": {
                "db2": self._mysql_to_db2_pagination,
                "oracle": self._mysql_to_oracle_pagination,
                "postgres": self._mysql_to_postgres_pagination,
                "gaussdb": self._mysql_to_postgres_pagination,
                "sqlserver": self._mysql_to_sqlserver_pagination
            }
        }

        # 函数转换规则
        self.function_rules = {
            # DB2 -> 其他数据库
            "db2": {
                "oracle": self._db2_to_oracle_functions,
                "postgres": self._db2_to_postgres_functions,
                "gaussdb": self._db2_to_postgres_functions,
                "sqlserver": self._db2_to_sqlserver_functions,
                "mysql": self._db2_to_mysql_functions
            },
            # Oracle -> 其他数据库
            "oracle": {
                "db2": self._oracle_to_db2_functions,
                "postgres": self._oracle_to_postgres_functions,
                "gaussdb": self._oracle_to_postgres_functions,
                "sqlserver": self._oracle_to_sqlserver_functions,
                "mysql": self._oracle_to_mysql_functions
            },
            # PostgreSQL/GaussDB -> 其他数据库
            "postgres": {
                "db2": self._postgres_to_db2_functions,
                "oracle": self._postgres_to_oracle_functions,
                "sqlserver": self._postgres_to_sqlserver_functions,
                "mysql": self._postgres_to_mysql_functions
            },
            "gaussdb": {
                "db2": self._postgres_to_db2_functions,
                "oracle": self._postgres_to_oracle_functions,
                "sqlserver": self._postgres_to_sqlserver_functions,
                "mysql": self._postgres_to_mysql_functions
            },
            # SQL Server -> 其他数据库
            "sqlserver": {
                "db2": self._sqlserver_to_db2_functions,
                "oracle": self._sqlserver_to_oracle_functions,
                "postgres": self._sqlserver_to_postgres_functions,
                "gaussdb": self._sqlserver_to_postgres_functions,
                "mysql": self._sqlserver_to_mysql_functions
            },
            # MySQL -> 其他数据库
            "mysql": {
                "db2": self._mysql_to_db2_functions,
                "oracle": self._mysql_to_oracle_functions,
                "postgres": self._mysql_to_postgres_functions,
                "gaussdb": self._mysql_to_postgres_functions,
                "sqlserver": self._mysql_to_sqlserver_functions
            }
        }

    def transform(self, query: str) -> str:
        """
        转换SQL查询语句

        Args:
            query: SQL查询语句

        Returns:
            str: 转换后的SQL查询语句
        """
        # 如果源数据库和目标数据库相同，无需转换
        if self.source_db == self.target_db:
            return query

        try:
            # 解析SQL查询语句
            sql_tree = self.parser.parse(query)

            # 转换SQL语法树
            transformed_tree = self._transform_tree(sql_tree)

            # 生成转换后的SQL查询语句
            transformed_query = self._generate_query(transformed_tree)

            return transformed_query
        except Exception as e:
            logger.warning(f"SQL转换失败: {str(e)}，返回原始查询")
            return query

    def _transform_tree(self, node: SQLNode) -> SQLNode:
        """
        转换SQL语法树

        Args:
            node: SQL语法树节点

        Returns:
            SQLNode: 转换后的SQL语法树节点
        """
        # 创建新节点
        new_node = SQLNode(node.node_type, node.content)

        # 根据节点类型进行转换
        if node.node_type == SQLClauseType.SELECT:
            new_node.content = self._transform_select_clause(node.content)
        elif node.node_type == SQLClauseType.FROM:
            new_node.content = self._transform_from_clause(node.content)
        elif node.node_type == SQLClauseType.WHERE:
            new_node.content = self._transform_where_clause(node.content)
        elif node.node_type == SQLClauseType.GROUP_BY:
            new_node.content = self._transform_group_by_clause(node.content)
        elif node.node_type == SQLClauseType.HAVING:
            new_node.content = self._transform_having_clause(node.content)
        elif node.node_type == SQLClauseType.ORDER_BY:
            new_node.content = self._transform_order_by_clause(node.content)
        elif node.node_type == SQLClauseType.LIMIT:
            new_node.content = self._transform_limit_clause(node.content)
        elif node.node_type == SQLClauseType.OFFSET:
            new_node.content = self._transform_offset_clause(node.content)
        elif node.node_type == SQLClauseType.JOIN:
            new_node.content = self._transform_join_clause(node.content)
        elif node.node_type == SQLClauseType.FUNCTION:
            new_node.content = self._transform_function(node.content)
        elif node.node_type == SQLClauseType.EXPRESSION:
            new_node.content = self._transform_expression(node.content)

        # 递归转换子节点
        for child in node.children:
            new_child = self._transform_tree(child)
            new_node.add_child(new_child)

        return new_node

    def _generate_query(self, node: SQLNode) -> str:
        """
        生成SQL查询语句

        Args:
            node: SQL语法树节点

        Returns:
            str: SQL查询语句
        """
        # 如果是根节点，递归生成子节点的查询语句
        if node.node_type == SQLClauseType.UNKNOWN:
            query_parts = []

            # 按照SQL语句的顺序添加子句
            clause_order = [
                SQLClauseType.SELECT,
                SQLClauseType.FROM,
                SQLClauseType.WHERE,
                SQLClauseType.GROUP_BY,
                SQLClauseType.HAVING,
                SQLClauseType.ORDER_BY,
                SQLClauseType.LIMIT,
                SQLClauseType.OFFSET,
                SQLClauseType.UNION
            ]

            for clause_type in clause_order:
                for child in node.children:
                    if child.node_type == clause_type:
                        if clause_type == SQLClauseType.SELECT:
                            query_parts.append(f"SELECT {child.content}")
                        elif clause_type == SQLClauseType.FROM:
                            query_parts.append(f"FROM {child.content}")
                        elif clause_type == SQLClauseType.WHERE:
                            query_parts.append(f"WHERE {child.content}")
                        elif clause_type == SQLClauseType.GROUP_BY:
                            query_parts.append(f"GROUP BY {child.content}")
                        elif clause_type == SQLClauseType.HAVING:
                            query_parts.append(f"HAVING {child.content}")
                        elif clause_type == SQLClauseType.ORDER_BY:
                            query_parts.append(f"ORDER BY {child.content}")
                        elif clause_type == SQLClauseType.LIMIT:
                            # 特殊处理LIMIT子句，根据目标数据库类型
                            if self.target_db in ["db2", "oracle"]:
                                query_parts.append(f"FETCH FIRST {child.content} ROWS ONLY")
                            elif self.target_db == "sqlserver":
                                # SQL Server需要ORDER BY子句才能使用OFFSET FETCH
                                if SQLClauseType.ORDER_BY in [c.node_type for c in node.children]:
                                    query_parts.append(f"OFFSET 0 ROWS FETCH NEXT {child.content} ROWS ONLY")
                                else:
                                    # 如果没有ORDER BY子句，需要在SELECT子句中添加TOP
                                    for i, part in enumerate(query_parts):
                                        if part.upper().startswith("SELECT"):
                                            query_parts[i] = f"SELECT TOP {child.content} {part[7:]}"
                                            break
                            else:
                                query_parts.append(f"LIMIT {child.content}")
                        elif clause_type == SQLClauseType.OFFSET:
                            if self.target_db in ["db2", "oracle", "sqlserver"]:
                                query_parts.append(f"OFFSET {child.content} ROWS")
                            else:
                                query_parts.append(f"OFFSET {child.content}")
                        elif clause_type == SQLClauseType.UNION:
                            union_query = self._generate_query(child.children[0])
                            query_parts.append(f"UNION {union_query}")

            return " ".join(query_parts)

        # 如果是子查询节点，递归生成子查询
        elif node.node_type == SQLClauseType.SUBQUERY:
            if node.children:
                subquery = self._generate_query(node.children[0])
                return f"({subquery})"
            else:
                return node.content

        # 其他节点类型，直接返回内容
        else:
            return node.content

    # 转换SELECT子句
    def _transform_select_clause(self, content: str) -> str:
        """
        转换SELECT子句

        Args:
            content: SELECT子句内容

        Returns:
            str: 转换后的SELECT子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        return content

    # 转换FROM子句
    def _transform_from_clause(self, content: str) -> str:
        """
        转换FROM子句

        Args:
            content: FROM子句内容

        Returns:
            str: 转换后的FROM子句内容
        """
        # 转换子查询
        content = self._transform_subqueries_in_text(content)

        return content

    # 转换WHERE子句
    def _transform_where_clause(self, content: str) -> str:
        """
        转换WHERE子句

        Args:
            content: WHERE子句内容

        Returns:
            str: 转换后的WHERE子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        # 转换子查询
        content = self._transform_subqueries_in_text(content)

        return content

    # 转换GROUP BY子句
    def _transform_group_by_clause(self, content: str) -> str:
        """
        转换GROUP BY子句

        Args:
            content: GROUP BY子句内容

        Returns:
            str: 转换后的GROUP BY子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        return content

    # 转换HAVING子句
    def _transform_having_clause(self, content: str) -> str:
        """
        转换HAVING子句

        Args:
            content: HAVING子句内容

        Returns:
            str: 转换后的HAVING子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        # 转换子查询
        content = self._transform_subqueries_in_text(content)

        return content

    # 转换ORDER BY子句
    def _transform_order_by_clause(self, content: str) -> str:
        """
        转换ORDER BY子句

        Args:
            content: ORDER BY子句内容

        Returns:
            str: 转换后的ORDER BY子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        return content

    # 转换LIMIT子句
    def _transform_limit_clause(self, content: str) -> str:
        """
        转换LIMIT子句

        Args:
            content: LIMIT子句内容

        Returns:
            str: 转换后的LIMIT子句内容
        """
        # 获取分页转换规则
        pagination_rule = self.pagination_rules.get(self.source_db, {}).get(self.target_db)

        if pagination_rule:
            return pagination_rule(content)

        return content

    # 转换OFFSET子句
    def _transform_offset_clause(self, content: str) -> str:
        """
        转换OFFSET子句

        Args:
            content: OFFSET子句内容

        Returns:
            str: 转换后的OFFSET子句内容
        """
        return content

    # 转换JOIN子句
    def _transform_join_clause(self, content: str) -> str:
        """
        转换JOIN子句

        Args:
            content: JOIN子句内容

        Returns:
            str: 转换后的JOIN子句内容
        """
        # 转换函数调用
        content = self._transform_functions_in_text(content)

        # 转换子查询
        content = self._transform_subqueries_in_text(content)

        return content

    # 转换函数调用
    def _transform_function(self, content: str) -> str:
        """
        转换函数调用

        Args:
            content: 函数调用内容

        Returns:
            str: 转换后的函数调用内容
        """
        # 检查缓存中是否已有转换结果
        if content in self.function_cache:
            return self.function_cache[content]

        # 解析函数名和参数
        match = self.func_args_pattern.match(content)
        if not match:
            return content

        func_name = match.group(1)
        func_args_str = match.group(2)

        # 解析函数参数
        func_args = self._parse_function_args(func_args_str)

        # 转换函数参数
        transformed_args = []
        for arg in func_args:
            # 识别参数类型
            param_type = self._identify_param_type(arg)

            # 转换参数
            transformed_arg = self._convert_param_type(arg, param_type, self.source_db, self.target_db)

            transformed_args.append(transformed_arg)

        # 重新组合函数调用
        transformed_content = f"{func_name}({', '.join(transformed_args)})"

        # 获取函数转换规则
        function_rule = self.function_rules.get(self.source_db, {}).get(self.target_db)

        if function_rule:
            # 应用基本函数转换规则
            transformed_content = function_rule(transformed_content)

        # 应用窗口函数转换规则
        transformed_content = self._transform_window_function(transformed_content)

        # 缓存转换结果
        self.function_cache[content] = transformed_content

        return transformed_content

    def _transform_window_function(self, content: str) -> str:
        """
        转换窗口函数

        Args:
            content: 包含窗口函数的内容

        Returns:
            str: 转换后的内容
        """
        # 检查是否包含窗口函数关键字
        if not self.window_pattern.search(content):
            return content

        # 窗口函数缓存，避免重复转换相同的窗口函数
        cache_key = f"{self.source_db}_{self.target_db}_{content}"
        if cache_key in self.function_cache:
            return self.function_cache[cache_key]

        # 处理不同数据库之间窗口函数语法的差异
        if self.source_db == 'postgres' and self.target_db in ['db2', 'oracle', 'sqlserver', 'mysql']:
            # PostgreSQL窗口函数语法基本兼容其他数据库，但可能有一些特殊情况

            # 预编译正则表达式
            rows_pattern = re.compile(
                r'OVER\s*\(\s*PARTITION\s+BY\s+([^)]+?)\s+ORDER\s+BY\s+([^)]+?)\s+ROWS\s+BETWEEN\s+UNBOUNDED\s+PRECEDING\s+AND\s+CURRENT\s+ROW\s*\)',
                re.IGNORECASE
            )
            range_pattern = re.compile(
                r'OVER\s*\(\s*PARTITION\s+BY\s+([^)]+?)\s+ORDER\s+BY\s+([^)]+?)\s+RANGE\s+BETWEEN\s+UNBOUNDED\s+PRECEDING\s+AND\s+CURRENT\s+ROW\s*\)',
                re.IGNORECASE
            )

            # 处理ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW语法
            content = rows_pattern.sub(
                r'OVER (PARTITION BY \1 ORDER BY \2 ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)',
                content
            )

            # 处理RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW语法
            content = range_pattern.sub(
                r'OVER (PARTITION BY \1 ORDER BY \2 RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)',
                content
            )

        elif self.source_db == 'mysql' and self.target_db in ['db2', 'oracle', 'postgres', 'sqlserver']:
            # MySQL 8.0+支持窗口函数，语法与其他数据库基本兼容
            pass

        elif self.source_db == 'sqlserver' and self.target_db in ['db2', 'oracle', 'postgres', 'mysql']:
            # SQL Server窗口函数语法基本兼容其他数据库
            pass

        elif self.source_db == 'oracle' and self.target_db in ['db2', 'postgres', 'sqlserver', 'mysql']:
            # Oracle窗口函数语法基本兼容其他数据库
            pass

        elif self.source_db == 'db2' and self.target_db in ['oracle', 'postgres', 'sqlserver', 'mysql']:
            # DB2窗口函数语法基本兼容其他数据库
            pass

        # 处理特定窗口函数的差异
        if self.source_db in ['postgres', 'oracle'] and self.target_db == 'mysql':
            # MySQL 8.0之前不支持PERCENT_RANK和CUME_DIST函数
            # 这里简化处理，实际可能需要更复杂的转换
            percent_rank_pattern = re.compile(r'PERCENT_RANK\s*\(\s*\)', re.IGNORECASE)
            cume_dist_pattern = re.compile(r'CUME_DIST\s*\(\s*\)', re.IGNORECASE)

            if percent_rank_pattern.search(content) and not self._is_mysql_8_plus():
                content = "/* MySQL 8.0+ required for PERCENT_RANK function */ " + content

            if cume_dist_pattern.search(content) and not self._is_mysql_8_plus():
                content = "/* MySQL 8.0+ required for CUME_DIST function */ " + content

        # 缓存转换结果
        self.function_cache[cache_key] = content

        return content

    def _is_mysql_8_plus(self) -> bool:
        """
        检查目标MySQL版本是否为8.0+

        Returns:
            bool: 是否为MySQL 8.0+
        """
        # 这里简化处理，实际应该根据连接信息或配置获取MySQL版本
        # 默认假设为MySQL 8.0+
        return True

    # 转换表达式
    def _transform_expression(self, content: str) -> str:
        """
        转换表达式

        Args:
            content: 表达式内容

        Returns:
            str: 转换后的表达式内容
        """
        # 检查缓存中是否已有转换结果
        if content in self.function_cache:
            return self.function_cache[content]

        # 使用复杂表达式转换方法
        result = self._transform_complex_expression(content)

        # 缓存转换结果
        self.function_cache[content] = result

        return result

    # 在文本中转换函数调用
    def _transform_functions_in_text(self, text: str) -> str:
        """
        在文本中转换函数调用

        Args:
            text: 文本内容

        Returns:
            str: 转换后的文本内容
        """
        # 使用更精确的方法查找函数调用，处理嵌套括号
        # 这个方法能够处理嵌套函数调用和复杂参数
        # 例如：func1(arg1, func2(arg2, arg3), arg4)

        # 检查是否有函数调用
        if not self.func_pattern.search(text):
            return text

        # 检查缓存中是否已有转换结果
        if text in self.function_cache:
            return self.function_cache[text]

        # 使用正则表达式直接匹配函数调用
        matches = list(self.func_args_pattern.finditer(text))

        # 如果没有匹配到函数调用，返回原始文本
        if not matches:
            return text

        # 从最内层的函数调用开始处理
        # 这样可以确保嵌套函数调用的参数被正确转换
        result = text
        for match in reversed(matches):
            func_name = match.group(1)
            func_args_str = match.group(2)
            full_func = match.group(0)

            # 检查是否是已知的函数
            is_known_func = False
            for pattern in [self.date_func_pattern, self.string_func_pattern,
                           self.special_func_pattern, self.numeric_func_pattern,
                           self.aggregate_func_pattern, self.json_func_pattern]:
                if pattern.match(full_func):
                    is_known_func = True
                    break

            if not is_known_func:
                continue

            # 解析函数参数
            func_args = self._parse_function_args(func_args_str)

            # 转换函数参数
            transformed_args = []
            for arg in func_args:
                # 识别参数类型
                param_type = self._identify_param_type(arg)

                # 转换参数
                transformed_arg = self._convert_param_type(arg, param_type, self.source_db, self.target_db)

                transformed_args.append(transformed_arg)

            # 重新组合函数调用
            transformed_func = f"{func_name}({', '.join(transformed_args)})"

            # 获取函数转换规则
            function_rule = self.function_rules.get(self.source_db, {}).get(self.target_db)

            if function_rule:
                # 应用基本函数转换规则
                transformed_func = function_rule(transformed_func)

            # 应用窗口函数转换规则
            if self.window_pattern.search(transformed_func):
                transformed_func = self._transform_window_function(transformed_func)

            # 替换原始函数调用
            start, end = match.span()
            result = result[:start] + transformed_func + result[end:]

            # 缓存转换结果
            self.function_cache[full_func] = transformed_func

        # 缓存整个文本的转换结果
        self.function_cache[text] = result

        return result

    def _transform_complex_expression(self, expression: str) -> str:
        """
        转换复杂表达式

        Args:
            expression: 表达式内容

        Returns:
            str: 转换后的表达式内容
        """
        # 检查缓存中是否已有转换结果
        if expression in self.function_cache:
            return self.function_cache[expression]

        # 首先转换表达式中的函数调用
        result = self._transform_functions_in_text(expression)

        # 然后转换表达式中的子查询
        result = self._transform_subqueries_in_text(result)

        # 处理特殊的表达式模式
        # 1. 日期运算
        if re.search(r'\b(date|timestamp|time)\b', result, re.IGNORECASE):
            # 日期加减运算
            if self.source_db == 'oracle' and self.target_db == 'mysql':
                # Oracle: date + n -> MySQL: DATE_ADD(date, INTERVAL n DAY)
                result = re.sub(
                    r'(\w+)\s*\+\s*(\d+)',
                    r'DATE_ADD(\1, INTERVAL \2 DAY)',
                    result,
                    flags=re.IGNORECASE
                )

                # Oracle: date - n -> MySQL: DATE_SUB(date, INTERVAL n DAY)
                result = re.sub(
                    r'(\w+)\s*\-\s*(\d+)',
                    r'DATE_SUB(\1, INTERVAL \2 DAY)',
                    result,
                    flags=re.IGNORECASE
                )
            elif self.source_db == 'mysql' and self.target_db == 'oracle':
                # MySQL: DATE_ADD(date, INTERVAL n DAY) -> Oracle: date + n
                result = re.sub(
                    r'DATE_ADD\s*\(\s*([^,]+)\s*,\s*INTERVAL\s+(\d+)\s+DAY\s*\)',
                    r'(\1 + \2)',
                    result,
                    flags=re.IGNORECASE
                )

                # MySQL: DATE_SUB(date, INTERVAL n DAY) -> Oracle: date - n
                result = re.sub(
                    r'DATE_SUB\s*\(\s*([^,]+)\s*,\s*INTERVAL\s+(\d+)\s+DAY\s*\)',
                    r'(\1 - \2)',
                    result,
                    flags=re.IGNORECASE
                )

        # 2. 字符串连接
        if re.search(r'(concat|[\|\|])', result, re.IGNORECASE):
            if self.source_db in ['oracle', 'postgres'] and self.target_db == 'mysql':
                # Oracle/Postgres: str1 || str2 -> MySQL: CONCAT(str1, str2)
                result = re.sub(
                    r'([^|]+)\s*\|\|\s*([^|]+)',
                    r'CONCAT(\1, \2)',
                    result,
                    flags=re.IGNORECASE
                )
            elif self.source_db == 'mysql' and self.target_db in ['oracle', 'postgres']:
                # MySQL: CONCAT(str1, str2) -> Oracle/Postgres: str1 || str2
                result = re.sub(
                    r'CONCAT\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
                    r'(\1 || \2)',
                    result,
                    flags=re.IGNORECASE
                )

        # 3. 类型转换
        if re.search(r'(cast|convert|::)', result, re.IGNORECASE):
            if self.source_db == 'postgres' and self.target_db == 'oracle':
                # Postgres: expr::type -> Oracle: CAST(expr AS type)
                result = re.sub(
                    r'([^:]+)::\s*(\w+)',
                    r'CAST(\1 AS \2)',
                    result,
                    flags=re.IGNORECASE
                )
            elif self.source_db == 'oracle' and self.target_db == 'postgres':
                # Oracle: CAST(expr AS type) -> Postgres: expr::type
                result = re.sub(
                    r'CAST\s*\(\s*([^A]+)\s*AS\s*(\w+)\s*\)',
                    r'\1::\2',
                    result,
                    flags=re.IGNORECASE
                )

        # 缓存转换结果
        self.function_cache[expression] = result

        return result

    # 在文本中转换子查询
    def _transform_subqueries_in_text(self, text: str) -> str:
        """
        在文本中转换子查询

        Args:
            text: 文本内容

        Returns:
            str: 转换后的文本内容
        """
        # 使用更精确的方法查找子查询，处理嵌套子查询
        # 例如：SELECT * FROM (SELECT * FROM (SELECT * FROM table) t1) t2

        # 检查是否有子查询
        if not self.subquery_pattern.search(text):
            return text

        # 子查询的起始位置
        pos = 0
        result = ""

        # 子查询缓存，避免重复转换相同的子查询
        subquery_cache = {}

        while pos < len(text):
            # 查找下一个可能的子查询
            subquery_match = self.subquery_pattern.search(text[pos:])

            if not subquery_match:
                # 没有找到更多子查询，添加剩余文本
                result += text[pos:]
                break

            # 子查询的起始位置
            subquery_start = pos + subquery_match.start()
            inner_start = pos + subquery_match.end()

            # 添加子查询前的文本
            result += text[pos:subquery_start]

            # 查找匹配的右括号，考虑嵌套括号
            bracket_count = 1
            subquery_end = inner_start

            while subquery_end < len(text) and bracket_count > 0:
                if text[subquery_end] == '(':
                    bracket_count += 1
                elif text[subquery_end] == ')':
                    bracket_count -= 1
                subquery_end += 1

            if bracket_count > 0:
                # 括号不匹配，保留原始文本
                result += text[subquery_start:subquery_end]
                pos = subquery_end
                continue

            # 提取完整的子查询（包括外层括号）
            full_subquery = text[subquery_start:subquery_end]

            # 检查缓存中是否已有转换结果
            if full_subquery in subquery_cache:
                transformed_subquery = subquery_cache[full_subquery]
            else:
                # 提取子查询内容（不包括外层括号）
                inner_query = text[inner_start-7:subquery_end-1]  # -7 to include "SELECT"

                # 转换子查询
                transformed_query = self.transform(inner_query)

                # 添加转换后的子查询（包括外层括号）
                transformed_subquery = f"({transformed_query})"

                # 缓存转换结果
                subquery_cache[full_subquery] = transformed_subquery

            # 添加转换后的子查询
            result += transformed_subquery

            # 更新位置
            pos = subquery_end

        return result

    # 分页转换规则
    def _db2_to_oracle_pagination(self, content: str) -> str:
        """DB2 -> Oracle分页转换"""
        return content

    def _db2_to_postgres_pagination(self, content: str) -> str:
        """DB2 -> PostgreSQL分页转换"""
        return content

    def _db2_to_sqlserver_pagination(self, content: str) -> str:
        """DB2 -> SQL Server分页转换"""
        return content

    def _db2_to_mysql_pagination(self, content: str) -> str:
        """DB2 -> MySQL分页转换"""
        return content

    def _oracle_to_db2_pagination(self, content: str) -> str:
        """Oracle -> DB2分页转换"""
        return content

    def _oracle_to_postgres_pagination(self, content: str) -> str:
        """Oracle -> PostgreSQL分页转换"""
        return content

    def _oracle_to_sqlserver_pagination(self, content: str) -> str:
        """Oracle -> SQL Server分页转换"""
        return content

    def _oracle_to_mysql_pagination(self, content: str) -> str:
        """Oracle -> MySQL分页转换"""
        return content

    def _postgres_to_db2_pagination(self, content: str) -> str:
        """PostgreSQL -> DB2分页转换"""
        return content

    def _postgres_to_oracle_pagination(self, content: str) -> str:
        """PostgreSQL -> Oracle分页转换"""
        return content

    def _postgres_to_sqlserver_pagination(self, content: str) -> str:
        """PostgreSQL -> SQL Server分页转换"""
        return content

    def _postgres_to_mysql_pagination(self, content: str) -> str:
        """PostgreSQL -> MySQL分页转换"""
        return content

    def _sqlserver_to_db2_pagination(self, content: str) -> str:
        """SQL Server -> DB2分页转换"""
        return content

    def _sqlserver_to_oracle_pagination(self, content: str) -> str:
        """SQL Server -> Oracle分页转换"""
        return content

    def _sqlserver_to_postgres_pagination(self, content: str) -> str:
        """SQL Server -> PostgreSQL分页转换"""
        return content

    def _sqlserver_to_mysql_pagination(self, content: str) -> str:
        """SQL Server -> MySQL分页转换"""
        return content

    def _mysql_to_db2_pagination(self, content: str) -> str:
        """MySQL -> DB2分页转换"""
        return content

    def _mysql_to_oracle_pagination(self, content: str) -> str:
        """MySQL -> Oracle分页转换"""
        return content

    def _mysql_to_postgres_pagination(self, content: str) -> str:
        """MySQL -> PostgreSQL分页转换"""
        return content

    def _mysql_to_sqlserver_pagination(self, content: str) -> str:
        """MySQL -> SQL Server分页转换"""
        return content

    # 函数转换规则
    def _db2_to_oracle_functions(self, content: str) -> str:
        """DB2 -> Oracle函数转换"""
        # 日期函数转换
        # VARCHAR_FORMAT -> TO_CHAR
        content = re.sub(
            r'VARCHAR_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), 'db2', 'oracle')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT DATE -> SYSDATE
        content = content.replace("CURRENT DATE", "SYSDATE")

        # CURRENT TIME -> SYSTIMESTAMP
        content = content.replace("CURRENT TIME", "SYSTIMESTAMP")

        # CURRENT TIMESTAMP -> SYSTIMESTAMP
        content = content.replace("CURRENT TIMESTAMP", "SYSTIMESTAMP")

        # YEAR -> EXTRACT(YEAR FROM ...)
        content = re.sub(
            r'YEAR\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(YEAR FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # MONTH -> EXTRACT(MONTH FROM ...)
        content = re.sub(
            r'MONTH\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(MONTH FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # DAY -> EXTRACT(DAY FROM ...)
        content = re.sub(
            r'DAY\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(DAY FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # DATE -> TO_DATE
        content = re.sub(
            r'DATE\s*\(\s*([^)]+)\s*\)',
            r"TO_DATE(\1, 'YYYY-MM-DD')",
            content,
            flags=re.IGNORECASE
        )

        # DAYS(date1) - DAYS(date2) -> (date1 - date2)
        content = re.sub(
            r'DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)',
            r'(\1 - \2)',
            content,
            flags=re.IGNORECASE
        )

        # (date + n DAYS) -> (date + n)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*DAYS\s*\)',
            r'(\1 + \2)',
            content,
            flags=re.IGNORECASE
        )

        # 数值函数转换
        # CEILING -> CEIL
        content = re.sub(
            r'CEILING\s*\(\s*([^)]+)\s*\)',
            r'CEIL(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # 大多数字符串函数在DB2和Oracle中是相同的，如LENGTH, SUBSTR等

        # 特殊函数转换
        # COALESCE -> NVL (两者都支持，但Oracle更常用NVL)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'NVL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # VALUE -> NVL
        content = re.sub(
            r'VALUE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'NVL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _db2_to_postgres_functions(self, content: str) -> str:
        """DB2 -> PostgreSQL函数转换"""
        # 日期函数转换
        # VARCHAR_FORMAT -> TO_CHAR
        content = re.sub(
            r'VARCHAR_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), 'db2', 'postgres')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT DATE -> CURRENT_DATE
        content = content.replace("CURRENT DATE", "CURRENT_DATE")

        # CURRENT TIME -> CURRENT_TIME
        content = content.replace("CURRENT TIME", "CURRENT_TIME")

        # CURRENT TIMESTAMP -> CURRENT_TIMESTAMP
        content = content.replace("CURRENT TIMESTAMP", "CURRENT_TIMESTAMP")

        # YEAR -> EXTRACT(YEAR FROM ...)
        content = re.sub(
            r'YEAR\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(YEAR FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # MONTH -> EXTRACT(MONTH FROM ...)
        content = re.sub(
            r'MONTH\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(MONTH FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # DAY -> EXTRACT(DAY FROM ...)
        content = re.sub(
            r'DAY\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(DAY FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # DATE -> TO_DATE
        content = re.sub(
            r'DATE\s*\(\s*([^)]+)\s*\)',
            r"TO_DATE(\1, 'YYYY-MM-DD')",
            content,
            flags=re.IGNORECASE
        )

        # DAYS(date1) - DAYS(date2) -> DATE_PART('day', date1 - date2)
        content = re.sub(
            r'DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)',
            r"DATE_PART('day', \1 - \2)",
            content,
            flags=re.IGNORECASE
        )

        # (date + n DAYS) -> (date + INTERVAL 'n DAY')
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*DAYS\s*\)',
            r"(\1 + INTERVAL '\2 DAY')",
            content,
            flags=re.IGNORECASE
        )

        return content

    def _db2_to_sqlserver_functions(self, content: str) -> str:
        """DB2 -> SQL Server函数转换"""
        # 日期函数转换
        # VARCHAR_FORMAT -> FORMAT
        content = re.sub(
            r'VARCHAR_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'db2', 'sqlserver')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT DATE -> CONVERT(DATE, GETDATE())
        content = content.replace("CURRENT DATE", "CONVERT(DATE, GETDATE())")

        # CURRENT TIME -> CONVERT(TIME, GETDATE())
        content = content.replace("CURRENT TIME", "CONVERT(TIME, GETDATE())")

        # CURRENT TIMESTAMP -> GETDATE()
        content = content.replace("CURRENT TIMESTAMP", "GETDATE()")

        # YEAR -> YEAR
        # MONTH -> MONTH
        # DAY -> DAY
        # 这些函数在SQL Server中有相同的名称，无需转换

        # DATE -> CONVERT(DATE, ...)
        content = re.sub(
            r'DATE\s*\(\s*([^)]+)\s*\)',
            r"CONVERT(DATE, \1)",
            content,
            flags=re.IGNORECASE
        )

        # DAYS(date1) - DAYS(date2) -> DATEDIFF(DAY, date2, date1)
        content = re.sub(
            r'DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)',
            r'DATEDIFF(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # (date + n DAYS) -> DATEADD(DAY, n, date)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*DAYS\s*\)',
            r'DATEADD(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # LENGTH -> LEN
        content = re.sub(
            r'LENGTH\s*\(\s*([^)]+)\s*\)',
            r'LEN(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 数值函数转换
        # 大多数数值函数在DB2和SQL Server中是相同的，如CEILING, FLOOR等

        # 特殊函数转换
        # COALESCE -> ISNULL (SQL Server更常用ISNULL，但也支持COALESCE)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'ISNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # VALUE -> ISNULL
        content = re.sub(
            r'VALUE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'ISNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _db2_to_mysql_functions(self, content: str) -> str:
        """DB2 -> MySQL函数转换"""
        # 日期函数转换
        # VARCHAR_FORMAT -> DATE_FORMAT
        content = re.sub(
            r'VARCHAR_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"DATE_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'db2', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT DATE -> CURDATE()
        content = content.replace("CURRENT DATE", "CURDATE()")

        # CURRENT TIME -> CURTIME()
        content = content.replace("CURRENT TIME", "CURTIME()")

        # CURRENT TIMESTAMP -> NOW()
        content = content.replace("CURRENT TIMESTAMP", "NOW()")

        # YEAR -> YEAR
        # MONTH -> MONTH
        # DAY -> DAY
        # 这些函数在MySQL中有相同的名称，无需转换

        # DATE -> STR_TO_DATE
        content = re.sub(
            r'DATE\s*\(\s*([^)]+)\s*\)',
            r"STR_TO_DATE(\1, '%Y-%m-%d')",
            content,
            flags=re.IGNORECASE
        )

        # DAYS(date1) - DAYS(date2) -> DATEDIFF(date1, date2)
        content = re.sub(
            r'DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)',
            r'DATEDIFF(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # (date + n DAYS) -> DATE_ADD(date, INTERVAL n DAY)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*DAYS\s*\)',
            r'DATE_ADD(\1, INTERVAL \2 DAY)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _oracle_to_db2_functions(self, content: str) -> str:
        """Oracle -> DB2函数转换"""
        # 日期函数转换
        # TO_CHAR -> VARCHAR_FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"VARCHAR_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'db2')}')",
            content,
            flags=re.IGNORECASE
        )

        # SYSDATE -> CURRENT DATE
        content = content.replace("SYSDATE", "CURRENT DATE")

        # SYSTIMESTAMP -> CURRENT TIMESTAMP
        content = content.replace("SYSTIMESTAMP", "CURRENT TIMESTAMP")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> DATE
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\)',
            r'DATE(\1)',
            content,
            flags=re.IGNORECASE
        )

        # (date1 - date2) -> DAYS(date1) - DAYS(date2)
        content = re.sub(
            r'\(\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'(DAYS(\1) - DAYS(\2))',
            content,
            flags=re.IGNORECASE
        )

        # (date + n) -> (date + n DAYS)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*\)',
            r'(\1 + \2 DAYS)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _oracle_to_postgres_functions(self, content: str) -> str:
        """Oracle -> PostgreSQL函数转换"""
        # 日期函数转换
        # TO_CHAR -> TO_CHAR (相同函数名，但格式可能不同)
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'postgres')}')",
            content,
            flags=re.IGNORECASE
        )

        # SYSDATE -> CURRENT_DATE
        content = content.replace("SYSDATE", "CURRENT_DATE")

        # SYSTIMESTAMP -> CURRENT_TIMESTAMP
        content = content.replace("SYSTIMESTAMP", "CURRENT_TIMESTAMP")

        # EXTRACT(YEAR FROM ...) -> EXTRACT(YEAR FROM ...) (相同语法)
        # EXTRACT(MONTH FROM ...) -> EXTRACT(MONTH FROM ...) (相同语法)
        # EXTRACT(DAY FROM ...) -> EXTRACT(DAY FROM ...) (相同语法)

        # TO_DATE -> TO_DATE (相同函数名，但格式可能不同)
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_DATE({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'postgres')}')",
            content,
            flags=re.IGNORECASE
        )

        # (date1 - date2) -> DATE_PART('day', date1 - date2)
        content = re.sub(
            r'\(\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r"DATE_PART('day', \1 - \2)",
            content,
            flags=re.IGNORECASE
        )

        # (date + n) -> (date + INTERVAL 'n DAY')
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*\)',
            r"(\1 + INTERVAL '\2 DAY')",
            content,
            flags=re.IGNORECASE
        )

        return content

    def _oracle_to_sqlserver_functions(self, content: str) -> str:
        """Oracle -> SQL Server函数转换"""
        # 日期函数转换
        # TO_CHAR -> FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'sqlserver')}')",
            content,
            flags=re.IGNORECASE
        )

        # SYSDATE -> CONVERT(DATE, GETDATE())
        content = content.replace("SYSDATE", "CONVERT(DATE, GETDATE())")

        # SYSTIMESTAMP -> GETDATE()
        content = content.replace("SYSTIMESTAMP", "GETDATE()")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> CONVERT(DATE, ...)
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\)',
            r'CONVERT(DATE, \1)',
            content,
            flags=re.IGNORECASE
        )

        # (date1 - date2) -> DATEDIFF(DAY, date2, date1)
        content = re.sub(
            r'\(\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'DATEDIFF(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # (date + n) -> DATEADD(DAY, n, date)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*\)',
            r'DATEADD(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # 数值函数转换
        # CEIL -> CEILING
        content = re.sub(
            r'CEIL\s*\(\s*([^)]+)\s*\)',
            r'CEILING(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # 大多数字符串函数在Oracle和SQL Server中有不同的名称
        # SUBSTR -> SUBSTRING
        content = re.sub(
            r'SUBSTR\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTRING({m.group(1)}, {m.group(2)}, {m.group(3) if m.group(3) else '8000'})",
            content,
            flags=re.IGNORECASE
        )

        # INSTR -> CHARINDEX (参数顺序不同)
        content = re.sub(
            r'INSTR\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^,)]+))?(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"CHARINDEX({m.group(2)}, {m.group(1)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # NVL -> ISNULL
        content = re.sub(
            r'NVL\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'ISNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NVL2 -> CASE WHEN ... THEN ... ELSE ... END
        content = re.sub(
            r'NVL2\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'CASE WHEN \1 IS NOT NULL THEN \2 ELSE \3 END',
            content,
            flags=re.IGNORECASE
        )

        # DECODE -> CASE
        content = re.sub(
            r'DECODE\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^,]+)(?:\s*,\s*([^,]+)\s*,\s*([^,]+))*(?:\s*,\s*([^)]+))?\s*\)',
            self._oracle_decode_to_sqlserver_case,
            content,
            flags=re.IGNORECASE
        )

        return content

    def _oracle_to_mysql_functions(self, content: str) -> str:
        """Oracle -> MySQL函数转换"""
        # 日期函数转换
        # TO_CHAR -> DATE_FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"DATE_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # SYSDATE -> CURDATE()
        content = content.replace("SYSDATE", "CURDATE()")

        # SYSTIMESTAMP -> NOW()
        content = content.replace("SYSTIMESTAMP", "NOW()")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> STR_TO_DATE
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"STR_TO_DATE({m.group(1)}, '{self._adapt_date_format(m.group(2), 'oracle', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # (date1 - date2) -> DATEDIFF(date1, date2)
        content = re.sub(
            r'\(\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'DATEDIFF(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # (date + n) -> DATE_ADD(date, INTERVAL n DAY)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*(\d+)\s*\)',
            r'DATE_ADD(\1, INTERVAL \2 DAY)',
            content,
            flags=re.IGNORECASE
        )

        # 数值函数转换
        # CEIL -> CEILING (MySQL两者都支持，但CEILING更常用)
        content = re.sub(
            r'CEIL\s*\(\s*([^)]+)\s*\)',
            r'CEILING(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # SUBSTR -> SUBSTRING (MySQL两者都支持，但SUBSTRING更常用)
        content = re.sub(
            r'SUBSTR\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTRING({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # INSTR -> LOCATE (参数顺序不同)
        content = re.sub(
            r'INSTR\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^,)]+))?(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"LOCATE({m.group(2)}, {m.group(1)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # NVL -> IFNULL
        content = re.sub(
            r'NVL\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'IFNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NVL2 -> IF
        content = re.sub(
            r'NVL2\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'IF(\1 IS NOT NULL, \2, \3)',
            content,
            flags=re.IGNORECASE
        )

        # DECODE -> CASE
        content = re.sub(
            r'DECODE\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^,]+)(?:\s*,\s*([^,]+)\s*,\s*([^,]+))*(?:\s*,\s*([^)]+))?\s*\)',
            self._oracle_decode_to_mysql_case,
            content,
            flags=re.IGNORECASE
        )

        return content

    def _postgres_to_db2_functions(self, content: str) -> str:
        """PostgreSQL -> DB2函数转换"""
        # 日期函数转换
        # TO_CHAR -> VARCHAR_FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"VARCHAR_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'db2')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT_DATE -> CURRENT DATE
        content = content.replace("CURRENT_DATE", "CURRENT DATE")

        # CURRENT_TIME -> CURRENT TIME
        content = content.replace("CURRENT_TIME", "CURRENT TIME")

        # CURRENT_TIMESTAMP -> CURRENT TIMESTAMP
        content = content.replace("CURRENT_TIMESTAMP", "CURRENT TIMESTAMP")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> DATE
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\)',
            r'DATE(\1)',
            content,
            flags=re.IGNORECASE
        )

        # DATE_PART('day', date1 - date2) -> DAYS(date1) - DAYS(date2)
        content = re.sub(
            r'DATE_PART\s*\(\s*\'day\'\s*,\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'(DAYS(\1) - DAYS(\2))',
            content,
            flags=re.IGNORECASE
        )

        # (date + INTERVAL 'n DAY') -> (date + n DAYS)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
            r'(\1 + \2 DAYS)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # SUBSTRING(str FROM pos FOR len) -> SUBSTR(str, pos, len)
        content = re.sub(
            r'SUBSTRING\s*\(\s*([^F]+)\s*FROM\s*([^F]+)(?:\s*FOR\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTR({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 数组函数转换
        # array_length(arr, 1) -> CARDINALITY(arr)
        content = re.sub(
            r'array_length\s*\(\s*([^,]+)\s*,\s*1\s*\)',
            r'CARDINALITY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # array_to_string(arr, ',') -> LISTAGG(arr, ',')
        content = re.sub(
            r'array_to_string\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"LISTAGG(\1, '\2')",
            content,
            flags=re.IGNORECASE
        )

        # string_to_array(str, ',') -> 没有直接对应函数，保持原样

        # 聚合函数转换
        # string_agg(expr, ',') -> LISTAGG(expr, ',')
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"LISTAGG(\1, '\2')",
            content,
            flags=re.IGNORECASE
        )

        # 窗口函数大多数在DB2中有相同的名称，如RANK, DENSE_RANK, ROW_NUMBER等

        # JSON函数转换 - DB2支持有限的JSON功能
        # jsonb_extract_path_text(data, 'key') -> JSON_VALUE(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path_text\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_VALUE(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # jsonb_extract_path(data, 'key') -> JSON_QUERY(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_QUERY(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # COALESCE -> VALUE (DB2支持COALESCE，但VALUE是DB2特有的)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'VALUE(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NULLIF -> NULLIF (相同函数名)

        return content

    def _postgres_to_oracle_functions(self, content: str) -> str:
        """PostgreSQL -> Oracle函数转换"""
        # 日期函数转换
        # TO_CHAR -> TO_CHAR (相同函数名，但格式可能不同)
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'oracle')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT_DATE -> SYSDATE
        content = content.replace("CURRENT_DATE", "SYSDATE")

        # CURRENT_TIME -> SYSTIMESTAMP
        content = content.replace("CURRENT_TIME", "SYSTIMESTAMP")

        # CURRENT_TIMESTAMP -> SYSTIMESTAMP
        content = content.replace("CURRENT_TIMESTAMP", "SYSTIMESTAMP")

        # EXTRACT(YEAR FROM ...) -> EXTRACT(YEAR FROM ...) (相同语法)
        # EXTRACT(MONTH FROM ...) -> EXTRACT(MONTH FROM ...) (相同语法)
        # EXTRACT(DAY FROM ...) -> EXTRACT(DAY FROM ...) (相同语法)

        # TO_DATE -> TO_DATE (相同函数名，但格式可能不同)
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_DATE({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'oracle')}')",
            content,
            flags=re.IGNORECASE
        )

        # DATE_PART('day', date1 - date2) -> (date1 - date2)
        content = re.sub(
            r'DATE_PART\s*\(\s*\'day\'\s*,\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'(\1 - \2)',
            content,
            flags=re.IGNORECASE
        )

        # (date + INTERVAL 'n DAY') -> (date + n)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
            r'(\1 + \2)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # SUBSTRING(str FROM pos FOR len) -> SUBSTR(str, pos, len)
        content = re.sub(
            r'SUBSTRING\s*\(\s*([^F]+)\s*FROM\s*([^F]+)(?:\s*FOR\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTR({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 数组函数转换 - Oracle不直接支持数组，需要使用嵌套表或VARRAY
        # array_length(arr, 1) -> 没有直接对应函数，可能需要使用自定义函数

        # array_to_string(arr, ',') -> 可以使用LISTAGG或自定义函数
        content = re.sub(
            r'array_to_string\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"(SELECT LISTAGG(column_value, '{m.group(2)}') FROM TABLE({m.group(1)}))",
            content,
            flags=re.IGNORECASE
        )

        # 聚合函数转换
        # string_agg(expr, ',') -> LISTAGG(expr, ',')
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"LISTAGG(\1, '\2') WITHIN GROUP (ORDER BY NULL)",
            content,
            flags=re.IGNORECASE
        )

        # string_agg(expr, ',' ORDER BY sort_expr) -> LISTAGG(expr, ',' WITHIN GROUP (ORDER BY sort_expr))
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)\s*WITHIN\s+GROUP\s*\(\s*ORDER\s+BY\s+([^)]+)\s*\)',
            r"LISTAGG(\1, '\2') WITHIN GROUP (ORDER BY \3)",
            content,
            flags=re.IGNORECASE
        )

        # 窗口函数大多数在Oracle中有相同的名称，如RANK, DENSE_RANK, ROW_NUMBER等

        # JSON函数转换 - Oracle 12c+支持JSON
        # jsonb_extract_path_text(data, 'key') -> JSON_VALUE(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path_text\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_VALUE(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # jsonb_extract_path(data, 'key') -> JSON_QUERY(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_QUERY(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # COALESCE -> NVL (Oracle更常用NVL，但也支持COALESCE)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'NVL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NULLIF -> NULLIF (相同函数名)

        # 正则表达式函数转换
        # regexp_replace -> REGEXP_REPLACE (相同函数名，但参数可能不同)
        content = re.sub(
            r'regexp_replace\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*,\s*\'([^\']+)\'\s*(?:,\s*\'([^\']+)\')?\s*\)',
            lambda m: f"REGEXP_REPLACE({m.group(1)}, '{m.group(2)}', '{m.group(3)}'{', ' + m.group(4) if m.group(4) else ''})",
            content,
            flags=re.IGNORECASE
        )

        return content

    def _postgres_to_sqlserver_functions(self, content: str) -> str:
        """PostgreSQL -> SQL Server函数转换"""
        # 日期函数转换
        # TO_CHAR -> FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'sqlserver')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT_DATE -> CONVERT(DATE, GETDATE())
        content = content.replace("CURRENT_DATE", "CONVERT(DATE, GETDATE())")

        # CURRENT_TIME -> CONVERT(TIME, GETDATE())
        content = content.replace("CURRENT_TIME", "CONVERT(TIME, GETDATE())")

        # CURRENT_TIMESTAMP -> GETDATE()
        content = content.replace("CURRENT_TIMESTAMP", "GETDATE()")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> CONVERT(DATE, ...)
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\)',
            r'CONVERT(DATE, \1)',
            content,
            flags=re.IGNORECASE
        )

        # DATE_PART('day', date1 - date2) -> DATEDIFF(DAY, date2, date1)
        content = re.sub(
            r'DATE_PART\s*\(\s*\'day\'\s*,\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'DATEDIFF(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # (date + INTERVAL 'n DAY') -> DATEADD(DAY, n, date)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
            r'DATEADD(DAY, \2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # SUBSTRING(str FROM pos FOR len) -> SUBSTRING(str, pos, len)
        content = re.sub(
            r'SUBSTRING\s*\(\s*([^F]+)\s*FROM\s*([^F]+)(?:\s*FOR\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTRING({m.group(1)}, {m.group(2)}, {m.group(3) if m.group(3) else '8000'})",
            content,
            flags=re.IGNORECASE
        )

        # LENGTH -> LEN
        content = re.sub(
            r'LENGTH\s*\(\s*([^)]+)\s*\)',
            r'LEN(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 数组函数转换 - SQL Server不直接支持数组
        # array_length, array_to_string等函数需要使用自定义函数或STRING_SPLIT/STRING_AGG(SQL Server 2017+)

        # 聚合函数转换
        # string_agg(expr, ',') -> STRING_AGG(expr, ',') (SQL Server 2017+)
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"STRING_AGG(\1, '\2')",
            content,
            flags=re.IGNORECASE
        )

        # string_agg(expr, ',' ORDER BY sort_expr) -> STRING_AGG(expr, ',') WITHIN GROUP (ORDER BY sort_expr) (SQL Server 2017+)
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)\s*WITHIN\s+GROUP\s*\(\s*ORDER\s+BY\s+([^)]+)\s*\)',
            r"STRING_AGG(\1, '\2') WITHIN GROUP (ORDER BY \3)",
            content,
            flags=re.IGNORECASE
        )

        # 窗口函数大多数在SQL Server中有相同的名称，如RANK, DENSE_RANK, ROW_NUMBER等

        # JSON函数转换 - SQL Server 2016+支持JSON
        # jsonb_extract_path_text(data, 'key') -> JSON_VALUE(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path_text\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_VALUE(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # jsonb_extract_path(data, 'key') -> JSON_QUERY(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_QUERY(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # COALESCE -> ISNULL (SQL Server更常用ISNULL，但也支持COALESCE)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'ISNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NULLIF -> NULLIF (相同函数名)

        # 正则表达式函数转换
        # regexp_replace -> 使用自定义函数或REPLACE (SQL Server 2017+支持REPLACE)
        # 这里简化处理，实际可能需要更复杂的转换
        content = re.sub(
            r'regexp_replace\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*,\s*\'([^\']+)\'\s*\)',
            r"REPLACE(\1, '\2', '\3')",
            content,
            flags=re.IGNORECASE
        )

        return content

    def _postgres_to_mysql_functions(self, content: str) -> str:
        """PostgreSQL -> MySQL函数转换"""
        # 日期函数转换
        # TO_CHAR -> DATE_FORMAT
        content = re.sub(
            r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"DATE_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # CURRENT_DATE -> CURDATE()
        content = content.replace("CURRENT_DATE", "CURDATE()")

        # CURRENT_TIME -> CURTIME()
        content = content.replace("CURRENT_TIME", "CURTIME()")

        # CURRENT_TIMESTAMP -> NOW()
        content = content.replace("CURRENT_TIMESTAMP", "NOW()")

        # EXTRACT(YEAR FROM ...) -> YEAR(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
            r'YEAR(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(MONTH FROM ...) -> MONTH(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
            r'MONTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # EXTRACT(DAY FROM ...) -> DAY(...)
        content = re.sub(
            r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
            r'DAY(\1)',
            content,
            flags=re.IGNORECASE
        )

        # TO_DATE -> STR_TO_DATE
        content = re.sub(
            r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"STR_TO_DATE({m.group(1)}, '{self._adapt_date_format(m.group(2), 'postgres', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # DATE_PART('day', date1 - date2) -> DATEDIFF(date1, date2)
        content = re.sub(
            r'DATE_PART\s*\(\s*\'day\'\s*,\s*([^-]+)\s*-\s*([^)]+)\s*\)',
            r'DATEDIFF(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # (date + INTERVAL 'n DAY') -> DATE_ADD(date, INTERVAL n DAY)
        content = re.sub(
            r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
            r'DATE_ADD(\1, INTERVAL \2 DAY)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # SUBSTRING(str FROM pos FOR len) -> SUBSTRING(str, pos, len)
        content = re.sub(
            r'SUBSTRING\s*\(\s*([^F]+)\s*FROM\s*([^F]+)(?:\s*FOR\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTRING({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 数组函数转换 - MySQL 8.0+支持JSON数组
        # array_length(arr, 1) -> JSON_LENGTH(arr)
        content = re.sub(
            r'array_length\s*\(\s*([^,]+)\s*,\s*1\s*\)',
            r'JSON_LENGTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # array_to_string(arr, ',') -> 使用JSON_ARRAYAGG和GROUP_CONCAT
        content = re.sub(
            r'array_to_string\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"(SELECT GROUP_CONCAT(value SEPARATOR '{m.group(2)}') FROM JSON_TABLE({m.group(1)}, '$[*]' COLUMNS (value VARCHAR(255) PATH '$')) as jt)",
            content,
            flags=re.IGNORECASE
        )

        # 聚合函数转换
        # string_agg(expr, ',') -> GROUP_CONCAT(expr SEPARATOR ',')
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"GROUP_CONCAT(\1 SEPARATOR '\2')",
            content,
            flags=re.IGNORECASE
        )

        # string_agg(expr, ',' ORDER BY sort_expr) -> GROUP_CONCAT(expr ORDER BY sort_expr SEPARATOR ',')
        content = re.sub(
            r'string_agg\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)\s*WITHIN\s+GROUP\s*\(\s*ORDER\s+BY\s+([^)]+)\s*\)',
            r"GROUP_CONCAT(\1 ORDER BY \3 SEPARATOR '\2')",
            content,
            flags=re.IGNORECASE
        )

        # 窗口函数 - MySQL 8.0+支持窗口函数
        # 大多数窗口函数在MySQL中有相同的名称，如RANK, DENSE_RANK, ROW_NUMBER等

        # JSON函数转换 - MySQL 5.7+支持JSON
        # jsonb_extract_path_text(data, 'key') -> JSON_EXTRACT(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path_text\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_UNQUOTE(JSON_EXTRACT(\1, '$.\2'))",
            content,
            flags=re.IGNORECASE
        )

        # jsonb_extract_path(data, 'key') -> JSON_EXTRACT(data, '$.key')
        content = re.sub(
            r'jsonb_extract_path\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            r"JSON_EXTRACT(\1, '$.\2')",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # COALESCE -> IFNULL (MySQL支持COALESCE，但IFNULL更常用)
        content = re.sub(
            r'COALESCE\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'IFNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        # NULLIF -> NULLIF (相同函数名)

        # 正则表达式函数转换
        # regexp_replace -> REGEXP_REPLACE (MySQL 8.0+支持)
        content = re.sub(
            r'regexp_replace\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*,\s*\'([^\']+)\'\s*\)',
            r"REGEXP_REPLACE(\1, '\2', '\3')",
            content,
            flags=re.IGNORECASE
        )

        return content

    def _sqlserver_to_db2_functions(self, content: str) -> str:
        """SQL Server -> DB2函数转换"""
        # 实现SQL Server到DB2的函数转换
        return content

    def _sqlserver_to_oracle_functions(self, content: str) -> str:
        """SQL Server -> Oracle函数转换"""
        # 日期函数转换
        # FORMAT -> TO_CHAR
        content = re.sub(
            r'FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), 'sqlserver', 'oracle')}')",
            content,
            flags=re.IGNORECASE
        )

        # CONVERT(DATE, GETDATE()) -> SYSDATE
        content = re.sub(
            r'CONVERT\s*\(\s*DATE\s*,\s*GETDATE\s*\(\s*\)\s*\)',
            r'SYSDATE',
            content,
            flags=re.IGNORECASE
        )

        # GETDATE() -> SYSTIMESTAMP
        content = re.sub(
            r'GETDATE\s*\(\s*\)',
            r'SYSTIMESTAMP',
            content,
            flags=re.IGNORECASE
        )

        # YEAR -> EXTRACT(YEAR FROM ...)
        content = re.sub(
            r'YEAR\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(YEAR FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # MONTH -> EXTRACT(MONTH FROM ...)
        content = re.sub(
            r'MONTH\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(MONTH FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # DAY -> EXTRACT(DAY FROM ...)
        content = re.sub(
            r'DAY\s*\(\s*([^)]+)\s*\)',
            r'EXTRACT(DAY FROM \1)',
            content,
            flags=re.IGNORECASE
        )

        # CONVERT(DATE, ...) -> TO_DATE
        content = re.sub(
            r'CONVERT\s*\(\s*DATE\s*,\s*([^)]+)\s*\)',
            r"TO_DATE(\1, 'YYYY-MM-DD')",
            content,
            flags=re.IGNORECASE
        )

        # DATEDIFF(DAY, date2, date1) -> (date1 - date2)
        content = re.sub(
            r'DATEDIFF\s*\(\s*DAY\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'(\2 - \1)',
            content,
            flags=re.IGNORECASE
        )

        # DATEADD(DAY, n, date) -> (date + n)
        content = re.sub(
            r'DATEADD\s*\(\s*DAY\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'(\2 + \1)',
            content,
            flags=re.IGNORECASE
        )

        # 数值函数转换
        # CEILING -> CEIL
        content = re.sub(
            r'CEILING\s*\(\s*([^)]+)\s*\)',
            r'CEIL(\1)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # LEN -> LENGTH
        content = re.sub(
            r'LEN\s*\(\s*([^)]+)\s*\)',
            r'LENGTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # SUBSTRING -> SUBSTR
        content = re.sub(
            r'SUBSTRING\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"SUBSTR({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # CHARINDEX -> INSTR (参数顺序不同)
        content = re.sub(
            r'CHARINDEX\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"INSTR({m.group(2)}, {m.group(1)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # ISNULL -> NVL
        content = re.sub(
            r'ISNULL\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'NVL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _sqlserver_to_postgres_functions(self, content: str) -> str:
        """SQL Server -> PostgreSQL函数转换"""
        # 实现SQL Server到PostgreSQL的函数转换
        return content

    def _sqlserver_to_mysql_functions(self, content: str) -> str:
        """SQL Server -> MySQL函数转换"""
        # 日期函数转换
        # FORMAT -> DATE_FORMAT
        content = re.sub(
            r'FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
            lambda m: f"DATE_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), 'sqlserver', 'mysql')}')",
            content,
            flags=re.IGNORECASE
        )

        # CONVERT(DATE, GETDATE()) -> CURDATE()
        content = re.sub(
            r'CONVERT\s*\(\s*DATE\s*,\s*GETDATE\s*\(\s*\)\s*\)',
            r'CURDATE()',
            content,
            flags=re.IGNORECASE
        )

        # GETDATE() -> NOW()
        content = re.sub(
            r'GETDATE\s*\(\s*\)',
            r'NOW()',
            content,
            flags=re.IGNORECASE
        )

        # YEAR, MONTH, DAY函数在MySQL中有相同的名称，无需转换

        # CONVERT(DATE, ...) -> STR_TO_DATE
        content = re.sub(
            r'CONVERT\s*\(\s*DATE\s*,\s*([^)]+)\s*\)',
            r"STR_TO_DATE(\1, '%Y-%m-%d')",
            content,
            flags=re.IGNORECASE
        )

        # DATEDIFF(DAY, date2, date1) -> DATEDIFF(date1, date2)
        content = re.sub(
            r'DATEDIFF\s*\(\s*DAY\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'DATEDIFF(\2, \1)',
            content,
            flags=re.IGNORECASE
        )

        # DATEADD(DAY, n, date) -> DATE_ADD(date, INTERVAL n DAY)
        content = re.sub(
            r'DATEADD\s*\(\s*DAY\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'DATE_ADD(\2, INTERVAL \1 DAY)',
            content,
            flags=re.IGNORECASE
        )

        # 字符串函数转换
        # LEN -> LENGTH
        content = re.sub(
            r'LEN\s*\(\s*([^)]+)\s*\)',
            r'LENGTH(\1)',
            content,
            flags=re.IGNORECASE
        )

        # CHARINDEX -> LOCATE (参数顺序相同)
        content = re.sub(
            r'CHARINDEX\s*\(\s*([^,]+)\s*,\s*([^,)]+)(?:\s*,\s*([^)]+))?\s*\)',
            lambda m: f"LOCATE({m.group(1)}, {m.group(2)}{', ' + m.group(3) if m.group(3) else ''})",
            content,
            flags=re.IGNORECASE
        )

        # 特殊函数转换
        # ISNULL -> IFNULL
        content = re.sub(
            r'ISNULL\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
            r'IFNULL(\1, \2)',
            content,
            flags=re.IGNORECASE
        )

        return content

    def _mysql_to_db2_functions(self, content: str) -> str:
        """MySQL -> DB2函数转换"""
        # 实现MySQL到DB2的函数转换
        return content

    def _mysql_to_oracle_functions(self, content: str) -> str:
        """MySQL -> Oracle函数转换"""
        # 实现MySQL到Oracle的函数转换
        return content

    def _mysql_to_postgres_functions(self, content: str) -> str:
        """MySQL -> PostgreSQL函数转换"""
        # 实现MySQL到PostgreSQL的函数转换
        return content

    def _mysql_to_sqlserver_functions(self, content: str) -> str:
        """MySQL -> SQL Server函数转换"""
        # 实现MySQL到SQL Server的函数转换
        return content

    def _identify_param_type(self, param: str) -> str:
        """
        识别参数类型

        Args:
            param: 参数值

        Returns:
            str: 参数类型，可能的值有：'number', 'date', 'string', 'boolean', 'null', 'function', 'expression'
        """
        param = param.strip()

        # 检查是否为NULL
        if self.null_pattern.match(param):
            return 'null'

        # 检查是否为布尔值
        if self.boolean_pattern.match(param):
            return 'boolean'

        # 检查是否为数字
        if self.number_pattern.match(param):
            return 'number'

        # 检查是否为日期
        if self.date_pattern.match(param):
            return 'date'

        # 检查是否为字符串（带引号）
        if self.string_pattern.match(param):
            return 'string'

        # 检查是否为函数调用
        if re.search(r'\w+\s*\(', param):
            return 'function'

        # 默认为表达式
        return 'expression'

    def _convert_param_type(self, param: str, param_type: str, from_db: str, to_db: str) -> str:
        """
        转换参数类型

        Args:
            param: 参数值
            param_type: 参数类型
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 转换后的参数值
        """
        if param_type == 'null':
            # NULL值在不同数据库中的表示基本相同
            return param

        elif param_type == 'boolean':
            # 布尔值转换
            param_lower = param.lower()
            if param_lower in ('true', 'yes', 'y', 't', '1'):
                if to_db in ['oracle', 'db2']:
                    return '1'
                elif to_db in ['postgres', 'gaussdb']:
                    return 'TRUE'
                elif to_db == 'sqlserver':
                    return '1'
                elif to_db == 'mysql':
                    return 'TRUE'
            else:  # false, no, n, f, 0
                if to_db in ['oracle', 'db2']:
                    return '0'
                elif to_db in ['postgres', 'gaussdb']:
                    return 'FALSE'
                elif to_db == 'sqlserver':
                    return '0'
                elif to_db == 'mysql':
                    return 'FALSE'
            return param

        elif param_type == 'number':
            # 数字值在不同数据库中的表示基本相同
            return param

        elif param_type == 'date':
            # 日期值转换
            # 如果是带引号的日期字符串，需要根据目标数据库调整日期格式
            if self.string_pattern.match(param):
                # 提取日期字符串（去掉引号）
                date_str = param[1:-1]

                # 根据目标数据库调整日期格式
                if to_db == 'oracle':
                    return f"TO_DATE('{date_str}', 'YYYY-MM-DD')"
                elif to_db == 'db2':
                    return f"DATE('{date_str}')"
                elif to_db in ['postgres', 'gaussdb']:
                    return f"'{date_str}'::DATE"
                elif to_db == 'sqlserver':
                    return f"CONVERT(DATE, '{date_str}')"
                elif to_db == 'mysql':
                    return f"STR_TO_DATE('{date_str}', '%Y-%m-%d')"
            return param

        elif param_type == 'string':
            # 字符串值在不同数据库中的表示基本相同，但可能需要处理转义字符
            return param

        elif param_type == 'function':
            # 函数调用需要递归转换
            return self._transform_functions_in_text(param)

        elif param_type == 'expression':
            # 表达式需要递归转换
            return self._transform_expression(param)

        # 默认情况下，不做转换
        return param

    def _parse_function_args(self, func_args: str) -> List[str]:
        """
        解析函数参数

        Args:
            func_args: 函数参数字符串

        Returns:
            List[str]: 解析后的参数列表
        """
        if not func_args:
            return []

        args = []
        current_arg = ""
        bracket_count = 0
        quote_char = None

        for char in func_args:
            if quote_char:
                # 在引号内
                current_arg += char
                if char == quote_char and current_arg[-2:] != '\\' + quote_char:
                    quote_char = None
            elif char in ['"', "'"]:
                # 开始或结束引号
                current_arg += char
                if quote_char is None:
                    quote_char = char
                elif char == quote_char and current_arg[-2:] != '\\' + quote_char:
                    quote_char = None
            elif char == '(':
                # 开始括号
                current_arg += char
                bracket_count += 1
            elif char == ')':
                # 结束括号
                current_arg += char
                bracket_count -= 1
            elif char == ',' and bracket_count == 0:
                # 参数分隔符
                args.append(current_arg.strip())
                current_arg = ""
            else:
                # 其他字符
                current_arg += char

        # 添加最后一个参数
        if current_arg:
            args.append(current_arg.strip())

        return args

    def _adapt_date_format(self, format_str: str, from_db: str, to_db: str) -> str:
        """
        适配日期格式字符串

        Args:
            format_str: 原始日期格式字符串
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的日期格式字符串
        """
        # 定义各数据库的日期格式映射
        date_format_mapping = {
            "db2": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH": "HH", "MI": "MI", "SS": "SS",
                "YEAR": "YYYY", "MONTH": "MM", "DAY": "DD",
                "HOUR": "HH", "MINUTE": "MI", "SECOND": "SS"
            },
            "oracle": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH24": "HH24", "MI": "MI", "SS": "SS",
                "YEAR": "YYYY", "MONTH": "MM", "DAY": "DD",
                "HOUR": "HH24", "MINUTE": "MI", "SECOND": "SS"
            },
            "postgres": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH24": "HH24", "MI": "MI", "SS": "SS",
                "YEAR": "YYYY", "MONTH": "MM", "DAY": "DD",
                "HOUR": "HH24", "MINUTE": "MI", "SECOND": "SS"
            },
            "gaussdb": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH24": "HH24", "MI": "MI", "SS": "SS",
                "YEAR": "YYYY", "MONTH": "MM", "DAY": "DD",
                "HOUR": "HH24", "MINUTE": "MI", "SECOND": "SS"
            },
            "sqlserver": {
                "yyyy": "yyyy", "MM": "MM", "dd": "dd",
                "HH": "HH", "mm": "mm", "ss": "ss",
                "yy": "yy", "y": "y", "M": "M", "d": "d",
                "h": "h", "m": "m", "s": "s",
                "YEAR": "yyyy", "MONTH": "MM", "DAY": "dd",
                "HOUR": "HH", "MINUTE": "mm", "SECOND": "ss"
            },
            "mysql": {
                "%Y": "%Y", "%m": "%m", "%d": "%d",
                "%H": "%H", "%i": "%i", "%s": "%s",
                "%y": "%y", "%c": "%c", "%e": "%e",
                "%h": "%h", "%k": "%k", "%l": "%l", "%p": "%p",
                "%r": "%r", "%S": "%S", "%T": "%T",
                "YEAR": "%Y", "MONTH": "%m", "DAY": "%d",
                "HOUR": "%H", "MINUTE": "%i", "SECOND": "%s"
            }
        }

        # 获取源和目标数据库的日期格式映射
        from_format = date_format_mapping.get(from_db, {})
        to_format = date_format_mapping.get(to_db, {})

        # 如果源和目标数据库的日期格式映射相同，无需适配
        if from_format == to_format:
            return format_str

        # 特殊处理MySQL格式到其他数据库格式
        if from_db == "mysql" and to_db in ["db2", "oracle", "postgres", "gaussdb", "sqlserver"]:
            # MySQL: %Y-%m-%d %H:%i:%s -> DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS
            format_str = format_str.replace("%Y", "YYYY")
            format_str = format_str.replace("%m", "MM")
            format_str = format_str.replace("%d", "DD")
            format_str = format_str.replace("%H", "HH24")
            format_str = format_str.replace("%i", "MI")
            format_str = format_str.replace("%s", "SS")

            # 处理MySQL特有的格式
            format_str = format_str.replace("%y", "YY")
            format_str = format_str.replace("%c", "MM")
            format_str = format_str.replace("%e", "DD")
            format_str = format_str.replace("%h", "HH12")
            format_str = format_str.replace("%k", "HH24")
            format_str = format_str.replace("%l", "HH12")
            format_str = format_str.replace("%p", "AM")
            format_str = format_str.replace("%r", "HH12:MI:SS AM")
            format_str = format_str.replace("%S", "SS")
            format_str = format_str.replace("%T", "HH24:MI:SS")

            # SQL Server特殊处理
            if to_db == "sqlserver":
                format_str = format_str.replace("YYYY", "yyyy")
                format_str = format_str.replace("DD", "dd")
                format_str = format_str.replace("HH24", "HH")
                format_str = format_str.replace("MI", "mm")
                format_str = format_str.replace("SS", "ss")
                format_str = format_str.replace("YY", "yy")
                format_str = format_str.replace("HH12", "hh")
                format_str = format_str.replace("AM", "tt")

            return format_str

        # 特殊处理其他数据库格式到MySQL格式
        elif to_db == "mysql" and from_db in ["db2", "oracle", "postgres", "gaussdb", "sqlserver"]:
            # DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS -> MySQL: %Y-%m-%d %H:%i:%s
            format_str = format_str.replace("YYYY", "%Y")
            format_str = format_str.replace("yyyy", "%Y")
            format_str = format_str.replace("MM", "%m")
            format_str = format_str.replace("DD", "%d")
            format_str = format_str.replace("dd", "%d")
            format_str = format_str.replace("HH24", "%H")
            format_str = format_str.replace("HH", "%H")
            format_str = format_str.replace("MI", "%i")
            format_str = format_str.replace("mm", "%i")
            format_str = format_str.replace("SS", "%s")
            format_str = format_str.replace("ss", "%s")

            # 处理其他格式
            format_str = format_str.replace("YY", "%y")
            format_str = format_str.replace("yy", "%y")
            format_str = format_str.replace("HH12", "%h")
            format_str = format_str.replace("hh", "%h")
            format_str = format_str.replace("AM", "%p")
            format_str = format_str.replace("tt", "%p")

            return format_str

        # 处理SQL Server和其他数据库之间的转换
        elif from_db == "sqlserver" and to_db in ["db2", "oracle", "postgres", "gaussdb"]:
            # SQL Server: yyyy-MM-dd HH:mm:ss -> DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS
            format_str = format_str.replace("yyyy", "YYYY")
            format_str = format_str.replace("yy", "YY")
            format_str = format_str.replace("dd", "DD")
            format_str = format_str.replace("d", "D")
            format_str = format_str.replace("HH", "HH24")
            format_str = format_str.replace("hh", "HH12")
            format_str = format_str.replace("mm", "MI")
            format_str = format_str.replace("m", "MI")
            format_str = format_str.replace("ss", "SS")
            format_str = format_str.replace("s", "SS")
            format_str = format_str.replace("tt", "AM")

            return format_str
        elif to_db == "sqlserver" and from_db in ["db2", "oracle", "postgres", "gaussdb"]:
            # DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS -> SQL Server: yyyy-MM-dd HH:mm:ss
            format_str = format_str.replace("YYYY", "yyyy")
            format_str = format_str.replace("YY", "yy")
            format_str = format_str.replace("DD", "dd")
            format_str = format_str.replace("D", "d")
            format_str = format_str.replace("HH24", "HH")
            format_str = format_str.replace("HH12", "hh")
            format_str = format_str.replace("MI", "mm")
            format_str = format_str.replace("SS", "ss")
            format_str = format_str.replace("AM", "tt")

            return format_str

        # 处理Oracle和DB2之间的转换
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: HH24 -> DB2: HH
            format_str = format_str.replace("HH24", "HH")
            format_str = format_str.replace("HH12", "HH")

            return format_str
        elif from_db == "db2" and to_db == "oracle":
            # DB2: HH -> Oracle: HH24
            format_str = format_str.replace("HH", "HH24")

            return format_str

        # 默认情况下，不做转换
        return format_str

    def _oracle_decode_to_sqlserver_case(self, match) -> str:
        """
        将Oracle的DECODE函数转换为SQL Server的CASE语句

        Args:
            match: 正则表达式匹配对象

        Returns:
            str: 转换后的CASE语句
        """
        # 提取DECODE函数的参数
        expr = match.group(1)

        # 构建CASE语句
        case_stmt = f"CASE {expr}"

        # 处理所有的when-then对
        groups = match.groups()
        i = 1
        while i < len(groups) - 1:
            if groups[i] is not None and groups[i+1] is not None:
                case_stmt += f" WHEN {groups[i]} THEN {groups[i+1]}"
                i += 2
            else:
                break

        # 处理可选的默认值
        if len(groups) > 1 and groups[-1] is not None and i == len(groups) - 1:
            case_stmt += f" ELSE {groups[-1]}"

        case_stmt += " END"
        return case_stmt

    def _oracle_decode_to_mysql_case(self, match) -> str:
        """
        将Oracle的DECODE函数转换为MySQL的CASE语句

        Args:
            match: 正则表达式匹配对象

        Returns:
            str: 转换后的CASE语句
        """
        # 提取DECODE函数的参数
        expr = match.group(1)

        # 构建CASE语句
        case_stmt = f"CASE {expr}"

        # 处理所有的when-then对
        groups = match.groups()
        i = 1
        while i < len(groups) - 1:
            if groups[i] is not None and groups[i+1] is not None:
                case_stmt += f" WHEN {groups[i]} THEN {groups[i+1]}"
                i += 2
            else:
                break

        # 处理可选的默认值
        if len(groups) > 1 and groups[-1] is not None and i == len(groups) - 1:
            case_stmt += f" ELSE {groups[-1]}"

        case_stmt += " END"
        return case_stmt
