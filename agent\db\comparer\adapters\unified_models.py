#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据模型定义
提供三种比对模式的统一数据结构
"""

import time
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Callable
from enum import Enum


class ComparisonMode(Enum):
    """比对模式枚举"""
    DIRECT = "direct"
    API = "api"
    LOCAL = "local"


class DifferenceType(Enum):
    """差异类型枚举"""
    TARGET_MISSING = "target_missing"
    SOURCE_MISSING = "source_missing"
    DIFFERENCE = "difference"
    UNKNOWN = "unknown"


@dataclass
class UnifiedProgress:
    """统一进度模型"""
    current: int
    total: int
    percentage: float
    message: str
    stage: str
    estimated_remaining: Optional[float] = None
    start_time: Optional[float] = field(default_factory=time.time)
    
    def __post_init__(self):
        """后处理初始化"""
        if self.percentage is None and self.total > 0:
            self.percentage = round((self.current / self.total) * 100, 2)


@dataclass
class UnifiedDifference:
    """统一差异记录模型"""
    id: str
    source_data: Dict[str, Any]
    target_data: Dict[str, Any]
    diff_fields: List[str]
    diff_type: str
    table_id: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        # 确保metadata包含基本信息
        if 'timestamp' not in self.metadata:
            self.metadata['timestamp'] = time.time()
        if 'source' not in self.metadata:
            self.metadata['source'] = 'unknown'


@dataclass
class UnifiedSummary:
    """统一摘要模型"""
    total_tables: int
    completed_tables: int
    total_differences: int
    difference_types: Dict[str, int] = field(default_factory=dict)
    table_differences: Dict[str, int] = field(default_factory=dict)
    success_rate: float = 0.0
    execution_time: float = 0.0
    
    def __post_init__(self):
        """后处理初始化"""
        if self.total_tables > 0:
            self.success_rate = (self.completed_tables / self.total_tables) * 100


@dataclass
class UnifiedResult:
    """统一比对结果模型"""
    success: bool
    mode: str
    differences: List[UnifiedDifference]
    summary: UnifiedSummary
    execution_time: float
    error: Optional[str] = None
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """后处理初始化"""
        # 确保metadata包含基本信息
        if 'timestamp' not in self.metadata:
            self.metadata['timestamp'] = time.time()
        if 'version' not in self.metadata:
            self.metadata['version'] = '1.0'


@dataclass
class UnifiedError:
    """统一错误模型"""
    error_code: str
    error_type: str
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    recoverable: bool = False
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'error_type': self.error_type,
            'message': self.message,
            'details': self.details,
            'recoverable': self.recoverable,
            'timestamp': self.timestamp
        }


class UnifiedConfig:
    """统一配置类"""
    
    def __init__(self):
        # 模式配置
        self.enable_unified_comparison = True
        self.fallback_to_legacy = True
        self.mode_priority = [ComparisonMode.API, ComparisonMode.LOCAL, ComparisonMode.DIRECT]
        
        # 性能配置
        self.batch_size = 1000
        self.connection_timeout = 30
        self.query_timeout = 60
        self.max_retries = 3
        
        # 进度配置
        self.progress_update_interval = 2  # 秒
        self.enable_progress_estimation = True
        self.batch_report_size = 100
        
        # 缓存配置
        self.enable_result_caching = False
        self.cache_max_size = 100
        self.cache_ttl = 3600  # 1小时
        
        # 错误处理配置
        self.enable_auto_retry = True
        self.retry_delay = 2  # 秒
        self.max_error_details = 1000
        
        # 调试配置
        self.debug_mode = False
        self.log_level = "INFO"
        self.enable_performance_monitoring = True
    
    @classmethod
    def from_env(cls) -> 'UnifiedConfig':
        """从环境变量创建配置"""
        import os
        config = cls()
        
        # 读取环境变量
        config.enable_unified_comparison = os.getenv('ENABLE_UNIFIED_COMPARISON', 'true').lower() == 'true'
        config.fallback_to_legacy = os.getenv('FALLBACK_TO_LEGACY', 'true').lower() == 'true'
        config.debug_mode = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
        
        # 性能配置
        try:
            config.batch_size = int(os.getenv('BATCH_SIZE', '1000'))
            config.connection_timeout = int(os.getenv('CONNECTION_TIMEOUT', '30'))
            config.query_timeout = int(os.getenv('QUERY_TIMEOUT', '60'))
            config.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        except ValueError:
            pass  # 使用默认值
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'enable_unified_comparison': self.enable_unified_comparison,
            'fallback_to_legacy': self.fallback_to_legacy,
            'mode_priority': [mode.value for mode in self.mode_priority],
            'batch_size': self.batch_size,
            'connection_timeout': self.connection_timeout,
            'query_timeout': self.query_timeout,
            'max_retries': self.max_retries,
            'progress_update_interval': self.progress_update_interval,
            'enable_progress_estimation': self.enable_progress_estimation,
            'enable_result_caching': self.enable_result_caching,
            'debug_mode': self.debug_mode,
            'log_level': self.log_level
        }


# 类型别名
ProgressCallback = Callable[[UnifiedProgress], None]
ErrorCallback = Callable[[UnifiedError], None]
CompletionCallback = Callable[[UnifiedResult], None]


# 工具函数
def create_unified_difference(
    id: str,
    source_data: Dict[str, Any],
    target_data: Dict[str, Any],
    diff_fields: List[str],
    diff_type: str,
    table_id: str,
    source: str = "unknown"
) -> UnifiedDifference:
    """创建统一差异记录的便捷函数"""
    return UnifiedDifference(
        id=id,
        source_data=source_data,
        target_data=target_data,
        diff_fields=diff_fields,
        diff_type=diff_type,
        table_id=table_id,
        metadata={
            'source': source,
            'timestamp': time.time()
        }
    )


def create_unified_summary(
    total_tables: int,
    completed_tables: int,
    differences: List[UnifiedDifference],
    execution_time: float = 0.0
) -> UnifiedSummary:
    """创建统一摘要的便捷函数"""
    # 统计差异类型
    diff_types = {}
    table_diffs = {}
    
    for diff in differences:
        # 差异类型统计
        diff_type = diff.diff_type
        diff_types[diff_type] = diff_types.get(diff_type, 0) + 1
        
        # 表差异统计
        table_id = diff.table_id
        table_diffs[table_id] = table_diffs.get(table_id, 0) + 1
    
    return UnifiedSummary(
        total_tables=total_tables,
        completed_tables=completed_tables,
        total_differences=len(differences),
        difference_types=diff_types,
        table_differences=table_diffs,
        execution_time=execution_time
    )


def create_unified_error(
    error_code: str,
    message: str,
    error_type: str = "SystemError",
    details: Optional[Dict[str, Any]] = None,
    recoverable: bool = False
) -> UnifiedError:
    """创建统一错误的便捷函数"""
    return UnifiedError(
        error_code=error_code,
        error_type=error_type,
        message=message,
        details=details or {},
        recoverable=recoverable
    )


# 默认配置实例
DEFAULT_CONFIG = UnifiedConfig()
