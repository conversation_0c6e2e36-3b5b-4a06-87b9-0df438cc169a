#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接数据库比对模式
实现内置的数据库直接连接比对功能，不依赖外部API或服务
"""

import os
import sys
import time
import uuid
import asyncio
import logging
from typing import Dict, Any, List, Optional

# 添加comparer目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
COMPARER_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))  # gui -> backend -> comparer
sys.path.insert(0, COMPARER_ROOT)

try:
    from .comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )
except ImportError:
    from comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )

logger = logging.getLogger(__name__)


class DirectComparisonMode(ComparisonInterface):
    """直接数据库比对模式
    
    特点：
    - 直接连接源数据库和目标数据库
    - 使用内置的比对引擎
    - 不依赖外部API或服务
    - 适合小到中等规模的数据比对
    """
    
    def __init__(self, gui_app, config: Optional[Dict[str, Any]] = None):
        super().__init__(gui_app, config)
        self.engine_factory = None
        self.source_connection = None
        self.target_connection = None
        self._comparison_engines = {}
        self.db_connector_factory = None
        
    @property
    def mode_name(self) -> str:
        return "direct"
    
    @property
    def display_name(self) -> str:
        return "内置数据库比对"
    
    async def is_available(self) -> bool:
        """检查直接比对模式是否可用"""
        try:
            # 检查比对引擎是否可用
            return await self._check_comparison_engines()
        except Exception as e:
            logger.error(f"检查直接比对可用性失败: {e}")
            return False
    
    async def _check_comparison_engines(self) -> bool:
        """检查比对引擎是否可用"""
        try:
            # 尝试导入比对引擎
            from comparison.engines.base_engine import (
                ComparisonTask, ComparisonType, ComparisonResult, 
                ComparisonEngineFactory, DatabaseConnection as EngineDBConnection
            )
            
            # 创建引擎工厂
            self.engine_factory = ComparisonEngineFactory()
            
            # 存储引擎类
            self._comparison_engines = {
                'ComparisonTask': ComparisonTask,
                'ComparisonType': ComparisonType,
                'ComparisonResult': ComparisonResult,
                'EngineDBConnection': EngineDBConnection
            }
            
            return True
            
        except ImportError as e:
            logger.warning(f"比对引擎导入失败: {e}")
            return False
        except Exception as e:
            logger.error(f"比对引擎检查异常: {e}")
            return False
    
    async def initialize(self) -> bool:
        """初始化直接比对模式"""
        try:
            self.status = ComparisonStatus.INITIALIZING
            
            # 确保比对引擎可用
            if not await self._check_comparison_engines():
                self._report_error("比对引擎不可用")
                self.status = ComparisonStatus.FAILED
                return False
            
            # 初始化数据库连接工厂
            await self._initialize_database_connections()
            
            self.status = ComparisonStatus.IDLE
            return True
            
        except Exception as e:
            self._report_error(f"直接比对模式初始化失败: {str(e)}")
            self.status = ComparisonStatus.FAILED
            return False
    
    async def _initialize_database_connections(self) -> None:
        """初始化数据库连接"""
        try:
            # 导入数据库连接器
            from comparison.connectors.database_connector import DatabaseConnectorFactory
            self.db_connector_factory = DatabaseConnectorFactory()
            
        except ImportError as e:
            raise Exception(f"数据库连接器导入失败: {e}")
    
    async def execute_comparison(self, comparison_config: ComparisonConfig) -> ComparisonResult:
        """执行直接数据库比对"""
        self.start_time = time.time()
        self.status = ComparisonStatus.RUNNING
        self.current_task_id = f"direct_{uuid.uuid4().hex[:8]}"
        
        try:
            # 验证配置
            is_valid, error_msg = await self.validate_config(comparison_config)
            if not is_valid:
                raise ValueError(error_msg)
            
            self._update_progress(0, "开始直接数据库比对")
            
            # 建立数据库连接
            await self._establish_connections(comparison_config)
            
            # 执行表比对
            differences = await self._execute_table_comparisons(comparison_config)
            
            # 创建结果
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time
            
            result = self._create_result(
                success=True,
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=len(comparison_config.tables),
                differences=differences
            )
            
            self.last_result = result
            self.status = ComparisonStatus.COMPLETED
            self._update_progress(100, "直接比对完成")
            self._report_completion(result)
            
            return result
            
        except Exception as e:
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time if self.start_time else 0
            
            error_message = str(e)
            self._report_error(f"直接比对失败: {error_message}")
            
            result = self._create_result(
                success=False,
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=0,
                differences=[],
                error_message=error_message
            )
            
            self.last_result = result
            self.status = ComparisonStatus.FAILED
            return result
        
        finally:
            await self._cleanup_connections()
    
    async def _establish_connections(self, comparison_config: ComparisonConfig) -> None:
        """建立数据库连接"""
        try:
            self._update_progress(10, "建立数据库连接")

            # 确保数据库连接器工厂已初始化
            if self.db_connector_factory is None:
                await self._initialize_database_connections()

            # 创建源数据库连接
            source_connector = self.db_connector_factory.create_connector(
                comparison_config.source_config['db_type']
            )
            
            # 创建目标数据库连接
            target_connector = self.db_connector_factory.create_connector(
                comparison_config.target_config['db_type']
            )
            
            # 建立连接
            try:
                source_result = await source_connector.connect(
                    self._create_connection_info(comparison_config.source_config)
                )

                # 处理不同连接器的返回值格式
                if source_result is None:
                    # 大部分连接器返回None表示成功
                    source_success = True
                    source_msg = "连接成功"
                elif isinstance(source_result, tuple) and len(source_result) >= 2:
                    # DB2连接器返回(success, message, details)
                    source_success, source_msg = source_result[0], source_result[1]
                else:
                    # 其他情况
                    source_success = bool(source_result)
                    source_msg = "连接成功" if source_success else "连接失败"

                if not source_success:
                    raise Exception(f"源数据库连接失败: {source_msg}")

            except Exception as e:
                raise Exception(f"源数据库连接失败: {str(e)}")

            try:
                target_result = await target_connector.connect(
                    self._create_connection_info(comparison_config.target_config)
                )

                # 处理不同连接器的返回值格式
                if target_result is None:
                    # 大部分连接器返回None表示成功
                    target_success = True
                    target_msg = "连接成功"
                elif isinstance(target_result, tuple) and len(target_result) >= 2:
                    # DB2连接器返回(success, message, details)
                    target_success, target_msg = target_result[0], target_result[1]
                else:
                    # 其他情况
                    target_success = bool(target_result)
                    target_msg = "连接成功" if target_success else "连接失败"

                if not target_success:
                    raise Exception(f"目标数据库连接失败: {target_msg}")

            except Exception as e:
                raise Exception(f"目标数据库连接失败: {str(e)}")
            
            self.source_connection = source_connector
            self.target_connection = target_connector
            
            self._update_progress(20, "数据库连接建立成功")
            
        except Exception as e:
            raise Exception(f"建立数据库连接失败: {str(e)}")
    
    def _create_connection_info(self, config: Dict[str, Any]):
        """创建连接信息对象"""
        # 这里需要根据实际的连接信息类来创建
        # 简化实现，实际应该使用正确的连接信息类
        class ConnectionInfo:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
        
        return ConnectionInfo(**config)
    
    async def _execute_table_comparisons(self, comparison_config: ComparisonConfig) -> List[Dict[str, Any]]:
        """执行表比对"""
        all_differences = []
        total_tables = len(comparison_config.tables)
        
        for i, table_config in enumerate(comparison_config.tables):
            if self.cancel_requested:
                break
            
            table_id = table_config['table_id']
            progress = int(20 + (i / total_tables) * 70)  # 20-90%的进度用于表比对
            
            self._update_progress(progress, f"比对表: {table_id}")
            
            try:
                # 执行单表比对
                table_differences = await self._compare_single_table(table_config, comparison_config)
                all_differences.extend(table_differences)
                
                # 更新GUI表状态
                if hasattr(self.gui_app, '_update_table_status'):
                    self.gui_app._update_table_status(table_id, "完成")
                
            except Exception as e:
                logger.error(f"表{table_id}比对失败: {e}")
                
                # 更新GUI表状态
                if hasattr(self.gui_app, '_update_table_status'):
                    self.gui_app._update_table_status(table_id, "失败")
                
                # 继续处理其他表
                continue
        
        return all_differences
    
    async def _compare_single_table(self, table_config: Dict[str, Any], 
                                   comparison_config: ComparisonConfig) -> List[Dict[str, Any]]:
        """比对单个表"""
        try:
            # 创建比对任务
            ComparisonTask = self._comparison_engines['ComparisonTask']
            ComparisonType = self._comparison_engines['ComparisonType']
            EngineDBConnection = self._comparison_engines['EngineDBConnection']
            
            # 创建源和目标连接对象
            source_conn = EngineDBConnection(
                db_type=comparison_config.source_config['db_type'],
                host=comparison_config.source_config['host'],
                port=comparison_config.source_config['port'],
                database=comparison_config.source_config['database'],
                username=comparison_config.source_config['username'],
                password=comparison_config.source_config['password']
            )
            
            target_conn = EngineDBConnection(
                db_type=comparison_config.target_config['db_type'],
                host=comparison_config.target_config['host'],
                port=comparison_config.target_config['port'],
                database=comparison_config.target_config['database'],
                username=comparison_config.target_config['username'],
                password=comparison_config.target_config['password']
            )
            
            # 创建比对任务
            task = ComparisonTask(
                task_id=f"{self.current_task_id}_{table_config['table_id']}",
                comparison_type=ComparisonType.CONTENT,
                source_connection=source_conn,
                target_connection=target_conn,
                table_name=table_config['table_id'],
                sql_rules={
                    "sql_1": table_config.get("sql_1", ""),
                    "sql_2": table_config.get("sql_2", "")
                },
                batch_size=comparison_config.batch_size,
                timeout=comparison_config.timeout
            )
            
            # 获取比对引擎并执行
            engine = self.engine_factory.create_engine(ComparisonType.CONTENT)
            
            # 验证任务
            is_valid, validation_msg = engine.validate_task(task)
            if not is_valid:
                raise Exception(f"任务验证失败: {validation_msg}")
            
            # 执行比对
            result = await engine.compare(task)
            
            # 转换结果格式
            return self._convert_engine_result_to_differences(result, table_config['table_id'])
            
        except Exception as e:
            logger.error(f"单表比对失败: {e}")
            raise
    
    def _convert_engine_result_to_differences(self, engine_result, table_id: str) -> List[Dict[str, Any]]:
        """将引擎结果转换为差异记录格式"""
        differences = []

        try:
            # 检查引擎结果是否成功
            if engine_result.status != "completed":
                logger.warning(f"引擎比对未成功完成，状态: {engine_result.status}")
                if engine_result.error_message:
                    logger.error(f"引擎错误信息: {engine_result.error_message}")
                return differences

            # 转换引擎差异记录格式
            if hasattr(engine_result, 'differences') and engine_result.differences:
                for diff in engine_result.differences:
                    # 处理字典格式的差异记录
                    if isinstance(diff, dict):
                        # 提取字段差异信息
                        diff_fields = []
                        if 'field_differences' in diff:
                            diff_fields = [field_diff.get('field', '') for field_diff in diff['field_differences']]

                        differences.append({
                            'id': diff.get('key', str(uuid.uuid4())),
                            'table_id': table_id,
                            'diff_type': diff.get('type', 'difference'),
                            'source_data': diff.get('source_record', {}),
                            'target_data': diff.get('target_record', {}),
                            'diff_fields': diff_fields
                        })
                    else:
                        # 处理对象格式的差异记录（向后兼容）
                        differences.append({
                            'id': getattr(diff, 'key', str(uuid.uuid4())),
                            'table_id': table_id,
                            'diff_type': getattr(diff, 'type', 'difference'),
                            'source_data': getattr(diff, 'source_record', {}),
                            'target_data': getattr(diff, 'target_record', {}),
                            'diff_fields': getattr(diff, 'diff_fields', [])
                        })

            logger.info(f"表{table_id}转换完成，差异记录数: {len(differences)}")

        except Exception as e:
            logger.error(f"转换引擎结果失败: {e}")

        return differences
    
    async def cancel_comparison(self) -> bool:
        """取消比对"""
        try:
            self.cancel_requested = True
            self.status = ComparisonStatus.CANCELLED
            self._update_progress(0, "比对已取消")
            return True
        except Exception as e:
            logger.error(f"取消比对失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """清理资源"""
        await self._cleanup_connections()
        self.status = ComparisonStatus.IDLE
    
    async def _cleanup_connections(self) -> None:
        """清理数据库连接"""
        try:
            if self.source_connection:
                # 使用连接器的disconnect方法
                if hasattr(self.source_connection, 'disconnect'):
                    await self.source_connection.disconnect()
                elif hasattr(self.source_connection, 'close'):
                    await self.source_connection.close()
                self.source_connection = None

            if self.target_connection:
                # 使用连接器的disconnect方法
                if hasattr(self.target_connection, 'disconnect'):
                    await self.target_connection.disconnect()
                elif hasattr(self.target_connection, 'close'):
                    await self.target_connection.close()
                self.target_connection = None

        except Exception as e:
            logger.error(f"清理数据库连接失败: {e}")


# 注册直接比对模式
try:
    ComparisonModeFactory.register_mode("direct", DirectComparisonMode)
except Exception as e:
    logger.error(f"❌ DirectComparisonMode 注册失败: {e}")
