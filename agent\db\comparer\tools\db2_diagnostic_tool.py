"""
DB2连接诊断工具
专门用于诊断和解决DB2连接问题，特别是SQL1042C系统错误
"""
import os
import sys
import logging
import asyncio
import platform
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 添加项目根目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)

from comparison.connectors.database_connector import DB2Connector
from comparison.connectors.db2_driver_manager import db2_driver_manager, DB2DriverStatus


@dataclass
class DB2ConnectionInfo:
    """DB2连接信息"""
    host: str
    port: int
    database: str
    username: str
    password: str
    schema: Optional[str] = None


class DB2DiagnosticTool:
    """DB2诊断工具"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('db2_diagnostic.log')
            ]
        )
    
    async def run_comprehensive_diagnostic(self, connection_info: DB2ConnectionInfo) -> Dict[str, Any]:
        """运行全面的DB2诊断"""
        self.logger.info("开始DB2连接诊断...")
        
        diagnostic_result = {
            "timestamp": datetime.now().isoformat(),
            "connection_info": {
                "host": connection_info.host,
                "port": connection_info.port,
                "database": connection_info.database,
                "username": connection_info.username,
                "schema": connection_info.schema
            },
            "system_info": self._collect_system_info(),
            "driver_diagnostic": self._run_driver_diagnostic(),
            "environment_check": self._check_environment(),
            "connection_test": await self._test_connection(connection_info),
            "recommendations": []
        }
        
        # 生成建议
        diagnostic_result["recommendations"] = self._generate_recommendations(diagnostic_result)
        
        # 保存诊断报告
        self._save_diagnostic_report(diagnostic_result)
        
        return diagnostic_result
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        return {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.architecture()[0],
            "python_version": platform.python_version(),
            "processor": platform.processor(),
            "hostname": platform.node()
        }
    
    def _run_driver_diagnostic(self) -> Dict[str, Any]:
        """运行驱动诊断"""
        try:
            return db2_driver_manager.get_diagnostic_info()
        except Exception as e:
            return {
                "error": f"驱动诊断失败: {str(e)}",
                "status": "diagnostic_failed"
            }
    
    def _check_environment(self) -> Dict[str, Any]:
        """检查环境配置"""
        env_check = {
            "environment_variables": {},
            "path_analysis": {},
            "file_permissions": {}
        }
        
        # 检查关键环境变量
        key_vars = ['IBM_DB_HOME', 'PATH', 'LD_LIBRARY_PATH', 'DYLD_LIBRARY_PATH']
        for var in key_vars:
            value = os.environ.get(var)
            env_check["environment_variables"][var] = {
                "value": value[:100] + "..." if value and len(value) > 100 else value,
                "set": value is not None
            }
        
        # 分析IBM_DB_HOME路径
        ibm_db_home = os.environ.get('IBM_DB_HOME')
        if ibm_db_home:
            env_check["path_analysis"]["ibm_db_home"] = {
                "path": ibm_db_home,
                "exists": os.path.exists(ibm_db_home),
                "is_directory": os.path.isdir(ibm_db_home) if os.path.exists(ibm_db_home) else False,
                "readable": os.access(ibm_db_home, os.R_OK) if os.path.exists(ibm_db_home) else False
            }
            
            # 检查关键子目录
            if os.path.exists(ibm_db_home):
                subdirs = ['bin', 'lib', 'include']
                for subdir in subdirs:
                    subdir_path = os.path.join(ibm_db_home, subdir)
                    env_check["path_analysis"][f"ibm_db_home_{subdir}"] = {
                        "path": subdir_path,
                        "exists": os.path.exists(subdir_path)
                    }
        
        return env_check
    
    async def _test_connection(self, connection_info: DB2ConnectionInfo) -> Dict[str, Any]:
        """测试DB2连接"""
        test_result = {
            "connection_attempts": [],
            "final_status": "unknown",
            "error_analysis": {},
            "performance_metrics": {}
        }
        
        connector = DB2Connector()
        
        try:
            start_time = datetime.now()
            
            # 尝试连接
            await connector.connect(connection_info)
            
            end_time = datetime.now()
            connection_time = (end_time - start_time).total_seconds()
            
            test_result["connection_attempts"].append({
                "attempt": 1,
                "status": "success",
                "connection_time": connection_time,
                "timestamp": start_time.isoformat()
            })
            
            test_result["final_status"] = "success"
            test_result["performance_metrics"]["connection_time"] = connection_time
            
            # 执行额外测试
            await self._run_additional_tests(connector, test_result)
            
        except Exception as e:
            error_msg = str(e)
            test_result["connection_attempts"].append({
                "attempt": 1,
                "status": "failed",
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            
            test_result["final_status"] = "failed"
            test_result["error_analysis"] = self._analyze_connection_error(error_msg)
            
        finally:
            try:
                await connector.disconnect()
            except:
                pass
        
        return test_result
    
    async def _run_additional_tests(self, connector: DB2Connector, test_result: Dict[str, Any]):
        """运行额外的连接测试"""
        additional_tests = []
        
        try:
            # 测试基本查询
            start_time = datetime.now()
            result = connector._execute_sql("SELECT 1 FROM SYSIBM.SYSDUMMY1")
            query_time = (datetime.now() - start_time).total_seconds()
            
            additional_tests.append({
                "test": "basic_query",
                "status": "success",
                "query_time": query_time,
                "result_count": len(result) if result else 0
            })
            
            # 测试系统信息查询
            try:
                start_time = datetime.now()
                version_result = connector._execute_sql("SELECT SERVICE_LEVEL FROM SYSIBMADM.ENV_INST_INFO")
                version_query_time = (datetime.now() - start_time).total_seconds()
                
                additional_tests.append({
                    "test": "version_query",
                    "status": "success",
                    "query_time": version_query_time,
                    "db2_version": version_result[0] if version_result else "unknown"
                })
            except Exception as e:
                additional_tests.append({
                    "test": "version_query",
                    "status": "failed",
                    "error": str(e)
                })
            
        except Exception as e:
            additional_tests.append({
                "test": "basic_query",
                "status": "failed",
                "error": str(e)
            })
        
        test_result["additional_tests"] = additional_tests
    
    def _analyze_connection_error(self, error_msg: str) -> Dict[str, Any]:
        """分析连接错误"""
        analysis = {
            "error_type": "unknown",
            "sql_error_code": None,
            "likely_causes": [],
            "specific_recommendations": []
        }
        
        if "SQL1042C" in error_msg:
            analysis["error_type"] = "sql1042c_system_error"
            analysis["sql_error_code"] = "SQL1042C"
            analysis["likely_causes"] = [
                "IBM DB2 CLI Driver配置问题",
                "环境变量设置错误",
                "系统资源不足",
                "网络连接问题",
                "DB2服务器配置问题"
            ]
            analysis["specific_recommendations"] = [
                "检查IBM_DB_HOME环境变量设置",
                "验证CLI Driver安装完整性",
                "检查系统内存和临时空间",
                "测试网络连通性",
                "查看DB2服务器日志"
            ]
        elif "SQL30082N" in error_msg:
            analysis["error_type"] = "authentication_error"
            analysis["sql_error_code"] = "SQL30082N"
            analysis["likely_causes"] = [
                "用户名或密码错误",
                "用户权限不足",
                "认证方式配置错误"
            ]
            analysis["specific_recommendations"] = [
                "验证用户名和密码",
                "检查用户数据库访问权限",
                "确认认证方式配置"
            ]
        elif "timeout" in error_msg.lower():
            analysis["error_type"] = "timeout_error"
            analysis["likely_causes"] = [
                "网络延迟过高",
                "DB2服务器响应慢",
                "连接超时设置过短"
            ]
            analysis["specific_recommendations"] = [
                "增加连接超时时间",
                "检查网络连接质量",
                "优化DB2服务器性能"
            ]
        
        return analysis
    
    def _generate_recommendations(self, diagnostic_result: Dict[str, Any]) -> List[str]:
        """生成诊断建议"""
        recommendations = []
        
        # 基于驱动状态的建议
        driver_status = diagnostic_result.get("driver_diagnostic", {}).get("driver_status", {})
        overall_status = driver_status.get("overall_status")
        
        if overall_status == "python_driver_missing":
            recommendations.append("安装Python DB2驱动: pip install ibm_db")
        elif overall_status == "cli_driver_missing":
            recommendations.append("下载并安装IBM Data Server Driver Package")
        
        # 基于连接测试结果的建议
        connection_test = diagnostic_result.get("connection_test", {})
        if connection_test.get("final_status") == "failed":
            error_analysis = connection_test.get("error_analysis", {})
            specific_recs = error_analysis.get("specific_recommendations", [])
            recommendations.extend(specific_recs)
        
        # 基于环境检查的建议
        env_check = diagnostic_result.get("environment_check", {})
        ibm_db_home_info = env_check.get("path_analysis", {}).get("ibm_db_home", {})
        if not ibm_db_home_info.get("exists", False):
            recommendations.append("设置正确的IBM_DB_HOME环境变量")
        
        return recommendations
    
    def _save_diagnostic_report(self, diagnostic_result: Dict[str, Any]):
        """保存诊断报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"db2_diagnostic_report_{timestamp}.json"
        
        try:
            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(diagnostic_result, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"诊断报告已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存诊断报告失败: {e}")


async def main():
    """主函数 - 示例用法"""
    # 示例连接信息
    connection_info = DB2ConnectionInfo(
        host="***********",
        port=50000,
        database="KGDBJY",
        username="kgdb",
        password="Dev3@@@@2019",
        schema="KGDBJY"
    )
    
    tool = DB2DiagnosticTool()
    result = await tool.run_comprehensive_diagnostic(connection_info)
    
    print("=== DB2诊断结果 ===")
    print(f"最终状态: {result['connection_test']['final_status']}")
    print(f"驱动状态: {result['driver_diagnostic']['driver_status']['overall_status']}")
    
    if result['recommendations']:
        print("\n=== 建议 ===")
        for i, rec in enumerate(result['recommendations'], 1):
            print(f"{i}. {rec}")


if __name__ == "__main__":
    asyncio.run(main())
