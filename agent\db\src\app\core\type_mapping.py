"""
数据类型映射和转换模块

提供不同数据库之间的数据类型映射和转换功能，确保跨数据库比对的准确性。
"""
import re
import datetime
import decimal
from typing import Any, Dict, List, Tuple, Optional, Union

from app.models.logger import logger


# 标准化数据类型枚举
class StandardType:
    """标准化数据类型枚举"""
    # 数值类型
    INTEGER = "INTEGER"          # 整数类型
    DECIMAL = "DECIMAL"          # 小数类型
    FLOAT = "FLOAT"              # 浮点类型
    
    # 字符串类型
    CHAR = "CHAR"                # 定长字符串
    VARCHAR = "VARCHAR"          # 变长字符串
    TEXT = "TEXT"                # 长文本
    
    # 日期时间类型
    DATE = "DATE"                # 日期
    TIME = "TIME"                # 时间
    TIMESTAMP = "TIMESTAMP"      # 时间戳
    
    # 二进制类型
    BINARY = "BINARY"            # 二进制数据
    BLOB = "BLOB"                # 二进制大对象
    
    # 布尔类型
    BOOLEAN = "BOOLEAN"          # 布尔值
    
    # 其他类型
    XML = "XML"                  # XML数据
    JSON = "JSON"                # JSON数据
    ARRAY = "ARRAY"              # 数组
    OTHER = "OTHER"              # 其他类型


# 数据库特定类型到标准类型的映射
DB_TYPE_MAPPINGS = {
    # DB2类型映射
    "db2": {
        # 数值类型
        "SMALLINT": StandardType.INTEGER,
        "INTEGER": StandardType.INTEGER,
        "INT": StandardType.INTEGER,
        "BIGINT": StandardType.INTEGER,
        "DECIMAL": StandardType.DECIMAL,
        "DEC": StandardType.DECIMAL,
        "NUMERIC": StandardType.DECIMAL,
        "REAL": StandardType.FLOAT,
        "DOUBLE": StandardType.FLOAT,
        "FLOAT": StandardType.FLOAT,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "CHARACTER": StandardType.CHAR,
        "VARCHAR": StandardType.VARCHAR,
        "LONG VARCHAR": StandardType.TEXT,
        "CLOB": StandardType.TEXT,
        "DBCLOB": StandardType.TEXT,
        "GRAPHIC": StandardType.CHAR,
        "VARGRAPHIC": StandardType.VARCHAR,
        "LONG VARGRAPHIC": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.DATE,
        "TIME": StandardType.TIME,
        "TIMESTAMP": StandardType.TIMESTAMP,
        
        # 二进制类型
        "BLOB": StandardType.BLOB,
        "BINARY": StandardType.BINARY,
        "VARBINARY": StandardType.BINARY,
        
        # 其他类型
        "XML": StandardType.XML,
        "BOOLEAN": StandardType.BOOLEAN,
        "ARRAY": StandardType.ARRAY
    },
    
    # Oracle类型映射
    "oracle": {
        # 数值类型
        "NUMBER": StandardType.DECIMAL,
        "FLOAT": StandardType.FLOAT,
        "BINARY_FLOAT": StandardType.FLOAT,
        "BINARY_DOUBLE": StandardType.FLOAT,
        "INTEGER": StandardType.INTEGER,
        "INT": StandardType.INTEGER,
        "SMALLINT": StandardType.INTEGER,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "VARCHAR2": StandardType.VARCHAR,
        "NCHAR": StandardType.CHAR,
        "NVARCHAR2": StandardType.VARCHAR,
        "CLOB": StandardType.TEXT,
        "NCLOB": StandardType.TEXT,
        "LONG": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.TIMESTAMP,  # Oracle DATE包含时间部分
        "TIMESTAMP": StandardType.TIMESTAMP,
        "TIMESTAMP WITH TIME ZONE": StandardType.TIMESTAMP,
        "TIMESTAMP WITH LOCAL TIME ZONE": StandardType.TIMESTAMP,
        "INTERVAL YEAR TO MONTH": StandardType.OTHER,
        "INTERVAL DAY TO SECOND": StandardType.OTHER,
        
        # 二进制类型
        "BLOB": StandardType.BLOB,
        "BFILE": StandardType.BLOB,
        "RAW": StandardType.BINARY,
        "LONG RAW": StandardType.BLOB,
        
        # 其他类型
        "XMLTYPE": StandardType.XML,
        "JSON": StandardType.JSON
    },
    
    # GaussDB类型映射（基于PostgreSQL）
    "gaussdb": {
        # 数值类型
        "SMALLINT": StandardType.INTEGER,
        "INTEGER": StandardType.INTEGER,
        "BIGINT": StandardType.INTEGER,
        "DECIMAL": StandardType.DECIMAL,
        "NUMERIC": StandardType.DECIMAL,
        "REAL": StandardType.FLOAT,
        "DOUBLE PRECISION": StandardType.FLOAT,
        "FLOAT": StandardType.FLOAT,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "CHARACTER": StandardType.CHAR,
        "VARCHAR": StandardType.VARCHAR,
        "CHARACTER VARYING": StandardType.VARCHAR,
        "TEXT": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.DATE,
        "TIME": StandardType.TIME,
        "TIME WITH TIME ZONE": StandardType.TIME,
        "TIMESTAMP": StandardType.TIMESTAMP,
        "TIMESTAMP WITH TIME ZONE": StandardType.TIMESTAMP,
        "INTERVAL": StandardType.OTHER,
        
        # 二进制类型
        "BYTEA": StandardType.BINARY,
        
        # 布尔类型
        "BOOLEAN": StandardType.BOOLEAN,
        
        # 其他类型
        "XML": StandardType.XML,
        "JSON": StandardType.JSON,
        "JSONB": StandardType.JSON,
        "ARRAY": StandardType.ARRAY
    },
    
    # SQL Server类型映射
    "sqlserver": {
        # 数值类型
        "BIT": StandardType.BOOLEAN,
        "TINYINT": StandardType.INTEGER,
        "SMALLINT": StandardType.INTEGER,
        "INT": StandardType.INTEGER,
        "BIGINT": StandardType.INTEGER,
        "DECIMAL": StandardType.DECIMAL,
        "NUMERIC": StandardType.DECIMAL,
        "MONEY": StandardType.DECIMAL,
        "SMALLMONEY": StandardType.DECIMAL,
        "FLOAT": StandardType.FLOAT,
        "REAL": StandardType.FLOAT,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "VARCHAR": StandardType.VARCHAR,
        "TEXT": StandardType.TEXT,
        "NCHAR": StandardType.CHAR,
        "NVARCHAR": StandardType.VARCHAR,
        "NTEXT": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.DATE,
        "TIME": StandardType.TIME,
        "DATETIME": StandardType.TIMESTAMP,
        "DATETIME2": StandardType.TIMESTAMP,
        "SMALLDATETIME": StandardType.TIMESTAMP,
        "DATETIMEOFFSET": StandardType.TIMESTAMP,
        
        # 二进制类型
        "BINARY": StandardType.BINARY,
        "VARBINARY": StandardType.BINARY,
        "IMAGE": StandardType.BLOB,
        
        # 其他类型
        "XML": StandardType.XML,
        "SQL_VARIANT": StandardType.OTHER,
        "UNIQUEIDENTIFIER": StandardType.OTHER
    },
    
    # PostgreSQL类型映射
    "postgres": {
        # 数值类型
        "SMALLINT": StandardType.INTEGER,
        "INTEGER": StandardType.INTEGER,
        "BIGINT": StandardType.INTEGER,
        "DECIMAL": StandardType.DECIMAL,
        "NUMERIC": StandardType.DECIMAL,
        "REAL": StandardType.FLOAT,
        "DOUBLE PRECISION": StandardType.FLOAT,
        "FLOAT": StandardType.FLOAT,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "CHARACTER": StandardType.CHAR,
        "VARCHAR": StandardType.VARCHAR,
        "CHARACTER VARYING": StandardType.VARCHAR,
        "TEXT": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.DATE,
        "TIME": StandardType.TIME,
        "TIME WITH TIME ZONE": StandardType.TIME,
        "TIMESTAMP": StandardType.TIMESTAMP,
        "TIMESTAMP WITH TIME ZONE": StandardType.TIMESTAMP,
        "INTERVAL": StandardType.OTHER,
        
        # 二进制类型
        "BYTEA": StandardType.BINARY,
        
        # 布尔类型
        "BOOLEAN": StandardType.BOOLEAN,
        
        # 其他类型
        "XML": StandardType.XML,
        "JSON": StandardType.JSON,
        "JSONB": StandardType.JSON,
        "ARRAY": StandardType.ARRAY
    },
    
    # MySQL类型映射
    "mysql": {
        # 数值类型
        "TINYINT": StandardType.INTEGER,
        "SMALLINT": StandardType.INTEGER,
        "MEDIUMINT": StandardType.INTEGER,
        "INT": StandardType.INTEGER,
        "INTEGER": StandardType.INTEGER,
        "BIGINT": StandardType.INTEGER,
        "DECIMAL": StandardType.DECIMAL,
        "NUMERIC": StandardType.DECIMAL,
        "FLOAT": StandardType.FLOAT,
        "DOUBLE": StandardType.FLOAT,
        
        # 字符串类型
        "CHAR": StandardType.CHAR,
        "VARCHAR": StandardType.VARCHAR,
        "TINYTEXT": StandardType.TEXT,
        "TEXT": StandardType.TEXT,
        "MEDIUMTEXT": StandardType.TEXT,
        "LONGTEXT": StandardType.TEXT,
        
        # 日期时间类型
        "DATE": StandardType.DATE,
        "TIME": StandardType.TIME,
        "DATETIME": StandardType.TIMESTAMP,
        "TIMESTAMP": StandardType.TIMESTAMP,
        "YEAR": StandardType.INTEGER,
        
        # 二进制类型
        "BINARY": StandardType.BINARY,
        "VARBINARY": StandardType.BINARY,
        "TINYBLOB": StandardType.BLOB,
        "BLOB": StandardType.BLOB,
        "MEDIUMBLOB": StandardType.BLOB,
        "LONGBLOB": StandardType.BLOB,
        
        # 布尔类型
        "BOOLEAN": StandardType.BOOLEAN,
        "BOOL": StandardType.BOOLEAN,
        
        # 其他类型
        "JSON": StandardType.JSON,
        "ENUM": StandardType.OTHER,
        "SET": StandardType.OTHER
    }
}


def get_standard_type(db_type: str, data_type: str) -> str:
    """
    获取标准化数据类型
    
    Args:
        db_type: 数据库类型
        data_type: 数据库特定的数据类型
        
    Returns:
        str: 标准化数据类型
    """
    # 转换为大写并移除括号中的长度信息
    clean_type = re.sub(r'\(.*\)', '', data_type.upper()).strip()
    
    # 获取数据库特定的类型映射
    type_mapping = DB_TYPE_MAPPINGS.get(db_type.lower(), {})
    
    # 返回标准化类型，如果找不到映射则返回OTHER
    return type_mapping.get(clean_type, StandardType.OTHER)


def normalize_value(value: Any, standard_type: str) -> Any:
    """
    标准化值，用于跨数据库比较
    
    Args:
        value: 原始值
        standard_type: 标准化数据类型
        
    Returns:
        Any: 标准化后的值
    """
    # 处理NULL值
    if value is None:
        return None
    
    try:
        # 根据标准类型进行转换
        if standard_type == StandardType.INTEGER:
            # 整数类型转换
            return int(value) if value is not None else None
        elif standard_type == StandardType.DECIMAL:
            # 小数类型转换，使用Decimal确保精度
            return decimal.Decimal(str(value)) if value is not None else None
        elif standard_type == StandardType.FLOAT:
            # 浮点类型转换
            return float(value) if value is not None else None
        elif standard_type in (StandardType.CHAR, StandardType.VARCHAR, StandardType.TEXT):
            # 字符串类型转换
            return str(value).strip() if value is not None else None
        elif standard_type == StandardType.DATE:
            # 日期类型转换
            if isinstance(value, datetime.date):
                return value
            elif isinstance(value, str):
                # 尝试解析日期字符串
                return _parse_date(value)
            else:
                return value
        elif standard_type == StandardType.TIME:
            # 时间类型转换
            if isinstance(value, datetime.time):
                return value
            elif isinstance(value, str):
                # 尝试解析时间字符串
                return _parse_time(value)
            else:
                return value
        elif standard_type == StandardType.TIMESTAMP:
            # 时间戳类型转换
            if isinstance(value, (datetime.datetime, datetime.date)):
                return value
            elif isinstance(value, str):
                # 尝试解析时间戳字符串
                return _parse_timestamp(value)
            else:
                return value
        elif standard_type == StandardType.BOOLEAN:
            # 布尔类型转换
            if isinstance(value, bool):
                return value
            elif isinstance(value, (int, float)):
                return bool(value)
            elif isinstance(value, str):
                return value.lower() in ('true', 'yes', 'y', '1', 't')
            else:
                return bool(value)
        else:
            # 其他类型，保持原值
            return value
    except Exception as e:
        logger.warning(f"值标准化失败: {value} -> {standard_type}, 错误: {str(e)}")
        # 转换失败时返回原值
        return value


def _parse_date(date_str: str) -> Optional[datetime.date]:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串
        
    Returns:
        Optional[datetime.date]: 解析后的日期对象
    """
    # 尝试多种日期格式
    formats = [
        '%Y-%m-%d',
        '%Y/%m/%d',
        '%d-%m-%Y',
        '%d/%m/%Y',
        '%m-%d-%Y',
        '%m/%d/%Y',
        '%Y%m%d'
    ]
    
    for fmt in formats:
        try:
            return datetime.datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    # 如果所有格式都失败，记录警告并返回None
    logger.warning(f"无法解析日期字符串: {date_str}")
    return None


def _parse_time(time_str: str) -> Optional[datetime.time]:
    """
    解析时间字符串
    
    Args:
        time_str: 时间字符串
        
    Returns:
        Optional[datetime.time]: 解析后的时间对象
    """
    # 尝试多种时间格式
    formats = [
        '%H:%M:%S',
        '%H:%M',
        '%I:%M:%S %p',
        '%I:%M %p',
        '%H%M%S'
    ]
    
    for fmt in formats:
        try:
            return datetime.datetime.strptime(time_str, fmt).time()
        except ValueError:
            continue
    
    # 如果所有格式都失败，记录警告并返回None
    logger.warning(f"无法解析时间字符串: {time_str}")
    return None


def _parse_timestamp(timestamp_str: str) -> Optional[datetime.datetime]:
    """
    解析时间戳字符串
    
    Args:
        timestamp_str: 时间戳字符串
        
    Returns:
        Optional[datetime.datetime]: 解析后的时间戳对象
    """
    # 尝试多种时间戳格式
    formats = [
        '%Y-%m-%d %H:%M:%S',
        '%Y/%m/%d %H:%M:%S',
        '%d-%m-%Y %H:%M:%S',
        '%d/%m/%Y %H:%M:%S',
        '%Y-%m-%d %H:%M:%S.%f',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%dT%H:%M:%S.%f',
        '%Y-%m-%dT%H:%M:%SZ',
        '%Y-%m-%dT%H:%M:%S.%fZ',
        '%Y%m%d%H%M%S'
    ]
    
    for fmt in formats:
        try:
            return datetime.datetime.strptime(timestamp_str, fmt)
        except ValueError:
            continue
    
    # 如果所有格式都失败，尝试解析为日期
    date_obj = _parse_date(timestamp_str)
    if date_obj:
        return datetime.datetime.combine(date_obj, datetime.time())
    
    # 如果所有尝试都失败，记录警告并返回None
    logger.warning(f"无法解析时间戳字符串: {timestamp_str}")
    return None
