#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地函数比对模式
使用本地Agent模块进行数据比对，不需要启动独立的API服务
"""

import os
import sys
import time
import uuid
import asyncio
import logging
from typing import Dict, Any, List, Optional

# 添加comparer目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
COMPARER_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))  # gui -> backend -> comparer
sys.path.insert(0, COMPARER_ROOT)

try:
    from .comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )
except ImportError:
    from comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )

logger = logging.getLogger(__name__)


class LocalComparisonMode(ComparisonInterface):
    """本地函数比对模式
    
    特点：
    - 使用本地Agent模块
    - 直接调用比对服务函数
    - 不需要HTTP API通信
    - 适合中等规模的数据比对
    """
    
    def __init__(self, gui_app, config: Optional[Dict[str, Any]] = None):
        super().__init__(gui_app, config)
        self.comparison_service = None
        self.agent_modules = {}
        self._module_manager = None
        
    @property
    def mode_name(self) -> str:
        return "local"
    
    @property
    def display_name(self) -> str:
        return "本地函数比对"
    
    async def is_available(self) -> bool:
        """检查本地函数比对模式是否可用"""
        try:
            return await self._check_agent_modules()
        except Exception as e:
            logger.error(f"检查本地函数比对可用性失败: {e}")
            return False
    
    async def _check_agent_modules(self) -> bool:
        """检查Agent模块是否可用"""
        try:
            # 尝试导入Agent模块管理器
            if hasattr(self.gui_app, '_module_manager'):
                self._module_manager = self.gui_app._module_manager
                return self._module_manager.load_agent_modules()
            
            # 如果GUI应用没有模块管理器，尝试直接导入
            return await self._direct_import_agent_modules()
            
        except Exception as e:
            logger.error(f"Agent模块检查失败: {e}")
            return False
    
    async def _direct_import_agent_modules(self) -> bool:
        """直接导入Agent模块"""
        try:
            # 导入比对服务
            from backend.services.comparison_service import ComparisonService
            self.comparison_service = ComparisonService()
            
            # 导入其他必要模块
            from backend.api.legacy.models import (
                DatabaseType, DatabaseConnection, ConnectionParameters,
                CompareTaskRequest, CompareConfig, TaskStatus
            )
            
            from backend.models.comparison_models import (
                ComparisonTaskCreate, ComparisonTaskResponse, ComparisonResultResponse
            )
            
            # 存储模块引用
            self.agent_modules = {
                'comparison_service': self.comparison_service,
                'DatabaseType': DatabaseType,
                'DatabaseConnection': DatabaseConnection,
                'CompareTaskRequest': CompareTaskRequest,
                'CompareConfig': CompareConfig
            }
            
            return True
            
        except ImportError as e:
            logger.warning(f"Agent模块导入失败: {e}")
            return False
        except Exception as e:
            logger.error(f"Agent模块导入异常: {e}")
            return False
    
    async def initialize(self) -> bool:
        """初始化本地函数比对模式"""
        try:
            self.status = ComparisonStatus.INITIALIZING
            
            # 确保Agent模块可用
            if not await self._check_agent_modules():
                self._report_error("Agent模块不可用")
                self.status = ComparisonStatus.FAILED
                return False
            
            # 初始化比对服务
            if self.comparison_service:
                # 如果比对服务有初始化方法，调用它
                if hasattr(self.comparison_service, 'initialize'):
                    await self.comparison_service.initialize()
            
            self.status = ComparisonStatus.IDLE
            return True
            
        except Exception as e:
            self._report_error(f"本地函数比对模式初始化失败: {str(e)}")
            self.status = ComparisonStatus.FAILED
            return False
    
    async def execute_comparison(self, comparison_config: ComparisonConfig) -> ComparisonResult:
        """执行本地函数比对"""
        self.start_time = time.time()
        self.status = ComparisonStatus.RUNNING
        self.current_task_id = f"local_{uuid.uuid4().hex[:8]}"
        
        try:
            # 验证配置
            is_valid, error_msg = await self.validate_config(comparison_config)
            if not is_valid:
                raise ValueError(error_msg)
            
            self._update_progress(0, "开始本地函数比对")
            
            # 检查比对服务
            if not self.comparison_service:
                raise Exception("比对服务不可用")
            
            # 执行表比对
            differences = await self._execute_local_comparisons(comparison_config)
            
            # 创建结果
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time
            
            result = self._create_result(
                success=True,
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=len(comparison_config.tables),
                differences=differences
            )
            
            self.last_result = result
            self.status = ComparisonStatus.COMPLETED
            self._update_progress(100, "本地函数比对完成")
            self._report_completion(result)
            
            return result
            
        except Exception as e:
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time if self.start_time else 0
            
            error_message = str(e)
            self._report_error(f"本地函数比对失败: {error_message}")
            
            result = self._create_result(
                success=False,
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=0,
                differences=[],
                error_message=error_message
            )
            
            self.last_result = result
            self.status = ComparisonStatus.FAILED
            return result
    
    async def _execute_local_comparisons(self, comparison_config: ComparisonConfig) -> List[Dict[str, Any]]:
        """执行本地函数比对"""
        all_differences = []
        total_tables = len(comparison_config.tables)
        completed_tables = 0
        
        for i, table_config in enumerate(comparison_config.tables):
            if self.cancel_requested:
                break
            
            table_id = table_config['table_id']
            progress = int((i / total_tables) * 90)  # 0-90%的进度用于表比对
            
            self._update_progress(progress, f"本地函数比对表: {table_id}")
            
            try:
                # 执行单表比对
                table_differences = await self._compare_single_table_local(
                    table_config, comparison_config
                )
                all_differences.extend(table_differences)
                completed_tables += 1
                
                # 更新GUI表状态
                if hasattr(self.gui_app, '_update_table_status'):
                    self.gui_app._update_table_status(table_id, "完成")
                
                # 通知GUI完成表比对
                if hasattr(self.gui_app, 'complete_table_comparison'):
                    self.gui_app.complete_table_comparison(table_id)
                
            except Exception as e:
                logger.error(f"表{table_id}本地函数比对失败: {e}")
                
                # 更新GUI表状态
                if hasattr(self.gui_app, '_update_table_status'):
                    self.gui_app._update_table_status(table_id, "失败")
                
                # 继续处理其他表
                continue
        
        self._update_progress(95, f"本地函数比对完成，成功: {completed_tables}/{total_tables}")
        return all_differences
    
    async def _compare_single_table_local(self, table_config: Dict[str, Any], 
                                         comparison_config: ComparisonConfig) -> List[Dict[str, Any]]:
        """使用本地函数比对单个表"""
        try:
            # 构建比对请求
            request_data = self._build_local_comparison_request(table_config, comparison_config)
            
            # 调用本地比对服务
            if hasattr(self.comparison_service, 'run_comparison'):
                # 如果有异步方法
                result = await self.comparison_service.run_comparison(request_data)
            elif hasattr(self.comparison_service, 'compare'):
                # 如果有同步方法，在线程池中执行
                result = await asyncio.to_thread(self.comparison_service.compare, request_data)
            else:
                # 使用通用方法
                result = await self._execute_generic_local_comparison(request_data)
            
            # 转换结果格式
            return self._convert_local_result_to_differences(result, table_config['table_id'])
            
        except Exception as e:
            logger.error(f"本地函数单表比对失败: {e}")
            raise
    
    def _build_local_comparison_request(self, table_config: Dict[str, Any], 
                                       comparison_config: ComparisonConfig) -> Dict[str, Any]:
        """构建本地比对请求"""
        return {
            'source_connection': comparison_config.source_config,
            'target_connection': comparison_config.target_config,
            'table_config': table_config,
            'config': {
                'batch_size': comparison_config.batch_size,
                'timeout': comparison_config.timeout,
                'table_id': table_config['table_id'],
                'source_sql': table_config.get('sql_1', ''),
                'target_sql': table_config.get('sql_2', ''),
                'primary_keys': table_config.get('primary_keys', ['id']),
                'algorithm_type': 'primary_key'
            }
        }
    
    async def _execute_generic_local_comparison(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行通用本地比对"""
        try:
            logger.info("开始执行通用本地比对逻辑")

            # 从请求数据中提取配置
            source_config = request_data.get('source_connection', {})
            target_config = request_data.get('target_connection', {})
            table_config = request_data.get('table_config', {})
            config = request_data.get('config', {})

            # 获取SQL语句
            source_sql = config.get('source_sql', table_config.get('sql_1', ''))
            target_sql = config.get('target_sql', table_config.get('sql_2', ''))

            if not source_sql or not target_sql:
                raise ValueError("缺少源端或目标端SQL语句")

            logger.info(f"执行比对 - 源SQL: {source_sql[:100]}...")
            logger.info(f"执行比对 - 目标SQL: {target_sql[:100]}...")

            # 尝试使用现有的比对引擎
            try:
                result = await self._execute_with_comparison_engine(
                    source_config, target_config, source_sql, target_sql, config
                )
                return result
            except Exception as engine_error:
                logger.warning(f"比对引擎执行失败，尝试直接比对: {engine_error}")

                # 回退到直接比对
                result = await self._execute_direct_comparison(
                    source_config, target_config, source_sql, target_sql, config
                )
                return result

        except Exception as e:
            logger.error(f"通用本地比对执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'differences': []
            }

    async def _execute_with_comparison_engine(self, source_config, target_config,
                                            source_sql, target_sql, config):
        """使用比对引擎执行比对"""
        try:
            # 尝试导入比对引擎
            from comparison.engines.base_engine import ComparisonEngineFactory, ComparisonTask, ComparisonType, DatabaseConnection

            # 创建数据库连接对象
            source_conn = DatabaseConnection(
                db_type=source_config.get('db_type', 'mysql'),
                host=source_config.get('host', 'localhost'),
                port=source_config.get('port', 3306),
                database=source_config.get('database', ''),
                username=source_config.get('username', ''),
                password=source_config.get('password', '')
            )

            target_conn = DatabaseConnection(
                db_type=target_config.get('db_type', 'mysql'),
                host=target_config.get('host', 'localhost'),
                port=target_config.get('port', 3306),
                database=target_config.get('database', ''),
                username=target_config.get('username', ''),
                password=target_config.get('password', '')
            )

            # 创建比对任务
            task = ComparisonTask(
                task_id=f"local_{int(time.time())}",
                comparison_type=ComparisonType.CONTENT,
                source_connection=source_conn,
                target_connection=target_conn,
                table_name=config.get('table_id', 'unknown'),
                sql_rules={
                    'sql_1': source_sql,
                    'sql_2': target_sql
                },
                batch_size=config.get('batch_size', 1000)
            )

            # 创建比对引擎
            engine_factory = ComparisonEngineFactory()
            engine = engine_factory.create_engine(ComparisonType.CONTENT)

            # 执行比对
            logger.info("使用比对引擎执行比对")
            result = await engine.compare(task)

            # 转换结果格式
            return {
                'success': True,
                'differences': result.differences if hasattr(result, 'differences') else [],
                'summary': {
                    'total_records': getattr(result, 'total_records', 0),
                    'matched_records': getattr(result, 'matched_records', 0),
                    'different_records': getattr(result, 'different_records', 0),
                    'source_only_records': getattr(result, 'source_only_records', 0),
                    'target_only_records': getattr(result, 'target_only_records', 0)
                }
            }

        except ImportError as e:
            logger.warning(f"比对引擎模块导入失败: {e}")
            raise Exception("比对引擎不可用")
        except Exception as e:
            logger.error(f"比对引擎执行失败: {e}")
            raise

    async def _execute_direct_comparison(self, source_config, target_config,
                                       source_sql, target_sql, config):
        """直接执行比对（回退方案）"""
        try:
            # 导入数据库连接器
            from comparison.connectors.database_connector import DatabaseConnector

            # 创建数据库连接器
            source_connector = DatabaseConnector(source_config)
            target_connector = DatabaseConnector(target_config)

            logger.info("使用直接比对方式")

            # 获取源端数据
            logger.info("获取源端数据...")
            source_data = await self._fetch_data_async(source_connector, source_sql)

            # 获取目标端数据
            logger.info("获取目标端数据...")
            target_data = await self._fetch_data_async(target_connector, target_sql)

            # 执行比对
            logger.info(f"开始比对数据 - 源端: {len(source_data)} 条, 目标端: {len(target_data)} 条")
            differences = await self._compare_data_direct(source_data, target_data, config)

            return {
                'success': True,
                'differences': differences,
                'summary': {
                    'total_records': len(source_data) + len(target_data),
                    'matched_records': len(source_data) + len(target_data) - len(differences),
                    'different_records': len(differences),
                    'source_only_records': 0,  # 简化处理
                    'target_only_records': 0   # 简化处理
                }
            }

        except Exception as e:
            logger.error(f"直接比对执行失败: {e}")
            raise

    def _convert_local_result_to_differences(self, local_result: Dict[str, Any],
                                           table_id: str) -> List[Dict[str, Any]]:
        """将本地比对结果转换为差异记录格式"""
        differences = []
        
        try:
            if not local_result.get('success', False):
                logger.warning(f"本地比对结果不成功: {local_result.get('error', '未知错误')}")
                return differences
            
            # 处理差异记录
            result_differences = local_result.get('differences', [])
            
            for diff in result_differences:
                if isinstance(diff, dict):
                    differences.append({
                        'id': diff.get('id', str(uuid.uuid4())),
                        'table_id': table_id,
                        'diff_type': diff.get('type', 'difference'),
                        'source_data': diff.get('source_data', {}),
                        'target_data': diff.get('target_data', {}),
                        'diff_fields': diff.get('diff_fields', [])
                    })
                else:
                    # 处理其他格式的差异记录
                    differences.append({
                        'id': str(uuid.uuid4()),
                        'table_id': table_id,
                        'diff_type': 'difference',
                        'source_data': getattr(diff, 'source_data', {}),
                        'target_data': getattr(diff, 'target_data', {}),
                        'diff_fields': getattr(diff, 'diff_fields', [])
                    })
            
        except Exception as e:
            logger.error(f"转换本地比对结果失败: {e}")
        
        return differences

    async def _fetch_data_async(self, connector, sql):
        """异步获取数据"""
        try:
            # 在线程池中执行同步数据库操作
            import asyncio

            def fetch_data_sync():
                with connector.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(sql)

                    # 获取列名
                    columns = [desc[0] for desc in cursor.description]

                    # 获取数据
                    rows = cursor.fetchall()

                    # 转换为字典列表
                    return [dict(zip(columns, row)) for row in rows]

            return await asyncio.to_thread(fetch_data_sync)

        except Exception as e:
            logger.error(f"数据获取失败: {e}")
            raise

    async def _compare_data_direct(self, source_data, target_data, config):
        """直接比对数据"""
        try:
            differences = []
            primary_keys = config.get('primary_keys', ['id'])

            # 创建索引以提高查找效率
            source_index = {}
            target_index = {}

            # 构建源端索引
            for record in source_data:
                key = self._build_record_key(record, primary_keys)
                source_index[key] = record

            # 构建目标端索引
            for record in target_data:
                key = self._build_record_key(record, primary_keys)
                target_index[key] = record

            # 比对数据
            all_keys = set(source_index.keys()) | set(target_index.keys())

            for key in all_keys:
                source_record = source_index.get(key)
                target_record = target_index.get(key)

                if source_record and target_record:
                    # 两端都有记录，比对内容
                    diff_fields = self._compare_records(source_record, target_record)
                    if diff_fields:
                        differences.append({
                            'id': str(uuid.uuid4()),
                            'type': 'DATA_MISMATCH',
                            'key': key,
                            'source_data': source_record,
                            'target_data': target_record,
                            'diff_fields': diff_fields
                        })
                elif source_record:
                    # 只有源端有记录
                    differences.append({
                        'id': str(uuid.uuid4()),
                        'type': 'SOURCE_ONLY',
                        'key': key,
                        'source_data': source_record,
                        'target_data': None,
                        'diff_fields': []
                    })
                elif target_record:
                    # 只有目标端有记录
                    differences.append({
                        'id': str(uuid.uuid4()),
                        'type': 'TARGET_ONLY',
                        'key': key,
                        'source_data': None,
                        'target_data': target_record,
                        'diff_fields': []
                    })

            logger.info(f"比对完成，发现 {len(differences)} 条差异")
            return differences

        except Exception as e:
            logger.error(f"数据比对失败: {e}")
            raise

    def _build_record_key(self, record, primary_keys):
        """构建记录的主键"""
        try:
            key_parts = []
            for pk in primary_keys:
                value = record.get(pk, '')
                key_parts.append(str(value))
            return '|'.join(key_parts)
        except Exception:
            # 如果主键构建失败，使用记录的哈希值
            return str(hash(str(sorted(record.items()))))

    def _compare_records(self, source_record, target_record):
        """比对两条记录"""
        try:
            diff_fields = []

            # 获取所有字段
            all_fields = set(source_record.keys()) | set(target_record.keys())

            for field in all_fields:
                source_value = source_record.get(field)
                target_value = target_record.get(field)

                # 比较值（处理None和类型转换）
                if not self._values_equal(source_value, target_value):
                    diff_fields.append({
                        'field': field,
                        'source': source_value,
                        'target': target_value
                    })

            return diff_fields

        except Exception as e:
            logger.error(f"记录比对失败: {e}")
            return []

    def _values_equal(self, value1, value2):
        """比较两个值是否相等"""
        try:
            # 处理None值
            if value1 is None and value2 is None:
                return True
            if value1 is None or value2 is None:
                return False

            # 转换为字符串比较（处理类型差异）
            return str(value1).strip() == str(value2).strip()

        except Exception:
            return False

    async def cancel_comparison(self) -> bool:
        """取消比对"""
        try:
            self.cancel_requested = True
            self.status = ComparisonStatus.CANCELLED
            self._update_progress(0, "本地函数比对已取消")
            
            # 如果比对服务支持取消操作
            if self.comparison_service and hasattr(self.comparison_service, 'cancel'):
                await self.comparison_service.cancel(self.current_task_id)
            
            return True
        except Exception as e:
            logger.error(f"取消本地函数比对失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理比对服务资源
            if self.comparison_service and hasattr(self.comparison_service, 'cleanup'):
                await self.comparison_service.cleanup()
            
            self.status = ComparisonStatus.IDLE
            
        except Exception as e:
            logger.error(f"清理本地函数比对资源失败: {e}")


# 注册本地函数比对模式
try:
    ComparisonModeFactory.register_mode("local", LocalComparisonMode)
except Exception as e:
    logger.error(f"❌ LocalComparisonMode 注册失败: {e}")
