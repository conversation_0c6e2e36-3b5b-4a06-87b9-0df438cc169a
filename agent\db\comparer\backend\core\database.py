#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统数据库连接和会话管理
完全抽象 SQLAlchemy 的使用，通过抽象接口与数据库交互
专门用于管理比对系统自身的元数据存储（任务状态、配置、结果等）

架构设计：
- 本模块：完全不直接导入 SQLAlchemy，通过抽象接口使用数据库
- comparison层：提供抽象接口和具体实现，隐藏 SQLAlchemy 细节
- 职责分离：系统数据库（ORM，长连接）vs 业务数据库（SQL，短连接）
"""
import os
import sys
import logging
from typing import Generator
from contextlib import contextmanager

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)
from backend.core.config import settings
from comparison.connectors.sqlalchemy_enginer import SystemDatabaseManager, SessionProtocol

logger = logging.getLogger(__name__)

# ================================
# 全局系统数据库管理器实例
# ================================

_system_db_manager = SystemDatabaseManager(
    database_url=settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    debug=settings.DEBUG
)

# 初始化系统数据库
_system_db_manager.initialize()

# ================================
# 核心数据库接口函数 - 保持原有功能完全不变
# 完全通过抽象接口操作，不直接使用 SQLAlchemy
# ================================

@contextmanager
def get_db_context() -> Generator[SessionProtocol, None, None]:
    """
    系统数据库会话上下文管理器

    这是系统中最重要的数据库接口函数，原有功能完全保持不变：
    - 自动事务管理（提交/回滚）
    - 会话生命周期管理
    - 错误处理和资源清理
    - 完全兼容现有的所有调用方式
    """
    # 委托给抽象的系统数据库管理器
    with _system_db_manager.get_session_context() as session:
        yield session

def get_db() -> SessionProtocol:
    """
    获取数据库会话

    功能：
    - 返回数据库会话对象
    - 支持手动事务管理
    - 需要手动关闭会话
    """
    return _system_db_manager.get_session()

# ================================
# 向后兼容的全局变量 - 保持原有接口
# ================================

# 创建向后兼容的数据库管理器实例
db_manager = _system_db_manager

engine = _system_db_manager._engine  # 抽象的数据库引擎
SessionLocal = _system_db_manager._session_factory  # 抽象的会话工厂

# ================================
# 简化的向后兼容接口
# ================================

def get_engine():
    """获取数据库引擎 - 简化接口"""
    return _system_db_manager._engine

def get_metadata():
    """获取数据库元数据 - 简化接口"""
    if _system_db_manager._engine:
        return _system_db_manager._engine.metadata
    return None

# ================================
# 辅助函数 - 保持原有功能
# ================================

def create_database_engine():
    """创建系统数据库引擎 - 向后兼容函数"""
    return _system_db_manager._engine

def get_system_db_metrics():
    """获取系统数据库性能指标"""
    try:
        # 通过抽象接口获取指标
        return {
            'database_type': 'system',
            'manager_type': type(_system_db_manager).__name__,
            'initialized': _system_db_manager._engine is not None,
            'connection_test': _system_db_manager.test_connection()
        }
    except Exception as e:
        logger.error(f"获取系统数据库指标失败: {e}")
        return {'error': str(e)}

def log_system_db_status():
    """记录系统数据库状态到日志"""
    try:
        metrics = get_system_db_metrics()
        logger.info(f"系统数据库状态: {metrics}")
    except Exception as e:
        logger.error(f"记录系统数据库状态失败: {e}")

# 初始化数据库
def init_db():
    """
    初始化数据库
    """
    try:
        # 1. 通过抽象接口初始化数据库连接
        if not _system_db_manager._engine:
            _system_db_manager.initialize()

        # 2. 导入所有模型以确保表定义被注册到Base.metadata中
        try:
            import backend.models.comparison_models
            logger.info("✅ 数据库模型导入完成")
        except ImportError as e:
            logger.warning(f"⚠️ 导入数据库模型时出现警告: {e}")

        # 3. 创建所有数据库表
        _system_db_manager.create_all_tables()

        # 4. 验证数据库初始化结果
        if _system_db_manager.test_connection():
            logger.info("✅ 数据库连接测试成功")
        else:
            logger.warning("⚠️ 数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        # 记录详细的错误信息用于调试
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise

# 记录初始化状态
try:
    log_system_db_status()
except Exception as e:
    logger.warning(f"初始状态记录失败: {e}")
