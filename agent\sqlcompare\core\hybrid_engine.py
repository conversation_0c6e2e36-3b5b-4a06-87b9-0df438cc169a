# core/hybrid_engine.py
"""
混合比对引擎
根据数据规模智能选择最适合的比对策略
"""
import os
import sys
import logging
from typing import Optional, Callable, Dict, Any
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(current_dir))
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter
from .strategies import StrategySelector

logger = logging.getLogger(__name__)


class HybridComparisonEngine:
    """混合比对引擎"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化混合比对引擎
        
        Args:
            config: 引擎配置
        """
        self.config = config or {}
        self.strategy_selector = StrategySelector(self.config.get('strategy_selector', {}))
    
    def compare_sources(
        self,
        source_a: BaseConnector,
        source_b: BaseConnector,
        reporter: BaseReporter,
        progress_callback: Optional[Callable] = None,
        estimate_counts: bool = True
    ) -> Dict[str, Any]:
        """
        执行智能数据比对
        
        Args:
            source_a: 数据源A的连接器
            source_b: 数据源B的连接器
            reporter: 差异报告器
            progress_callback: 进度回调函数
            estimate_counts: 是否估算记录数量（影响策略选择）
            
        Returns:
            比对结果统计信息
        """
        logger.info("开始混合智能数据比对...")
        
        try:
            # 选择最适合的策略
            strategy, selection_info = self.strategy_selector.select_strategy(
                source_a, source_b, estimate_counts
            )
            
            # 检查是否需要排序
            if selection_info['requires_sorting']:
                logger.info("⚠️  所选策略需要数据排序，请确保数据源已正确排序")
                logger.info("💡 DB2Connector会自动添加ORDER BY子句确保排序")
            
            # 执行比对
            logger.info(f"使用策略: {selection_info['strategy_name']}")
            result = strategy.compare(source_a, source_b, reporter, progress_callback)
            
            # 合并选择信息到结果中
            result['strategy_info'] = selection_info
            
            return result
            
        except Exception as e:
            logger.error(f"混合比对引擎执行失败: {e}", exc_info=True)
            raise


def compare_sources_hybrid(
    source_a: BaseConnector,
    source_b: BaseConnector,
    reporter: BaseReporter,
    config: Optional[Dict[str, Any]] = None,
    progress_callback: Optional[Callable] = None
) -> Dict[str, Any]:
    """
    混合比对的便捷函数
    
    Args:
        source_a: 数据源A的连接器
        source_b: 数据源B的连接器
        reporter: 差异报告器
        config: 比对配置
        progress_callback: 进度回调函数
        
    Returns:
        比对结果统计信息
    """
    engine = HybridComparisonEngine(config)
    return engine.compare_sources(source_a, source_b, reporter, progress_callback)


# 向后兼容的函数别名
def compare_sources_smart(
    source_a: BaseConnector,
    source_b: BaseConnector,
    reporter: BaseReporter,
    config: Optional[Dict[str, Any]] = None,
    progress_callback: Optional[Callable] = None
) -> Dict[str, Any]:
    """智能比对函数（向后兼容别名）"""
    return compare_sources_hybrid(source_a, source_b, reporter, config, progress_callback)
