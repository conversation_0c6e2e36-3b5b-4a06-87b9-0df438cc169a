"""
比对服务
"""
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from backend.core.database import get_db_context
from backend.models.comparison_models import (
    ComparisonTask as ComparisonTaskModel,
    ComparisonResult as ComparisonResultModel,
    ComparisonTaskCreate,
    ComparisonTaskResponse,
    ComparisonResultResponse,
    TaskStatusEnum
)
from comparison.engines.base_engine import (
    ComparisonEngineFactory, ComparisonTask, ComparisonType, DatabaseConnection
)


class ComparisonService:
    """比对服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.engine_factory = ComparisonEngineFactory()
    
    async def validate_task(self, task_create: ComparisonTaskCreate) -> Tuple[bool, str]:
        """验证比对任务"""
        try:
            # 转换为内部任务对象
            task = self._convert_to_internal_task(task_create)
            
            # 获取对应的比对引擎
            comparison_type = ComparisonType(task_create.comparison_type)
            engine = self.engine_factory.create_engine(comparison_type)
            
            # 执行验证
            return engine.validate_task(task)
            
        except ValueError as e:
            return False, str(e)
        except Exception as e:
            self.logger.error(f"任务验证失败: {e}")
            return False, f"任务验证失败: {str(e)}"
    
    async def execute_comparison_task(self, task_create: ComparisonTaskCreate):
        """执行比对任务"""
        task_id = task_create.task_id
        
        try:
            # 保存任务到数据库
            await self._save_task_to_db(task_create)
            
            # 更新任务状态为运行中
            await self._update_task_status(task_id, TaskStatusEnum.RUNNING)
            
            # 转换为内部任务对象
            task = self._convert_to_internal_task(task_create)
            
            # 获取比对引擎
            comparison_type = ComparisonType(task_create.comparison_type)
            engine = self.engine_factory.create_engine(comparison_type)
            
            self.logger.info(f"开始执行比对任务: {task_id}")
            
            # 执行比对
            result = await engine.compare(task)
            
            # 保存结果到数据库
            await self._save_result_to_db(result)
            
            # 更新任务状态
            if result.status == "completed":
                await self._update_task_status(task_id, TaskStatusEnum.COMPLETED)
                self.logger.info(f"比对任务 {task_id} 执行成功")
            else:
                await self._update_task_status(
                    task_id, TaskStatusEnum.FAILED, result.error_message
                )
                self.logger.error(f"比对任务 {task_id} 执行失败: {result.error_message}")
            
        except Exception as e:
            error_msg = f"比对任务执行异常: {str(e)}"
            self.logger.error(f"任务 {task_id} 执行异常: {e}", exc_info=True)
            
            # 更新任务状态为失败
            await self._update_task_status(task_id, TaskStatusEnum.FAILED, error_msg)
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            with get_db_context() as db:
                task = db.query(ComparisonTaskModel).filter(
                    ComparisonTaskModel.task_id == task_id
                ).first()
                
                if not task:
                    return None
                
                return {
                    "task_id": task.task_id,
                    "status": task.status,
                    "comparison_type": task.comparison_type,
                    "table_name": task.table_name,
                    "created_at": task.created_at,
                    "started_at": task.started_at,
                    "completed_at": task.completed_at,
                    "error_message": task.error_message
                }
                
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}")
            return None
    
    async def get_comparison_result(self, task_id: str) -> Optional[ComparisonResultResponse]:
        """获取比对结果"""
        try:
            with get_db_context() as db:
                result = db.query(ComparisonResultModel).filter(
                    ComparisonResultModel.task_id == task_id
                ).first()
                
                if not result:
                    return None
                
                return ComparisonResultResponse.from_orm(result)
                
        except Exception as e:
            self.logger.error(f"获取比对结果失败: {e}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            with get_db_context() as db:
                task = db.query(ComparisonTaskModel).filter(
                    ComparisonTaskModel.task_id == task_id
                ).first()
                
                if not task:
                    return False
                
                # 只能取消待执行或运行中的任务
                if task.status in [TaskStatusEnum.PENDING, TaskStatusEnum.RUNNING]:
                    task.status = TaskStatusEnum.CANCELLED
                    task.completed_at = datetime.utcnow()
                    db.commit()
                    
                    self.logger.info(f"任务 {task_id} 已取消")
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False
    
    async def list_tasks(self, status: Optional[str] = None, 
                        limit: int = 50, offset: int = 0) -> List[ComparisonTaskResponse]:
        """获取任务列表"""
        try:
            with get_db_context() as db:
                query = db.query(ComparisonTaskModel)
                
                if status:
                    query = query.filter(ComparisonTaskModel.status == status)
                
                tasks = query.order_by(
                    ComparisonTaskModel.created_at.desc()
                ).offset(offset).limit(limit).all()
                
                return [ComparisonTaskResponse.from_orm(task) for task in tasks]
                
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {e}")
            return []
    
    def _convert_to_internal_task(self, task_create: ComparisonTaskCreate) -> ComparisonTask:
        """转换为内部任务对象"""
        # 转换数据库连接信息
        source_conn = DatabaseConnection(
            db_type=task_create.source_connection['db_type'],
            host=task_create.source_connection['host'],
            port=task_create.source_connection['port'],
            database=task_create.source_connection['database'],
            username=task_create.source_connection['username'],
            password=task_create.source_connection['password']
        )
        
        target_conn = DatabaseConnection(
            db_type=task_create.target_connection['db_type'],
            host=task_create.target_connection['host'],
            port=task_create.target_connection['port'],
            database=task_create.target_connection['database'],
            username=task_create.target_connection['username'],
            password=task_create.target_connection['password']
        )
        
        return ComparisonTask(
            task_id=task_create.task_id,
            comparison_type=ComparisonType(task_create.comparison_type),
            source_connection=source_conn,
            target_connection=target_conn,
            table_name=task_create.table_name,
            sql_rules=task_create.sql_rules,
            batch_size=task_create.batch_size,
            timeout=task_create.timeout
        )
    
    async def _save_task_to_db(self, task_create: ComparisonTaskCreate):
        """保存任务到数据库"""
        try:
            with get_db_context() as db:
                task = ComparisonTaskModel(
                    task_id=task_create.task_id,
                    comparison_type=task_create.comparison_type,
                    source_connection=task_create.source_connection,
                    target_connection=task_create.target_connection,
                    table_name=task_create.table_name,
                    sql_rules=task_create.sql_rules,
                    batch_size=task_create.batch_size,
                    timeout=task_create.timeout,
                    status=TaskStatusEnum.PENDING
                )
                
                db.add(task)
                db.commit()
                
        except Exception as e:
            self.logger.error(f"保存任务到数据库失败: {e}")
            raise
    
    async def _save_result_to_db(self, result):
        """保存结果到数据库"""
        try:
            with get_db_context() as db:
                result_model = ComparisonResultModel(
                    task_id=result.comparison_id,
                    comparison_type=result.comparison_type.value,
                    source_info=result.source_info,
                    target_info=result.target_info,
                    total_records=result.total_records,
                    matched_records=result.matched_records,
                    different_records=result.different_records,
                    source_only_records=result.source_only_records,
                    target_only_records=result.target_only_records,
                    execution_time=result.execution_time,
                    differences=result.differences
                )
                
                db.add(result_model)
                db.commit()
                
        except Exception as e:
            self.logger.error(f"保存结果到数据库失败: {e}")
            raise
    
    async def _update_task_status(self, task_id: str, status: TaskStatusEnum, 
                                error_message: Optional[str] = None):
        """更新任务状态"""
        try:
            with get_db_context() as db:
                task = db.query(ComparisonTaskModel).filter(
                    ComparisonTaskModel.task_id == task_id
                ).first()
                
                if task:
                    task.status = status
                    
                    if status == TaskStatusEnum.RUNNING:
                        task.started_at = datetime.utcnow()
                    elif status in [TaskStatusEnum.COMPLETED, TaskStatusEnum.FAILED, TaskStatusEnum.CANCELLED]:
                        task.completed_at = datetime.utcnow()
                    
                    if error_message:
                        task.error_message = error_message
                    
                    db.commit()
                    
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
            raise
