"""
智能分区策略模块

提供数据比对任务的智能分区策略，根据数据分布特性自动选择最优分区方案，
提高大数据量比对的性能和内存效率。
"""

import logging
import math
from typing import List, Dict, Any, Tuple, Optional, Union

from app.models.database import DatabaseEngine
from app.models.task import CompareConfig

logger = logging.getLogger(__name__)


class SmartPartitioningStrategy:
    """智能分区策略"""

    async def create_partitions(self, db_engine, config: CompareConfig) -> List[Optional[Tuple]]:
        """
        创建智能分区

        Args:
            db_engine: 数据库引擎（可能是DatabaseEngine实例或旧版连接对象）
            config: 比对配置

        Returns:
            分区列表，每个分区为(start, end)元组或None(表示不分区)
        """
        # 检查是否为DatabaseEngine实例
        is_database_engine = isinstance(db_engine, DatabaseEngine)
        # 获取数据分布信息
        try:
            # 根据引擎类型调用不同的分析方法
            if is_database_engine:
                distribution = await self._analyze_data_distribution_with_engine(db_engine, config)
            else:
                distribution = await self._analyze_data_distribution_legacy(db_engine, config)

            if distribution["total_count"] < 10000:
                # 小数据集，不分区
                logger.info(f"数据集较小 ({distribution['total_count']} 条)，不进行分区")
                return [None]

            if distribution["is_uniform"]:
                # 均匀分布，使用等分分区
                logger.info("数据分布均匀，使用等分分区策略")
                return self._create_equal_partitions(distribution, config)
            else:
                # 不均匀分布，使用自适应分区
                logger.info("数据分布不均匀，使用自适应分区策略")
                return self._create_adaptive_partitions(distribution, config)
        except Exception as e:
            logger.warning(f"智能分区分析失败: {str(e)}，回退到默认分区策略")
            # 回退到简单分区策略
            return self._create_default_partitions(config)

    async def _analyze_data_distribution_with_engine(self, db_engine: DatabaseEngine, config: CompareConfig) -> Dict[str, Any]:
        """
        使用DatabaseEngine分析数据分布

        Args:
            db_engine: 数据库引擎
            config: 比对配置

        Returns:
            数据分布信息字典
        """
        # 获取数据总量
        table_name = config.source_table
        count_query = f"SELECT COUNT(*) FROM {table_name}"

        try:
            total_count = await db_engine.execute_scalar(count_query)
            logger.info(f"表 {table_name} 总记录数: {total_count}")
        except Exception as e:
            logger.warning(f"获取表 {table_name} 记录数失败: {str(e)}")
            # 使用默认值
            total_count = 100000

        # 分析主键分布
        if config.primary_keys and len(config.primary_keys) == 1:
            # 单一主键，分析其分布
            pk = config.primary_keys[0]

            try:
                # 获取主键类型
                type_query = self._get_column_type_query(db_engine.db_type, table_name, pk)
                pk_type = await db_engine.execute_scalar(type_query)

                # 获取主键范围
                min_max_query = f"SELECT MIN({pk}), MAX({pk}) FROM {table_name}"
                min_val, max_val = await db_engine.execute_row(min_max_query)

                logger.info(f"主键 {pk} 范围: {min_val} - {max_val}, 类型: {pk_type}")

                # 获取分布情况
                if pk_type and self._is_numeric_type(pk_type):
                    # 数值型主键，分析分布
                    buckets = 10
                    if max_val and min_val and max_val > min_val:
                        bucket_size = (max_val - min_val) / buckets

                        distribution_query = self._get_distribution_query(
                            db_engine.db_type, table_name, pk, min_val, max_val, buckets
                        )

                        distribution_data = await db_engine.execute_query(distribution_query)

                        # 计算分布均匀性
                        counts = [row["count"] for row in distribution_data]
                        if counts:
                            avg_count = sum(counts) / len(counts)
                            variance = sum((c - avg_count) ** 2 for c in counts) / len(counts)
                            std_dev = math.sqrt(variance) if variance > 0 else 0

                            # 判断是否均匀分布
                            is_uniform = (std_dev / avg_count) < 0.5 if avg_count > 0 else True

                            logger.info(f"数据分布分析: 平均={avg_count:.2f}, 标准差={std_dev:.2f}, 均匀性={is_uniform}")

                            return {
                                "total_count": total_count,
                                "min_val": min_val,
                                "max_val": max_val,
                                "is_uniform": is_uniform,
                                "distribution": distribution_data,
                                "pk_type": pk_type,
                                "pk": pk
                            }
            except Exception as e:
                logger.warning(f"分析主键分布失败: {str(e)}")

        # 默认返回基本信息
        return {
            "total_count": total_count,
            "is_uniform": True  # 默认假设均匀分布
        }

    async def _analyze_data_distribution_legacy(self, db_engine, config: CompareConfig) -> Dict[str, Any]:
        """
        使用旧版连接对象分析数据分布

        Args:
            db_engine: 旧版连接对象（可能是连接池或连接包装器）
            config: 比对配置

        Returns:
            数据分布信息字典
        """
        # 对于旧版连接，我们只返回基本信息，不进行详细分析
        # 这是为了保持向后兼容性
        return {
            "total_count": 100000,  # 使用默认值
            "is_uniform": True      # 默认假设均匀分布
        }

    def _get_distribution_query(self, db_type: str, table_name: str, column_name: str, min_val: Any, max_val: Any, buckets: int) -> str:
        """根据数据库类型生成分布查询"""
        bucket_size = (max_val - min_val) / buckets

        if db_type.lower() in ['postgresql', 'postgres', 'pg', 'gaussdb']:
            # PostgreSQL风格的分布查询
            return f"""
            WITH bucket_ranges AS (
                SELECT
                    width_bucket({column_name}, {min_val}, {max_val}, {buckets}) AS bucket,
                    COUNT(*) AS count
                FROM {table_name}
                GROUP BY bucket
                ORDER BY bucket
            )
            SELECT
                bucket,
                count,
                {min_val} + (bucket - 1) * {bucket_size} AS range_start,
                {min_val} + bucket * {bucket_size} AS range_end
            FROM bucket_ranges
            """
        elif db_type.lower() in ['mysql', 'mariadb']:
            # MySQL风格的分布查询
            buckets_list = []
            for i in range(buckets):
                start = min_val + i * bucket_size
                end = min_val + (i + 1) * bucket_size
                buckets_list.append(f"COUNT(CASE WHEN {column_name} >= {start} AND {column_name} < {end} THEN 1 END) AS bucket_{i}")

            buckets_str = ", ".join(buckets_list)
            return f"SELECT {buckets_str} FROM {table_name}"
        else:
            # 通用查询（可能不是最优的）
            return f"SELECT COUNT(*) AS count FROM {table_name}"

    def _get_column_type_query(self, db_type: str, table_name: str, column_name: str) -> str:
        """根据数据库类型生成获取列类型的查询"""
        if db_type.lower() in ['postgresql', 'postgres', 'pg']:
            return f"""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = '{table_name}' AND column_name = '{column_name}'
            """
        elif db_type.lower() in ['mysql', 'mariadb']:
            return f"""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = '{table_name}' AND column_name = '{column_name}'
            """
        elif db_type.lower() in ['oracle']:
            return f"""
                SELECT data_type
                FROM all_tab_columns
                WHERE table_name = '{table_name.upper()}' AND column_name = '{column_name.upper()}'
            """
        elif db_type.lower() in ['sqlserver', 'mssql']:
            return f"""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = '{table_name}' AND column_name = '{column_name}'
            """
        elif db_type.lower() in ['db2']:
            return f"""
                SELECT typename
                FROM syscat.columns
                WHERE tabname = '{table_name.upper()}' AND colname = '{column_name.upper()}'
            """
        else:
            # 默认使用通用SQL
            return f"""
                SELECT data_type
                FROM information_schema.columns
                WHERE table_name = '{table_name}' AND column_name = '{column_name}'
            """

    def _get_distribution_query(self, db_type: str, table_name: str, column_name: str,
                               min_val: Any, max_val: Any, buckets: int) -> str:
        """根据数据库类型生成分布查询"""
        bucket_size = (max_val - min_val) / buckets

        if db_type.lower() in ['postgresql', 'postgres', 'pg']:
            return f"""
                SELECT
                    FLOOR(({column_name} - {min_val}) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY bucket
                ORDER BY bucket
            """
        elif db_type.lower() in ['mysql', 'mariadb']:
            return f"""
                SELECT
                    FLOOR(({column_name} - {min_val}) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY bucket
                ORDER BY bucket
            """
        elif db_type.lower() in ['oracle']:
            return f"""
                SELECT
                    FLOOR(({column_name} - {min_val}) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY FLOOR(({column_name} - {min_val}) / {bucket_size})
                ORDER BY bucket
            """
        elif db_type.lower() in ['sqlserver', 'mssql']:
            return f"""
                SELECT
                    FLOOR(CAST(({column_name} - {min_val}) AS FLOAT) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY FLOOR(CAST(({column_name} - {min_val}) AS FLOAT) / {bucket_size})
                ORDER BY bucket
            """
        elif db_type.lower() in ['db2']:
            return f"""
                SELECT
                    FLOOR(({column_name} - {min_val}) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY FLOOR(({column_name} - {min_val}) / {bucket_size})
                ORDER BY bucket
            """
        else:
            # 默认使用通用SQL
            return f"""
                SELECT
                    FLOOR(({column_name} - {min_val}) / {bucket_size}) as bucket,
                    COUNT(*) as count
                FROM {table_name}
                GROUP BY FLOOR(({column_name} - {min_val}) / {bucket_size})
                ORDER BY bucket
            """

    def _is_numeric_type(self, data_type: str) -> bool:
        """判断是否为数值类型"""
        numeric_types = [
            'int', 'integer', 'smallint', 'bigint', 'decimal', 'numeric',
            'float', 'real', 'double', 'number', 'tinyint'
        ]

        return any(t in data_type.lower() for t in numeric_types)

    def _create_equal_partitions(self, distribution: Dict[str, Any], config: CompareConfig) -> List[Tuple]:
        """
        创建等分分区

        Args:
            distribution: 数据分布信息
            config: 比对配置

        Returns:
            分区列表，每个分区为(start, end)元组
        """
        # 根据数据量确定分区数
        total_count = distribution["total_count"]

        if total_count < 100000:
            partition_count = 4
        elif total_count < 1000000:
            partition_count = 8
        else:
            partition_count = 16

        # 如果有主键范围信息，按范围分区
        if "min_val" in distribution and "max_val" in distribution:
            min_val = distribution["min_val"]
            max_val = distribution["max_val"]

            if min_val is not None and max_val is not None and max_val > min_val:
                step = (max_val - min_val) / partition_count

                partitions = []
                for i in range(partition_count):
                    start = min_val + i * step
                    end = min_val + (i + 1) * step if i < partition_count - 1 else max_val + 1
                    partitions.append((start, end))

                logger.info(f"创建了 {len(partitions)} 个等分分区")
                return partitions

        # 如果无法按主键范围分区，返回默认分区
        logger.info(f"无法创建等分分区，回退到默认分区")
        return self._create_default_partitions(config)

    def _create_adaptive_partitions(self, distribution: Dict[str, Any], config: CompareConfig) -> List[Tuple]:
        """
        创建自适应分区

        Args:
            distribution: 数据分布信息
            config: 比对配置

        Returns:
            分区列表，每个分区为(start, end)元组
        """
        # 根据分布数据创建不等大小的分区，使每个分区数据量接近
        if "distribution" not in distribution:
            # 没有分布数据，回退到等分分区
            logger.info("缺少分布数据，回退到等分分区")
            return self._create_equal_partitions(distribution, config)

        # 目标是创建大约8-16个分区，每个分区数据量接近
        target_partitions = 12
        target_count_per_partition = distribution["total_count"] / target_partitions

        try:
            # 根据分布数据创建分区
            current_count = 0
            current_start = distribution["min_val"]
            partitions = []

            # 获取分布数据
            distribution_data = distribution["distribution"]
            bucket_size = (distribution["max_val"] - distribution["min_val"]) / len(distribution_data)

            for bucket, count in distribution_data:
                current_count += count

                if current_count >= target_count_per_partition:
                    # 创建一个分区
                    current_end = distribution["min_val"] + (bucket + 1) * bucket_size

                    partitions.append((current_start, current_end))

                    # 重置计数
                    current_count = 0
                    current_start = current_end

            # 添加最后一个分区
            if current_start < distribution["max_val"]:
                partitions.append((current_start, distribution["max_val"] + 1))

            logger.info(f"创建了 {len(partitions)} 个自适应分区")
            return partitions
        except Exception as e:
            logger.warning(f"创建自适应分区失败: {str(e)}，回退到等分分区")
            return self._create_equal_partitions(distribution, config)

    def _is_numeric_type(self, type_name: str) -> bool:
        """判断数据类型是否为数值型"""
        numeric_types = [
            'int', 'integer', 'smallint', 'bigint', 'decimal', 'numeric',
            'real', 'double', 'float', 'number', 'tinyint', 'mediumint'
        ]

        type_lower = type_name.lower()
        return any(num_type in type_lower for num_type in numeric_types)

    def _create_default_partitions(self, config: CompareConfig) -> List[None]:
        """创建默认分区（不分区）"""
        # 根据数据量估计分区数
        partition_count = 8
        logger.info(f"使用默认分区策略，创建 {partition_count} 个分区")
        return [None] * partition_count
