#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置管理
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field
from functools import lru_cache
from pathlib import Path


class Settings(BaseSettings):
    """应用程序设置"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化路径配置
        self._setup_paths()

    def _setup_paths(self):
        """设置路径配置"""
        # 获取项目根目录
        backend_root = Path(__file__).parent.parent.resolve()
        project_root = backend_root.parent.resolve()

        # 更新路径配置为绝对路径
        if self.database_url.startswith("sqlite:///./"):
            db_name = self.database_url.replace("sqlite:///./", "")
            self.database_url = f"sqlite:///{backend_root / db_name}"

        if self.sqlite_reporter_db_path.startswith("./"):
            db_name = self.sqlite_reporter_db_path.replace("./", "")
            self.sqlite_reporter_db_path = str(project_root / db_name)

        # 确保日志目录存在
        if self.log_file and not os.path.isabs(self.log_file):
            log_dir = backend_root / "logs"
            log_dir.mkdir(exist_ok=True)
            self.log_file = str(log_dir / self.log_file)

    # 基本配置
    app_name: str = Field("SQLCompare API", description="应用程序名称")
    version: str = Field("1.0.0", description="版本号")
    debug: bool = Field(False, description="调试模式")

    # 服务器配置
    host: str = Field("0.0.0.0", description="服务器主机")
    port: int = Field(8000, description="服务器端口")
    workers: int = Field(1, description="工作进程数")

    # 安全配置
    secret_key: str = Field("your-secret-key-change-in-production", description="密钥")
    allowed_hosts: List[str] = Field(["*"], description="允许的主机列表")
    cors_origins: List[str] = Field(["*"], description="CORS允许的源")

    # 数据库配置
    database_url: str = Field("sqlite:///./sqlcompare_api.db", description="数据库URL")
    database_echo: bool = Field(False, description="数据库SQL日志")

    # SQLite报告器配置
    sqlite_reporter_db_path: str = Field("./comparison_tasks.db", description="SQLite报告器数据库路径")
    sqlite_reporter_high_performance: bool = Field(True, description="启用高性能模式")
    sqlite_reporter_batch_size: int = Field(100000, description="批量大小")
    
    # 任务配置
    max_concurrent_tasks: int = Field(5, description="最大并发任务数")
    task_timeout_seconds: int = Field(3600, description="任务超时时间(秒)")
    task_cleanup_interval: int = Field(300, description="任务清理间隔(秒)")
    
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    log_max_size: int = Field(10 * 1024 * 1024, description="日志文件最大大小(字节)")
    log_backup_count: int = Field(5, description="日志文件备份数量")
    
    # Redis配置(可选，用于缓存和任务队列)
    redis_url: Optional[str] = Field(None, description="Redis URL")
    redis_password: Optional[str] = Field(None, description="Redis密码")
    
    # JWT配置
    jwt_secret_key: str = Field("jwt-secret-key-change-in-production", description="JWT密钥")
    jwt_algorithm: str = Field("HS256", description="JWT算法")
    jwt_expire_minutes: int = Field(30, description="JWT过期时间(分钟)")
    
    # API限流配置
    rate_limit_enabled: bool = Field(True, description="启用API限流")
    rate_limit_requests: int = Field(100, description="限流请求数")
    rate_limit_window: int = Field(60, description="限流时间窗口(秒)")
    
    # 文件上传配置
    max_upload_size: int = Field(10 * 1024 * 1024, description="最大上传文件大小(字节)")
    upload_dir: str = Field("./uploads", description="上传文件目录")
    
    # WebSocket配置
    websocket_enabled: bool = Field(True, description="启用WebSocket")
    websocket_heartbeat_interval: int = Field(30, description="WebSocket心跳间隔(秒)")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """开发环境配置"""
    debug: bool = True
    log_level: str = "DEBUG"
    database_echo: bool = True


class ProductionSettings(Settings):
    """生产环境配置"""
    debug: bool = False
    log_level: str = "INFO"
    allowed_hosts: List[str] = ["localhost", "127.0.0.1"]
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]


class TestingSettings(Settings):
    """测试环境配置"""
    debug: bool = True
    database_url: str = "sqlite:///./test_sqlcompare_api.db"
    sqlite_reporter_db_path: str = "./test_comparison_tasks.db"


@lru_cache()
def get_settings() -> Settings:
    """获取应用程序设置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# 导出设置实例
settings = get_settings()
