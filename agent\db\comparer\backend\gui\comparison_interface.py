#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一比对接口定义
定义所有比对模式必须实现的统一接口，确保一致性和可扩展性
"""

import os
import sys
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass

# 添加comparer目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
COMPARER_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))  # gui -> backend -> comparer
sys.path.insert(0, COMPARER_ROOT)

# 导入统一适配器模型
try:
    from adapters.unified_models import (
        UnifiedResult, UnifiedDifference, UnifiedProgress, UnifiedSummary,
        UnifiedError, UnifiedConfig, ComparisonMode, DifferenceType,
        ProgressCallback, ErrorCallback, CompletionCallback
    )
except ImportError:
    # 如果统一适配器不可用，定义基本类型
    from typing import Callable
    ProgressCallback = Callable[[int, str], None]
    ErrorCallback = Callable[[str], None]
    CompletionCallback = Callable[[Dict[str, Any]], None]


class ComparisonStatus(Enum):
    """比对状态枚举"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ComparisonConfig:
    """比对配置"""
    source_config: Dict[str, Any]
    target_config: Dict[str, Any]
    tables: List[Dict[str, Any]]
    batch_size: int = 1000
    timeout: int = 3600
    enable_progress_callback: bool = True
    enable_error_callback: bool = True
    max_retries: int = 3
    retry_delay: float = 2.0


@dataclass
class ComparisonResult:
    """比对结果"""
    success: bool
    mode: str
    execution_time: float
    total_tables: int
    completed_tables: int
    total_differences: int
    differences: List[Dict[str, Any]]
    summary: Dict[str, Any]
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ComparisonInterface(ABC):
    """统一比对接口抽象基类
    
    所有比对模式（直接比对、API比对、本地函数比对）都必须实现此接口
    """
    
    def __init__(self, gui_app, config: Optional[Dict[str, Any]] = None):
        """初始化比对接口
        
        Args:
            gui_app: GUI应用实例，用于回调和状态更新
            config: 可选的配置参数
        """
        self.gui_app = gui_app
        self.config = config or {}
        self.status = ComparisonStatus.IDLE
        self.current_task_id: Optional[str] = None
        self.cancel_requested = False
        
        # 回调函数
        self.progress_callback: Optional[ProgressCallback] = None
        self.error_callback: Optional[ErrorCallback] = None
        self.completion_callback: Optional[CompletionCallback] = None
        
        # 性能监控
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        
        # 结果存储
        self.last_result: Optional[ComparisonResult] = None
    
    @property
    @abstractmethod
    def mode_name(self) -> str:
        """返回比对模式名称"""
        pass
    
    @property
    @abstractmethod
    def display_name(self) -> str:
        """返回用于界面显示的模式名称"""
        pass
    
    @abstractmethod
    async def is_available(self) -> bool:
        """检查比对模式是否可用
        
        Returns:
            bool: True表示可用，False表示不可用
        """
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化比对模式
        
        Returns:
            bool: True表示初始化成功，False表示失败
        """
        pass
    
    @abstractmethod
    async def execute_comparison(self, comparison_config: ComparisonConfig) -> ComparisonResult:
        """执行比对操作
        
        Args:
            comparison_config: 比对配置
            
        Returns:
            ComparisonResult: 比对结果
        """
        pass
    
    @abstractmethod
    async def cancel_comparison(self) -> bool:
        """取消正在进行的比对
        
        Returns:
            bool: True表示取消成功，False表示取消失败
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass
    
    # 通用方法实现
    
    def set_progress_callback(self, callback: ProgressCallback) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_error_callback(self, callback: ErrorCallback) -> None:
        """设置错误回调函数"""
        self.error_callback = callback
    
    def set_completion_callback(self, callback: CompletionCallback) -> None:
        """设置完成回调函数"""
        self.completion_callback = callback
    
    def _update_progress(self, progress: int, message: str) -> None:
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)
        
        # 同时更新GUI
        if hasattr(self.gui_app, 'update_progress'):
            self.gui_app.update_progress(progress, message)
    
    def _report_error(self, error_message: str) -> None:
        """报告错误"""
        if self.error_callback:
            self.error_callback(error_message)
    
    def _report_completion(self, result: ComparisonResult) -> None:
        """报告完成"""
        if self.completion_callback:
            self.completion_callback(result.__dict__)
    
    def get_status(self) -> ComparisonStatus:
        """获取当前状态"""
        return self.status
    
    def get_last_result(self) -> Optional[ComparisonResult]:
        """获取最后一次比对结果"""
        return self.last_result
    
    def get_execution_time(self) -> Optional[float]:
        """获取执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    async def validate_config(self, comparison_config: ComparisonConfig) -> tuple[bool, str]:
        """验证比对配置
        
        Args:
            comparison_config: 比对配置
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 基本验证
            if not comparison_config.source_config:
                return False, "源数据库配置不能为空"
            
            if not comparison_config.target_config:
                return False, "目标数据库配置不能为空"
            
            if not comparison_config.tables:
                return False, "比对表列表不能为空"
            
            # 验证数据库连接配置
            required_fields = ['host', 'port', 'database', 'username', 'password', 'db_type']
            
            for field in required_fields:
                if field not in comparison_config.source_config:
                    return False, f"源数据库配置缺少字段: {field}"
                if field not in comparison_config.target_config:
                    return False, f"目标数据库配置缺少字段: {field}"
            
            # 验证表配置
            for i, table in enumerate(comparison_config.tables):
                if 'table_id' not in table:
                    return False, f"第{i+1}个表配置缺少table_id"
                if 'sql_1' not in table or 'sql_2' not in table:
                    return False, f"表{table.get('table_id', i+1)}缺少SQL配置"
            
            return True, ""
            
        except Exception as e:
            return False, f"配置验证异常: {str(e)}"
    
    def _create_result(self, success: bool, execution_time: float, 
                      total_tables: int, completed_tables: int,
                      differences: List[Dict[str, Any]], 
                      error_message: Optional[str] = None) -> ComparisonResult:
        """创建比对结果"""
        return ComparisonResult(
            success=success,
            mode=self.mode_name,
            execution_time=execution_time,
            total_tables=total_tables,
            completed_tables=completed_tables,
            total_differences=len(differences),
            differences=differences,
            summary={
                'mode': self.mode_name,
                'total_tables': total_tables,
                'completed_tables': completed_tables,
                'success_rate': (completed_tables / total_tables * 100) if total_tables > 0 else 0,
                'total_differences': len(differences),
                'execution_time': execution_time
            },
            error_message=error_message,
            metadata={
                'task_id': self.current_task_id,
                'start_time': self.start_time,
                'end_time': self.end_time,
                'status': self.status.value
            }
        )


class ComparisonModeFactory:
    """比对模式工厂类"""
    
    _modes: Dict[str, type] = {}
    
    @classmethod
    def register_mode(cls, mode_name: str, mode_class: type) -> None:
        """注册比对模式"""
        cls._modes[mode_name] = mode_class
    
    @classmethod
    def create_mode(cls, mode_name: str, gui_app, config: Optional[Dict[str, Any]] = None) -> ComparisonInterface:
        """创建比对模式实例"""
        if mode_name not in cls._modes:
            raise ValueError(f"未知的比对模式: {mode_name}")
        
        mode_class = cls._modes[mode_name]
        return mode_class(gui_app, config)
    
    @classmethod
    def get_available_modes(cls) -> List[str]:
        """获取所有可用的比对模式"""
        return list(cls._modes.keys())
    
    @classmethod
    def get_mode_info(cls, mode_name: str) -> Dict[str, Any]:
        """获取比对模式信息"""
        if mode_name not in cls._modes:
            return {}
        
        mode_class = cls._modes[mode_name]
        return {
            'name': mode_name,
            'class': mode_class.__name__,
            'description': mode_class.__doc__ or "无描述",
            'module': mode_class.__module__
        }
