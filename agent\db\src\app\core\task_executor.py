"""
任务执行器模块

负责执行数据比对任务，包括数据源连接、数据提取、比对执行和结果生成。
"""
import asyncio
import time
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union

from app.models.task import (
    TaskStatus, CompareTaskRequest, CompareMode, CompareConfig, AlgorithmType,
    TaskStats, ComparisonSummary, RecordDiff, FieldDiff
)
from app.models.db import DatabaseConnection
from app.core.config import settings
from app.core.db_utils import get_database_connection, execute_query, connect_database
from app.core.comparison import compare_result_sets, unified_comparison_service
from app.models.logger import logger
from app.core.error_reporter import report_error

class TaskExecutor:
    """任务执行器类，负责执行数据比对任务"""

    def __init__(self):
        """初始化任务执行器"""
        self._cancel_events = {}  # 任务ID -> Event 映射，用于取消任务
        self._pause_events = {}   # 任务ID -> Event 映射，用于暂停/恢复任务
        # 使用全局的统一比对服务
        self.comparison_service = unified_comparison_service

    async def execute_task(self, task_id: str, request: CompareTaskRequest) -> Dict[str, Any]:
        """
        执行数据比对任务

        Args:
            task_id: 任务ID
            request: 比对任务配置

        Returns:
            任务执行结果
        """
        logger.info(f"开始执行任务 - 任务ID: {task_id}, 源: {request.source_db.host}, 目标: {request.target_db.host}")

        # 初始化取消事件和暂停事件
        self._cancel_events[task_id] = asyncio.Event()
        self._pause_events[task_id] = asyncio.Event()
        self._pause_events[task_id].set()  # 默认不暂停

        # 记录任务开始时间
        start_time = time.time()

        # 更新任务状态为运行中
        await self._update_task_status(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            message="任务正在执行中...",
            execution_time=0
        )

        try:
            # 验证源和目标数据库配置
            source_db = request.source_db
            target_db = request.target_db

            if not source_db or not target_db:
                raise ValueError("源数据库或目标数据库配置缺失")

            # 创建比对配置
            config = CompareConfig(
                mode=request.mode,
                source_table=request.source_table,
                target_table=request.target_table,
                source_query=request.source_query,
                target_query=request.target_query,
                primary_keys=request.primary_keys,
                exclude_columns=request.exclude_columns or [],
                include_columns=request.include_columns or [],
                where_clause=request.where_clause,
                batch_size=request.batch_size or 1000,
                limit=request.limit,
                algorithm_type=AlgorithmType.PRIMARY_KEY  # 默认使用主键比对算法
            )

            # 执行比对
            result = await self.comparison_service.run_direct_comparison(
                source_db, target_db, config
            )

            # 从响应中提取摘要
            summary = result.summary if result.success else None

            # 更新任务状态为完成
            elapsed_time = int(time.time() - start_time)
            await self._update_task_status(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                message="任务已完成",
                summary=summary,
                execution_time=elapsed_time
            )

            return {
                "success": True,
                "task_id": task_id,
                "summary": summary,
                "message": "任务执行成功",
                "execution_time": elapsed_time
            }

        except asyncio.CancelledError:
            logger.info(f"任务被取消 - 任务ID: {task_id}")
            elapsed_time = int(time.time() - start_time)
            await self._update_task_status(
                task_id=task_id,
                status=TaskStatus.CANCELED,
                message="任务已被取消",
                execution_time=elapsed_time
            )
            return {"success": False, "task_id": task_id, "message": "任务已被取消"}

        except Exception as e:
            error_message = f"任务执行失败 - 任务ID: {task_id}, 错误: {str(e)}"
            logger.exception(error_message)

            # 将详细错误信息报告给服务器
            error_context = {
                "source_db": f"{request.source_db.host}:{request.source_db.port}",
                "target_db": f"{request.target_db.host}:{request.target_db.port}",
                "mode": request.mode,
                "traceback": traceback.format_exc()
            }
            # 在后台发送错误报告
            asyncio.create_task(report_error(task_id, e, error_context))

            elapsed_time = int(time.time() - start_time)
            await self._update_task_status(
                task_id=task_id,
                status=TaskStatus.FAILED,
                message=f"任务执行失败: {str(e)}",
                execution_time=elapsed_time
            )
            return {"success": False, "task_id": task_id, "message": str(e)}

        finally:
            # 清理资源
            if task_id in self._cancel_events:
                del self._cancel_events[task_id]
            if task_id in self._pause_events:
                del self._pause_events[task_id]
            logger.info(f"任务执行结束 - 任务ID: {task_id}")

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消正在执行的任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 操作是否成功
        """
        if task_id in self._cancel_events:
            self._cancel_events[task_id].set()
            logger.info(f"发送取消信号 - 任务ID: {task_id}")

            # TODO: 取消比对功能需要重新实现
            # await self.comparison_service.cancel_comparison(task_id)

            return True
        return False

    async def pause_task(self, task_id: str) -> bool:
        """
        暂停正在执行的任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 操作是否成功
        """
        if task_id in self._pause_events:
            self._pause_events[task_id].clear()
            logger.info(f"发送暂停信号 - 任务ID: {task_id}")

            # TODO: 暂停比对功能需要重新实现
            # result = await self.comparison_service.pause_comparison(task_id)
            result = True  # 暂时返回成功

            return result
        return False

    async def resume_task(self, task_id: str) -> bool:
        """
        恢复已暂停的任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 操作是否成功
        """
        if task_id in self._pause_events:
            self._pause_events[task_id].set()
            logger.info(f"发送恢复信号 - 任务ID: {task_id}")

            # TODO: 恢复比对功能需要重新实现
            # result = await self.comparison_service.resume_comparison(task_id, None, None, None)
            result = True  # 暂时返回成功

            return result
        return False

    async def _update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        message: Optional[str] = None,
        summary: Optional[ComparisonSummary] = None,
        execution_time: Optional[int] = None
    ):
        """
        更新任务状态

        应该由TaskManager类实现，这里为简化示例暂不实现具体逻辑
        """
        # 实际实现中应调用任务管理器的状态更新方法
        logger.info(f"任务状态更新 - 任务ID: {task_id}, 状态: {status.name}, 消息: {message}")
        # 此处应实现状态更新逻辑


# 创建任务执行器实例
task_executor = TaskExecutor()