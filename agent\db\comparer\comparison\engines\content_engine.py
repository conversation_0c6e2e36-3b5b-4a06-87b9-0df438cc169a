"""
表内容比对引擎
"""
import time
import hashlib
from typing import Dict, List, Any, Tuple, Optional

from .base_engine import (
    BaseComparisonEngine, ComparisonTask, ComparisonResult, 
    ComparisonType, DifferenceType
)
from ..connectors.database_connector import DatabaseConnectorFactory
from ..rules.sql_rule_parser import SQLRuleParser

class ContentComparisonEngine(BaseComparisonEngine):
    """表内容比对引擎"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.connector_factory = DatabaseConnectorFactory()
        self.rule_parser = SQLRuleParser()

        # 配置管理（保留有用的配置，移除分页相关）
        self.config = config or {}
        self.enable_progress_logging = self.config.get('enable_progress_logging', True)
        self.memory_warning_threshold = self.config.get('memory_warning_threshold', 100000)  # 内存警告阈值
    
    async def compare(self, task: ComparisonTask) -> ComparisonResult:
        """执行表内容比对"""
        start_time = time.time()
        result = self._create_comparison_result(task)

        try:
            self._log_progress("开始表内容比对", task.task_id)

            # 验证任务
            is_valid, error_msg = self.validate_task(task)
            if not is_valid:
                result.status = "failed"
                result.error_message = error_msg
                return result

            # 解析SQL规则
            source_sql, target_sql = self._parse_sql_rules(task)

            # 获取源端数据
            self._log_progress("获取源端数据", task.task_id)
            source_data = await self._fetch_data(
                task.source_connection, source_sql, task.batch_size
            )

            # 获取目标端数据
            self._log_progress("获取目标端数据", task.task_id)
            target_data = await self._fetch_data(
                task.target_connection, target_sql, task.batch_size
            )

            # 执行内容比对
            self._log_progress("执行数据比对", task.task_id)
            differences = await self._compare_data(
                source_data, target_data, task.task_id
            )
            
            # 统计结果
            result.total_records = len(source_data) + len(target_data)
            result.differences = differences
            result.different_records = len([d for d in differences if d['type'] == DifferenceType.DATA_MISMATCH.value])
            result.source_only_records = len([d for d in differences if d['type'] == DifferenceType.MISSING_IN_TARGET.value])
            result.target_only_records = len([d for d in differences if d['type'] == DifferenceType.MISSING_IN_SOURCE.value])
            result.matched_records = result.total_records - len(differences)
            result.execution_time = time.time() - start_time
            result.status = "completed"
            
            self._log_progress(
                f"表内容比对完成，总记录数: {result.total_records}, "
                f"匹配: {result.matched_records}, 差异: {len(differences)}", 
                task.task_id
            )
            
        except Exception as e:
            result.status = "failed"
            result.error_message = self._handle_error(e, task.task_id)
            result.execution_time = time.time() - start_time
        
        return result
    
    def validate_task(self, task: ComparisonTask) -> Tuple[bool, str]:
        """验证比对任务"""
        try:
            if task.comparison_type != ComparisonType.CONTENT:
                return False, "任务类型不匹配，期望表内容比对"

            if not task.sql_rules:
                return False, "SQL规则不能为空"

            if 'sql_1' not in task.sql_rules or 'sql_2' not in task.sql_rules:
                return False, "SQL规则必须包含sql_1和sql_2"

            # 验证SQL语句不为空
            if not task.sql_rules['sql_1'].strip():
                return False, "源端SQL语句不能为空"

            if not task.sql_rules['sql_2'].strip():
                return False, "目标端SQL语句不能为空"

            # 验证数据库连接信息
            for conn_name, conn in [("源", task.source_connection), ("目标", task.target_connection)]:
                if not conn:
                    return False, f"{conn_name}数据库连接对象不能为空"

                if not all([conn.host, conn.database, conn.username]):
                    return False, f"{conn_name}数据库连接信息不完整 (host: {conn.host}, database: {conn.database}, username: {conn.username})"

                # 验证数据库类型
                if not hasattr(conn, 'db_type') or not conn.db_type:
                    return False, f"{conn_name}数据库类型未指定"

            # 验证批处理大小
            if task.batch_size <= 0:
                return False, f"批处理大小必须大于0，当前值: {task.batch_size}"

            return True, ""

        except Exception as e:
            return False, f"任务验证过程中发生错误: {str(e)}"
    
    def _parse_sql_rules(self, task: ComparisonTask) -> Tuple[str, str]:
        """解析SQL规则"""
        source_sql = task.sql_rules['sql_1'].strip()
        target_sql = task.sql_rules['sql_2'].strip()
        
        # 验证SQL语句
        if not source_sql.upper().startswith('SELECT'):
            raise ValueError("源端SQL必须是SELECT语句")
        
        if not target_sql.upper().startswith('SELECT'):
            raise ValueError("目标端SQL必须是SELECT语句")
        
        return source_sql, target_sql
    
    async def _fetch_data(self, connection, sql: str, batch_size: int) -> List[Dict[str, Any]]:
        """获取数据"""
        connector = self.connector_factory.create_connector(connection.db_type)

        try:
            # 建立连接
            await connector.connect(connection)

            # 执行查询
            data = await connector.execute_query(sql, batch_size)

            return data

        except Exception as e:
            self.logger.error(f"数据获取失败: {str(e)}", exc_info=True)
            raise
        finally:
            # 确保连接释放，即使disconnect失败也要记录
            try:
                await connector.disconnect()
            except Exception as disconnect_error:
                self.logger.warning(f"连接释放失败: {str(disconnect_error)}", exc_info=True)
    
    async def _compare_data(self, source_data: List[Dict],
                          target_data: List[Dict], task_id: str) -> List[Dict[str, Any]]:
        """比对数据内容"""
        differences = []

        # 检查数据量，对大数据集进行内存警告
        total_records = len(source_data) + len(target_data)
        if total_records > self.memory_warning_threshold:
            self._log_progress(f"⚠️ 处理大数据集 ({total_records:,} 条记录)，请确保有足够内存", task_id)

        # 创建数据映射 (使用KEY字段作为主键)
        # 注意：这里必须保持完整的数据集在内存中，以确保KEY值匹配的正确性
        source_map = {}
        target_map = {}

        # 处理源端数据
        self._log_progress(f"开始处理源端数据 ({len(source_data):,} 条记录)", task_id)
        for i, record in enumerate(source_data):
            if self.enable_progress_logging and i > 0 and i % 10000 == 0:
                self._log_progress(f"已处理源端数据 {i:,}/{len(source_data):,} 条", task_id)

            key = record.get('KEY') or record.get('key')
            if key is None:
                # 如果没有KEY字段，使用所有字段的哈希值作为键
                key = self._generate_record_hash(record)
            source_map[key] = record

        # 处理目标端数据
        self._log_progress(f"开始处理目标端数据 ({len(target_data):,} 条记录)", task_id)
        for i, record in enumerate(target_data):
            if self.enable_progress_logging and i > 0 and i % 10000 == 0:
                self._log_progress(f"已处理目标端数据 {i:,}/{len(target_data):,} 条", task_id)

            key = record.get('KEY') or record.get('key')
            if key is None:
                key = self._generate_record_hash(record)
            target_map[key] = record
        
        # 检查源端存在但目标端不存在的记录
        for key, record in source_map.items():
            if key not in target_map:
                differences.append({
                    'type': DifferenceType.MISSING_IN_TARGET.value,
                    'key': key,
                    'source_record': record,
                    'target_record': None,
                    'description': f"记录 '{key}' 在目标端缺失"
                })
        
        # 检查目标端存在但源端不存在的记录
        for key, record in target_map.items():
            if key not in source_map:
                differences.append({
                    'type': DifferenceType.MISSING_IN_SOURCE.value,
                    'key': key,
                    'source_record': None,
                    'target_record': record,
                    'description': f"记录 '{key}' 在源端缺失"
                })
        
        # 检查两端都存在但内容不同的记录
        for key in source_map.keys() & target_map.keys():
            source_record = source_map[key]
            target_record = target_map[key]
            
            field_differences = self._compare_records(source_record, target_record)
            
            if field_differences:
                differences.append({
                    'type': DifferenceType.DATA_MISMATCH.value,
                    'key': key,
                    'source_record': source_record,
                    'target_record': target_record,
                    'field_differences': field_differences,
                    'description': f"记录 '{key}' 数据不匹配: {len(field_differences)} 个字段差异"
                })
        
        self._log_progress(f"数据比对完成，发现 {len(differences)} 个差异", task_id)
        return differences
    
    def _compare_records(self, source_record: Dict, target_record: Dict) -> List[Dict[str, Any]]:
        """比对单条记录"""
        differences = []
        
        # 获取所有字段
        all_fields = set(source_record.keys()) | set(target_record.keys())
        
        for field in all_fields:
            source_value = source_record.get(field)
            target_value = target_record.get(field)
            
            # 处理None值比较
            if source_value != target_value:
                differences.append({
                    'field': field,
                    'source_value': source_value,
                    'target_value': target_value
                })
        
        return differences
    
    def _generate_record_hash(self, record: Dict[str, Any]) -> str:
        """生成记录哈希值"""
        try:
            # 排序字段以确保一致性，处理None值
            sorted_items = []
            for key, value in sorted(record.items()):
                # 标准化值的表示，确保哈希一致性
                if value is None:
                    normalized_value = "NULL"
                elif isinstance(value, (int, float)):
                    normalized_value = str(value)
                elif isinstance(value, str):
                    normalized_value = value.strip()
                else:
                    normalized_value = str(value)
                sorted_items.append((key, normalized_value))

            record_str = str(sorted_items)
            return hashlib.md5(record_str.encode('utf-8')).hexdigest()
        except Exception as e:
            # 如果哈希生成失败，使用记录的字符串表示作为备选
            self.logger.warning(f"哈希生成失败，使用备选方案: {str(e)}")
            return hashlib.md5(str(record).encode('utf-8')).hexdigest()


# 注册内容比对引擎
from .base_engine import ComparisonEngineFactory
ComparisonEngineFactory.register_engine(ComparisonType.CONTENT, ContentComparisonEngine)
