# ArkReplay-Agent 配置文件示例
# 基于INI格式，支持多种数据类型

[application]
# 应用基本信息
name = ArkReplay-Agent
version = 1.0.0
description = High-performance traffic replay execution engine

# 运行模式：standalone, cluster
mode = standalone

# 工作目录
work_dir = ./workspace

# 进程相关配置
max_threads = 8
worker_threads = 4

[logging]
# 日志级别：TRACE, DEBUG, INFO, WARN, ERROR, FATAL
level = INFO

# 日志输出
console = true
file = true

# 文件日志配置
log_file = logs/arkreplay.log
max_file_size = 10485760  # 10MB
max_files = 5
rotate_on_startup = false

# 日志格式：default, json
format = default

# 异步日志配置
async_logging = true
queue_size = 10000

[memory]
# 内存池配置
enable_memory_pool = true
initial_pool_size = 1048576      # 1MB
max_pool_size = 104857600        # 100MB
block_size = 4096                # 4KB

# 对象池配置
message_pool_size = 1024
session_pool_size = 256
buffer_pool_size = 512

# 内存统计
enable_memory_stats = true
stats_interval = 60              # 秒

[network]
# 网络基础配置
default_buffer_size = 65536      # 64KB
max_connections = 1000
connection_timeout = 5000        # 毫秒
send_timeout = 3000              # 毫秒
recv_timeout = 3000              # 毫秒

# TCP配置
tcp_nodelay = true
tcp_keepalive = true
keepalive_idle = 60              # 秒
keepalive_interval = 10          # 秒
keepalive_count = 3

# 重连配置
auto_reconnect = true
max_reconnect_attempts = 5
reconnect_interval = 1000        # 毫秒

# 网络统计
enable_network_stats = true

[replay]
# 回放基础配置
max_concurrent_tasks = 10
default_speed_multiplier = 1.0
preserve_timing = true
enable_metrics = true

# 任务调度配置
scheduler_threads = 2
task_queue_size = 1000
priority_levels = 4

# 超时配置
task_timeout = 300               # 秒
message_timeout = 30             # 秒
response_timeout = 10            # 秒

# 结果收集配置
enable_result_collection = true
max_results_memory = 52428800    # 50MB
auto_export_results = true
export_format = json

[protocols]
# 支持的协议列表
enabled_protocols = FIX,KCXP,MID

# FIX协议配置
[protocol_fix]
version = FIX.4.4
validate_checksum = true
validate_sequence = true
heartbeat_interval = 30          # 秒
max_message_size = 8192          # 字节

# KCXP协议配置
[protocol_kcxp]
version = 1.0
byte_order = little_endian
validate_length = true
max_message_size = 16384         # 字节

# MID协议配置
[protocol_mid]
version = 2.0
validate_checksum = true
compression = false
max_message_size = 4096          # 字节

# 连接配置示例
[connection_primary]
name = Primary Trading System
type = tcp_client
host = *************
port = 8080
protocol = FIX
auto_connect = true
priority = high

[connection_secondary]
name = Secondary Trading System
type = tcp_client
host = *************
port = 8081
protocol = KCXP
auto_connect = false
priority = normal

[connection_market_data]
name = Market Data Feed
type = multicast
host = *********
port = 9999
protocol = MID
auto_connect = true
priority = low

[performance]
# 性能监控配置
enable_monitoring = true
metrics_interval = 10            # 秒
export_metrics = true
metrics_file = logs/metrics.json

# 性能优化配置
cpu_affinity = true
numa_aware = true
huge_pages = false

# 缓存配置
enable_cpu_cache_optimization = true
cache_line_size = 64
prefetch_distance = 2

[security]
# 安全配置
enable_encryption = false
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
ssl_ca_file = certs/ca.crt

# 认证配置
enable_authentication = false
auth_method = certificate
auth_timeout = 30                # 秒

[data]
# 数据存储配置
data_dir = ./data
temp_dir = ./temp
backup_dir = ./backup

# 数据文件配置
max_file_size = **********       # 1GB
compression = gzip
compression_level = 6

# 数据清理配置
auto_cleanup = true
retention_days = 30
cleanup_interval = 86400         # 秒（24小时）

[monitoring]
# 监控和告警配置
enable_health_check = true
health_check_interval = 30       # 秒
health_check_port = 8090

# 告警配置
enable_alerts = true
alert_thresholds_cpu = 80        # 百分比
alert_thresholds_memory = 85     # 百分比
alert_thresholds_disk = 90       # 百分比

# 外部监控集成
prometheus_enabled = false
prometheus_port = 9090
grafana_enabled = false

[debug]
# 调试配置
enable_debug_mode = false
debug_level = 1
dump_messages = false
dump_dir = ./debug

# 性能分析
enable_profiling = false
profiling_interval = 60         # 秒
profiling_output = ./profiling

# 内存调试
enable_memory_debug = false
detect_leaks = true
detect_corruption = true

[experimental]
# 实验性功能
enable_experimental_features = false
new_scheduler = false
optimized_memory_pool = false
advanced_metrics = false

# 测试配置
test_mode = false
mock_connections = false
simulate_latency = false
latency_ms = 10
