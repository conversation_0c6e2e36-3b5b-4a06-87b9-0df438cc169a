#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLCompare Backend API启动脚本
提供便捷的启动方式和配置选项
"""

import os
import sys
import argparse
import uvicorn
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

def setup_environment():
    """设置环境变量"""
    # 设置默认环境变量
    default_env = {
        "ENVIRONMENT": "development",
        "HOST": "0.0.0.0",
        "PORT": "8000",
        "DEBUG": "true",
        "LOG_LEVEL": "INFO",
        "SECRET_KEY": "dev-secret-key-change-in-production",
        "JWT_SECRET_KEY": "dev-jwt-secret-key-change-in-production",
        "DATABASE_URL": "sqlite:///./sqlcompare_api.db",
        "SQLITE_REPORTER_DB_PATH": "./comparison_tasks.db",
        "MAX_CONCURRENT_TASKS": "5",
        "TASK_TIMEOUT_SECONDS": "3600"
    }
    
    for key, value in default_env.items():
        if key not in os.environ:
            os.environ[key] = value


def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "uploads",
        "exports"
    ]
    
    for directory in directories:
        dir_path = current_dir / directory
        dir_path.mkdir(exist_ok=True)
        print(f"📁 目录已创建: {dir_path}")


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "sqlalchemy",
        "psutil"
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    print("✅ 所有依赖包已安装")
    return True


def check_project_integration():
    """检查项目集成状态"""
    print("🔍 检查项目集成状态...")

    try:
        # 检查项目路径
        from backend.core.paths import get_project_paths
        paths = get_project_paths()

        if not paths.is_valid_project_root():
            print("⚠️ 项目根目录验证失败，可能影响功能")
            return False

        # 检查核心模块
        try:
            from utils.config_manager import SmartConfigManager
            from reporters.sqlite_reporter import TaskManager
            print("✅ 原项目核心模块可用")
        except ImportError as e:
            print(f"❌ 原项目核心模块导入失败: {e}")
            return False

        # 检查适配器
        try:
            from backend.adapters.config_adapter import ConfigAdapter
            from backend.adapters.engine_adapter import EngineAdapter
            print("✅ Backend适配器模块可用")
        except ImportError as e:
            print(f"❌ Backend适配器模块导入失败: {e}")
            return False

        print("✅ 项目集成检查通过")
        return True

    except Exception as e:
        print(f"❌ 项目集成检查失败: {e}")
        return False


def print_startup_info(host: str, port: int, debug: bool):
    """打印启动信息"""
    print("\n" + "=" * 60)
    print("🚀 SQLCompare Backend API 启动中...")
    print("=" * 60)
    print(f"📍 服务地址: http://{host}:{port}")
    print(f"📖 API文档: http://{host}:{port}/docs")
    print(f"📚 ReDoc文档: http://{host}:{port}/redoc")
    print(f"💚 健康检查: http://{host}:{port}/health")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    print(f"📊 监控面板: http://{host}:{port}/api/v1/monitoring/metrics")
    print("=" * 60)
    
    if debug:
        print("⚠️  开发模式警告:")
        print("   - 使用默认密钥，生产环境请更改")
        print("   - 启用了详细错误信息")
        print("   - 自动重载已启用")
        print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SQLCompare Backend API启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--log-level", default="info", 
                       choices=["critical", "error", "warning", "info", "debug"],
                       help="日志级别")
    parser.add_argument("--env", default="development",
                       choices=["development", "production", "testing"],
                       help="运行环境")
    
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ["ENVIRONMENT"] = args.env
    os.environ["HOST"] = args.host
    os.environ["PORT"] = str(args.port)
    os.environ["DEBUG"] = str(args.debug).lower()
    os.environ["LOG_LEVEL"] = args.log_level.upper()
    
    setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查项目集成
    if not check_project_integration():
        print("\n⚠️ 项目集成检查失败，但仍可尝试启动服务")
        print("如遇到问题，请检查项目目录结构和依赖安装")

    # 创建目录
    create_directories()
    
    # 打印启动信息
    print_startup_info(args.host, args.port, args.debug or args.reload)
    
    # 启动服务
    try:
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            workers=args.workers if not (args.reload or args.debug) else 1,
            reload=args.reload or args.debug,
            log_level=args.log_level,
            access_log=True,
            use_colors=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
