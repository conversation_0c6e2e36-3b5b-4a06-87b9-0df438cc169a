#pragma once

#include "core/data_structures.hpp"
#include "protocol/protocol_types.hpp"
#include "network/network_types.hpp"
#include "memory/memory_pool.hpp"
#include <queue>
#include <vector>
#include <thread>
#include <future>
#include <atomic>
#include <mutex>
#include <condition_variable>

namespace arkreplay {
namespace replay {

// ==================== 回放任务结构 ====================

/**
 * 回放任务状态
 */
enum class TaskStatus : uint8_t {
    PENDING = 0,
    RUNNING = 1,
    PAUSED = 2,
    COMPLETED = 3,
    FAILED = 4,
    CANCELLED = 5
};

/**
 * 回放任务优先级
 */
enum class TaskPriority : uint8_t {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * 回放任务结构
 */
struct ReplayTask {
    uint64_t task_id;
    std::string task_name;
    std::string description;
    TaskPriority priority;
    std::atomic<TaskStatus> status;
    
    // 时间控制
    core::Timestamp scheduled_time;
    core::Timestamp start_time;
    core::Timestamp end_time;
    core::Duration estimated_duration;
    
    // 消息列表
    std::vector<core::Message> messages;
    std::atomic<size_t> current_message_index{0};
    
    // 配置
    core::ReplayConfig config;
    
    // 统计信息
    core::ReplayStatistics stats;
    
    // 错误信息
    std::vector<core::ErrorInfo> errors;
    
    ReplayTask(uint64_t id, const std::string& name)
        : task_id(id), task_name(name), priority(TaskPriority::NORMAL)
        , status(TaskStatus::PENDING)
        , scheduled_time(std::chrono::high_resolution_clock::now()) {}
    
    bool is_completed() const {
        TaskStatus current_status = status.load();
        return current_status == TaskStatus::COMPLETED || 
               current_status == TaskStatus::FAILED || 
               current_status == TaskStatus::CANCELLED;
    }
    
    double get_progress() const {
        if (messages.empty()) return 0.0;
        return static_cast<double>(current_message_index.load()) / messages.size() * 100.0;
    }
};

// ==================== 回放调度器 ====================

/**
 * 任务比较器（用于优先队列）
 */
struct TaskComparator {
    bool operator()(const std::shared_ptr<ReplayTask>& a, const std::shared_ptr<ReplayTask>& b) const {
        // 优先级高的任务优先
        if (a->priority != b->priority) {
            return a->priority < b->priority;
        }
        // 相同优先级按调度时间排序
        return a->scheduled_time > b->scheduled_time;
    }
};

/**
 * 回放调度器
 * 负责管理和调度回放任务
 */
class ReplayScheduler {
private:
    // 任务队列
    std::priority_queue<std::shared_ptr<ReplayTask>, 
                       std::vector<std::shared_ptr<ReplayTask>>, 
                       TaskComparator> task_queue_;
    
    // 运行中的任务
    std::unordered_map<uint64_t, std::shared_ptr<ReplayTask>> running_tasks_;
    
    // 已完成的任务
    std::unordered_map<uint64_t, std::shared_ptr<ReplayTask>> completed_tasks_;
    
    // 线程安全
    mutable std::mutex scheduler_mutex_;
    std::condition_variable task_available_;
    
    // 调度器状态
    std::atomic<bool> is_running_{false};
    std::atomic<bool> should_stop_{false};
    
    // 工作线程
    std::vector<std::thread> worker_threads_;
    size_t max_concurrent_tasks_;
    
    // 统计信息
    std::atomic<uint64_t> total_tasks_scheduled_{0};
    std::atomic<uint64_t> total_tasks_completed_{0};
    std::atomic<uint64_t> total_tasks_failed_{0};

public:
    explicit ReplayScheduler(size_t max_concurrent = std::thread::hardware_concurrency());
    ~ReplayScheduler();
    
    // 禁用拷贝和移动
    ReplayScheduler(const ReplayScheduler&) = delete;
    ReplayScheduler& operator=(const ReplayScheduler&) = delete;
    
    /**
     * 启动调度器
     */
    void start();
    
    /**
     * 停止调度器
     */
    void stop();
    
    /**
     * 添加任务
     */
    bool schedule_task(std::shared_ptr<ReplayTask> task);
    
    /**
     * 取消任务
     */
    bool cancel_task(uint64_t task_id);
    
    /**
     * 暂停任务
     */
    bool pause_task(uint64_t task_id);
    
    /**
     * 恢复任务
     */
    bool resume_task(uint64_t task_id);
    
    /**
     * 获取任务状态
     */
    std::shared_ptr<ReplayTask> get_task(uint64_t task_id) const;
    
    /**
     * 获取所有任务
     */
    std::vector<std::shared_ptr<ReplayTask>> get_all_tasks() const;
    
    /**
     * 获取调度器统计信息
     */
    struct SchedulerStats {
        uint64_t total_scheduled;
        uint64_t total_completed;
        uint64_t total_failed;
        uint64_t currently_running;
        uint64_t pending_tasks;
        double success_rate;
    };
    
    SchedulerStats get_statistics() const;

private:
    void worker_thread_function();
    void execute_task(std::shared_ptr<ReplayTask> task);
    std::shared_ptr<ReplayTask> get_next_task();
};

// ==================== 回放执行器 ====================

/**
 * 回放执行器
 * 负责执行具体的回放逻辑
 */
class ReplayExecutor {
private:
    // 网络连接管理
    std::unordered_map<core::SessionId, std::unique_ptr<network::NetworkConnection>> connections_;
    
    // 协议解析器
    std::unordered_map<core::ProtocolType, std::unique_ptr<protocol::ProtocolParser>> parsers_;
    
    // 内存池
    memory::ThreadSafeMemoryPool memory_pool_;
    memory::ObjectPool<core::Message> message_pool_;
    
    // 时序控制
    std::atomic<bool> timing_enabled_{true};
    std::atomic<double> speed_multiplier_{1.0};
    
    // 统计信息
    core::ReplayStatistics global_stats_;
    
    mutable std::mutex executor_mutex_;

public:
    explicit ReplayExecutor(const core::MemoryPoolConfig& pool_config);
    ~ReplayExecutor();
    
    /**
     * 执行回放任务
     */
    bool execute(std::shared_ptr<ReplayTask> task);
    
    /**
     * 添加网络连接
     */
    bool add_connection(core::SessionId session_id, 
                       std::unique_ptr<network::NetworkConnection> connection);
    
    /**
     * 移除网络连接
     */
    void remove_connection(core::SessionId session_id);
    
    /**
     * 添加协议解析器
     */
    void add_protocol_parser(core::ProtocolType protocol, 
                           std::unique_ptr<protocol::ProtocolParser> parser);
    
    /**
     * 设置时序控制
     */
    void set_timing_enabled(bool enabled) { timing_enabled_.store(enabled); }
    void set_speed_multiplier(double multiplier) { speed_multiplier_.store(multiplier); }
    
    /**
     * 获取统计信息
     */
    const core::ReplayStatistics& get_statistics() const { return global_stats_; }

private:
    bool send_message(const core::Message& message);
    bool wait_for_response(const core::Message& request, core::Message& response, 
                          std::chrono::milliseconds timeout);
    void calculate_timing_delay(const core::Message& current, const core::Message& next);
    void update_statistics(const core::Message& message, bool success);
    void handle_network_event(const network::NetworkEvent& event);
    void handle_data_received(core::SessionId session_id, const uint8_t* data, size_t length);
};

// ==================== 回放结果收集器 ====================

/**
 * 回放结果类型
 */
enum class ResultType : uint8_t {
    SUCCESS = 0,
    TIMEOUT = 1,
    ERROR = 2,
    MISMATCH = 3
};

/**
 * 回放结果结构
 */
struct ReplayResult {
    uint64_t task_id;
    core::MessageId message_id;
    ResultType type;
    core::Timestamp timestamp;
    
    // 原始消息和响应
    core::Message original_request;
    core::Message original_response;
    core::Message replay_response;
    
    // 差异信息
    std::vector<std::string> differences;
    
    // 性能指标
    core::Duration response_time;
    size_t bytes_sent;
    size_t bytes_received;
    
    ReplayResult(uint64_t tid, core::MessageId mid, ResultType t)
        : task_id(tid), message_id(mid), type(t)
        , timestamp(std::chrono::high_resolution_clock::now())
        , bytes_sent(0), bytes_received(0) {}
};

/**
 * 结果收集器
 */
class ResultCollector {
private:
    std::vector<ReplayResult> results_;
    mutable std::mutex results_mutex_;
    
    // 统计信息
    std::atomic<uint64_t> total_results_{0};
    std::atomic<uint64_t> success_count_{0};
    std::atomic<uint64_t> timeout_count_{0};
    std::atomic<uint64_t> error_count_{0};
    std::atomic<uint64_t> mismatch_count_{0};

public:
    ResultCollector() = default;
    
    /**
     * 添加结果
     */
    void add_result(ReplayResult result);
    
    /**
     * 获取任务结果
     */
    std::vector<ReplayResult> get_task_results(uint64_t task_id) const;
    
    /**
     * 获取所有结果
     */
    std::vector<ReplayResult> get_all_results() const;
    
    /**
     * 清除结果
     */
    void clear_results();
    
    /**
     * 获取统计信息
     */
    struct CollectorStats {
        uint64_t total_results;
        uint64_t success_count;
        uint64_t timeout_count;
        uint64_t error_count;
        uint64_t mismatch_count;
        double success_rate;
    };
    
    CollectorStats get_statistics() const;
    
    /**
     * 导出结果
     */
    bool export_to_json(const std::string& filename) const;
    bool export_to_csv(const std::string& filename) const;
};

} // namespace replay
} // namespace arkreplay
