"""
数据库引擎实现模块

提供各种数据库的DatabaseEngine具体实现类。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

import asyncpg
import aiomysql
import oracledb
import pyodbc
import ibm_db_dbi
import ibm_db_sa

from app.models.database import DatabaseEngine
from app.models.db import DatabaseConnection, DatabaseType

logger = logging.getLogger(__name__)


class PostgreSQLEngine(DatabaseEngine):
    """PostgreSQL数据库引擎实现"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化PostgreSQL数据库引擎

        Args:
            connection: 数据库连接配置
        """
        self.connection = connection
        self.pool = None
        self.conn = None
        self._db_type = "postgresql"

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 获取连接参数
            host = self.connection.host
            port = self.connection.port
            user = self.connection.username
            password = self.connection.password
            database = self.connection.database

            # 创建连接池
            self.pool = await asyncpg.create_pool(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database,
                min_size=1,
                max_size=10
            )

            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开数据库连接"""
        if self.pool:
            await self.pool.close()
            self.pool = None
        return True

    async def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.pool is not None

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果集"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            # 转换命名参数为位置参数
            query_text, args = self._convert_params(query, params)

            # 执行查询
            rows = await conn.fetch(query_text, *args)

            # 转换为字典列表
            result = [dict(row) for row in rows]
            return result

    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并返回单个值"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            # 转换命名参数为位置参数
            query_text, args = self._convert_params(query, params)

            # 执行查询
            result = await conn.fetchval(query_text, *args)
            return result

    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """执行查询并返回单行结果"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            # 转换命名参数为位置参数
            query_text, args = self._convert_params(query, params)

            # 执行查询
            row = await conn.fetchrow(query_text, *args)

            if row:
                return tuple(row)
            return None

    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行非查询操作并返回影响的行数"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            # 转换命名参数为位置参数
            query_text, args = self._convert_params(query, params)

            # 执行查询
            result = await conn.execute(query_text, *args)

            # 解析影响的行数
            if result and "UPDATE" in result:
                return int(result.split(" ")[1])
            elif result and "DELETE" in result:
                return int(result.split(" ")[1])
            elif result and "INSERT" in result:
                return 1
            return 0

    async def begin_transaction(self) -> None:
        """开始事务"""
        if not self.pool:
            raise Exception("数据库未连接")

        self.conn = await self.pool.acquire()
        self.transaction = self.conn.transaction()
        await self.transaction.start()

    async def commit(self) -> None:
        """提交事务"""
        if self.transaction:
            await self.transaction.commit()
            self.transaction = None

        if self.conn:
            await self.pool.release(self.conn)
            self.conn = None

    async def rollback(self) -> None:
        """回滚事务"""
        if self.transaction:
            await self.transaction.rollback()
            self.transaction = None

        if self.conn:
            await self.pool.release(self.conn)
            self.conn = None

    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构"""
        query = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position
        """

        columns = await self.execute_query(query, {"table_name": table_name})

        return {
            "table_name": table_name,
            "columns": columns
        }

    async def get_primary_keys(self, table_name: str) -> List[str]:
        """获取表的主键列表"""
        query = """
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = $1::regclass AND i.indisprimary
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["attname"] for row in rows]

    async def get_column_names(self, table_name: str) -> List[str]:
        """获取表的所有列名"""
        query = """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["column_name"] for row in rows]

    async def acquire(self):
        """获取连接"""
        if not self.pool:
            raise Exception("数据库未连接")

        return await self.pool.acquire()

    def release(self):
        """释放连接"""
        if self.conn:
            asyncio.create_task(self.pool.release(self.conn))
            self.conn = None

    @property
    def db_type(self) -> str:
        """获取数据库类型"""
        return self._db_type

    def _convert_params(self, query: str, params: Optional[Dict[str, Any]]) -> Tuple[str, List[Any]]:
        """
        将命名参数转换为位置参数

        Args:
            query: 原始查询语句
            params: 命名参数字典

        Returns:
            转换后的查询语句和位置参数列表
        """
        if not params:
            return query, []

        # 替换命名参数为位置参数
        args = []
        param_index = 1

        # 创建参数映射
        param_map = {}

        # 查找所有命名参数
        import re
        named_params = re.findall(r':(\w+)', query)

        # 为每个命名参数分配位置
        for name in named_params:
            if name not in param_map:
                param_map[name] = param_index
                param_index += 1
                args.append(params.get(name))

        # 替换查询中的命名参数
        for name, index in param_map.items():
            query = query.replace(f":{name}", f"${index}")

        return query, args


class MySQLEngine(DatabaseEngine):
    """MySQL数据库引擎实现"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化MySQL数据库引擎

        Args:
            connection: 数据库连接配置
        """
        self.connection = connection
        self.pool = None
        self.conn = None
        self.cursor = None
        self._db_type = "mysql"

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 获取连接参数
            host = self.connection.host
            port = self.connection.port
            user = self.connection.username
            password = self.connection.password
            database = self.connection.database

            # 创建连接池
            self.pool = await aiomysql.create_pool(
                host=host,
                port=port,
                user=user,
                password=password,
                db=database,
                minsize=1,
                maxsize=10,
                autocommit=True
            )

            return True
        except Exception as e:
            logger.error(f"MySQL连接失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开数据库连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.pool = None
        return True

    async def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.pool is not None

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果集"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # 转换命名参数为MySQL参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                rows = await cursor.fetchall()

                # 转换为字典列表
                result = [dict(row) for row in rows]
                return result

    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并返回单个值"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为MySQL参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                row = await cursor.fetchone()

                if row and len(row) > 0:
                    return row[0]
                return None

    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """执行查询并返回单行结果"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为MySQL参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                row = await cursor.fetchone()

                if row:
                    return row
                return None

    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行非查询操作并返回影响的行数"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为MySQL参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                return cursor.rowcount

    async def begin_transaction(self) -> None:
        """开始事务"""
        if not self.pool:
            raise Exception("数据库未连接")

        self.conn = await self.pool.acquire()
        self.cursor = await self.conn.cursor()
        await self.conn.begin()

    async def commit(self) -> None:
        """提交事务"""
        if self.conn:
            await self.conn.commit()
            self.cursor.close()
            self.cursor = None
            self.pool.release(self.conn)
            self.conn = None

    async def rollback(self) -> None:
        """回滚事务"""
        if self.conn:
            await self.conn.rollback()
            self.cursor.close()
            self.cursor = None
            self.pool.release(self.conn)
            self.conn = None

    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构"""
        query = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = %s AND table_schema = DATABASE()
        ORDER BY ordinal_position
        """

        columns = await self.execute_query(query, {"table_name": table_name})

        return {
            "table_name": table_name,
            "columns": columns
        }

    async def get_primary_keys(self, table_name: str) -> List[str]:
        """获取表的主键列表"""
        query = """
        SELECT column_name
        FROM information_schema.key_column_usage
        WHERE table_name = %s AND table_schema = DATABASE() AND constraint_name = 'PRIMARY'
        ORDER BY ordinal_position
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["column_name"] for row in rows]

    async def get_column_names(self, table_name: str) -> List[str]:
        """获取表的所有列名"""
        query = """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = %s AND table_schema = DATABASE()
        ORDER BY ordinal_position
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["column_name"] for row in rows]

    async def acquire(self):
        """获取连接"""
        if not self.pool:
            raise Exception("数据库未连接")

        conn = await self.pool.acquire()
        return conn

    def release(self):
        """释放连接"""
        if self.conn:
            self.pool.release(self.conn)
            self.conn = None

    @property
    def db_type(self) -> str:
        """获取数据库类型"""
        return self._db_type

    def _convert_params(self, query: str, params: Optional[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
        """
        将命名参数转换为MySQL参数

        Args:
            query: 原始查询语句
            params: 命名参数字典

        Returns:
            转换后的查询语句和参数字典
        """
        if not params:
            return query, {}

        # 替换命名参数为MySQL参数
        import re
        named_params = re.findall(r':(\w+)', query)

        # 创建参数字典
        args = {}

        # 替换查询中的命名参数
        for name in named_params:
            query = query.replace(f":{name}", "%s")
            if name in params:
                args[name] = params[name]

        return query, args


class OracleEngine(DatabaseEngine):
    """Oracle数据库引擎实现"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化Oracle数据库引擎

        Args:
            connection: 数据库连接配置
        """
        self.connection = connection
        self.pool = None
        self.conn = None
        self._db_type = "oracle"

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 导入oracledb库
            import oracledb

            # 获取连接参数
            host = self.connection.host
            port = self.connection.port or 1521
            user = self.connection.username
            password = self.connection.password
            service = self.connection.database

            # 构建DSN
            dsn = f"{host}:{port}/{service}"

            # 创建连接池
            self.pool = await oracledb.create_pool(
                user=user,
                password=password,
                dsn=dsn,
                min=1,
                max=5,
                timeout=5
            )

            return True
        except Exception as e:
            logger.error(f"Oracle连接失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开数据库连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.pool = None
        return True

    async def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.pool is not None

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果集"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为Oracle参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                rows = await cursor.fetchall()

                # 获取列名
                columns = [col[0].lower() for col in cursor.description]

                # 转换为字典列表
                result = []
                for row in rows:
                    result.append(dict(zip(columns, row)))

                return result

    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并返回单个值"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为Oracle参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                row = await cursor.fetchone()

                if row and len(row) > 0:
                    return row[0]
                return None

    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """执行查询并返回单行结果"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为Oracle参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)
                row = await cursor.fetchone()

                if row:
                    return row
                return None

    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行非查询操作并返回影响的行数"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为Oracle参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, args)

                # Oracle不直接返回影响的行数，需要手动获取
                rowcount = cursor.rowcount

                # 提交事务
                await conn.commit()

                return rowcount

    async def begin_transaction(self) -> None:
        """开始事务"""
        if not self.pool:
            raise Exception("数据库未连接")

        self.conn = await self.pool.acquire()
        # Oracle默认开始事务，不需要显式调用

    async def commit(self) -> None:
        """提交事务"""
        if self.conn:
            await self.conn.commit()
            self.pool.release(self.conn)
            self.conn = None

    async def rollback(self) -> None:
        """回滚事务"""
        if self.conn:
            await self.conn.rollback()
            self.pool.release(self.conn)
            self.conn = None

    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构"""
        query = """
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE table_name = :table_name
        ORDER BY column_id
        """

        columns = await self.execute_query(query, {"table_name": table_name.upper()})

        return {
            "table_name": table_name,
            "columns": columns
        }

    async def get_primary_keys(self, table_name: str) -> List[str]:
        """获取表的主键列表"""
        query = """
        SELECT cols.column_name
        FROM all_constraints cons, all_cons_columns cols
        WHERE cols.table_name = :table_name
        AND cons.constraint_type = 'P'
        AND cons.constraint_name = cols.constraint_name
        AND cons.owner = cols.owner
        ORDER BY cols.position
        """

        rows = await self.execute_query(query, {"table_name": table_name.upper()})
        return [row["column_name"] for row in rows]

    async def get_column_names(self, table_name: str) -> List[str]:
        """获取表的所有列名"""
        query = """
        SELECT column_name
        FROM user_tab_columns
        WHERE table_name = :table_name
        ORDER BY column_id
        """

        rows = await self.execute_query(query, {"table_name": table_name.upper()})
        return [row["column_name"] for row in rows]

    async def acquire(self):
        """获取连接"""
        if not self.pool:
            raise Exception("数据库未连接")

        conn = await self.pool.acquire()
        return conn

    def release(self):
        """释放连接"""
        if self.conn:
            self.pool.release(self.conn)
            self.conn = None

    @property
    def db_type(self) -> str:
        """获取数据库类型"""
        return self._db_type

    def _convert_params(self, query: str, params: Optional[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
        """
        将命名参数转换为Oracle参数

        Args:
            query: 原始查询语句
            params: 命名参数字典

        Returns:
            转换后的查询语句和参数字典
        """
        if not params:
            return query, {}

        # Oracle已经支持命名参数，只需要确保参数名前有冒号
        new_params = {}
        for name, value in params.items():
            if not name.startswith(':'):
                new_params[f":{name}"] = value
            else:
                new_params[name] = value

        # 确保查询中的参数名前有冒号
        import re
        param_pattern = r':(\w+)'
        named_params = re.findall(param_pattern, query)

        for name in named_params:
            if f":{name}" not in new_params and name in params:
                new_params[f":{name}"] = params[name]

        return query, new_params


class SQLServerEngine(DatabaseEngine):
    """SQL Server数据库引擎实现"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化SQL Server数据库引擎

        Args:
            connection: 数据库连接配置
        """
        self.connection = connection
        self.pool = None
        self.conn = None
        self._db_type = "sqlserver"

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 导入aioodbc库
            import aioodbc

            # 获取连接参数
            host = self.connection.host
            port = self.connection.port or 1433
            user = self.connection.username
            password = self.connection.password
            database = self.connection.database

            # 构建DSN
            dsn = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={host},{port};"
                f"DATABASE={database};"
                f"UID={user};"
                f"PWD={password}"
            )

            # 创建连接池
            self.pool = await aioodbc.create_pool(
                dsn=dsn,
                autocommit=True,
                minsize=1,
                maxsize=5
            )

            return True
        except Exception as e:
            logger.error(f"SQL Server连接失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开数据库连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.pool = None
        return True

    async def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.pool is not None

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果集"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为SQL Server参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, *args)
                rows = await cursor.fetchall()

                # 获取列名
                columns = [column[0] for column in cursor.description]

                # 转换为字典列表
                result = []
                for row in rows:
                    result.append(dict(zip(columns, row)))

                return result

    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并返回单个值"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为SQL Server参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, *args)
                row = await cursor.fetchone()

                if row and len(row) > 0:
                    return row[0]
                return None

    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """执行查询并返回单行结果"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为SQL Server参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, *args)
                row = await cursor.fetchone()

                if row:
                    return row
                return None

    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行非查询操作并返回影响的行数"""
        if not self.pool:
            raise Exception("数据库未连接")

        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 转换命名参数为SQL Server参数
                query_text, args = self._convert_params(query, params)

                # 执行查询
                await cursor.execute(query_text, *args)

                # 获取影响的行数
                rowcount = cursor.rowcount

                return rowcount

    async def begin_transaction(self) -> None:
        """开始事务"""
        if not self.pool:
            raise Exception("数据库未连接")

        self.conn = await self.pool.acquire()
        self.cursor = await self.conn.cursor()
        await self.cursor.execute("BEGIN TRANSACTION")

    async def commit(self) -> None:
        """提交事务"""
        if self.conn and self.cursor:
            await self.cursor.execute("COMMIT TRANSACTION")
            await self.cursor.close()
            self.cursor = None
            self.pool.release(self.conn)
            self.conn = None

    async def rollback(self) -> None:
        """回滚事务"""
        if self.conn and self.cursor:
            await self.cursor.execute("ROLLBACK TRANSACTION")
            await self.cursor.close()
            self.cursor = None
            self.pool.release(self.conn)
            self.conn = None

    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构"""
        query = """
        SELECT
            c.name AS column_name,
            t.name AS data_type,
            c.is_nullable
        FROM sys.columns c
        JOIN sys.types t ON c.user_type_id = t.user_type_id
        JOIN sys.tables tbl ON c.object_id = tbl.object_id
        WHERE tbl.name = ?
        ORDER BY c.column_id
        """

        columns = await self.execute_query(query, {"table_name": table_name})

        return {
            "table_name": table_name,
            "columns": columns
        }

    async def get_primary_keys(self, table_name: str) -> List[str]:
        """获取表的主键列表"""
        query = """
        SELECT c.name AS column_name
        FROM sys.indexes i
        JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
        JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        JOIN sys.tables t ON i.object_id = t.object_id
        WHERE i.is_primary_key = 1 AND t.name = ?
        ORDER BY ic.key_ordinal
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["column_name"] for row in rows]

    async def get_column_names(self, table_name: str) -> List[str]:
        """获取表的所有列名"""
        query = """
        SELECT c.name AS column_name
        FROM sys.columns c
        JOIN sys.tables t ON c.object_id = t.object_id
        WHERE t.name = ?
        ORDER BY c.column_id
        """

        rows = await self.execute_query(query, {"table_name": table_name})
        return [row["column_name"] for row in rows]

    async def acquire(self):
        """获取连接"""
        if not self.pool:
            raise Exception("数据库未连接")

        conn = await self.pool.acquire()
        return conn

    def release(self):
        """释放连接"""
        if self.conn:
            self.pool.release(self.conn)
            self.conn = None

    @property
    def db_type(self) -> str:
        """获取数据库类型"""
        return self._db_type

    def _convert_params(self, query: str, params: Optional[Dict[str, Any]]) -> Tuple[str, List[Any]]:
        """
        将命名参数转换为SQL Server参数

        Args:
            query: 原始查询语句
            params: 命名参数字典

        Returns:
            转换后的查询语句和参数列表
        """
        if not params:
            return query, []

        # SQL Server使用?作为参数占位符
        import re
        param_pattern = r':(\w+)'
        named_params = re.findall(param_pattern, query)

        # 创建参数列表
        args = []
        for name in named_params:
            if name in params:
                args.append(params[name])
                query = query.replace(f":{name}", "?")

        return query, args


class DB2Engine(DatabaseEngine):
    """IBM DB2数据库引擎实现"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化DB2数据库引擎

        Args:
            connection: 数据库连接配置
        """
        self.connection = connection
        self.conn = None
        self.cursor = None
        self._db_type = "db2"

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 导入DB2驱动
            import ibm_db
            import ibm_db_dbi

            # 获取连接参数
            host = self.connection.host
            port = self.connection.port or 50000
            user = self.connection.username
            password = self.connection.password
            database = self.connection.database

            # 构建连接字符串
            conn_str = (
                f"DATABASE={database};"
                f"HOSTNAME={host};"
                f"PORT={port};"
                f"PROTOCOL=TCPIP;"
                f"UID={user};"
                f"PWD={password};"
                f"AUTHENTICATION=SERVER;"
            )

            # 在线程池中执行同步连接
            self.conn = await asyncio.to_thread(ibm_db_dbi.connect, conn_str)

            return True
        except Exception as e:
            logger.error(f"DB2连接失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开数据库连接"""
        if self.conn:
            try:
                await asyncio.to_thread(self.conn.close)
                self.conn = None
                return True
            except Exception as e:
                logger.error(f"DB2断开连接失败: {str(e)}")
                return False
        return True

    async def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.conn is not None

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果集"""
        if not self.conn:
            raise Exception("数据库未连接")

        # 转换命名参数为DB2参数
        query_text, args = self._convert_params(query, params)

        # 在线程池中执行同步查询
        cursor = await asyncio.to_thread(self.conn.cursor)
        try:
            await asyncio.to_thread(cursor.execute, query_text, args)
            rows = await asyncio.to_thread(cursor.fetchall)

            # 获取列名
            columns = [desc[0] for desc in cursor.description]

            # 转换为字典列表
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))

            return result
        finally:
            await asyncio.to_thread(cursor.close)

    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询并返回单个值"""
        if not self.conn:
            raise Exception("数据库未连接")

        # 转换命名参数为DB2参数
        query_text, args = self._convert_params(query, params)

        # 在线程池中执行同步查询
        cursor = await asyncio.to_thread(self.conn.cursor)
        try:
            await asyncio.to_thread(cursor.execute, query_text, args)
            row = await asyncio.to_thread(cursor.fetchone)

            if row and len(row) > 0:
                return row[0]
            return None
        finally:
            await asyncio.to_thread(cursor.close)

    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """执行查询并返回单行结果"""
        if not self.conn:
            raise Exception("数据库未连接")

        # 转换命名参数为DB2参数
        query_text, args = self._convert_params(query, params)

        # 在线程池中执行同步查询
        cursor = await asyncio.to_thread(self.conn.cursor)
        try:
            await asyncio.to_thread(cursor.execute, query_text, args)
            row = await asyncio.to_thread(cursor.fetchone)

            if row:
                return row
            return None
        finally:
            await asyncio.to_thread(cursor.close)

    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行非查询操作并返回影响的行数"""
        if not self.conn:
            raise Exception("数据库未连接")

        # 转换命名参数为DB2参数
        query_text, args = self._convert_params(query, params)

        # 在线程池中执行同步查询
        cursor = await asyncio.to_thread(self.conn.cursor)
        try:
            await asyncio.to_thread(cursor.execute, query_text, args)

            # 获取影响的行数
            rowcount = cursor.rowcount

            # 提交事务
            await asyncio.to_thread(self.conn.commit)

            return rowcount
        finally:
            await asyncio.to_thread(cursor.close)

    async def begin_transaction(self) -> None:
        """开始事务"""
        if not self.conn:
            raise Exception("数据库未连接")

        # DB2默认自动开始事务，不需要显式调用
        pass

    async def commit(self) -> None:
        """提交事务"""
        if self.conn:
            await asyncio.to_thread(self.conn.commit)

    async def rollback(self) -> None:
        """回滚事务"""
        if self.conn:
            await asyncio.to_thread(self.conn.rollback)

    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """获取表结构"""
        schema = self.connection.parameters.db_schema if hasattr(self.connection, 'parameters') and hasattr(self.connection.parameters, 'db_schema') else None
        schema = schema or self.connection.username.upper()

        query = """
        SELECT colname AS column_name, typename AS data_type, nulls AS is_nullable
        FROM syscat.columns
        WHERE tabname = ? AND tabschema = ?
        ORDER BY colno
        """

        columns = await self.execute_query(query, {"table_name": table_name.upper(), "schema": schema})

        return {
            "table_name": table_name,
            "columns": columns
        }

    async def get_primary_keys(self, table_name: str) -> List[str]:
        """获取表的主键列表"""
        schema = self.connection.parameters.db_schema if hasattr(self.connection, 'parameters') and hasattr(self.connection.parameters, 'db_schema') else None
        schema = schema or self.connection.username.upper()

        query = """
        SELECT colname AS column_name
        FROM syscat.keycoluse
        WHERE tabname = ? AND tabschema = ? AND colseq > 0
        ORDER BY colseq
        """

        rows = await self.execute_query(query, {"table_name": table_name.upper(), "schema": schema})
        return [row["column_name"] for row in rows]

    async def get_column_names(self, table_name: str) -> List[str]:
        """获取表的所有列名"""
        schema = self.connection.parameters.db_schema if hasattr(self.connection, 'parameters') and hasattr(self.connection.parameters, 'db_schema') else None
        schema = schema or self.connection.username.upper()

        query = """
        SELECT colname AS column_name
        FROM syscat.columns
        WHERE tabname = ? AND tabschema = ?
        ORDER BY colno
        """

        rows = await self.execute_query(query, {"table_name": table_name.upper(), "schema": schema})
        return [row["column_name"] for row in rows]

    async def acquire(self):
        """获取连接"""
        if not self.conn:
            raise Exception("数据库未连接")

        return self.conn

    def release(self):
        """释放连接"""
        # DB2不使用连接池，不需要释放
        pass

    @property
    def db_type(self) -> str:
        """获取数据库类型"""
        return self._db_type

    def _convert_params(self, query: str, params: Optional[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
        """
        将命名参数转换为DB2参数

        Args:
            query: 原始查询语句
            params: 命名参数字典

        Returns:
            转换后的查询语句和参数字典
        """
        if not params:
            return query, {}

        # DB2使用?作为参数占位符
        import re
        param_pattern = r':(\w+)'
        named_params = re.findall(param_pattern, query)

        # 创建参数字典
        args = {}
        for name in named_params:
            if name in params:
                query = query.replace(f":{name}", "?")
                args[name] = params[name]

        return query, args