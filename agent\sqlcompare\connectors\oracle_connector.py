# connectors/oracle_connector.py

import oracledb
from .base_connector import BaseConnector
from core.models import Record

class OracleConnector(BaseConnector):
    """Connector for Oracle databases."""

    def __init__(self, dsn, user, password, chunk_size=1000):
        super().__init__(chunk_size)
        self.dsn = dsn
        self.user = user
        self.password = password
        self.connection = None
        self.cursor = None

    def connect(self):
        """Establishes a connection to the Oracle database."""
        try:
            self.connection = oracledb.connect(user=self.user, password=self.password, dsn=self.dsn)
            print("Oracle connection established.")
        except oracledb.DatabaseError as e:
            print(f"Error connecting to Oracle: {e}")
            raise

    def close(self):
        """Closes the database connection."""
        if self.cursor:
            self.cursor.close()
            self.cursor = None
        if self.connection:
            self.connection.close()
            self.connection = None
            print("Oracle connection closed.")

    def fetch_data(self, query):
        """Fetches data from the database using the given query."""
        if not self.connection:
            self.connect()

        print(f"Executing query: {query}")
        self.cursor = self.connection.cursor()
        try:
            self.cursor.execute(query)
            self.columns = [desc[0].lower() for desc in self.cursor.description]
            if 'key' not in self.columns or 'value' not in self.columns:
                raise ValueError("Query must select columns named 'key' and 'value'.")
            return self
        except oracledb.DatabaseError as e:
            print(f"Error executing query on Oracle: {e}")
            self.close()
            raise

    def _fetch_next_chunk(self):
        """Fetches the next chunk of rows from the cursor."""
        try:
            rows = self.cursor.fetchmany(self.chunk_size)
            if not rows:
                return []
            return [Record(key=row[self.columns.index('key')], value=row[self.columns.index('value')]) for row in rows]
        except oracledb.DatabaseError as e:
            print(f"Error fetching data from Oracle: {e}")
            self.close()
            raise