#pragma once

#include "core/data_structures.hpp"
#include <memory>
#include <mutex>
#include <atomic>
#include <vector>
#include <stack>
#include <thread>

namespace arkreplay {
namespace memory {

// ==================== 内存块结构 ====================

/**
 * 内存块头部信息
 */
struct alignas(16) BlockHeader {
    size_t size;                    // 块大小
    bool is_free;                   // 是否空闲
    BlockHeader* next;              // 下一个块
    BlockHeader* prev;              // 前一个块
    uint32_t magic;                 // 魔数，用于检测内存损坏
    uint32_t thread_id;             // 分配线程ID
    
    static constexpr uint32_t MAGIC_NUMBER = 0xDEADBEEF;
    
    BlockHeader(size_t s) 
        : size(s), is_free(true), next(nullptr), prev(nullptr)
        , magic(MAGIC_NUMBER), thread_id(std::hash<std::thread::id>{}(std::this_thread::get_id())) {}
    
    bool is_valid() const noexcept {
        return magic == MAGIC_NUMBER;
    }
    
    void* get_data() noexcept {
        return reinterpret_cast<uint8_t*>(this) + sizeof(BlockHeader);
    }
    
    const void* get_data() const noexcept {
        return reinterpret_cast<const uint8_t*>(this) + sizeof(BlockHeader);
    }
};

/**
 * 内存池统计信息
 */
struct MemoryPoolStats {
    std::atomic<size_t> total_allocated{0};
    std::atomic<size_t> total_freed{0};
    std::atomic<size_t> current_usage{0};
    std::atomic<size_t> peak_usage{0};
    std::atomic<size_t> allocation_count{0};
    std::atomic<size_t> deallocation_count{0};
    std::atomic<size_t> fragmentation_count{0};
    
    double get_fragmentation_ratio() const noexcept {
        size_t alloc_count = allocation_count.load();
        if (alloc_count == 0) return 0.0;
        return static_cast<double>(fragmentation_count.load()) / alloc_count;
    }
    
    double get_efficiency() const noexcept {
        size_t total_alloc = total_allocated.load();
        if (total_alloc == 0) return 0.0;
        return static_cast<double>(current_usage.load()) / total_alloc * 100.0;
    }
};

// ==================== 线程安全内存池 ====================

/**
 * 高性能线程安全内存池
 * 特点：
 * 1. 线程本地缓存减少锁竞争
 * 2. 分级内存管理
 * 3. 内存对齐优化
 * 4. 碎片整理机制
 */
class ThreadSafeMemoryPool {
private:
    // 内存池配置
    core::MemoryPoolConfig config_;
    
    // 全局内存池
    std::unique_ptr<uint8_t[]> pool_memory_;
    size_t pool_size_;
    BlockHeader* free_list_;
    
    // 线程安全
    mutable std::mutex pool_mutex_;
    
    // 统计信息
    MemoryPoolStats stats_;
    
    // 线程本地缓存
    thread_local static std::stack<void*> thread_cache_;
    thread_local static size_t cache_size_;
    static constexpr size_t MAX_CACHE_SIZE = 64;
    
    // 内存对齐
    static constexpr size_t ALIGNMENT = 16;
    
    // 私有方法
    size_t align_size(size_t size) const noexcept {
        return (size + ALIGNMENT - 1) & ~(ALIGNMENT - 1);
    }
    
    BlockHeader* find_free_block(size_t size);
    void split_block(BlockHeader* block, size_t size);
    void merge_free_blocks();
    bool expand_pool(size_t additional_size);

public:
    explicit ThreadSafeMemoryPool(const core::MemoryPoolConfig& config);
    ~ThreadSafeMemoryPool();
    
    // 禁用拷贝和移动
    ThreadSafeMemoryPool(const ThreadSafeMemoryPool&) = delete;
    ThreadSafeMemoryPool& operator=(const ThreadSafeMemoryPool&) = delete;
    ThreadSafeMemoryPool(ThreadSafeMemoryPool&&) = delete;
    ThreadSafeMemoryPool& operator=(ThreadSafeMemoryPool&&) = delete;
    
    /**
     * 分配内存
     * @param size 请求的内存大小
     * @return 分配的内存指针，失败返回nullptr
     */
    void* allocate(size_t size);
    
    /**
     * 释放内存
     * @param ptr 要释放的内存指针
     */
    void deallocate(void* ptr);
    
    /**
     * 获取统计信息
     */
    MemoryPoolStats get_stats() const { return stats_; }
    
    /**
     * 重置内存池
     */
    void reset();
    
    /**
     * 执行碎片整理
     */
    void defragment();
    
    /**
     * 检查内存池健康状态
     */
    bool is_healthy() const;
    
    /**
     * 获取可用内存大小
     */
    size_t get_available_memory() const;
};

// ==================== 对象池模板 ====================

/**
 * 高性能对象池模板
 * 特点：
 * 1. 类型安全
 * 2. 构造/析构优化
 * 3. 内存预分配
 * 4. 无锁设计（单线程版本）
 */
template<typename T, size_t PoolSize = 1024>
class ObjectPool {
private:
    alignas(T) uint8_t storage_[sizeof(T) * PoolSize];
    std::stack<T*> free_objects_;
    std::atomic<size_t> allocated_count_{0};
    std::atomic<size_t> peak_count_{0};
    
    void initialize_pool() {
        for (size_t i = 0; i < PoolSize; ++i) {
            T* obj = reinterpret_cast<T*>(&storage_[i * sizeof(T)]);
            free_objects_.push(obj);
        }
    }

public:
    ObjectPool() {
        initialize_pool();
    }
    
    ~ObjectPool() {
        // 对象池析构时不需要显式析构对象，因为它们可能已经被手动析构
    }
    
    /**
     * 获取对象
     * @param args 构造函数参数
     * @return 构造好的对象指针
     */
    template<typename... Args>
    T* acquire(Args&&... args) {
        if (free_objects_.empty()) {
            return nullptr; // 池已满
        }
        
        T* obj = free_objects_.top();
        free_objects_.pop();
        
        // 在已分配的内存上构造对象
        new(obj) T(std::forward<Args>(args)...);
        
        size_t current = allocated_count_.fetch_add(1) + 1;
        size_t peak = peak_count_.load();
        while (current > peak && !peak_count_.compare_exchange_weak(peak, current)) {
            // 更新峰值
        }
        
        return obj;
    }
    
    /**
     * 释放对象
     * @param obj 要释放的对象指针
     */
    void release(T* obj) {
        if (!obj) return;
        
        // 检查指针是否在池的范围内
        if (obj < reinterpret_cast<T*>(storage_) || 
            obj >= reinterpret_cast<T*>(storage_ + sizeof(storage_))) {
            return; // 不是池中的对象
        }
        
        // 显式调用析构函数
        obj->~T();
        
        free_objects_.push(obj);
        allocated_count_.fetch_sub(1);
    }
    
    /**
     * 获取统计信息
     */
    size_t get_allocated_count() const { return allocated_count_.load(); }
    size_t get_peak_count() const { return peak_count_.load(); }
    size_t get_available_count() const { return PoolSize - allocated_count_.load(); }
    size_t get_total_capacity() const { return PoolSize; }
    
    /**
     * 检查池是否已满
     */
    bool is_full() const { return allocated_count_.load() >= PoolSize; }
    
    /**
     * 检查池是否为空
     */
    bool is_empty() const { return allocated_count_.load() == 0; }
};

// ==================== 智能指针包装器 ====================

/**
 * 内存池智能指针
 * 自动管理内存池中对象的生命周期
 */
template<typename T>
class PoolPtr {
private:
    T* ptr_;
    ObjectPool<T>* pool_;

public:
    PoolPtr() : ptr_(nullptr), pool_(nullptr) {}
    
    PoolPtr(T* ptr, ObjectPool<T>* pool) : ptr_(ptr), pool_(pool) {}
    
    ~PoolPtr() {
        if (ptr_ && pool_) {
            pool_->release(ptr_);
        }
    }
    
    // 移动构造和赋值
    PoolPtr(PoolPtr&& other) noexcept : ptr_(other.ptr_), pool_(other.pool_) {
        other.ptr_ = nullptr;
        other.pool_ = nullptr;
    }
    
    PoolPtr& operator=(PoolPtr&& other) noexcept {
        if (this != &other) {
            if (ptr_ && pool_) {
                pool_->release(ptr_);
            }
            ptr_ = other.ptr_;
            pool_ = other.pool_;
            other.ptr_ = nullptr;
            other.pool_ = nullptr;
        }
        return *this;
    }
    
    // 禁用拷贝
    PoolPtr(const PoolPtr&) = delete;
    PoolPtr& operator=(const PoolPtr&) = delete;
    
    T* get() const { return ptr_; }
    T& operator*() const { return *ptr_; }
    T* operator->() const { return ptr_; }
    
    explicit operator bool() const { return ptr_ != nullptr; }
    
    T* release() {
        T* temp = ptr_;
        ptr_ = nullptr;
        pool_ = nullptr;
        return temp;
    }
};

/**
 * 创建池智能指针的辅助函数
 */
template<typename T, typename... Args>
PoolPtr<T> make_pool_ptr(ObjectPool<T>& pool, Args&&... args) {
    T* obj = pool.acquire(std::forward<Args>(args)...);
    return PoolPtr<T>(obj, &pool);
}

} // namespace memory
} // namespace arkreplay
