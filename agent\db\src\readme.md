# DB-Agent 微服务

提供数据库连接测试和异构数据库比对的微服务。

## 功能特性

- **多数据库支持**: 支持MySQL、PostgreSQL、Oracle、SQL Server、DB2、GaussDB等主流数据库
- **连接测试API**: 提供简单的API来测试各种数据库的连接
- **数据比对API**: 比较不同数据库之间表的数据差异
- **异步任务处理**: 支持长时间运行的任务异步执行和状态追踪
- **高并发支持**: 支持多个比对任务并发执行
- **资源动态分配**: 根据系统负载动态调整资源分配
- **任务优先级管理**: 支持任务优先级设置和队列管理

## 技术栈

- **FastAPI**: 高性能的异步API框架
- **Celery+Redis**: 分布式任务队列
- **多种数据库驱动**: 支持同步和异步驱动
- **Docker**: 容器化部署
- **Prometheus+Grafana**: 系统监控（可选）

### 使用Docker运行

1. 克隆仓库
```bash
git clone <repo-url>
cd msa/db_agent
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，根据需要修改配置
```

3. 启动服务
```bash
docker-compose up -d
```

4. 访问API文档
```
http://localhost:8000/docs
```

### 本地开发

1. 创建并激活虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 启动Redis（可以使用Docker）
```bash
docker run -d -p 6379:6379 redis:7.0-alpine
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，根据需要修改配置
```

5. 启动服务
```bash
uvicorn app.main:app --reload --port 8000
```

6. 在另一个终端启动Celery工作节点
```bash
celery -A app.workers.tasks worker --loglevel=info
```

## API文档

启动服务后，可以通过以下URL访问API文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要API接口

### 数据库连接测试

```
POST /api/v1/connection/test
```

测试与指定数据库的连接是否可用。

### 创建数据比对任务

```
POST /api/v1/tasks
```

创建新的数据比对任务，返回任务ID，任务将异步执行。

### 获取任务状态

```
GET /api/v1/tasks/{task_id}
```

获取指定任务的状态和进度信息。

### 获取任务结果

```
GET /api/v1/tasks/{task_id}/results
```

获取指定任务的比对结果，支持分页获取详细差异信息。

### 任务控制

```
PUT /api/v1/tasks/{task_id}/pause   # 暂停任务
PUT /api/v1/tasks/{task_id}/resume  # 恢复任务
DELETE /api/v1/tasks/{task_id}      # 取消任务
```

### 直接比对（仅适用于小数据集）

```
POST /api/v1/compare_data
```

直接比对两个数据源的数据，同步返回结果。

## 监控与管理

- Celery任务监控: `http://localhost:5555` (Flower)
- 健康检查: `http://localhost:8000/health` 

## 项目评估

### 1. 代码架构 (Code Architecture)

*   **整体结构**: 项目遵循了常见的 Python Web 应用分层结构，将入口 (`main.py`)、API 定义 (`api/`)、核心逻辑 (`core/` - 推测)、数据模型 (`models/` - 推测) 分开，结构清晰。
*   **API 设计**:
    *   采用了 FastAPI 框架，利用其现代特性（异步、类型提示、自动文档等）。
    *   API 路由按功能模块划分到 `endpoints` 目录下，并通过 `APIRouter` 进行组织和版本控制 (`/api/v1`)，具有良好的模块化和可扩展性。
    *   任务相关 API (`tasks.py`) 设计遵循了 RESTful 风格，提供了创建、查询、状态获取、结果获取、删除、暂停、恢复等操作。
    *   利用 HATEOAS 思想，在创建任务的响应中返回了状态和结果的链接。
*   **关注点分离**: Web 框架层（API 定义、请求/响应处理）与核心业务逻辑（任务管理、数据比对 - 推测在 `core/` 中）有较好的分离。API 层主要负责接收请求、调用服务、处理 HTTP 相关的逻辑（如错误码、后台任务）。

### 2. 编码实现 (Coding Implementation)

*   **FastAPI 特性利用**:
    *   广泛使用了 Pydantic 模型进行请求/响应的数据校验和序列化 (`models/task.py`)。
    *   有效利用了路径参数 (`Path`)、查询参数 (`Query`) 及其验证约束。
    *   通过 `BackgroundTasks` 处理耗时的比对任务，避免阻塞 API 请求。
    *   提供了 WebSocket 端点 (`/ws/{task_id}/status`) 用于任务状态的实时推送。
    *   使用了 `lifespan` 管理器处理应用启动/关闭事件（如初始化 `TaskManager`）。
    *   实现了全局异常处理和自定义中间件（如请求计时）。
*   **异步处理**: 大量使用 `async def`，符合 FastAPI 的异步特性，有利于提高 I/O 密集型操作的性能。
*   **任务管理**: `tasks.py` 显示了一个围绕 `TaskManager` 构建的任务管理系统，支持任务生命周期（创建、排队、运行、暂停、完成、失败、取消、删除）。
*   **错误处理**: 使用 `HTTPException` 在 API 层返回明确的错误状态码和信息，区分了客户端错误（4xx）和潜在的服务端问题（通过全局处理器返回 500）。
*   **配置管理**: 通过 `core.config.settings` 集中管理配置。
*   **日志**: 集成了日志系统 (`models.logger`)，并在关键节点（启动、异常、任务创建、WebSocket 连接）记录日志。

### 3. 代码风格 (Code Style)

*   **可读性**: 代码整体可读性较好，函数和变量命名清晰。
*   **注释与文档**: `tasks.py` 中的 API 端点有良好的 Docstrings，解释了端点的功能和参数，这对于自动生成 API 文档非常有帮助。`main.py` 中也有必要的注释。
*   **类型提示**: 代码中使用了类型提示，增强了代码的健壮性和可维护性。
*   **一致性**: 项目在不同文件中保持了相似的编码风格和结构（如路径处理、路由定义方式）。

### 4. 潜在优化空间与关注点

*   **依赖注入 (Dependency Injection)**: `TaskManager` 和 `DataComparisonService` 在 `tasks.py` 中是直接实例化的。如果这些服务是有状态的或者需要更灵活的配置/替换（例如为了测试），使用 FastAPI 的依赖注入系统 (`Depends`) 会是更优的实践。这能提高代码的可测试性和松耦合度。
*   **服务实例生命周期与状态管理**: 需要明确 `TaskManager` 和 `DataComparisonService` 的设计。它们是无状态的还是有状态的？如果是后者，当前的模块级实例化方式在多进程/多实例部署下如何保证状态一致性？（例如，任务状态是存储在内存中还是外部持久化存储中？）。如果存储在内存中，服务将是单实例的，无法水平扩展。
*   **后台任务健壮性**: `BackgroundTasks` 是“即发即忘”的。如果 `comparison_service.run_comparison` 或 `resume_comparison` 在后台执行时失败，需要确保 `TaskManager` 能正确地更新任务状态为 `FAILED`，并且相关的错误信息被记录或存储，以便用户查询。更健壮的后台任务处理可能需要引入 Celery、RQ 或 Arq 等专用的任务队列系统。
*   **WebSocket 扩展性**: 当前 WebSocket 实现委托给 `TaskManager`。如果 `TaskManager` 在内存中维护连接，那么 WebSocket 功能将难以水平扩展。对于需要支持大量并发连接或多实例部署的场景，通常需要借助外部消息中间件（如 Redis Pub/Sub, RabbitMQ）来实现状态广播。
*   **CORS 配置**: `main.py` 中的 CORS 配置 (`allow_origins=["*"]`) 过于宽松，生产环境必须收紧。
*   **生产部署**: `main.py` 中的 `uvicorn.run` 包含了 `reload=True` 和直接指定 `workers`，这更适合开发。生产部署应使用 Gunicorn + Uvicorn workers 或类似的 WSGI/ASGI 服务器，并通过配置文件或命令行参数进行管理。
*   **资源清理**: 删除任务时 (`DELETE /{task_id}`)，需要确保关联的所有资源（如存储的结果、临时文件等）都被彻底清理。这部分逻辑的健壮性很重要。
*   **认证与授权**: 当前分析的代码未涉及明确的 API 认证和授权机制。根据应用场景，可能需要添加。

### 5. 总结与后续步骤

`db_agent` 项目展现了良好的基础架构和对 FastAPI 框架的熟练运用。代码结构清晰、模块化程度高、风格一致且可读性强。项目成功地实现了数据库比对任务的异步处理、状态管理和实时更新通知。

主要的潜在优化方向集中在**依赖管理**、**状态管理的可扩展性**（特别是 `TaskManager` 和 WebSocket）、**后台任务的健壮性**以及**生产环境的配置和部署**上。

**建议的下一步深入分析**:

1.  **核心逻辑**: 详细阅读 `app/core/task_manager.py` 和 `app/core/comparison.py` 的实现，理解其状态管理机制（内存 vs 持久化）、错误处理逻辑、与数据库的交互方式以及 WebSocket 的具体实现。
2.  **数据模型**: 查看 `app/models/` 目录下的 Pydantic 模型定义，了解数据结构的细节。
3.  **配置**: 查看 `app/core/config.py` 了解配置项。
4.  **数据库交互**: 分析比对服务是如何连接和查询源/目标数据库的。
5.  **测试**: 检查项目是否包含单元测试或集成测试。

通过对核心逻辑和数据模型的深入了解，可以更准确地评估其健壮性、效率和可扩展性，并提出更具体的优化建议。