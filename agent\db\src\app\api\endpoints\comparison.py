"""
数据比对API端点
提供兼容性的直接数据比对API，用于小型数据集的同步比对。
对于大型数据集，推荐使用任务API异步执行比对。
"""
from fastapi import APIRouter

from app.models.task import CompareTaskRequest
from app.models.response import ComparisonResponse, ErrorResponse
from app.core.db_utils import test_connection
from app.core.comparison import unified_comparison_service
from app.models.logger import logger

router = APIRouter()

@router.post(
    "/compare_data",
    response_model=ComparisonResponse,
    summary="直接比对数据",
    description="直接比对两个数据源的数据，返回比对结果。仅适用于小型数据集，大型数据集请使用任务API。"
)
async def compare_data(compare_request: CompareTaskRequest):
    """
    直接比对数据

    接收数据比对请求，直接执行比对并返回结果。
    注意：此API适用于小型数据集的同步比对，大型数据集请使用任务API。

    Returns:
        ComparisonResponse: 统一格式的比对响应，包含成功状态、摘要和详细信息
    """
    try:
        logger.info(f"收到直接数据比对请求 - 源: {compare_request.source.type}@{compare_request.source.host}, "
                   f"目标: {compare_request.target.type}@{compare_request.target.host}, "
                   f"模式: {compare_request.config.mode}")

        # 测试源连接
        source_conn_test = await test_connection(compare_request.source)
        if not source_conn_test["success"]:
            logger.warning(f"源数据库连接失败: {source_conn_test['message']}")
            return ErrorResponse(
                success=False,
                message=f"源数据库连接失败: {source_conn_test['message']}",
                code=400,
                error_code="SOURCE_CONNECTION_FAILED",
                error_details={
                    "connection_type": compare_request.source.type,
                    "host": compare_request.source.host,
                    "details": source_conn_test.get('message', '未知错误')
                }
            )

        # 测试目标连接
        target_conn_test = await test_connection(compare_request.target)
        if not target_conn_test["success"]:
            logger.warning(f"目标数据库连接失败: {target_conn_test['message']}")
            return ErrorResponse(
                success=False,
                message=f"目标数据库连接失败: {target_conn_test['message']}",
                code=400,
                error_code="TARGET_CONNECTION_FAILED",
                error_details={
                    "connection_type": compare_request.target.type,
                    "host": compare_request.target.host,
                    "details": target_conn_test.get('message', '未知错误')
                }
            )

        # 使用统一的比对服务执行比对
        logger.info("开始执行数据比对")

        # 调用统一比对服务
        comparison_response = await unified_comparison_service.run_direct_comparison(
            source_conn=compare_request.source,
            target_conn=compare_request.target,
            config=compare_request.config
        )

        # 为了保持向后兼容性，在响应中添加旧格式的字段
        if comparison_response.success and comparison_response.summary:
            # 添加向后兼容的字段
            legacy_response = ComparisonResponse(
                success=comparison_response.success,
                message=comparison_response.message,
                code=comparison_response.code,
                data=comparison_response.data,
                summary=comparison_response.summary,
                details=comparison_response.data.get("details", []) if comparison_response.data else [],
                execution_time=comparison_response.execution_time
            )

            logger.info(f"数据比对完成 - 执行时间: {comparison_response.execution_time:.2f}秒")
            return legacy_response
        else:
            # 比对失败，返回错误响应
            return ErrorResponse(
                success=False,
                message=comparison_response.message,
                code=comparison_response.code or 500,
                error_code="COMPARISON_FAILED",
                error_details={
                    "details": comparison_response.message,
                    "execution_time": comparison_response.execution_time
                }
            )
    except Exception as e:
        logger.exception(f"数据比对失败: {str(e)}")
        return ErrorResponse(
            success=False,
            message=f"数据比对失败: {str(e)}",
            code=500,
            error_code="INTERNAL_SERVER_ERROR",
            error_details={
                "exception_type": type(e).__name__,
                "exception_message": str(e)
            }
        )