"""
规则管理API端点
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, List, Any
from pydantic import BaseModel
import logging

from comparison.rules.sql_rule_parser import SQLRuleParser, RuleValidator

router = APIRouter()
logger = logging.getLogger(__name__)


class RuleValidationRequest(BaseModel):
    """规则验证请求模型"""
    table_id: str
    remark: str = ""
    sql_1: str
    sql_2: str


class RuleValidationResponse(BaseModel):
    """规则验证响应模型"""
    valid: bool
    message: str
    warnings: List[str] = []
    errors: List[str] = []


class XMLRuleParseRequest(BaseModel):
    """XML规则解析请求模型"""
    xml_content: str


class XMLRuleParseResponse(BaseModel):
    """XML规则解析响应模型"""
    success: bool
    message: str
    rules: List[Dict[str, Any]] = []
    warnings: List[str] = []


@router.post("/validate", response_model=RuleValidationResponse)
async def validate_comparison_rule(request: RuleValidationRequest):
    """验证单个比对规则"""
    try:
        validator = RuleValidator()
        
        # 构建规则字典
        rule_dict = {
            "table_id": request.table_id,
            "remark": request.remark,
            "sql_1": request.sql_1,
            "sql_2": request.sql_2
        }
        
        # 执行验证
        is_valid, errors = validator.validate_single_rule(rule_dict)
        
        if is_valid:
            logger.info(f"规则验证通过: {request.table_id}")
            return RuleValidationResponse(
                valid=True,
                message="规则验证通过",
                warnings=[],
                errors=[]
            )
        else:
            logger.warning(f"规则验证失败: {request.table_id}, 错误: {errors}")
            return RuleValidationResponse(
                valid=False,
                message="规则验证失败",
                warnings=[],
                errors=errors
            )
            
    except Exception as e:
        logger.error(f"规则验证异常: {e}")
        raise HTTPException(status_code=500, detail=f"规则验证失败: {str(e)}")


@router.post("/parse-xml", response_model=XMLRuleParseResponse)
async def parse_xml_rules(request: XMLRuleParseRequest):
    """解析XML格式的比对规则"""
    try:
        parser = SQLRuleParser()
        
        # 解析XML规则
        rules = parser.parse_xml_rules(request.xml_content)
        
        # 验证规则一致性
        warnings = parser.validate_rule_consistency(rules)
        
        logger.info(f"XML规则解析成功，共解析 {len(rules)} 个规则")
        
        return XMLRuleParseResponse(
            success=True,
            message=f"成功解析 {len(rules)} 个比对规则",
            rules=rules,
            warnings=warnings
        )
        
    except ValueError as e:
        logger.error(f"XML规则解析失败: {e}")
        return XMLRuleParseResponse(
            success=False,
            message=f"XML解析失败: {str(e)}",
            rules=[],
            warnings=[]
        )
        
    except Exception as e:
        logger.error(f"XML规则解析异常: {e}")
        raise HTTPException(status_code=500, detail=f"XML规则解析失败: {str(e)}")


@router.get("/template/{table_id}")
async def get_rule_template(table_id: str, remark: str = ""):
    """获取规则模板"""
    try:
        parser = SQLRuleParser()
        template = parser.generate_rule_template(table_id, remark)
        
        return {
            "table_id": table_id,
            "template": template,
            "description": "XML格式的比对规则模板"
        }
        
    except Exception as e:
        logger.error(f"生成规则模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成规则模板失败: {str(e)}")


@router.get("/examples")
async def get_rule_examples():
    """获取规则示例"""
    examples = [
        {
            "name": "基础表内容比对",
            "description": "使用主键作为KEY的基础比对",
            "xml": """<rules>
<table table_id="users" remark="用户表">
    <sql_1>  
        SELECT USER_ID AS KEY, u.* FROM users u
    </sql_1>
    
    <sql_2>  
        SELECT USER_ID AS KEY, u.* FROM users u
    </sql_2>
</table>
</rules>"""
        },
        {
            "name": "复合键比对",
            "description": "使用多个字段组合作为KEY",
            "xml": """<rules>
<table table_id="orders" remark="订单表">
    <sql_1>  
        SELECT ORDER_ID||'-'||CUSTOMER_ID AS KEY, o.* FROM orders o
    </sql_1>
    
    <sql_2>  
        SELECT ORDER_ID||'-'||CUSTOMER_ID AS KEY, o.* FROM orders o
    </sql_2>
</table>
</rules>"""
        },
        {
            "name": "条件过滤比对",
            "description": "只比对满足特定条件的数据",
            "xml": """<rules>
<table table_id="products" remark="产品表">
    <sql_1>  
        SELECT PRODUCT_ID AS KEY, p.* FROM products p WHERE status = 'ACTIVE'
    </sql_1>
    
    <sql_2>  
        SELECT PRODUCT_ID AS KEY, p.* FROM products p WHERE status = 'ACTIVE'
    </sql_2>
</table>
</rules>"""
        },
        {
            "name": "多表比对",
            "description": "一次配置多个表的比对规则",
            "xml": """<rules>
<table table_id="users" remark="用户表">
    <sql_1>  
        SELECT USER_ID AS KEY, u.* FROM users u
    </sql_1>
    
    <sql_2>  
        SELECT USER_ID AS KEY, u.* FROM users u
    </sql_2>
</table>

<table table_id="orders" remark="订单表">
    <sql_1>  
        SELECT ORDER_ID AS KEY, o.* FROM orders o
    </sql_1>
    
    <sql_2>  
        SELECT ORDER_ID AS KEY, o.* FROM orders o
    </sql_2>
</table>
</rules>"""
        }
    ]
    
    return {
        "examples": examples,
        "total": len(examples)
    }


@router.get("/best-practices")
async def get_rule_best_practices():
    """获取规则编写最佳实践"""
    practices = [
        {
            "title": "KEY字段设计",
            "description": "确保KEY字段能唯一标识记录",
            "example": "SELECT USER_ID AS KEY, u.* FROM users u",
            "tips": [
                "使用主键或唯一键作为KEY",
                "复合键使用连接符组合",
                "避免使用可能为NULL的字段作为KEY"
            ]
        },
        {
            "title": "字段对齐",
            "description": "两个SQL的SELECT字段应保持一致",
            "example": "确保sql_1和sql_2选择相同的字段",
            "tips": [
                "字段名称要完全一致",
                "字段顺序建议保持一致",
                "数据类型要兼容"
            ]
        },
        {
            "title": "性能优化",
            "description": "添加适当的过滤条件和索引",
            "example": "WHERE created_date >= '2024-01-01'",
            "tips": [
                "使用WHERE条件减少数据量",
                "为比对字段创建索引",
                "避免使用SELECT *，明确指定字段"
            ]
        },
        {
            "title": "数据一致性",
            "description": "确保比对数据的一致性",
            "example": "ORDER BY created_date",
            "tips": [
                "使用ORDER BY确保结果顺序一致",
                "注意时区和字符编码问题",
                "处理NULL值的比对逻辑"
            ]
        }
    ]
    
    return {
        "best_practices": practices,
        "total": len(practices)
    }
