"""
SQL规则解析器
"""
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
import re
import logging


class SQLRuleParser:
    """SQL规则解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_xml_rules(self, xml_content: str) -> List[Dict[str, Any]]:
        """解析XML格式的比对规则"""
        try:
            root = ET.fromstring(xml_content)
            rules = []
            
            for table_element in root.findall('table'):
                rule = self._parse_table_rule(table_element)
                if rule:
                    rules.append(rule)
            
            self.logger.info(f"成功解析 {len(rules)} 个比对规则")
            return rules
            
        except ET.ParseError as e:
            self.logger.error(f"XML解析失败: {e}")
            raise ValueError(f"XML格式错误: {e}")
        except Exception as e:
            self.logger.error(f"规则解析失败: {e}")
            raise
    
    def _parse_table_rule(self, table_element: ET.Element) -> Optional[Dict[str, Any]]:
        """解析单个表的比对规则"""
        try:
            # 获取表属性
            table_id = table_element.get('table_id')
            remark = table_element.get('remark', '')
            
            if not table_id:
                self.logger.warning("表规则缺少table_id属性")
                return None
            
            # 获取SQL语句
            sql_1_element = table_element.find('sql_1')
            sql_2_element = table_element.find('sql_2')
            
            if sql_1_element is None or sql_2_element is None:
                self.logger.warning(f"表 {table_id} 缺少SQL语句")
                return None
            
            sql_1 = sql_1_element.text.strip() if sql_1_element.text else ""
            sql_2 = sql_2_element.text.strip() if sql_2_element.text else ""
            
            if not sql_1 or not sql_2:
                self.logger.warning(f"表 {table_id} 的SQL语句为空")
                return None
            
            # 验证SQL语句
            self._validate_sql(sql_1, f"{table_id}.sql_1")
            self._validate_sql(sql_2, f"{table_id}.sql_2")
            
            return {
                'table_id': table_id,
                'remark': remark,
                'sql_1': sql_1,
                'sql_2': sql_2,
                'parsed_at': self._get_current_timestamp()
            }
            
        except Exception as e:
            self.logger.error(f"解析表规则失败: {e}")
            return None
    
    def _validate_sql(self, sql: str, context: str):
        """验证SQL语句"""
        if not sql.strip():
            raise ValueError(f"{context}: SQL语句不能为空")
        
        # 检查是否为SELECT语句
        if not sql.strip().upper().startswith('SELECT'):
            raise ValueError(f"{context}: 必须是SELECT语句")
        
        # 检查是否包含KEY字段
        if 'KEY' not in sql.upper():
            self.logger.warning(f"{context}: SQL语句建议包含KEY字段用于数据匹配")
        
        # 基本SQL注入检查
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE']
        sql_upper = sql.upper()
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                raise ValueError(f"{context}: SQL语句包含危险关键字 {keyword}")
    
    def validate_rule_consistency(self, rules: List[Dict[str, Any]]) -> List[str]:
        """验证规则一致性"""
        warnings = []
        
        # 检查重复的table_id
        table_ids = [rule['table_id'] for rule in rules]
        duplicates = set([x for x in table_ids if table_ids.count(x) > 1])
        
        if duplicates:
            warnings.append(f"发现重复的table_id: {', '.join(duplicates)}")
        
        # 检查SQL语句结构一致性
        for rule in rules:
            try:
                sql_1_columns = self._extract_select_columns(rule['sql_1'])
                sql_2_columns = self._extract_select_columns(rule['sql_2'])
                
                if sql_1_columns != sql_2_columns:
                    warnings.append(
                        f"表 {rule['table_id']} 的两个SQL语句选择的字段不一致"
                    )
                    
            except Exception as e:
                warnings.append(
                    f"表 {rule['table_id']} 的SQL语句结构分析失败: {e}"
                )
        
        return warnings
    
    def _extract_select_columns(self, sql: str) -> List[str]:
        """提取SELECT语句中的字段列表"""
        try:
            # 简单的字段提取逻辑（可以根据需要改进）
            sql_upper = sql.upper()
            
            # 找到SELECT和FROM之间的内容
            select_start = sql_upper.find('SELECT') + 6
            from_start = sql_upper.find('FROM')
            
            if from_start == -1:
                raise ValueError("SQL语句缺少FROM子句")
            
            select_part = sql[select_start:from_start].strip()
            
            # 处理SELECT *的情况
            if select_part.strip() == '*':
                return ['*']
            
            # 分割字段
            columns = []
            for column in select_part.split(','):
                column = column.strip()
                
                # 处理别名 (AS关键字)
                if ' AS ' in column.upper():
                    column = column.split(' AS ')[-1].strip()
                elif ' ' in column and not any(func in column.upper() for func in ['(', ')', 'CASE', 'WHEN']):
                    # 处理没有AS的别名
                    parts = column.split()
                    if len(parts) > 1:
                        column = parts[-1]
                
                columns.append(column)
            
            return columns
            
        except Exception as e:
            self.logger.error(f"提取SQL字段失败: {e}")
            raise
    
    def generate_rule_template(self, table_id: str, remark: str = "") -> str:
        """生成规则模板"""
        template = f"""<rules>
<table table_id="{table_id}" remark="{remark}">
    <sql_1>  
        SELECT KEY, * FROM your_table_1
    </sql_1>
    
    <sql_2>  
        SELECT KEY, * FROM your_table_2
    </sql_2>
</table>
</rules>"""
        
        return template
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


class RuleValidator:
    """规则验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_rule_file(self, file_path: str) -> Tuple[bool, List[str]]:
        """验证规则文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()
            
            parser = SQLRuleParser()
            rules = parser.parse_xml_rules(xml_content)
            
            # 验证规则一致性
            warnings = parser.validate_rule_consistency(rules)
            
            return True, warnings
            
        except Exception as e:
            return False, [str(e)]
    
    def validate_single_rule(self, rule_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证单个规则"""
        errors = []
        
        # 检查必需字段
        required_fields = ['table_id', 'sql_1', 'sql_2']
        for field in required_fields:
            if field not in rule_dict or not rule_dict[field]:
                errors.append(f"缺少必需字段: {field}")
        
        if errors:
            return False, errors
        
        # 验证SQL语句
        try:
            parser = SQLRuleParser()
            parser._validate_sql(rule_dict['sql_1'], 'sql_1')
            parser._validate_sql(rule_dict['sql_2'], 'sql_2')
        except ValueError as e:
            errors.append(str(e))
        
        return len(errors) == 0, errors
