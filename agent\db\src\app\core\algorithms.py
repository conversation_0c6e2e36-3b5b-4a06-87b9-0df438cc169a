"""
数据比对算法模块

提供多种数据比对算法的实现，支持不同场景下的数据比对需求。
包含算法抽象基类和算法工厂，使得系统可以灵活切换不同的比对策略。
"""
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Tuple, Optional, AsyncIterator, Union, Set
import hashlib
import difflib
import time

from app.models.task import CompareConfig, TaskStats
from app.core.config import settings
from app.models.logger import logger

class ComparisonAlgorithmType(Enum):
    """比对算法类型枚举"""
    PRIMARY_KEY = "primary_key"  # 默认主键比对
    FULL_ROW = "full_row"                        # 全字段比对
    HASH_BASED = "hash_based"                    # 基于哈希比对
    TWO_PHASE = "two_phase"                      # 两阶段比对
    INCREMENTAL = "incremental"                  # 增量比对


class AbstractComparisonAlgorithm(ABC):
    """比对算法抽象基类"""

    @abstractmethod
    async def compare(self, source_data, target_data, config):
        """执行比对操作"""
        pass

    @abstractmethod
    async def get_query_statements(self, config, is_source=True):
        """获取查询语句，确保结果有序"""
        pass

    @abstractmethod
    async def handle_large_dataset(self, engine, config, is_source=True):
        """处理海量数据集"""
        pass


class DefaultPrimaryKeyAlgorithm(AbstractComparisonAlgorithm):
    """
    默认主键比对算法

    特点:
    1. 以SELECT的第一个字段作为主键(可以是多字段复合)
    2. 主键相同的记录视为匹配项进行比对
    3. 主键不同的记录视为缺失
    4. 支持海量数据的有序比对
    """

    async def compare(self, source_data, target_data, config):
        """比对两个数据集"""
        # 将数据按主键索引
        source_indexed = {self._extract_key(record, config): record for record in source_data}
        target_indexed = {self._extract_key(record, config): record for record in target_data}

        results = {
            "matched": [],      # 匹配记录
            "diff": [],         # 有差异记录
            "source_only": [],  # 仅源存在记录
            "target_only": []   # 仅目标存在记录
        }

        # 1. 检查源数据中的记录
        for key, source_record in source_indexed.items():
            if key in target_indexed:
                # 比对字段值
                target_record = target_indexed[key]
                field_diffs = self._compare_fields(source_record, target_record, config)

                if field_diffs:
                    # 存在差异
                    results["diff"].append({
                        "key": key,
                        "source": source_record,
                        "target": target_record,
                        "diffs": field_diffs
                    })
                else:
                    # 完全匹配
                    results["matched"].append({
                        "key": key,
                        "record": source_record
                    })
            else:
                # 源特有
                results["source_only"].append({
                    "key": key,
                    "record": source_record
                })

        # 2. 检查目标特有的记录
        for key in target_indexed:
            if key not in source_indexed:
                results["target_only"].append({
                    "key": key,
                    "record": target_indexed[key]
                })

        return results

    async def get_query_statements(self, config, is_source=True):
        """生成有序查询语句"""
        # 确定表名或基础查询
        if config.mode == "table":
            table_name = config.source_table if is_source else config.target_table
            where_clause = config.where_clause or "1=1"

            # 确保查询添加ORDER BY子句
            primary_keys = config.primary_keys or ["id"]  # 默认使用id字段
            order_clause = ", ".join(primary_keys)

            query = f"""
            SELECT * FROM {table_name}
            WHERE {where_clause}
            ORDER BY {order_clause}
            """

            if config.limit:
                query += f" LIMIT {config.limit}"

            return query
        else:
            # 使用自定义查询，直接使用原始SQL
            base_query = config.source_query if is_source else config.target_query
            # 移除查询末尾的分号，避免DB2语法错误
            base_query = base_query.strip().rstrip(';')

            if config.limit:
                base_query += f" LIMIT {config.limit}"

            return base_query

    async def handle_large_dataset(self, engine, config, is_source=True):
        """处理海量数据集，返回分批查询的生成器"""
        batch_size = config.batch_size or 10000
        primary_keys = config.primary_keys or ["id"]
        last_key_values = None

        while True:
            # 构建查询条件
            condition = ""
            params = []

            if last_key_values:
                # 构建条件，确保结果有序且不重叠
                conditions = []
                for i, pk in enumerate(primary_keys):
                    if i == len(primary_keys) - 1:
                        # 对最后一个主键使用大于
                        conditions.append(f"{pk} > ?")
                        params.append(last_key_values[i])
                    else:
                        # 对前面的主键使用大于等于
                        conditions.append(f"{pk} >= ?")
                        params.append(last_key_values[i])

                condition = " AND ".join(conditions)

            # 构建分批查询
            query = await self.get_query_statements(config, is_source)
            if condition:
                if "WHERE" in query.upper():
                    query = query.replace("WHERE", f"WHERE {condition} AND ")
                else:
                    query += f" WHERE {condition}"

            # 添加批量限制
            query += f" LIMIT {batch_size}"

            # 执行查询
            records = []
            async with engine.acquire() as conn:
                result = await conn.fetch(query, *params)
                if not result:
                    break

                for row in result:
                    record = dict(row)
                    records.append(record)

            # 如果没有获取到记录，结束
            if not records:
                break

            # 更新last_key_values为最后一条记录的主键值
            last_record = records[-1]
            last_key_values = [last_record.get(pk) for pk in primary_keys]

            # 返回当前批次
            yield records

    def _extract_key(self, record, config):
        """从记录中提取主键"""
        if not record:
            logger.warning("记录为空，无法提取主键")
            return (None,)

        if not config.primary_keys:
            # 默认使用第一个字段作为主键
            if not record:
                logger.warning("记录为空且未配置主键")
                return (None,)
            first_key = next(iter(record))
            logger.debug(f"未配置主键，使用第一个字段作为主键: {first_key}")
            return (record[first_key],)

        # 使用配置的主键字段
        key_values = []
        missing_keys = []
        found_keys = []

        for key in config.primary_keys:
            # 检查键是否存在于记录中
            if key in record:
                key_values.append(record[key])
                found_keys.append(key)
            else:
                # 如果键不存在，尝试使用不区分大小写的匹配
                key_lower = key.lower()
                found = False
                for record_key in record:
                    if record_key.lower() == key_lower:
                        key_values.append(record[record_key])
                        found_keys.append(record_key)
                        found = True
                        break

                # 如果仍然找不到，记录缺失的键
                if not found:
                    missing_keys.append(key)
                    key_values.append(None)

        # 如果有缺失的主键字段，尝试智能推断
        if missing_keys:
            # 检查是否是默认的"id"主键配置但数据中没有id字段
            if config.primary_keys == ["id"] and "id" not in record:
                # 尝试智能推断主键字段
                record_keys = list(record.keys())
                potential_keys = []

                # 查找可能的主键字段（常见的主键字段名）
                key_patterns = ['KEY', 'key', 'ID', 'id', 'PK', 'pk', 'PRIMARY_KEY', 'primary_key']
                for pattern in key_patterns:
                    for record_key in record_keys:
                        if pattern.lower() in record_key.lower():
                            potential_keys.append(record_key)
                            break

                # 如果找到潜在的主键字段，使用第一个
                if potential_keys:
                    inferred_key = potential_keys[0]
                    # 只在第一次推断时记录日志，避免重复日志
                    if not hasattr(config, '_key_inferred_logged'):
                        logger.info(f"智能推断主键字段: {inferred_key} (原配置: {config.primary_keys})")
                        config._key_inferred_logged = True
                    return (record[inferred_key],)
                else:
                    # 如果没有找到明显的主键字段，使用第一个字段
                    first_key = record_keys[0] if record_keys else None
                    if first_key:
                        logger.info(f"未找到明显主键字段，使用第一个字段作为主键: {first_key} (原配置: {config.primary_keys})")
                        return (record[first_key],)

            logger.warning(f"记录中缺失主键字段: {missing_keys}, 记录字段: {list(record.keys())}")

        # 如果所有主键值都为None，尝试使用第一个字段作为主键
        if all(v is None for v in key_values):
            record_keys = list(record.keys())
            if record_keys:
                first_key = record_keys[0]
                logger.warning(f"所有配置的主键值都为None，使用第一个字段作为主键: {first_key}")
                logger.warning(f"配置的主键: {config.primary_keys}, 记录字段: {record_keys}")
                return (record[first_key],)
            else:
                logger.error(f"记录为空，无法提取主键。配置的主键: {config.primary_keys}")
                return (None,)

        return tuple(key_values)

    def _extract_primary_keys_from_query(self, query):
        """
        从查询中提取主键字段

        尝试从SQL查询中识别主键字段，支持以下模式：
        1. SELECT field AS KEY - 将field识别为主键
        2. SELECT field AS key - 将field识别为主键（不区分大小写）
        3. 如果没有AS KEY/key模式，则尝试提取SELECT子句的第一个字段
        4. 如果以上都失败，则返回None

        Args:
            query: SQL查询语句

        Returns:
            主键字段列表或None
        """
        if not query:
            return None

        # 查找包含"as key"的字段表达式（不区分大小写）
        import re

        # 改进正则表达式，支持更多AS KEY模式
        key_regex_patterns = [
            # 标准模式: SELECT field AS KEY
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*[,)]',
            # 末尾模式: SELECT field AS KEY FROM
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s+from',
            # 单字段模式: SELECT field AS KEY
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*$'
        ]

        # 尝试所有模式
        for pattern in key_regex_patterns:
            key_regex = re.compile(pattern, re.IGNORECASE | re.DOTALL)
            matches = key_regex.search(query)

            if matches and matches.group(1):
                # 提取主键表达式
                key_expression = matches.group(1).strip()

                # 检查是否有连接操作符(||)
                if '||' in key_expression:
                    # 提取组合主键中的各个字段
                    key_parts = [part.strip() for part in key_expression.split('||')]
                    # 移除引号和空格，保留字段名
                    key_parts = [part.strip("'\"") for part in key_parts if not part.strip("'\"") == '-']
                    return key_parts

                # 返回单一主键
                return [key_expression]

        # 如果没有找到AS KEY模式，尝试提取SELECT子句的所有字段
        try:
            # 提取SELECT和FROM之间的部分
            select_clause_match = re.search(r'select\s+(.*?)\s+from', query, re.IGNORECASE | re.DOTALL)
            if select_clause_match:
                select_clause = select_clause_match.group(1).strip()

                # 如果有DISTINCT，移除它
                select_clause = re.sub(r'^\s*distinct\s+', '', select_clause, flags=re.IGNORECASE)

                # 分割字段列表
                fields = [f.strip() for f in select_clause.split(',')]

                # 提取第一个字段，处理可能的别名
                first_field = fields[0]

                # 如果字段有AS别名，提取实际字段名
                as_match = re.search(r'(.*?)\s+as\s+', first_field, re.IGNORECASE)
                if as_match:
                    first_field = as_match.group(1).strip()

                # 如果字段名是表达式或函数调用，可能需要使用别名
                if '(' in first_field or ')' in first_field:
                    # 尝试找到别名
                    alias_match = re.search(r'as\s+(\w+)', first_field, re.IGNORECASE)
                    if alias_match:
                        return [alias_match.group(1).strip()]

                return [first_field]
        except Exception as e:
            # 如果解析失败，记录错误并继续
            import logging
            logging.error(f"从查询中提取主键字段失败: {str(e)}")

        # 如果以上都失败，尝试提取SELECT子句的第一个单词
        select_regex = re.compile(r'select\s+(\w+)', re.IGNORECASE)
        select_match = select_regex.search(query)

        if select_match and select_match.group(1):
            first_field = select_match.group(1).strip()
            if first_field and first_field.lower() not in ('distinct', 'all'):
                return [first_field]

        # 如果无法提取主键，返回默认值
        return ["KEY"]

    def _compare_fields(self, source_record, target_record, config):
        """比对记录字段值"""
        field_diffs = []

        # 确定比对字段
        fields_to_compare = config.compare_columns or []
        if not fields_to_compare:
            # 如果未指定，比对所有非主键字段
            all_fields = set(source_record.keys()) | set(target_record.keys())
            primary_keys = set(config.primary_keys or [])
            ignore_fields = set(config.ignore_columns or [])

            # 特殊处理：如果主键是通过AS KEY语法生成的，不应该排除KEY字段的比较
            # 因为KEY字段是组合主键，而实际的业务字段仍需要比较
            if primary_keys == {"KEY"} or primary_keys == {"key"}:
                # 当主键是KEY时，比较除KEY字段外的所有字段
                fields_to_compare = list(all_fields - {"KEY", "key"} - ignore_fields)
            else:
                # 正常情况下排除主键字段
                fields_to_compare = list(all_fields - primary_keys - ignore_fields)

        # 比对每个字段
        for field in fields_to_compare:
            source_value = source_record.get(field)
            target_value = target_record.get(field)

            # 简单的值比较，实际应用中可能需要更复杂的比较逻辑
            if source_value != target_value:
                field_diffs.append({
                    "field": field,
                    "source_value": source_value,
                    "target_value": target_value
                })

        return field_diffs


class HashBasedAlgorithm(AbstractComparisonAlgorithm):
    """
    基于哈希的比对算法

    特点:
    1. 对记录计算哈希值加速比对
    2. 支持大规模数据集的高效比对
    3. 先比较哈希值，再比对内容不同的记录
    """

    async def compare(self, source_data, target_data, config):
        """使用哈希值比对两个数据集"""
        results = {
            "matched": [],      # 匹配记录
            "diff": [],         # 有差异记录
            "source_only": [],  # 仅源存在记录
            "target_only": []   # 仅目标存在记录
        }

        # 1. 计算源数据记录的哈希值
        source_hashes = {}
        source_keys = {}
        for record in source_data:
            key = self._extract_key(record, config)
            record_hash = self._calculate_record_hash(record, config)
            source_hashes[key] = record_hash
            source_keys[key] = record

        # 2. 计算目标数据记录的哈希值，并在计算过程中进行比对
        target_hashes = {}
        target_keys = {}
        for record in target_data:
            key = self._extract_key(record, config)
            record_hash = self._calculate_record_hash(record, config)
            target_hashes[key] = record_hash
            target_keys[key] = record

        # 3. 比较哈希值，找出差异记录
        for key, hash_value in source_hashes.items():
            if key in target_hashes:
                if hash_value == target_hashes[key]:
                    # 哈希相同，视为记录完全匹配
                    results["matched"].append({
                        "key": key,
                        "record": source_keys[key]
                    })
                else:
                    # 哈希不同，需要详细比对字段差异
                    source_record = source_keys[key]
                    target_record = target_keys[key]
                    field_diffs = self._compare_fields(source_record, target_record, config)

                    results["diff"].append({
                        "key": key,
                        "source": source_record,
                        "target": target_record,
                        "diffs": field_diffs
                    })
            else:
                # 源特有记录
                results["source_only"].append({
                    "key": key,
                    "record": source_keys[key]
                })

        # 4. 查找目标特有的记录
        for key in target_hashes:
            if key not in source_hashes:
                results["target_only"].append({
                    "key": key,
                    "record": target_keys[key]
                })

        return results

    async def get_query_statements(self, config, is_source=True):
        """生成有序查询语句与DefaultPrimaryKeyAlgorithm相同"""
        # 复用DefaultPrimaryKeyAlgorithm的实现
        alg = DefaultPrimaryKeyAlgorithm()
        return await alg.get_query_statements(config, is_source)

    async def handle_large_dataset(self, engine, config, is_source=True):
        """处理海量数据集与DefaultPrimaryKeyAlgorithm相同"""
        # 复用DefaultPrimaryKeyAlgorithm的实现
        alg = DefaultPrimaryKeyAlgorithm()
        async for batch in alg.handle_large_dataset(engine, config, is_source):
            yield batch

    def _calculate_record_hash(self, record, config):
        """计算记录的哈希值"""
        # 确定要参与哈希计算的字段
        fields_to_hash = config.compare_columns or []
        if not fields_to_hash:
            # 如果未指定，使用所有非主键字段
            all_fields = set(record.keys())
            primary_keys = set(config.primary_keys or [])
            ignore_fields = set(config.ignore_columns or [])
            fields_to_hash = list(all_fields - primary_keys - ignore_fields)
            # 排序以确保哈希值一致性
            fields_to_hash.sort()

        # 构建哈希数据
        hash_data = []
        for field in fields_to_hash:
            value = record.get(field)
            # 将各种类型转换为字符串
            if value is not None:
                hash_data.append(f"{field}:{str(value)}")
            else:
                hash_data.append(f"{field}:None")

        # 计算哈希值
        hash_str = ";".join(hash_data)
        return hashlib.md5(hash_str.encode('utf-8')).hexdigest()

    def _extract_key(self, record, config):
        """从记录中提取主键，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        return alg._extract_key(record, config)

    def _compare_fields(self, source_record, target_record, config):
        """比对记录字段值，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        return alg._compare_fields(source_record, target_record, config)


class TwoPhaseAlgorithm(AbstractComparisonAlgorithm):
    """
    两阶段比对算法

    特点:
    1. 第一阶段快速确定存在于两端的记录
    2. 第二阶段进行详细字段比对
    3. 适合源和目标数据差异较大的场景
    """

    async def compare(self, source_data, target_data, config):
        """使用两阶段策略比对数据集"""
        results = {
            "matched": [],      # 匹配记录
            "diff": [],         # 有差异记录
            "source_only": [],  # 仅源存在记录
            "target_only": []   # 仅目标存在记录
        }

        # 第一阶段：确定记录存在性
        source_keys = {self._extract_key(record, config): record for record in source_data}
        target_keys = {self._extract_key(record, config): record for record in target_data}

        # 查找交集、源特有和目标特有的记录
        common_keys = set(source_keys.keys()) & set(target_keys.keys())
        source_only_keys = set(source_keys.keys()) - common_keys
        target_only_keys = set(target_keys.keys()) - common_keys

        # 处理源特有记录
        for key in source_only_keys:
            results["source_only"].append({
                "key": key,
                "record": source_keys[key]
            })

        # 处理目标特有记录
        for key in target_only_keys:
            results["target_only"].append({
                "key": key,
                "record": target_keys[key]
            })

        # 第二阶段：对共同存在的记录进行详细比对
        for key in common_keys:
            source_record = source_keys[key]
            target_record = target_keys[key]

            # 比较字段值
            field_diffs = self._compare_fields(source_record, target_record, config)

            if field_diffs:
                # 存在差异
                results["diff"].append({
                    "key": key,
                    "source": source_record,
                    "target": target_record,
                    "diffs": field_diffs
                })
            else:
                # 完全匹配
                results["matched"].append({
                    "key": key,
                    "record": source_record
                })

        return results

    async def get_query_statements(self, config, is_source=True):
        """生成查询语句，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        return await alg.get_query_statements(config, is_source)

    async def handle_large_dataset(self, engine, config, is_source=True):
        """处理海量数据集，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        async for batch in alg.handle_large_dataset(engine, config, is_source):
            yield batch

    def _extract_key(self, record, config):
        """从记录中提取主键，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        return alg._extract_key(record, config)

    def _compare_fields(self, source_record, target_record, config):
        """比对记录字段值，与DefaultPrimaryKeyAlgorithm相同"""
        alg = DefaultPrimaryKeyAlgorithm()
        return alg._compare_fields(source_record, target_record, config)


class ComparisonAlgorithmFactory:
    """比对算法工厂类"""

    _algorithms = {}

    @classmethod
    def register(cls, algorithm_type, algorithm_class):
        """注册算法"""
        cls._algorithms[algorithm_type] = algorithm_class

    @classmethod
    def get_algorithm(cls, algorithm_type=None):
        """获取算法实例"""
        if algorithm_type is None:
            algorithm_type = ComparisonAlgorithmType.PRIMARY_KEY

        if isinstance(algorithm_type, str):
            algorithm_type = ComparisonAlgorithmType(algorithm_type)

        algorithm_class = cls._algorithms.get(algorithm_type)
        if not algorithm_class:
            raise ValueError(f"不支持的算法类型: {algorithm_type}")

        return algorithm_class()


# 注册所有算法
ComparisonAlgorithmFactory.register(
    ComparisonAlgorithmType.PRIMARY_KEY,
    DefaultPrimaryKeyAlgorithm
)
ComparisonAlgorithmFactory.register(
    ComparisonAlgorithmType.HASH_BASED,
    HashBasedAlgorithm
)
ComparisonAlgorithmFactory.register(
    ComparisonAlgorithmType.TWO_PHASE,
    TwoPhaseAlgorithm
)