"""
数据库引擎工厂模块

提供创建数据库引擎实例的工厂函数。
"""

import logging
from typing import Optional

from app.models.database import DatabaseEngine
from app.models.database_engines import (
    PostgreSQLEngine, MySQLEngine, OracleEngine,
    SQLServerEngine, DB2Engine
)
from app.models.db import DatabaseConnection, DatabaseType

logger = logging.getLogger(__name__)


async def create_database_engine(connection: DatabaseConnection) -> Optional[DatabaseEngine]:
    """
    创建数据库引擎实例

    根据数据库连接配置创建对应类型的数据库引擎实例。

    Args:
        connection: 数据库连接配置

    Returns:
        数据库引擎实例，如果不支持该类型则返回None
    """
    try:
        # 根据数据库类型创建对应的引擎
        if connection.type == DatabaseType.POSTGRESQL:
            engine = PostgreSQLEngine(connection)
        elif connection.type == DatabaseType.MYSQL:
            engine = MySQLEngine(connection)
        elif connection.type == DatabaseType.ORACLE:
            engine = OracleEngine(connection)
        elif connection.type == DatabaseType.SQLSERVER:
            engine = SQLServerEngine(connection)
        elif connection.type == DatabaseType.DB2:
            engine = DB2Engine(connection)
        elif connection.type == DatabaseType.GAUSSDB:
            # GaussDB兼容PostgreSQL，使用PostgreSQL引擎
            engine = PostgreSQLEngine(connection)
        else:
            logger.warning(f"不支持的数据库类型: {connection.type}")
            return None

        # 连接数据库
        success = await engine.connect()
        if not success:
            logger.error(f"数据库连接失败: {connection.host}:{connection.port}/{connection.database}")
            return None

        logger.info(f"数据库连接成功: {connection.host}:{connection.port}/{connection.database}")
        return engine
    except Exception as e:
        logger.error(f"创建数据库引擎失败: {str(e)}")
        return None
