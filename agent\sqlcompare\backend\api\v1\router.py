#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API v1路由汇总
"""

from fastapi import APIRouter

from .endpoints import tasks, configs, results, auth, monitoring, health

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

api_router.include_router(
    tasks.router,
    prefix="/tasks",
    tags=["任务管理"]
)

api_router.include_router(
    configs.router,
    prefix="/configs",
    tags=["配置管理"]
)

api_router.include_router(
    results.router,
    prefix="/results",
    tags=["结果查询"]
)

api_router.include_router(
    monitoring.router,
    prefix="/monitoring",
    tags=["监控"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["健康检查"]
)
