"""
Agent端结果同步服务
负责将比对结果同步到Server数据库
"""

import asyncio
import json
import logging
import aiohttp
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class ResultSyncService:
    """结果同步服务"""
    
    def __init__(self, server_url: str = None):
        self.server_url = server_url or os.getenv('SERVER_URL', 'http://localhost:8000')
        self.sync_endpoint = f"{self.server_url}/api/comparison/sync"
        self.status_endpoint = f"{self.server_url}/api/comparison/{{task_id}}/status"
        self.timeout = 30  # 30秒超时
        
    async def sync_results_to_server(self, task_id: str, results_dir: Path) -> bool:
        """
        将Agent的文件结果同步到Server数据库
        
        Args:
            task_id: 任务ID
            results_dir: 结果文件目录
            
        Returns:
            bool: 同步是否成功
        """
        try:
            logger.info(f"开始同步任务 {task_id} 的结果到Server")
            
            # 读取Agent的文件结果
            file_results = await self._read_file_results(results_dir)
            
            if not file_results:
                logger.warning(f"任务 {task_id} 没有找到结果文件")
                return False
            
            # 转换为标准格式
            standardized_results = self._standardize_results(file_results)
            
            # 发送到Server
            success = await self._send_to_server(task_id, standardized_results)
            
            if success:
                logger.info(f"任务 {task_id} 结果同步成功")
                # 可选：清理本地文件
                # await self._cleanup_local_files(results_dir)
            else:
                logger.error(f"任务 {task_id} 结果同步失败")
            
            return success
            
        except Exception as e:
            logger.error(f"同步任务 {task_id} 结果时发生异常: {str(e)}")
            return False
    
    async def _read_file_results(self, results_dir: Path) -> Optional[Dict[str, Any]]:
        """读取Agent的文件结果"""
        try:
            results = {}
            
            # 读取摘要文件
            summary_file = results_dir / "summary.json"
            if summary_file.exists():
                with open(summary_file, 'r', encoding='utf-8') as f:
                    results['summary'] = json.load(f)
            
            # 读取元数据文件
            metadata_file = results_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    results['metadata'] = json.load(f)
            
            # 读取差异记录
            differences_file = results_dir / "differences.jsonl"
            if differences_file.exists():
                differences = []
                with open(differences_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            differences.append(json.loads(line))
                results['differences'] = differences
            
            # 读取源独有记录
            source_only_file = results_dir / "source_only.jsonl"
            if source_only_file.exists():
                source_only = []
                with open(source_only_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            source_only.append(json.loads(line))
                results['source_only'] = source_only
            
            # 读取目标独有记录
            target_only_file = results_dir / "target_only.jsonl"
            if target_only_file.exists():
                target_only = []
                with open(target_only_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            target_only.append(json.loads(line))
                results['target_only'] = target_only
            
            return results if results else None
            
        except Exception as e:
            logger.error(f"读取结果文件失败: {str(e)}")
            return None
    
    def _standardize_results(self, file_results: Dict[str, Any]) -> Dict[str, Any]:
        """标准化结果格式"""
        try:
            # 标准化摘要数据
            summary = file_results.get('summary', {})
            standardized_summary = {
                'total_records': summary.get('total_count', 0),
                'matched_records': summary.get('matched_count', 0),
                'diff_records': summary.get('diff_count', 0),
                'source_only_records': summary.get('source_only_count', 0),
                'target_only_records': summary.get('target_only_count', 0),
                'execution_time': summary.get('execution_time', 0),
                'algorithm': summary.get('algorithm', ''),
            }
            
            # 标准化元数据
            metadata = file_results.get('metadata', {})
            standardized_metadata = {
                'source_type': metadata.get('source_type', ''),
                'target_type': metadata.get('target_type', ''),
                'algorithm': metadata.get('algorithm', ''),
                'batch_size': metadata.get('batch_size', 1000),
                'agent_version': metadata.get('agent_version', '1.0'),
                'schema_version': metadata.get('schema_version', '1.0'),
            }
            
            # 标准化差异记录
            differences = []
            for diff in file_results.get('differences', []):
                standardized_diff = {
                    'id': diff.get('id', ''),
                    'source_data': diff.get('source_data', {}),
                    'target_data': diff.get('target_data', {}),
                    'diff_fields': diff.get('diff_fields', []),
                    'diff_type': diff.get('diff_type', 'value_diff'),
                }
                differences.append(standardized_diff)
            
            # 标准化源独有记录
            source_only = []
            for record in file_results.get('source_only', []):
                standardized_record = {
                    'id': record.get('id', ''),
                    'data': record.get('data', {}),
                    'reason': record.get('reason', ''),
                }
                source_only.append(standardized_record)
            
            # 标准化目标独有记录
            target_only = []
            for record in file_results.get('target_only', []):
                standardized_record = {
                    'id': record.get('id', ''),
                    'data': record.get('data', {}),
                    'reason': record.get('reason', ''),
                }
                target_only.append(standardized_record)
            
            return {
                'summary': standardized_summary,
                'metadata': standardized_metadata,
                'differences': differences,
                'source_only': source_only,
                'target_only': target_only,
            }
            
        except Exception as e:
            logger.error(f"标准化结果格式失败: {str(e)}")
            return {}
    
    async def _send_to_server(self, task_id: str, results_data: Dict[str, Any]) -> bool:
        """发送结果到Server"""
        try:
            payload = {
                'task_id': task_id,
                'results': results_data,
                'timestamp': datetime.now().isoformat(),
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    self.sync_endpoint,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('success', False)
                    else:
                        logger.error(f"Server返回错误状态: {response.status}")
                        return False
                        
        except asyncio.TimeoutError:
            logger.error(f"同步请求超时: {task_id}")
            return False
        except Exception as e:
            logger.error(f"发送结果到Server失败: {str(e)}")
            return False
    
    async def update_task_status(self, task_id: str, status: str, progress: int = None, error_message: str = None) -> bool:
        """更新任务状态到Server"""
        try:
            payload = {
                'status': status,
                'progress': progress,
                'error_message': error_message,
                'timestamp': datetime.now().isoformat(),
            }
            
            # 移除None值
            payload = {k: v for k, v in payload.items() if v is not None}
            
            status_url = self.status_endpoint.format(task_id=task_id)
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.patch(
                    status_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            return False
    
    async def _cleanup_local_files(self, results_dir: Path):
        """清理本地文件（可选）"""
        try:
            if results_dir.exists():
                import shutil
                shutil.rmtree(results_dir)
                logger.info(f"已清理本地结果文件: {results_dir}")
        except Exception as e:
            logger.warning(f"清理本地文件失败: {str(e)}")


# 创建全局实例
result_sync_service = ResultSyncService()
