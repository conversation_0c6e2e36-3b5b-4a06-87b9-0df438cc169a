"""
数据库连接器基础类和工厂类
使用统一的数据库引擎工厂，避免SQLAlchemy代码重复

支持的数据库类型：
- PostgreSQL
- MySQL
- Oracle
- IBM DB2
- GaussDB

注意：本模块专门处理业务数据库连接（被比对的数据库），
与系统数据库（backend/core/database.py）职责分离：
- 本模块：业务数据库，原生SQL，短连接，数据查询
- database.py：系统数据库，ORM操作，长连接，元数据存储
"""
import os
import sys
import logging
from urllib.parse import quote_plus
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(SCRIPT_DIR)))
sys.path.insert(0, PROJECT_ROOT)
from abc import ABC, abstractmethod
from typing import Dict, List, Any
from sqlalchemy import text

from backend.core.config import DatabaseConfig
from .sqlalchemy_enginer import DatabaseEngineFactory
from .db2_driver_manager import db2_driver_manager, DB2DriverStatus


class BaseDatabaseConnector(ABC):
    """数据库连接器基础类"""

    def __init__(self):
        self.engine = None
        self.session = None
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    async def connect(self, connection_info):
        """建立数据库连接"""
        pass

    @abstractmethod
    async def disconnect(self):
        """断开数据库连接"""
        pass

    @abstractmethod
    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        pass

    @abstractmethod
    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行查询语句"""
        pass

    def _create_engine(self, connection_string: str):
        """
        创建业务数据库引擎
        使用统一的引擎工厂，针对业务数据库查询优化
        """
        try:
            # 使用统一的引擎工厂创建业务数据库引擎
            self.engine = DatabaseEngineFactory.create_business_engine(
                database_url=connection_string,
                pool_size=5,
                max_overflow=10,
                debug=False
            )

            # 使用工厂创建会话工厂
            SessionLocal = DatabaseEngineFactory.create_session_factory(
                engine=self.engine,
                autocommit=False,
                autoflush=False
            )
            self.session = SessionLocal()

            self.logger.info("业务数据库连接创建成功（使用统一引擎工厂）")

        except Exception as e:
            self.logger.error(f"创建业务数据库连接失败: {e}")
            raise

    def _execute_sql(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL语句"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(sql))

                # 转换为字典列表
                columns = list(result.keys()) if hasattr(result, '_metadata') else result.keys()
                rows = []
                for row in result:
                    row_dict = {}
                    for i, value in enumerate(row):
                        row_dict[columns[i]] = value
                    rows.append(row_dict)

                return rows

        except Exception as e:
            self.logger.error(f"执行SQL失败: {sql}, 错误: {e}")
            raise


class PostgreSQLConnector(BaseDatabaseConnector):
    """PostgreSQL连接器"""

    async def connect(self, connection_info):
        """建立PostgreSQL连接"""
        connection_string = DatabaseConfig.get_connection_string(
            "postgresql",
            connection_info.host,
            connection_info.port,
            connection_info.database,
            connection_info.username,
            connection_info.password
        )
        self._create_engine(connection_string)

    async def disconnect(self):
        """断开连接"""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()

    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取PostgreSQL表结构"""
        sql = f"""
        SELECT
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
        FROM information_schema.columns
        WHERE table_name = '{table_name}'
        ORDER BY ordinal_position
        """

        return self._execute_sql(sql)

    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行查询"""
        # 添加LIMIT限制
        if "LIMIT" not in sql.upper():
            sql = f"{sql} LIMIT {batch_size}"

        return self._execute_sql(sql)


class MySQLConnector(BaseDatabaseConnector):
    """MySQL连接器"""

    async def connect(self, connection_info):
        """建立MySQL连接"""
        connection_string = DatabaseConfig.get_connection_string(
            "mysql",
            connection_info.host,
            connection_info.port,
            connection_info.database,
            connection_info.username,
            connection_info.password
        )
        self._create_engine(connection_string)

    async def disconnect(self):
        """断开连接"""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()

    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取MySQL表结构"""
        sql = f"""
        SELECT
            COLUMN_NAME as column_name,
            DATA_TYPE as data_type,
            IS_NULLABLE as is_nullable,
            COLUMN_DEFAULT as column_default,
            CHARACTER_MAXIMUM_LENGTH as character_maximum_length,
            NUMERIC_PRECISION as numeric_precision,
            NUMERIC_SCALE as numeric_scale
        FROM information_schema.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """

        return self._execute_sql(sql)

    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行查询"""
        if "LIMIT" not in sql.upper():
            sql = f"{sql} LIMIT {batch_size}"

        return self._execute_sql(sql)


class OracleConnector(BaseDatabaseConnector):
    """Oracle连接器"""

    async def connect(self, connection_info):
        """建立Oracle连接"""
        connection_string = DatabaseConfig.get_connection_string(
            "oracle",
            connection_info.host,
            connection_info.port,
            connection_info.database,
            connection_info.username,
            connection_info.password
        )
        self._create_engine(connection_string)

    async def disconnect(self):
        """断开连接"""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()

    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取Oracle表结构"""
        sql = f"""
        SELECT
            COLUMN_NAME as column_name,
            DATA_TYPE as data_type,
            NULLABLE as is_nullable,
            DATA_DEFAULT as column_default,
            DATA_LENGTH as character_maximum_length,
            DATA_PRECISION as numeric_precision,
            DATA_SCALE as numeric_scale
        FROM USER_TAB_COLUMNS
        WHERE TABLE_NAME = UPPER('{table_name}')
        ORDER BY COLUMN_ID
        """

        return self._execute_sql(sql)

    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行查询"""
        if "ROWNUM" not in sql.upper():
            sql = f"SELECT * FROM ({sql}) WHERE ROWNUM <= {batch_size}"

        return self._execute_sql(sql)


class DB2Connector(BaseDatabaseConnector):
    """IBM DB2连接器"""

    def __init__(self):
        super().__init__()
        self.db_type = "db2"
        self.driver_name = "ibm_db_sa"
        self.driver_manager = db2_driver_manager

    async def connect(self, connection_info):
        """建立DB2连接
        Returns:
            tuple: (success: bool, message: str, details: dict)
        """
        connection_string = DatabaseConfig.get_connection_string(
            "db2",
            connection_info.host,
            connection_info.port,
            connection_info.database,
            connection_info.username,
            connection_info.password
        )
        self._create_engine(connection_string)

        await self._test_connection()

    def test_engine_connection(self) -> tuple[bool, str, dict]:
        """测试引擎连接 - 专门用于验证SQL1042C修复效果"""
        test_result = {
            "engine_created": False,
            "connection_test": False,
            "query_test": False,
            "attempts": [],
            "error_details": None
        }

        try:
            # 检查引擎是否已创建
            if not hasattr(self, 'engine') or self.engine is None:
                return False, "引擎未创建", test_result

            test_result["engine_created"] = True

            # 测试连接
            try:
                with self.engine.connect() as connection:
                    test_result["connection_test"] = True

                    # 测试简单查询
                    result = connection.execute(text("SELECT 1 FROM SYSIBM.SYSDUMMY1"))
                    row = result.fetchone()

                    if row and row[0] == 1:
                        test_result["query_test"] = True
                        return True, "连接和查询测试都成功", test_result
                    else:
                        return False, "查询结果异常", test_result

            except Exception as e:
                error_msg = str(e)
                test_result["error_details"] = {
                    "error_message": error_msg,
                    "error_type": "connection_error" if "SQL1042C" in error_msg else "other_error",
                    "is_sql1042c": "SQL1042C" in error_msg
                }

                if "SQL1042C" in error_msg:
                    return False, f"SQL1042C连接错误: {error_msg}", test_result
                else:
                    return False, f"连接错误: {error_msg}", test_result

        except Exception as e:
            test_result["error_details"] = {"unexpected_error": str(e)}
            return False, f"测试过程中发生意外错误: {str(e)}", test_result

    def _create_engine(self, connection_string: str) -> tuple[bool, str]:
        """
        创建 DB2 SQLAlchemy 引擎
        使用统一的引擎工厂，针对DB2进行特殊配置
        """
        try:
            # 测试原生连接的可行性
            if hasattr(self, '_native_connection_string') and self._native_connection_string:
                self.logger.debug("测试原生DB2连接可行性...")

                try:
                    import ibm_db
                    test_conn = ibm_db.connect(self._native_connection_string, '', '')
                    if test_conn:
                        ibm_db.close(test_conn)
                        self.logger.info("原生DB2连接测试成功")

                except ImportError:
                    self.logger.warning("ibm_db包未安装，跳过原生连接测试")
                except Exception as e:
                    self.logger.warning(f"原生DB2连接测试失败: {e}")

            # 使用工厂创建引擎
            self.engine = DatabaseEngineFactory.create_business_engine(
                database_url=connection_string,
                pool_size=1,        # DB2特殊配置：单一连接
                max_overflow=0,     # DB2特殊配置：禁用溢出
                debug=False
            )

            # 使用工厂创建会话工厂
            SessionLocal = DatabaseEngineFactory.create_session_factory(
                engine=self.engine,
                autocommit=False,
                autoflush=False
            )
            self.session = SessionLocal()

            return True, "引擎创建成功"

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"创建 DB2 引擎失败: {error_msg}")

            # 分析具体错误类型
            if "unexpected keyword argument" in error_msg:
                detailed_msg = "SQLAlchemy连接参数错误，可能是驱动版本不兼容"
            elif "SQL1042C" in error_msg:
                detailed_msg = "SQL1042C系统错误，可能是驱动配置或网络问题"
            elif "No module named" in error_msg:
                detailed_msg = "缺少必要的Python包，请安装ibm_db"
            else:
                detailed_msg = f"引擎创建失败: {error_msg}"

            return False, detailed_msg

    def _build_db2_connection_string(self, connection_info) -> str:
        """构建DB2连接字符串"""

        # 构建原生DB2连接字符串（用于测试和备用连接）
        native_params = {
            'DATABASE': connection_info.database,
            'HOSTNAME': connection_info.host,
            'PORT': str(connection_info.port),
            'PROTOCOL': 'TCPIP',
            'UID': connection_info.username,
            'PWD': connection_info.password,

            # 关键参数来避免SQL1042C错误
            'CONNECTTIMEOUT': '30',      # 连接超时
            'QUERYTIMEOUT': '60',        # 查询超时
            'AUTHENTICATION': 'SERVER',  # 服务器认证
            'AUTOCOMMIT': '0',           # 禁用自动提交

            # 网络稳定性参数
            'KEEPALIVE': '1',            # 保持连接活跃
            'TCPKEEPALIVE': '1',         # TCP保持活跃
            'TCPKEEPINTVL': '30',        # TCP保持活跃间隔30秒
            'TCPKEEPCNT': '3',           # TCP保持活跃计数
        }

        # 添加模式信息（如果提供）
        if hasattr(connection_info, 'schema') and connection_info.schema:
            native_params['CURRENTSCHEMA'] = connection_info.schema

        # 过滤空值参数
        filtered_native_params = {k: v for k, v in native_params.items() if v}

        # 构建原生连接字符串
        native_conn_str_parts = [f"{k}={v}" for k, v in filtered_native_params.items()]
        native_conn_str = ';'.join(native_conn_str_parts) + ';'

        # 存储原生连接字符串供测试使用
        self._native_connection_string = native_conn_str

        # 对用户名和密码进行URL编码，防止特殊字符导致解析错误
        encoded_username = quote_plus(connection_info.username) if connection_info.username else ""
        encoded_password = quote_plus(connection_info.password) if connection_info.password else ""

        sqlalchemy_url = (
            f"db2+ibm_db://{encoded_username}:{encoded_password}"
            f"@{connection_info.host}:{connection_info.port}/{connection_info.database}"
        )

        # 可选：添加SQLAlchemy支持的URL参数
        url_params = []

        # 添加模式参数（如果SQLAlchemy支持）
        if hasattr(connection_info, 'schema') and connection_info.schema:
            url_params.append(f"currentSchema={connection_info.schema}")

        # 如果有URL参数，添加到连接字符串
        if url_params:
            sqlalchemy_url += "?" + "&".join(url_params)

        self.logger.debug(f"构建的SQLAlchemy URL: {sqlalchemy_url}")
        self.logger.debug(f"原生连接字符串: {native_conn_str[:100]}...")  # 只显示前100字符

        return sqlalchemy_url

    async def _test_connection(self) -> bool:
        """测试DB2连接"""
        try:
            # 执行基础连接测试
            result = self._execute_sql("SELECT 1 FROM SYSIBM.SYSDUMMY1")

            if result and len(result) > 0:
                self.logger.info(f"DB2连接测试成功: {result[0]}")
                return True
            else:
                self.logger.error("DB2连接测试失败: 查询结果为空")
                return False
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"DB2连接测试失败: {error_msg}")
        return False

    async def disconnect(self):
        """断开DB2连接"""
        try:
            if self.session:
                self.session.close()
                self.session = None
            if self.engine:
                self.engine.dispose()
                self.engine = None
            self.logger.info("DB2连接已断开")
        except Exception as e:
            self.logger.error(f"断开DB2连接时出错: {e}")

    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取DB2表结构信息"""
        # DB2系统表查询
        sql = f"""
        SELECT
            COLNAME as column_name,
            TYPENAME as data_type,
            CASE NULLS
                WHEN 'Y' THEN 'YES'
                ELSE 'NO'
            END as is_nullable,
            DEFAULT as column_default,
            LENGTH as character_maximum_length,
            SCALE as numeric_precision,
            SCALE as numeric_scale,
            COLNO as ordinal_position
        FROM SYSCAT.COLUMNS
        WHERE TABNAME = UPPER('{table_name}')
        ORDER BY COLNO
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取DB2表结构失败: {table_name}, 错误: {e}")
            # 尝试使用备用查询方法
            return await self._get_table_structure_fallback(table_name)

    async def _get_table_structure_fallback(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构的备用方法"""
        try:
            # 使用DESCRIBE TABLE作为备用方案
            sql = f"DESCRIBE TABLE {table_name}"
            result = self._execute_sql(sql)

            # 转换DESCRIBE结果为标准格式
            standardized_result = []
            for row in result:
                standardized_result.append({
                    'column_name': row.get('COLUMN_NAME', ''),
                    'data_type': row.get('DATA_TYPE', ''),
                    'is_nullable': 'YES' if row.get('NULLABLE', 'N') == 'Y' else 'NO',
                    'column_default': row.get('DEFAULT', ''),
                    'character_maximum_length': row.get('LENGTH', 0),
                    'numeric_precision': row.get('PRECISION', 0),
                    'numeric_scale': row.get('SCALE', 0),
                    'ordinal_position': row.get('POSITION', 0)
                })

            return standardized_result

        except Exception as e:
            self.logger.error(f"备用表结构查询也失败: {table_name}, 错误: {e}")
            return []

    def _execute_sql(self, sql: str, chunk_size: int = 10000) -> List[Dict[str, Any]]:
        """执行SQL语句 - DB2优化版

        使用服务器端游标和分块获取来处理大规模数据集，避免内存溢出。
        """
        all_rows = []
        try:
            with self.engine.connect().execution_options(stream_results=True) as conn:
                result = conn.execute(text(sql))
                columns = list(result.keys())

                while True:
                    chunk = result.fetchmany(chunk_size)
                    if not chunk:
                        break

                    for row in chunk:
                        all_rows.append(dict(zip(columns, row)))

                self.logger.info(f"成功获取 {len(all_rows)} 行数据，采用分块处理，每块 {chunk_size} 行")
                return all_rows

        except Exception as e:
            self.logger.error(f"执行DB2 SQL失败: {sql[:100]}..., 错误: {e}")
            raise

    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行DB2查询"""
        try:
            # 添加DB2特定的查询优化
            optimized_sql = self._optimize_db2_query(sql, batch_size)

            # 调用优化后的_execute_sql
            return self._execute_sql(optimized_sql)
        except Exception as e:
            self.logger.error(f"执行DB2查询失败: {sql[:100]}..., 错误: {e}")
            raise

    def _optimize_db2_query(self, sql: str, batch_size: int) -> str:
        """优化DB2查询语句"""
        sql_upper = sql.upper().strip()

        # DB2分页语法优化
        if "FETCH FIRST" not in sql_upper and "LIMIT" not in sql_upper:
            if sql_upper.endswith(';'):
                sql = sql[:-1]
            sql = f"{sql} FETCH FIRST {batch_size} ROWS ONLY"

        # 添加查询优化提示
        if sql_upper.startswith("SELECT") and "WITH UR" not in sql_upper:
            # 添加未提交读隔离级别以提高性能
            sql = f"{sql} WITH UR"

        return sql

    async def get_table_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """获取DB2表索引信息"""
        sql = f"""
        SELECT
            INDNAME as index_name,
            COLNAMES as column_names,
            UNIQUERULE as is_unique,
            INDEXTYPE as index_type
        FROM SYSCAT.INDEXES
        WHERE TABNAME = UPPER('{table_name}')
        ORDER BY INDNAME
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取DB2索引信息失败: {e}")
            return []

    async def get_table_constraints(self, table_name: str) -> List[Dict[str, Any]]:
        """获取DB2表约束信息"""
        sql = f"""
        SELECT
            CONSTNAME as constraint_name,
            TYPE as constraint_type,
            COLNAMES as column_names
        FROM SYSCAT.TABCONST
        WHERE TABNAME = UPPER('{table_name}')
        ORDER BY CONSTNAME
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取DB2约束信息失败: {e}")
            return []



    def get_data_type_mapping(self) -> Dict[str, str]:
        """获取DB2数据类型映射"""
        return {
            'SMALLINT': 'INTEGER',
            'INTEGER': 'INTEGER',
            'BIGINT': 'BIGINT',
            'DECIMAL': 'DECIMAL',
            'NUMERIC': 'NUMERIC',
            'REAL': 'REAL',
            'DOUBLE': 'DOUBLE',
            'FLOAT': 'FLOAT',
            'CHAR': 'CHAR',
            'VARCHAR': 'VARCHAR',
            'CLOB': 'TEXT',
            'DATE': 'DATE',
            'TIME': 'TIME',
            'TIMESTAMP': 'TIMESTAMP',
            'BLOB': 'BLOB',
            'XML': 'XML'
        }


class GaussDBConnector(BaseDatabaseConnector):
    """华为GaussDB连接器（基于PostgreSQL协议）"""

    def __init__(self):
        super().__init__()
        self.db_type = "gaussdb"
        self.driver_name = "psycopg2"

    async def connect(self, connection_info):
        """建立GaussDB连接"""
        try:
            # GaussDB使用PostgreSQL协议
            connection_string = self._build_gaussdb_connection_string(connection_info)
            self._create_engine(connection_string)

            # 测试连接并获取版本信息
            await self._test_connection()
            self.logger.info(f"GaussDB数据库连接成功: {connection_info.host}:{connection_info.port}")

        except Exception as e:
            self.logger.error(f"GaussDB连接失败: {e}")
            raise ConnectionError(f"GaussDB数据库连接失败: {e}")

    def _build_gaussdb_connection_string(self, connection_info) -> str:
        """构建GaussDB连接字符串"""
        # 对用户名和密码进行URL编码，防止特殊字符导致解析错误
        encoded_username = quote_plus(connection_info.username) if connection_info.username else ""
        encoded_password = quote_plus(connection_info.password) if connection_info.password else ""

        # GaussDB连接字符串格式（基于PostgreSQL）
        return (
            f"postgresql+psycopg2://{encoded_username}:{encoded_password}"
            f"@{connection_info.host}:{connection_info.port}/{connection_info.database}"
        )

    async def _test_connection(self):
        """测试GaussDB连接"""
        test_sql = "SELECT version()"
        result = self._execute_sql(test_sql)
        if result:
            version_info = result[0].get('version', '')
            self.logger.info(f"GaussDB版本: {version_info}")
        else:
            raise ConnectionError("GaussDB连接测试失败")

    async def disconnect(self):
        """断开GaussDB连接"""
        try:
            if self.session:
                self.session.close()
                self.session = None
            if self.engine:
                self.engine.dispose()
                self.engine = None
            self.logger.info("GaussDB连接已断开")
        except Exception as e:
            self.logger.error(f"断开GaussDB连接时出错: {e}")

    async def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """获取GaussDB表结构信息"""
        # GaussDB基于PostgreSQL，但有一些扩展
        sql = f"""
        SELECT
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            ordinal_position,
            udt_name as user_defined_type
        FROM information_schema.columns
        WHERE table_name = '{table_name.lower()}'
        ORDER BY ordinal_position
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取GaussDB表结构失败: {e}")
            raise

    async def get_table_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """获取GaussDB表索引信息"""
        sql = f"""
        SELECT
            i.relname as index_name,
            a.attname as column_name,
            ix.indisunique as is_unique,
            ix.indisprimary as is_primary,
            am.amname as index_type
        FROM pg_class t
        JOIN pg_index ix ON t.oid = ix.indrelid
        JOIN pg_class i ON i.oid = ix.indexrelid
        JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
        JOIN pg_am am ON i.relam = am.oid
        WHERE t.relname = '{table_name.lower()}'
        ORDER BY i.relname, a.attnum
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取GaussDB索引信息失败: {e}")
            return []

    async def get_table_constraints(self, table_name: str) -> List[Dict[str, Any]]:
        """获取GaussDB表约束信息"""
        sql = f"""
        SELECT
            tc.constraint_name,
            tc.constraint_type,
            kcu.column_name,
            tc.is_deferrable,
            tc.initially_deferred
        FROM information_schema.table_constraints tc
        LEFT JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = '{table_name.lower()}'
        ORDER BY tc.constraint_name, kcu.ordinal_position
        """

        try:
            return self._execute_sql(sql)
        except Exception as e:
            self.logger.error(f"获取GaussDB约束信息失败: {e}")
            return []

    async def execute_query(self, sql: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
        """执行GaussDB查询"""
        try:
            # GaussDB支持PostgreSQL的LIMIT语法
            if not self._has_limit_clause(sql):
                sql = f"{sql} LIMIT {batch_size}"

            return self._execute_sql(sql)

        except Exception as e:
            self.logger.error(f"执行GaussDB查询失败: {sql}, 错误: {e}")
            raise

    def _has_limit_clause(self, sql: str) -> bool:
        """检查SQL是否已包含限制子句"""
        sql_upper = sql.upper()
        return 'LIMIT' in sql_upper or 'OFFSET' in sql_upper

    def get_data_type_mapping(self) -> Dict[str, str]:
        """获取GaussDB数据类型映射"""
        return {
            # 基础数据类型
            'smallint': 'INTEGER',
            'integer': 'INTEGER',
            'bigint': 'BIGINT',
            'decimal': 'DECIMAL',
            'numeric': 'NUMERIC',
            'real': 'REAL',
            'double precision': 'DOUBLE',
            'money': 'DECIMAL',

            # 字符类型
            'character varying': 'VARCHAR',
            'varchar': 'VARCHAR',
            'character': 'CHAR',
            'char': 'CHAR',
            'text': 'TEXT',
            'name': 'VARCHAR',

            # 日期时间类型
            'timestamp': 'TIMESTAMP',
            'timestamp without time zone': 'TIMESTAMP',
            'timestamp with time zone': 'TIMESTAMP',
            'date': 'DATE',
            'time': 'TIME',
            'time without time zone': 'TIME',
            'time with time zone': 'TIME',
            'interval': 'INTERVAL',

            # 二进制类型
            'bytea': 'BLOB',

            # 布尔类型
            'boolean': 'BOOLEAN',

            # JSON类型
            'json': 'JSON',
            'jsonb': 'JSON',

            # 数组类型
            'ARRAY': 'ARRAY',

            # GaussDB特有类型
            'tinyint': 'INTEGER',
            'blob': 'BLOB',
            'clob': 'TEXT'
        }

    async def get_gaussdb_specific_info(self, table_name: str) -> Dict[str, Any]:
        """获取GaussDB特有的表信息"""
        try:
            # 获取表的存储信息
            storage_sql = f"""
            SELECT
                schemaname,
                tablename,
                tableowner,
                tablespace,
                hasindexes,
                hasrules,
                hastriggers
            FROM pg_tables
            WHERE tablename = '{table_name.lower()}'
            """

            storage_info = self._execute_sql(storage_sql)

            # 获取表大小信息
            size_sql = f"""
            SELECT
                pg_size_pretty(pg_total_relation_size('{table_name.lower()}')) as total_size,
                pg_size_pretty(pg_relation_size('{table_name.lower()}')) as table_size
            """

            size_info = self._execute_sql(size_sql)

            return {
                'storage_info': storage_info[0] if storage_info else {},
                'size_info': size_info[0] if size_info else {}
            }

        except Exception as e:
            self.logger.error(f"获取GaussDB特有信息失败: {e}")
            return {}


class DatabaseConnectorFactory:
    """数据库连接器工厂类"""

    _connectors = {
        "postgresql": PostgreSQLConnector,
        "mysql": MySQLConnector,
        "oracle": OracleConnector,
        "db2": DB2Connector,
        "gaussdb": GaussDBConnector,
        # 别名支持
        "postgres": PostgreSQLConnector,
        "ibm_db2": DB2Connector,
        "huawei_gaussdb": GaussDBConnector,
    }

    # 数据库驱动依赖映射
    _driver_dependencies = {
        "postgresql": ["psycopg2-binary", "psycopg2"],
        "mysql": ["pymysql", "mysql-connector-python"],
        "oracle": ["cx_oracle", "oracledb"],
        "db2": ["ibm_db", "ibm_db_sa"],
        "gaussdb": ["psycopg2-binary", "psycopg2"],
    }

    @classmethod
    def create_connector(cls, db_type: str) -> BaseDatabaseConnector:
        """创建数据库连接器"""
        db_type = db_type.lower()

        if db_type not in cls._connectors:
            supported_types = ", ".join(cls.get_supported_types())
            raise ValueError(f"不支持的数据库类型: {db_type}. 支持的类型: {supported_types}")

        # 检查驱动依赖
        cls._check_driver_dependencies(db_type)

        connector_class = cls._connectors[db_type]
        return connector_class()

    @classmethod
    def _check_driver_dependencies(cls, db_type: str):
        """检查数据库驱动依赖"""
        if db_type == "db2":
            cls._check_db2_driver_dependencies()
        elif db_type in cls._driver_dependencies:
            drivers = cls._driver_dependencies[db_type]
            available_driver = None

            for driver in drivers:
                try:
                    __import__(driver)
                    available_driver = driver
                    break
                except ImportError:
                    continue

            if not available_driver:
                driver_list = ", ".join(drivers)
                raise ImportError(
                    f"缺少{db_type}数据库驱动。请安装以下驱动之一: {driver_list}\n"
                    f"安装命令示例: pip install {drivers[0]}"
                )

    @classmethod
    def _check_db2_driver_dependencies(cls):
        """检查 DB2 驱动依赖（使用驱动管理器）"""
        try:
            # 使用驱动管理器进行检查
            driver_status = db2_driver_manager.check_driver_status()

            if driver_status.status == DB2DriverStatus.READY:
                return  # 驱动就绪

            # 构建详细错误信息
            error_parts = ["DB2 驱动检查失败:"]
            error_parts.append(f"状态: {driver_status.status.value}")

            if driver_status.error_message:
                error_parts.append(f"错误: {driver_status.error_message}")

            # 添加安装指导
            if driver_status.installation_guide:
                error_parts.append("\n解决方案:")
                error_parts.append(driver_status.installation_guide)

            # 添加快速修复建议
            if driver_status.status == DB2DriverStatus.PYTHON_DRIVER_MISSING:
                error_parts.append("\n快速修复: pip install ibm_db")
            elif driver_status.status == DB2DriverStatus.CLI_DRIVER_MISSING:
                error_parts.append("\n请下载并安装 IBM Data Server Driver Package")

            raise ImportError("\n".join(error_parts))

        except Exception as e:
            if isinstance(e, ImportError):
                raise
            else:
                raise ImportError(f"DB2 驱动检查失败: {str(e)}")

    @classmethod
    def get_supported_types(cls) -> List[str]:
        """获取支持的数据库类型"""
        # 返回主要类型，不包括别名
        main_types = ["postgresql", "mysql", "oracle", "db2", "gaussdb"]
        return main_types

    @classmethod
    def get_all_types(cls) -> List[str]:
        """获取所有支持的数据库类型（包括别名）"""
        return list(cls._connectors.keys())

    @classmethod
    def register_connector(cls, db_type: str, connector_class):
        """注册新的连接器"""
        cls._connectors[db_type.lower()] = connector_class

    @classmethod
    def get_driver_info(cls, db_type: str) -> Dict[str, Any]:
        """获取数据库驱动信息"""
        db_type = db_type.lower()

        if db_type not in cls._driver_dependencies:
            return {"drivers": [], "status": "unknown"}

        drivers = cls._driver_dependencies[db_type]
        driver_status = []

        for driver in drivers:
            try:
                module = __import__(driver)
                version = getattr(module, '__version__', 'unknown')
                driver_status.append({
                    "name": driver,
                    "available": True,
                    "version": version
                })
            except ImportError:
                driver_status.append({
                    "name": driver,
                    "available": False,
                    "version": None
                })

        return {
            "drivers": driver_status,
            "status": "available" if any(d["available"] for d in driver_status) else "missing"
        }

    @classmethod
    def validate_connection_info(cls, db_type: str, connection_info) -> Dict[str, Any]:
        """验证连接信息"""
        db_type = db_type.lower()

        if db_type not in cls._connectors:
            return {"valid": False, "error": f"不支持的数据库类型: {db_type}"}

        # 基础验证
        required_fields = ["host", "port", "database", "username", "password"]
        missing_fields = []

        for field in required_fields:
            if not hasattr(connection_info, field) or not getattr(connection_info, field):
                missing_fields.append(field)

        if missing_fields:
            return {
                "valid": False,
                "error": f"缺少必要的连接信息: {', '.join(missing_fields)}"
            }

        # 数据库特定验证
        validation_result = cls._validate_db_specific(db_type, connection_info)

        return validation_result

    @classmethod
    def _validate_db_specific(cls, db_type: str, connection_info) -> Dict[str, Any]:
        """数据库特定验证"""
        if db_type == "db2":
            # DB2特定验证
            if connection_info.port not in [50000, 50001, 446]:
                return {
                    "valid": True,
                    "warning": f"DB2常用端口为50000/50001/446，当前端口: {connection_info.port}"
                }

        elif db_type == "gaussdb":
            # GaussDB特定验证
            if connection_info.port != 5432:
                return {
                    "valid": True,
                    "warning": f"GaussDB默认端口为5432，当前端口: {connection_info.port}"
                }

        elif db_type == "oracle":
            # Oracle特定验证
            if connection_info.port not in [1521, 1522]:
                return {
                    "valid": True,
                    "warning": f"Oracle常用端口为1521/1522，当前端口: {connection_info.port}"
                }

        elif db_type == "mysql":
            # MySQL特定验证
            if connection_info.port != 3306:
                return {
                    "valid": True,
                    "warning": f"MySQL默认端口为3306，当前端口: {connection_info.port}"
                }

        elif db_type == "postgresql":
            # PostgreSQL特定验证
            if connection_info.port != 5432:
                return {
                    "valid": True,
                    "warning": f"PostgreSQL默认端口为5432，当前端口: {connection_info.port}"
                }

        return {"valid": True}


# 数据库连接器辅助函数
def get_database_info(db_type: str) -> Dict[str, Any]:
    """获取数据库信息"""
    database_info = {
        "postgresql": {
            "name": "PostgreSQL",
            "description": "开源关系型数据库",
            "default_port": 5432,
            "url_format": "postgresql://user:password@host:port/database"
        },
        "mysql": {
            "name": "MySQL",
            "description": "开源关系型数据库",
            "default_port": 3306,
            "url_format": "mysql://user:password@host:port/database"
        },
        "oracle": {
            "name": "Oracle Database",
            "description": "企业级关系型数据库",
            "default_port": 1521,
            "url_format": "oracle://user:password@host:port/service_name"
        },
        "db2": {
            "name": "IBM DB2",
            "description": "IBM企业级关系型数据库",
            "default_port": 50000,
            "url_format": "db2://user:password@host:port/database"
        },
        "gaussdb": {
            "name": "华为GaussDB",
            "description": "华为企业级分布式数据库",
            "default_port": 5432,
            "url_format": "gaussdb://user:password@host:port/database"
        }
    }

    return database_info.get(db_type.lower(), {
        "name": "Unknown",
        "description": "未知数据库类型",
        "default_port": 0,
        "url_format": "unknown://user:password@host:port/database"
    })
