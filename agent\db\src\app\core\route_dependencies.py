"""
路由依赖装饰器模块
提供自动添加依赖项的装饰器
"""
import functools
import inspect
from typing import Callable, TypeVar

from app.core.task_manager import TaskManager
from app.core.comparison import unified_comparison_service
from app.core.dependencies import get_task_manager

T = TypeVar('T')

# 全局单例实例
_task_manager = None

def inject_dependencies(func: Callable[..., T]) -> Callable[..., T]:
    """
    装饰器：自动注入依赖项

    自动为路由处理函数添加TaskManager依赖项
    注意：比对服务现在使用全局的unified_comparison_service
    """
    # 获取函数签名
    sig = inspect.signature(func)

    # 检查是否已经有依赖项参数
    has_task_manager = 'task_manager' in sig.parameters

    # 创建新的签名
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        global _task_manager

        # 初始化全局单例（如果需要）
        if _task_manager is None:
            _task_manager = await get_task_manager()

        # 添加依赖项到kwargs
        if has_task_manager and 'task_manager' not in kwargs:
            kwargs['task_manager'] = _task_manager

        # 比对服务现在直接使用全局的unified_comparison_service
        # 不再需要通过依赖注入

        # 调用原始函数
        return await func(*args, **kwargs)

    return wrapper
