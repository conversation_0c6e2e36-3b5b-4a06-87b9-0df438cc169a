version: '3.8'

services:
  # DB-Agent 主服务
  db-agent:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - MAX_CONCURRENT_TASKS=${MAX_CONCURRENT_TASKS:-10}
      - DEFAULT_MAX_WORKERS=${DEFAULT_MAX_WORKERS:-4}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # Redis 用于任务队列和缓存
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    command: >
      --save 60 1
      --loglevel warning
      ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 工作节点，用于处理长时间运行的比对任务
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    command: ["celery", "-A", "app.workers.tasks", "worker", "--loglevel=info", "--concurrency=4"]
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    # 可以根据需求扩展多个worker实例
    deploy:
      replicas: ${WORKER_REPLICAS:-1}

  # Flower 监控任务队列 (可选)
  flower:
    image: mher/flower
    environment:
      - CELERY_BROKER_URL=redis://${REDIS_PASSWORD:+:${REDIS_PASSWORD}@}redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - worker
    restart: unless-stopped

volumes:
  redis-data: 