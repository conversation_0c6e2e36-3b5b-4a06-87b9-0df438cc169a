# 数据比对器代理 (Comparer Agent) v2.0

## 🎯 项目概述

数据比对器代理是一个专门的后端服务，专注于提供高性能、高一致性的数据比对能力。作为独立的微服务，它可以被其他系统通过REST API调用，实现多种异构数据源之间的表结构和表内容比对。

## 🏗️ 系统架构

### 核心特性
- 🚀 **高性能比对引擎**: 异步处理 + 批量优化
- 🔌 **多数据源支持**: Oracle、MySQL、PostgreSQL、SQL Server、DB2、GaussDB
- 📊 **双重比对模式**: 表结构比对 + 表内容比对
- 🎛️ **XML规则引擎**: 标准化SQL比对规则
- 💾 **灵活存储方案**: 文件存储 + 数据库存储
- 🌐 **REST API接口**: 完整的API服务，支持系统集成

### 技术栈
- **核心框架**: FastAPI + SQLAlchemy + Celery
- **数据库**: PostgreSQL (元数据) + Redis (缓存/队列)
- **部署**: Docker + Docker Compose
- **监控**: Prometheus + 健康检查

## 📁 目录结构

```
comparer/
├── backend/                 # 后端API服务
│   ├── app/                # FastAPI应用
│   ├── core/               # 核心配置模块
│   ├── api/                # REST API路由
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务层
│   └── utils/              # 工具函数
├── comparison/             # 比对引擎核心
│   ├── engines/            # 比对算法实现
│   ├── connectors/         # 数据源连接器
│   ├── rules/              # XML规则解析引擎
│   └── storage/            # 结果存储处理
├── config/                 # 配置文件
├── docker/                 # Docker部署配置
├── scripts/                # 部署和管理脚本
├── tests/                  # 测试用例
└── docs/                   # 技术文档
```

## 🚀 快速开始

### 1. 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 内存: 最低2GB，推荐4GB+
- 磁盘: 最低5GB可用空间

### 2. 一键启动
```bash
cd agent/db/comparer
chmod +x scripts/start.sh
./scripts/start.sh
```

### 3. 服务访问
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **API基础路径**: http://localhost:8000/api/v1

## 🔧 API接口概览

### 核心接口
- `POST /api/v1/comparison/start` - 启动比对任务
- `GET /api/v1/comparison/status/{task_id}` - 查询任务状态
- `GET /api/v1/comparison/result/{task_id}` - 获取比对结果
- `DELETE /api/v1/comparison/task/{task_id}` - 取消比对任务
- `GET /api/v1/comparison/tasks` - 获取任务列表

### 管理接口
- `POST /api/v1/database/test-connection` - 测试数据库连接
- `POST /api/v1/rules/validate` - 验证比对规则
- `GET /api/v1/results/export/{task_id}` - 导出比对结果

## 📊 使用示例

### 表结构比对
```bash
curl -X POST "http://localhost:8000/api/v1/comparison/start" \
  -H "Content-Type: application/json" \
  -d '{
    "comparison_type": "structure",
    "source_connection": {
      "db_type": "mysql",
      "host": "*************",
      "port": 3306,
      "database": "source_db",
      "username": "user",
      "password": "pass"
    },
    "target_connection": {
      "db_type": "postgresql",
      "host": "*************",
      "port": 5432,
      "database": "target_db",
      "username": "user",
      "password": "pass"
    },
    "table_name": "users"
  }'
```

### 表内容比对
```bash
curl -X POST "http://localhost:8000/api/v1/comparison/start" \
  -H "Content-Type: application/json" \
  -d '{
    "comparison_type": "content",
    "source_connection": {...},
    "target_connection": {...},
    "table_name": "orders",
    "sql_rules": {
      "sql_1": "SELECT ORDER_ID||'-'||CUSTOMER_ID AS KEY, o.* FROM orders o",
      "sql_2": "SELECT ORDER_ID||'-'||CUSTOMER_ID AS KEY, o.* FROM orders o"
    },
    "batch_size": 1000
  }'
```

## 🔒 系统集成

作为独立的比对器代理，本系统设计用于：
- **微服务架构**: 作为比对服务被其他系统调用
- **数据治理平台**: 提供数据一致性检测能力
- **ETL流程**: 集成到数据迁移和同步流程中
- **监控系统**: 定期执行数据质量检查

## 📈 性能特点

- **异步处理**: 支持大量并发比对任务
- **批量优化**: 大表数据分批处理，避免内存溢出
- **连接池**: 高效的数据库连接复用
- **缓存机制**: 智能缓存提升重复查询性能
- **资源管理**: 自动资源分配和负载均衡

## 🛠️ 运维支持

- **健康检查**: 完整的服务健康监控
- **日志管理**: 结构化日志和错误追踪
- **监控指标**: Prometheus指标收集
- **容器化**: Docker容器化部署
- **自动重启**: 服务异常自动恢复

详细使用说明请参考 `docs/` 目录下的技术文档。
