"""
敏感数据脱敏模块

提供敏感信息保护和数据脱敏功能
"""
import copy
import re
import logging
from typing import Dict, Any, List, Optional, Union
from enum import Enum

logger = logging.getLogger(__name__)


class SensitivityLevel(Enum):
    """敏感度级别"""
    PUBLIC = "public"           # 公开信息
    INTERNAL = "internal"       # 内部信息
    CONFIDENTIAL = "confidential"  # 机密信息
    SECRET = "secret"           # 秘密信息


class MaskingStrategy(Enum):
    """脱敏策略"""
    FULL_MASK = "full_mask"         # 完全遮蔽 -> ***
    PARTIAL_MASK = "partial_mask"   # 部分遮蔽 -> user***@example.com
    HASH_MASK = "hash_mask"         # 哈希遮蔽 -> sha256:abc123...
    REMOVE = "remove"               # 完全移除
    KEEP_TYPE = "keep_type"         # 保留类型 -> <string>


class SensitiveDataMasker:
    """敏感数据脱敏器"""
    
    # 敏感字段配置
    SENSITIVE_FIELDS = {
        # 认证信息
        'password': (SensitivityLevel.SECRET, MaskingStrategy.FULL_MASK),
        'passwd': (SensitivityLevel.SECRET, MaskingStrategy.FULL_MASK),
        'pwd': (SensitivityLevel.SECRET, MaskingStrategy.FULL_MASK),
        'secret': (SensitivityLevel.SECRET, MaskingStrategy.FULL_MASK),
        'token': (SensitivityLevel.SECRET, MaskingStrategy.PARTIAL_MASK),
        'api_key': (SensitivityLevel.SECRET, MaskingStrategy.PARTIAL_MASK),
        'access_token': (SensitivityLevel.SECRET, MaskingStrategy.PARTIAL_MASK),
        
        # 个人信息
        'email': (SensitivityLevel.CONFIDENTIAL, MaskingStrategy.PARTIAL_MASK),
        'phone': (SensitivityLevel.CONFIDENTIAL, MaskingStrategy.PARTIAL_MASK),
        'mobile': (SensitivityLevel.CONFIDENTIAL, MaskingStrategy.PARTIAL_MASK),
        'id_card': (SensitivityLevel.SECRET, MaskingStrategy.PARTIAL_MASK),
        'ssn': (SensitivityLevel.SECRET, MaskingStrategy.PARTIAL_MASK),
        
        # 数据库连接信息
        'username': (SensitivityLevel.INTERNAL, MaskingStrategy.PARTIAL_MASK),
        'user': (SensitivityLevel.INTERNAL, MaskingStrategy.PARTIAL_MASK),
        'host': (SensitivityLevel.INTERNAL, MaskingStrategy.KEEP_TYPE),
        'database': (SensitivityLevel.INTERNAL, MaskingStrategy.KEEP_TYPE),
        'db_name': (SensitivityLevel.INTERNAL, MaskingStrategy.KEEP_TYPE),
    }
    
    def __init__(self, default_strategy: MaskingStrategy = MaskingStrategy.FULL_MASK):
        self.default_strategy = default_strategy
        self.custom_rules = {}
    
    def add_custom_rule(
        self, 
        field_pattern: str, 
        sensitivity: SensitivityLevel, 
        strategy: MaskingStrategy
    ):
        """添加自定义脱敏规则"""
        self.custom_rules[field_pattern] = (sensitivity, strategy)
    
    def mask_data(
        self, 
        data: Union[Dict[str, Any], List, str, Any], 
        context: str = "default"
    ) -> Union[Dict[str, Any], List, str, Any]:
        """
        脱敏数据
        
        Args:
            data: 要脱敏的数据
            context: 上下文信息（用于日志）
            
        Returns:
            脱敏后的数据
        """
        try:
            if isinstance(data, dict):
                return self._mask_dict(data, context)
            elif isinstance(data, list):
                return self._mask_list(data, context)
            elif isinstance(data, str):
                return self._mask_string(data, context)
            else:
                return data
                
        except Exception as e:
            logger.error(f"数据脱敏失败 - 上下文: {context}, 错误: {str(e)}")
            return "<脱敏失败>"
    
    def _mask_dict(self, data: Dict[str, Any], context: str) -> Dict[str, Any]:
        """脱敏字典数据"""
        masked_data = {}
        
        for key, value in data.items():
            # 检查字段是否需要脱敏
            sensitivity, strategy = self._get_field_sensitivity(key)
            
            if sensitivity in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL]:
                masked_value = self._apply_masking_strategy(value, strategy, key)
                masked_data[key] = masked_value
                
                logger.debug(f"字段脱敏 - 上下文: {context}, 字段: {key}, 策略: {strategy.value}")
            else:
                # 递归处理嵌套数据
                masked_data[key] = self.mask_data(value, f"{context}.{key}")
        
        return masked_data
    
    def _mask_list(self, data: List[Any], context: str) -> List[Any]:
        """脱敏列表数据"""
        return [
            self.mask_data(item, f"{context}[{i}]") 
            for i, item in enumerate(data)
        ]
    
    def _mask_string(self, data: str, context: str) -> str:
        """脱敏字符串数据"""
        # 检查是否包含敏感模式
        if self._contains_sensitive_pattern(data):
            return self._apply_masking_strategy(data, MaskingStrategy.PARTIAL_MASK, context)
        return data
    
    def _get_field_sensitivity(self, field_name: str) -> tuple[SensitivityLevel, MaskingStrategy]:
        """获取字段的敏感度和脱敏策略"""
        field_lower = field_name.lower()
        
        # 检查精确匹配
        if field_lower in self.SENSITIVE_FIELDS:
            return self.SENSITIVE_FIELDS[field_lower]
        
        # 检查自定义规则（支持正则表达式）
        for pattern, (sensitivity, strategy) in self.custom_rules.items():
            if re.search(pattern, field_lower, re.IGNORECASE):
                return sensitivity, strategy
        
        # 检查模糊匹配
        for sensitive_field, (sensitivity, strategy) in self.SENSITIVE_FIELDS.items():
            if sensitive_field in field_lower or field_lower in sensitive_field:
                return sensitivity, strategy
        
        # 默认为公开信息
        return SensitivityLevel.PUBLIC, MaskingStrategy.KEEP_TYPE
    
    def _apply_masking_strategy(
        self, 
        value: Any, 
        strategy: MaskingStrategy, 
        field_name: str
    ) -> Any:
        """应用脱敏策略"""
        if value is None:
            return None
        
        value_str = str(value)
        
        if strategy == MaskingStrategy.FULL_MASK:
            return "***"
        
        elif strategy == MaskingStrategy.PARTIAL_MASK:
            return self._partial_mask(value_str, field_name)
        
        elif strategy == MaskingStrategy.HASH_MASK:
            return self._hash_mask(value_str)
        
        elif strategy == MaskingStrategy.REMOVE:
            return None
        
        elif strategy == MaskingStrategy.KEEP_TYPE:
            return f"<{type(value).__name__}>"
        
        else:
            return "***"
    
    def _partial_mask(self, value: str, field_name: str) -> str:
        """部分脱敏"""
        if not value:
            return value
        
        field_lower = field_name.lower()
        
        # 邮箱脱敏
        if 'email' in field_lower or '@' in value:
            return self._mask_email(value)
        
        # 电话号码脱敏
        if 'phone' in field_lower or 'mobile' in field_lower:
            return self._mask_phone(value)
        
        # 用户名脱敏
        if 'user' in field_lower:
            return self._mask_username(value)
        
        # 默认脱敏策略
        if len(value) <= 3:
            return "*" * len(value)
        elif len(value) <= 6:
            return value[:1] + "*" * (len(value) - 2) + value[-1:]
        else:
            return value[:2] + "*" * (len(value) - 4) + value[-2:]
    
    def _mask_email(self, email: str) -> str:
        """邮箱脱敏"""
        if '@' not in email:
            return self._partial_mask(email, 'default')
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = "*" * len(local)
        else:
            masked_local = local[:1] + "*" * (len(local) - 2) + local[-1:]
        
        return f"{masked_local}@{domain}"
    
    def _mask_phone(self, phone: str) -> str:
        """电话号码脱敏"""
        # 移除非数字字符
        digits = re.sub(r'\D', '', phone)
        
        if len(digits) <= 4:
            return "*" * len(digits)
        elif len(digits) <= 7:
            return digits[:2] + "*" * (len(digits) - 4) + digits[-2:]
        else:
            return digits[:3] + "*" * (len(digits) - 6) + digits[-3:]
    
    def _mask_username(self, username: str) -> str:
        """用户名脱敏"""
        if len(username) <= 2:
            return "*" * len(username)
        elif len(username) <= 4:
            return username[:1] + "*" * (len(username) - 1)
        else:
            return username[:2] + "*" * (len(username) - 3) + username[-1:]
    
    def _hash_mask(self, value: str) -> str:
        """哈希脱敏"""
        import hashlib
        hash_value = hashlib.sha256(value.encode()).hexdigest()
        return f"sha256:{hash_value[:8]}..."
    
    def _contains_sensitive_pattern(self, text: str) -> bool:
        """检查文本是否包含敏感模式"""
        sensitive_patterns = [
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # 信用卡号
            r'\b\d{3}-\d{2}-\d{4}\b',                        # SSN
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 邮箱
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',               # 电话号码
        ]
        
        for pattern in sensitive_patterns:
            if re.search(pattern, text):
                return True
        
        return False


# 全局脱敏器实例
default_masker = SensitiveDataMasker()


def mask_connection_info(connection_info: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏数据库连接信息"""
    return default_masker.mask_data(connection_info, "connection")


def mask_request_data(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏请求数据"""
    return default_masker.mask_data(request_data, "request")


def mask_for_logging(data: Any, context: str = "log") -> Any:
    """为日志记录脱敏数据"""
    return default_masker.mask_data(data, context)


def create_audit_safe_copy(data: Dict[str, Any]) -> Dict[str, Any]:
    """创建审计安全的数据副本"""
    # 深拷贝原始数据
    safe_copy = copy.deepcopy(data)
    
    # 应用脱敏
    return default_masker.mask_data(safe_copy, "audit")


# 配置示例
def configure_custom_masking():
    """配置自定义脱敏规则示例"""
    # 添加自定义敏感字段
    default_masker.add_custom_rule(
        r'.*key.*', 
        SensitivityLevel.SECRET, 
        MaskingStrategy.PARTIAL_MASK
    )
    
    default_masker.add_custom_rule(
        r'.*token.*', 
        SensitivityLevel.SECRET, 
        MaskingStrategy.HASH_MASK
    )
    
    default_masker.add_custom_rule(
        r'.*id.*', 
        SensitivityLevel.CONFIDENTIAL, 
        MaskingStrategy.PARTIAL_MASK
    )
