"""
任务管理相关模型

定义数据比对任务的请求和响应数据结构。
"""
from enum import Enum
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator, model_validator
from app.models.db import DatabaseConnection, DatabaseType

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELED = "canceled"  # 已取消
    PAUSED = "paused"  # 已暂停


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"


class CompareMode(str, Enum):
    """比对模式枚举"""
    TABLE = "table"  # 表模式
    QUERY = "query"  # 查询模式


class AlgorithmType(str, Enum):
    """比对算法类型枚举"""
    PRIMARY_KEY = "primary_key"  # 默认主键比对
    HASH_BASED = "hash_based"  # 基于哈希比对
    TWO_PHASE = "two_phase"  # 两阶段比对


class TaskResultFormat(str, Enum):
    """任务结果格式枚举"""
    JSON = "json"  # JSON格式
    CSV = "csv"   # CSV格式
    EXCEL = "excel"  # Excel格式
    HTML = "html"  # HTML格式


class CompareConfig(BaseModel):
    """数据比对配置"""
    mode: CompareMode = Field(..., description="比对模式: table或query")

    # 表模式配置
    source_table: Optional[str] = Field(None, description="源表名(当mode=table时必须)")
    target_table: Optional[str] = Field(None, description="目标表名(当mode=table时必须)")

    # 查询模式配置
    source_query: Optional[str] = Field(None, description="源查询SQL(当mode=query时必须)")
    target_query: Optional[str] = Field(None, description="目标查询SQL(当mode=query时必须)")

    # 通用配置
    primary_keys: List[str] = Field(..., min_items=1, description="主键字段列表(用于记录匹配)")
    compare_columns: Optional[List[str]] = Field(None, description="比对的字段列表(为空则比对所有非主键字段)")
    ignore_columns: Optional[List[str]] = Field(None, description="忽略比对的字段列表")
    where_clause: Optional[str] = Field(None, description="过滤条件(应用于源和目标)")
    limit: Optional[int] = Field(None, gt=0, description="限制比对的记录数")
    batch_size: int = Field(1000, gt=0, description="处理批次大小")

    # 算法配置
    algorithm_type: AlgorithmType = Field(AlgorithmType.PRIMARY_KEY, description="比对算法类型")

    # 高级配置
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="任务完成后的回调URL")

    @model_validator(mode='before')
    @classmethod
    def validate_mode_specific_fields(cls, values):
        """根据mode验证必须字段"""
        mode = values.get("mode")
        if mode == CompareMode.TABLE:
            if not values.get("source_table"):
                raise ValueError("表模式下source_table为必填项")
            if not values.get("target_table"):
                raise ValueError("表模式下target_table为必填项")
        elif mode == CompareMode.QUERY:
            if not values.get("source_query"):
                raise ValueError("查询模式下source_query为必填项")
            if not values.get("target_query"):
                raise ValueError("查询模式下target_query为必填项")
        return values


class CompareTaskRequest(BaseModel):
    """数据比对任务请求"""
    source: DatabaseConnection = Field(..., description="源数据库连接信息")
    target: DatabaseConnection = Field(..., description="目标数据库连接信息")
    config: CompareConfig = Field(..., description="比对配置")

    model_config = {
        "json_schema_extra": {
            "example": {
                "source": {
                    "type": "mysql",
                    "host": "mysql-server",
                    "port": 3306,
                    "username": "user",
                    "password": "password",
                    "database": "source_db",
                    "parameters": {"charset": "utf8mb4"}
                },
                "target": {
                    "type": "postgresql",
                    "host": "postgres-server",
                    "port": 5432,
                    "username": "user",
                    "password": "password",
                    "database": "target_db"
                },
                "config": {
                    "mode": "table",
                    "source_table": "source_schema.table_name",
                    "target_table": "target_schema.table_name",
                    "primary_keys": ["id"],
                    "compare_columns": ["name", "value", "update_time"],
                    "batch_size": 1000,
                    "algorithm_type": "primary_key",
                    "priority": "normal"
                }
            }
        }
    }


class TaskCreatedResponse(BaseModel):
    """任务创建响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(..., description="任务状态消息")
    created_at: datetime = Field(..., description="创建时间")
    links: Dict[str, str] = Field(..., description="相关资源链接")


class TaskStats(BaseModel):
    """任务统计信息"""
    source_count: int = Field(0, description="源数据记录数")
    target_count: int = Field(0, description="目标数据记录数")
    processed_count: int = Field(0, description="已处理记录数")
    diff_count: int = Field(0, description="差异记录数")
    matched_count: int = Field(0, description="匹配记录数")
    source_only_count: int = Field(0, description="仅源存在记录数")
    target_only_count: int = Field(0, description="仅目标存在记录数")
    elapsed_time: int = Field(0, description="已执行时间(秒)")
    estimated_remaining_time: Optional[int] = Field(None, description="预估剩余时间(秒)")


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: int = Field(0, ge=0, le=100, description="进度百分比")
    stats: Optional[TaskStats] = Field(None, description="任务统计信息")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    message: str = Field(..., description="状态消息")
    links: Dict[str, str] = Field(..., description="相关资源链接")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息（如果任务失败）")


class FieldDiff(BaseModel):
    """字段差异"""
    field: str = Field(..., description="字段名")
    source_value: Any = Field(..., description="源值")
    target_value: Any = Field(..., description="目标值")


class RecordDiff(BaseModel):
    """记录差异"""
    key: Dict[str, Any] = Field(..., description="记录主键")
    diff_type: str = Field(..., description="差异类型: value_diff, source_missing, target_missing")
    field_diffs: Optional[List[FieldDiff]] = Field(None, description="字段差异列表(仅当diff_type=value_diff时)")
    source_record: Optional[Dict[str, Any]] = Field(None, description="源记录(仅当diff_type=target_missing时)")
    target_record: Optional[Dict[str, Any]] = Field(None, description="目标记录(仅当diff_type=source_missing时)")


class DiffDetails(BaseModel):
    """差异详情(分页)"""
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., gt=0, description="每页记录数")
    total_pages: int = Field(..., ge=1, description="总页数")
    total_items: int = Field(..., ge=0, description="总记录数")
    items: List[RecordDiff] = Field(..., description="当前页差异列表")


class ComparisonSummary(BaseModel):
    """比对结果摘要"""
    source_count: int = Field(..., description="源记录数")
    target_count: int = Field(..., description="目标记录数")
    matched_count: int = Field(..., description="匹配记录数")
    diff_count: int = Field(..., description="差异记录数")
    source_only_count: int = Field(..., description="仅源存在记录数")
    target_only_count: int = Field(..., description="仅目标存在记录数")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息，包含type、message和details等字段")

    def model_dump(self, **kwargs):
        """重写model_dump方法，添加前端兼容字段"""
        data = super().model_dump(**kwargs)

        # 添加前端兼容的字段别名
        data['totalRecords'] = max(self.source_count, self.target_count)
        data['total_records'] = data['totalRecords']
        data['processed'] = self.matched_count + self.diff_count
        data['processed_records'] = data['processed']
        data['different'] = self.diff_count
        data['different_records'] = self.diff_count
        data['sourceCount'] = self.source_count
        data['targetCount'] = self.target_count
        data['matchedCount'] = self.matched_count
        data['diffCount'] = self.diff_count
        data['sourceOnlyCount'] = self.source_only_count
        data['targetOnlyCount'] = self.target_only_count

        return data


class TaskResultResponse(BaseModel):
    """任务结果响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    summary: Optional[ComparisonSummary] = Field(None, description="比对摘要")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    execution_time: Optional[int] = Field(None, description="执行时间(秒)")
    details: Optional[DiffDetails] = Field(None, description="差异详情(分页，仅当请求中指定details=true时返回)")


class DatabaseConfig(BaseModel):
    """数据库配置模型"""
    type: DatabaseType = Field(..., description="数据库类型")
    host: Optional[str] = Field(None, description="数据库主机地址")
    port: Optional[int] = Field(None, description="数据库端口")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    database: Optional[str] = Field(None, description="数据库名")
    path: Optional[str] = Field(None, description="本地数据库文件路径，用于SQLite")
    uri: Optional[str] = Field(None, description="数据库连接URI，优先使用")
    ssl: Optional[bool] = Field(False, description="是否使用SSL连接")
    options: Optional[Dict[str, Any]] = Field(None, description="其他连接选项")

    @field_validator('port', 'host')
    @classmethod
    def validate_connection_params(cls, v, info):
        """验证连接参数的有效性"""
        values = info.data
        db_type = values.get('type')
        if not db_type:
            return v

        if db_type in (DatabaseType.MONGODB, DatabaseType.MONGODB):
            if not values.get('host') and not values.get('uri'):
                raise ValueError(f"{db_type}数据库需要提供'host'参数或'uri'")


        return v


class TaskType(str, Enum):
    """任务类型枚举"""
    BACKUP = "backup"
    RESTORE = "restore"
    QUERY = "query"
    STRUCTURE = "structure"
    MIGRATION = "migration"


class TaskCreate(BaseModel):
    """任务创建请求模型"""
    type: TaskType = Field(..., description="任务类型")
    source_db: DatabaseConfig = Field(..., description="源数据库配置")
    target_db: Optional[DatabaseConfig] = Field(None, description="目标数据库配置，用于迁移和恢复任务")
    parameters: Optional[Dict[str, Any]] = Field({}, description="任务参数")
    description: Optional[str] = Field(None, description="任务描述")


class TaskBase(TaskCreate):
    """任务基础模型"""
    id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(TaskStatus.PENDING, description="任务状态")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error: Optional[str] = Field(None, description="错误信息")
    progress: Optional[float] = Field(0, description="任务进度 (0-100)")


class Task(TaskBase):
    """完整任务模型"""
    logs: Optional[List[str]] = Field([], description="任务日志记录")


class TaskUpdate(BaseModel):
    """任务更新模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error: Optional[str] = Field(None, description="错误信息")
    progress: Optional[float] = Field(None, description="任务进度 (0-100)")
    logs: Optional[List[str]] = Field(None, description="任务日志记录")