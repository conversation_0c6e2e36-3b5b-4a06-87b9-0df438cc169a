#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP API比对适配器
基于现有的API客户端逻辑，提供统一接口
"""

import time
import asyncio
from typing import Dict, Any, List

from .base_adapter import Comp<PERSON>on<PERSON>dapter, ErrorMapper
from .unified_models import (
    UnifiedResult, UnifiedDifference, UnifiedError, ComparisonMode,
    ProgressCallback, create_unified_difference
)


class APIComparisonAdapter(ComparisonAdapter):
    """HTTP API比对适配器"""
    
    def __init__(self, app_instance, config=None):
        super().__init__(app_instance, ComparisonMode.API, config)
        self.error_mapper = APIModeErrorMapper()
    
    async def execute(self, config: Dict[str, Any], 
                     progress_callback: ProgressCallback) -> UnifiedResult:
        """执行API比对"""
        try:
            # 检查API可用性
            if not self._is_api_available():
                raise Exception("HTTP API服务不可用")
            
            # 构建API请求
            api_request = self._build_api_request(config)
            
            self.logger.info("提交API比对任务")
            
            # 提交任务
            task_response = await self._submit_api_task(api_request)
            if not self._is_api_response_successful(task_response):
                error_msg = self._extract_error_message(task_response)
                raise Exception(f"API任务提交失败: {error_msg}")
            
            task_id = self._extract_task_id(task_response)
            self.logger.info(f"API任务已提交，任务ID: {task_id}")
            
            # 监控任务进度
            result = await self._monitor_api_task(task_id, progress_callback)
            result.task_id = task_id
            
            return result
            
        except Exception as e:
            return self._create_error_result(e)
    
    def _is_api_available(self) -> bool:
        """检查API是否可用"""
        return (hasattr(self.app, 'api_available') and self.app.api_available and 
                hasattr(self.app, 'api_client') and self.app.api_client)
    
    def _build_api_request(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """构建API请求"""
        source_config = self.app.config_manager.get_database_config('DB1')
        target_config = self.app.config_manager.get_database_config('DB2')
        tables = config.get('tables', [])
        
        # 适配Legacy API格式
        if getattr(self.app.api_client, 'legacy_api_available', False):
            return self._build_legacy_api_request(source_config, target_config, tables)
        else:
            return self._build_new_api_request(source_config, target_config, tables)
    
    def _build_legacy_api_request(self, source_config, target_config, tables) -> Dict[str, Any]:
        """构建Legacy API请求格式"""
        # 使用第一个表的SQL作为示例（Legacy API限制）
        first_table = tables[0] if tables else {}
        
        return {
            "source": {
                "type": source_config.type,
                "host": source_config.host,
                "port": source_config.port,
                "username": source_config.username,
                "password": source_config.password,
                "database": source_config.database
            },
            "target": {
                "type": target_config.type,
                "host": target_config.host,
                "port": target_config.port,
                "username": target_config.username,
                "password": target_config.password,
                "database": target_config.database
            },
            "config": {
                "mode": "query",
                "source_sql": first_table.get("sql_1", ""),
                "target_sql": first_table.get("sql_2", ""),
                "primary_keys": ["id"],  # 默认主键
                "batch_size": self.config.batch_size,
                "algorithm_type": "primary_key"
            }
        }
    
    def _build_new_api_request(self, source_config, target_config, tables) -> Dict[str, Any]:
        """构建新版本API请求格式"""
        return {
            "source_connection": {
                "db_type": source_config.type,
                "host": source_config.host,
                "port": source_config.port,
                "username": source_config.username,
                "password": source_config.password,
                "database": source_config.database
            },
            "target_connection": {
                "db_type": target_config.type,
                "host": target_config.host,
                "port": target_config.port,
                "username": target_config.username,
                "password": target_config.password,
                "database": target_config.database
            },
            "comparison_rules": [self._table_to_rule(table) for table in tables],
            "batch_size": self.config.batch_size
        }
    
    def _table_to_rule(self, table: Dict[str, Any]) -> Dict[str, Any]:
        """将表配置转换为比对规则"""
        return {
            "table_id": table['table_id'],
            "source_sql": table['sql_1'],
            "target_sql": table['sql_2'],
            "primary_keys": ["id"],  # 默认主键
            "remark": table.get('remark', '')
        }
    
    async def _submit_api_task(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """提交API任务"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.app.api_client.create_comparison_task,
            request_data
        )
    
    async def _monitor_api_task(self, task_id: str, 
                               progress_callback: ProgressCallback) -> UnifiedResult:
        """监控API任务"""
        start_time = time.time()
        
        while True:
            # 检查取消请求
            if self._check_cancellation():
                await self._cancel_api_task(task_id)
                return self._handle_cancellation()
            
            # 获取任务状态
            try:
                status_response = await self._get_task_status(task_id)
                
                if self._is_api_response_successful(status_response):
                    task_status = self._extract_task_status(status_response)
                    
                    # 更新进度
                    progress = self._create_progress(
                        current=task_status.get('progress', 0),
                        total=100,
                        message=task_status.get('message', '处理中...'),
                        stage="api_comparison",
                        start_time=start_time
                    )
                    progress_callback(progress)
                    self._log_progress(progress)
                    
                    # 检查任务完成
                    status = task_status.get('status')
                    if status in ['completed', 'failed', 'cancelled']:
                        if status == 'completed':
                            return await self._get_api_result(task_id)
                        else:
                            error_msg = task_status.get('error', f'任务{status}')
                            raise Exception(error_msg)
                else:
                    error_msg = self._extract_error_message(status_response)
                    raise Exception(f"获取任务状态失败: {error_msg}")
            
            except Exception as e:
                self.logger.error(f"监控API任务失败: {str(e)}")
                raise
            
            # 等待下次检查
            await asyncio.sleep(self.config.progress_update_interval)
    
    async def _get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.app.api_client.get_task_status,
            task_id
        )
    
    async def _get_api_result(self, task_id: str) -> UnifiedResult:
        """获取API结果"""
        try:
            loop = asyncio.get_event_loop()
            result_response = await loop.run_in_executor(
                None,
                self.app.api_client.get_task_result,
                task_id
            )
            
            if self._is_api_response_successful(result_response):
                task_result = self._extract_task_result(result_response)
                
                # 转换差异数据
                differences = self._convert_api_differences(task_result)
                
                # 提取摘要信息
                summary_data = self._extract_api_summary(task_result)
                
                # 创建成功结果
                return self._create_success_result(
                    differences=differences,
                    total_tables=summary_data.get('total_tables', 1),
                    completed_tables=summary_data.get('completed_tables', 1)
                )
            else:
                raise Exception("无法获取API任务结果")
                
        except Exception as e:
            return self._create_error_result(e)
    
    def _convert_api_differences(self, task_result: Dict[str, Any]) -> List[UnifiedDifference]:
        """转换API差异数据"""
        differences = []
        
        # 适配不同API版本的差异格式
        raw_differences = self._extract_differences_from_result(task_result)
        
        for diff in raw_differences:
            unified_diff = create_unified_difference(
                id=str(diff.get('key', diff.get('id', ''))),
                source_data=diff.get('source_record', diff.get('source_data', {})),
                target_data=diff.get('target_record', diff.get('target_data', {})),
                diff_fields=diff.get('diff_fields', []),
                diff_type=diff.get('diff_type', 'unknown'),
                table_id=diff.get('table_id', 'API'),
                source='api_comparison'
            )
            # 添加API特定的元数据
            unified_diff.metadata.update({
                'api_version': 'legacy' if getattr(self.app.api_client, 'legacy_api_available', False) else 'v1',
                'task_id': task_result.get('task_id', '')
            })
            differences.append(unified_diff)
        
        return differences
    
    def _extract_differences_from_result(self, task_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从任务结果中提取差异数据"""
        # Legacy API格式
        if "differences" in task_result:
            return task_result["differences"]
        
        # 新版本API格式
        if "details" in task_result:
            details = task_result["details"]
            if isinstance(details, list):
                return details
            elif isinstance(details, dict) and "items" in details:
                return details["items"]
        
        return []
    
    def _extract_api_summary(self, task_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取API摘要信息"""
        # Legacy API格式
        if "summary" in task_result:
            return task_result["summary"]
        
        # 新版本API格式 - 直接在结果中
        summary_fields = ["source_count", "target_count", "matched_count", "diff_count", 
                         "source_only_count", "target_only_count", "total_tables", "completed_tables"]
        if any(field in task_result for field in summary_fields):
            return {field: task_result.get(field, 0) for field in summary_fields}
        
        # 默认摘要
        return {
            'total_tables': 1,
            'completed_tables': 1,
            'total_differences': len(self._extract_differences_from_result(task_result))
        }
    
    async def _cancel_api_task(self, task_id: str):
        """取消API任务"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self.app.api_client.cancel_task,
                task_id
            )
            self.logger.info(f"API任务 {task_id} 已取消")
        except Exception as e:
            self.logger.error(f"取消API任务失败: {str(e)}")
    
    def _is_api_response_successful(self, response: dict) -> bool:
        """检查API响应是否成功"""
        return self.app._is_api_response_successful(response)
    
    def _extract_task_id(self, response: dict) -> str:
        """提取任务ID"""
        if "task_id" in response:
            return response["task_id"]
        elif "data" in response and "task_id" in response["data"]:
            return response["data"]["task_id"]
        else:
            raise Exception("无法从响应中提取任务ID")
    
    def _extract_task_status(self, response: dict) -> dict:
        """提取任务状态"""
        return self.app._extract_task_status_from_response(response)
    
    def _extract_task_result(self, response: dict) -> dict:
        """提取任务结果"""
        return self.app._extract_task_result_from_response(response)
    
    def _extract_error_message(self, response: dict) -> str:
        """提取错误消息"""
        return self.app._extract_error_message_from_response(response)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        # 检查API可用性
        if not self._is_api_available():
            self.logger.error("HTTP API服务不可用")
            return False
        
        # 检查表配置
        if 'tables' not in config:
            self.logger.error("配置中缺少 'tables' 字段")
            return False
        
        tables = config['tables']
        if not isinstance(tables, list) or len(tables) == 0:
            self.logger.error("tables 必须是非空列表")
            return False
        
        return True


class APIModeErrorMapper(ErrorMapper):
    """API模式错误映射器"""
    
    @staticmethod
    def map_error(error: Exception, mode: str = "api") -> 'UnifiedError':
        """映射API模式特定错误"""
        from .unified_models import create_unified_error
        
        error_msg = str(error)
        
        # HTTP相关错误
        if "timeout" in error_msg.lower():
            return create_unified_error(
                error_code="API_TIMEOUT",
                error_type="TimeoutError",
                message="API请求超时",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )
        elif "connection" in error_msg.lower():
            return create_unified_error(
                error_code="API_CONNECTION_ERROR",
                error_type="ConnectionError",
                message="API连接失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )
        elif "404" in error_msg or "not found" in error_msg.lower():
            return create_unified_error(
                error_code="API_NOT_FOUND",
                error_type="NotFoundError",
                message="API端点不存在",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=False
            )
        elif "401" in error_msg or "unauthorized" in error_msg.lower():
            return create_unified_error(
                error_code="API_UNAUTHORIZED",
                error_type="AuthenticationError",
                message="API认证失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=False
            )
        elif "500" in error_msg or "internal server error" in error_msg.lower():
            return create_unified_error(
                error_code="API_SERVER_ERROR",
                error_type="ServerError",
                message="API服务器内部错误",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )
        else:
            # 使用基类的通用映射
            return ErrorMapper.map_error(error, mode)
