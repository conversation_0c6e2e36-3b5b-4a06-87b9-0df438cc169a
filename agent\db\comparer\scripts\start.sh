#!/bin/bash

# 数据比对器代理启动脚本
# 作者: 数据比对器代理开发团队
# 版本: 2.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_step "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_step "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p results
    mkdir -p uploads
    mkdir -p data/postgres
    mkdir -p data/redis
    
    log_info "目录创建完成"
}

# 生成环境配置文件
generate_env_file() {
    log_step "生成环境配置文件..."
    
    if [ ! -f .env ]; then
        cat > .env << EOF
# 数据库配置
DATABASE_URL=postgresql://comparison_user:comparison_pass@localhost:5432/comparison_db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://:redis_pass@localhost:6379/0
REDIS_CACHE_TTL=3600

# Celery配置
CELERY_BROKER_URL=redis://:redis_pass@localhost:6379/1
CELERY_RESULT_BACKEND=redis://:redis_pass@localhost:6379/2

# 应用配置
DEBUG=false
HOST=0.0.0.0
PORT=8000
SECRET_KEY=$(openssl rand -hex 32)

# 文件存储配置
UPLOAD_DIR=./uploads
RESULT_DIR=./results
MAX_FILE_SIZE=104857600

# 比对配置
DEFAULT_BATCH_SIZE=1000
MAX_CONCURRENT_TASKS=5
COMPARISON_TIMEOUT=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5
EOF
        log_info "环境配置文件已生成: .env"
    else
        log_info "环境配置文件已存在，跳过生成"
    fi
}

# 启动服务
start_services() {
    log_step "启动数据比对器代理服务..."

    cd docker

    # 构建并启动服务
    docker-compose up -d --build

    log_info "服务启动中，请稍候..."

    # 等待服务启动
    sleep 30

    # 检查服务状态
    check_services_health
}

# 检查服务健康状态
check_services_health() {
    log_step "检查服务健康状态..."

    services=("postgres" "redis" "backend" "celery_worker")

    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "${service}.*Up"; then
            log_info "✓ ${service} 服务运行正常"
        else
            log_warn "✗ ${service} 服务可能存在问题"
        fi
    done
}

# 显示访问信息
show_access_info() {
    log_step "服务访问信息:"
    echo ""
    echo "📡 API文档: http://localhost:8000/docs"
    echo "🔍 API健康检查: http://localhost:8000/health"
    echo "📊 监控面板: http://localhost:3001 (admin/admin)"
    echo "🌐 API基础路径: http://localhost:8000/api/v1"
    echo ""
    log_info "数据比对器代理启动完成！"
}

# 主函数
main() {
    echo "========================================"
    echo "   数据比对器代理 v2.0 启动脚本"
    echo "========================================"
    echo ""
    
    check_prerequisites
    create_directories
    generate_env_file
    start_services
    show_access_info
    
    echo ""
    log_info "如需停止服务，请运行: ./scripts/stop.sh"
    log_info "如需查看日志，请运行: docker-compose logs -f"
}

# 执行主函数
main "$@"
