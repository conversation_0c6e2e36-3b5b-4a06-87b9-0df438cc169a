#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import site
import logging
# 全局标志，确保配置只运行一次
_db2_driver_configured = False

def configure_db2_driver() -> bool:
    """
    配置DB2驱动DLL路径

    Returns:
        bool: 配置成功返回True，失败返回False
    """
    global _db2_driver_configured

    # 如果已经配置过，直接返回
    if _db2_driver_configured:
        return True

    logger = logging.getLogger(__name__)
    logger.info("开始配置 IBM DB2 驱动...")

    # 可能的clidriver目录位置
    possible_paths = []

    # 使用环境变量的clidriver
    if os.environ.get('IBM_DB_HOME'):
        db2_home = os.environ.get('IBM_DB_HOME')
        possible_paths.append(os.path.join(db2_home, 'clidriver'))
        possible_paths.append(db2_home)  # 有时 IBM_DB_HOME 直接指向 clidriver

    # 1. site-packages中的clidriver
    try:
        for site_dir in site.getsitepackages():
            possible_paths.append(os.path.join(site_dir, 'clidriver'))
            possible_paths.append(os.path.join(site_dir, 'ibm_db', 'clidriver'))
    except:
        pass

    # 2. 当前工作目录
    possible_paths.append(os.path.join(os.getcwd(), 'clidriver'))

    # 3. 项目根目录
    script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    possible_paths.append(os.path.join(script_dir, 'clidriver'))

    # 4. Windows常见安装位置
    if sys.platform.startswith('win'):
        for drive in ['C:', 'D:', 'E:']:
            possible_paths.extend([
                f"{drive}\\IBM\\clidriver",
                f"{drive}\\Program Files\\IBM\\clidriver",
                f"{drive}\\Program Files (x86)\\IBM\\clidriver",
                f"{drive}\\Program Files\\IBM\\SQLLIB\\BIN",
                f"{drive}\\Program Files (x86)\\IBM\\SQLLIB\\BIN"
            ])
    # 5. Linux/Mac常见安装位置
    else:
        possible_paths.extend([
            '/opt/ibm/clidriver',
            '/opt/IBM/clidriver',
            '/usr/local/clidriver',
            '/opt/ibm/db2/V11.5',
            '/opt/ibm/db2/V11.1',
            '/home/<USER>/sqllib',
            os.path.expanduser('~/clidriver')
        ])

    # 检查每个可能的路径
    for path in possible_paths:
        if os.path.isdir(path):
            # 检查是否是有效的clidriver目录
            bin_dir = os.path.join(path, 'bin')
            lib_dir = os.path.join(path, 'lib')

            if os.path.exists(bin_dir) or os.path.exists(lib_dir):
                # 找到了有效的clidriver目录
                os.environ['IBM_DB_HOME'] = path

                # 更新PATH或LD_LIBRARY_PATH
                if sys.platform.startswith('win'):
                    # Windows
                    current_path = os.environ.get('PATH', '')
                    new_paths = []
                    if os.path.exists(bin_dir):
                        new_paths.append(bin_dir)
                    if os.path.exists(lib_dir):
                        new_paths.append(lib_dir)

                    for new_path in new_paths:
                        if new_path not in current_path:
                            os.environ['PATH'] = f"{new_path};{current_path}"
                            current_path = os.environ['PATH']

                    # Windows 特殊处理：添加 DLL 目录
                    try:
                        if hasattr(os, 'add_dll_directory') and os.path.exists(bin_dir):
                            os.add_dll_directory(bin_dir)
                            logger.info(f"添加DLL目录: {bin_dir}")
                    except Exception as e:
                        logger.warning(f"添加DLL目录失败: {e}")
                else:
                    # Linux/Mac
                    lib_paths = []
                    if os.path.exists(lib_dir):
                        lib_paths.append(lib_dir)
                    lib64_dir = os.path.join(path, 'lib64')
                    if os.path.exists(lib64_dir):
                        lib_paths.append(lib64_dir)

                    # 更新 LD_LIBRARY_PATH (Linux) 或 DYLD_LIBRARY_PATH (Mac)
                    lib_env_var = 'DYLD_LIBRARY_PATH' if sys.platform == 'darwin' else 'LD_LIBRARY_PATH'
                    current_lib_path = os.environ.get(lib_env_var, '')

                    for lib_path in lib_paths:
                        if lib_path not in current_lib_path:
                            if current_lib_path:
                                os.environ[lib_env_var] = f"{lib_path}:{current_lib_path}"
                            else:
                                os.environ[lib_env_var] = lib_path
                            current_lib_path = os.environ[lib_env_var]

                logger.info(f"IBM DB2 driver found at: {path}")
                _db2_driver_configured = True  # 标记为已配置
                return True

    logger.warning("IBM DB2 driver not found. Please install it manually.")
    _db2_driver_configured = True  # 即使失败也标记为已尝试配置，避免重复尝试
    return False


def get_db2_driver_status() -> dict:
    """
    获取DB2驱动配置状态

    Returns:
        dict: 包含驱动状态信息的字典
    """
    return {
        'configured': _db2_driver_configured,
        'ibm_db_home': os.environ.get('IBM_DB_HOME'),
        'path_contains_db2': any('clidriver' in path or 'IBM' in path for path in os.environ.get('PATH', '').split(os.pathsep)),
        'platform': sys.platform
    }


def ensure_db2_driver_configured() -> bool:
    """
    确保DB2驱动已配置（如果尚未配置则进行配置）

    Returns:
        bool: 配置成功返回True，失败返回False
    """
    if not _db2_driver_configured:
        return configure_db2_driver()
    return True


# 模块级别的初始化：自动配置DB2驱动
# 这确保在导入此模块时自动进行配置
if not _db2_driver_configured:
    configure_db2_driver()