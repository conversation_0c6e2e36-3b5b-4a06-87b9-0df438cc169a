#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查API端点
"""

import os
import sys
from fastapi import APIRouter, Depends
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.models.base import APIResponse, HealthResponse
from backend.core.dependencies import get_service_status, get_sqlalchemy_service

router = APIRouter()


@router.get("/", response_model=HealthResponse)
async def health_check():
    """基础健康检查"""
    return HealthResponse(
        status="healthy",
        message="SQLCompare Backend API is running",
        version="1.0.0"
    )


@router.get("/detailed", response_model=APIResponse)
async def detailed_health_check():
    """详细健康检查 - 包含服务状态"""
    try:
        # 获取服务状态
        service_status = get_service_status()
        
        # 测试数据库连接
        try:
            sqlalchemy_service = get_sqlalchemy_service()
            # 简单的数据库连接测试
            database_status = "connected"
        except Exception as e:
            database_status = f"error: {str(e)}"
        
        health_data = {
            "status": "healthy",
            "version": "1.0.0",
            "database": database_status,
            "services": service_status.get("services", {}),
            "database_url": service_status.get("database_url", "unknown")
        }
        
        return APIResponse.success_response(data=health_data)
        
    except Exception as e:
        return APIResponse.error_response(
            message="健康检查失败",
            error_details=str(e)
        )


@router.get("/services", response_model=APIResponse)
async def service_status():
    """服务状态检查"""
    try:
        status = get_service_status()
        return APIResponse.success_response(data=status)
    except Exception as e:
        return APIResponse.error_response(
            message="获取服务状态失败",
            error_details=str(e)
        )
