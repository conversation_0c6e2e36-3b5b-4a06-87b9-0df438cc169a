"""
比对管理API端点
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import uuid
import logging

from backend.services.comparison_service import ComparisonService
from backend.models.comparison_models import (
    ComparisonTaskCreate, ComparisonTaskResponse, 
    ComparisonResultResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)


class ComparisonRequest(BaseModel):
    """比对请求模型"""
    comparison_type: str  # "structure" 或 "content"
    source_connection: Dict[str, Any]
    target_connection: Dict[str, Any]
    table_name: str
    sql_rules: Optional[Dict[str, str]] = None
    batch_size: int = 1000
    timeout: int = 3600


class ComparisonResponse(BaseModel):
    """比对响应模型"""
    task_id: str
    status: str
    message: str


@router.post("/start", response_model=ComparisonResponse)
async def start_comparison(
    request: ComparisonRequest,
    background_tasks: BackgroundTasks,
    comparison_service: ComparisonService = Depends()
):
    """启动比对任务"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建比对任务
        task = ComparisonTaskCreate(
            task_id=task_id,
            comparison_type=request.comparison_type,
            source_connection=request.source_connection,
            target_connection=request.target_connection,
            table_name=request.table_name,
            sql_rules=request.sql_rules,
            batch_size=request.batch_size,
            timeout=request.timeout
        )
        
        # 验证任务
        is_valid, error_msg = await comparison_service.validate_task(task)
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 提交后台任务
        background_tasks.add_task(
            comparison_service.execute_comparison_task, task
        )
        
        logger.info(f"比对任务 {task_id} 已启动")
        
        return ComparisonResponse(
            task_id=task_id,
            status="started",
            message="比对任务已启动，正在后台执行"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动比对任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动比对任务失败: {str(e)}")


@router.get("/status/{task_id}")
async def get_comparison_status(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """获取比对任务状态"""
    try:
        status = await comparison_service.get_task_status(task_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/result/{task_id}", response_model=ComparisonResultResponse)
async def get_comparison_result(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """获取比对结果"""
    try:
        result = await comparison_service.get_comparison_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="比对结果不存在")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取比对结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取比对结果失败: {str(e)}")


@router.delete("/task/{task_id}")
async def cancel_comparison_task(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """取消比对任务"""
    try:
        success = await comparison_service.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": f"任务 {task_id} 已取消"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/tasks")
async def list_comparison_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    comparison_service: ComparisonService = Depends()
):
    """获取比对任务列表"""
    try:
        tasks = await comparison_service.list_tasks(
            status=status, limit=limit, offset=offset
        )
        
        return {
            "tasks": tasks,
            "total": len(tasks),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.post("/validate")
async def validate_comparison_config(
    request: ComparisonRequest,
    comparison_service: ComparisonService = Depends()
):
    """验证比对配置"""
    try:
        # 创建临时任务用于验证
        task = ComparisonTaskCreate(
            task_id="validation",
            comparison_type=request.comparison_type,
            source_connection=request.source_connection,
            target_connection=request.target_connection,
            table_name=request.table_name,
            sql_rules=request.sql_rules,
            batch_size=request.batch_size,
            timeout=request.timeout
        )
        
        # 执行验证
        is_valid, error_msg = await comparison_service.validate_task(task)
        
        return {
            "valid": is_valid,
            "message": error_msg if not is_valid else "配置验证通过"
        }
        
    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")


@router.get("/types")
async def get_comparison_types():
    """获取支持的比对类型"""
    return {
        "types": [
            {
                "type": "structure",
                "name": "表结构比对",
                "description": "比对两个表的结构差异，包括字段类型、约束等"
            },
            {
                "type": "content", 
                "name": "表内容比对",
                "description": "基于SQL规则比对两个表的数据内容差异"
            }
        ]
    }
