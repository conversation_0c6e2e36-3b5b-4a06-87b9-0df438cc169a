#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一适配器模块
提供三种比对模式的统一接口和数据格式
"""

from .unified_models import (
    UnifiedResult, UnifiedDifference, UnifiedProgress, UnifiedSummary,
    UnifiedError, UnifiedConfig, ComparisonMode, DifferenceType,
    ProgressCallback, ErrorCallback, CompletionCallback,
    create_unified_difference, create_unified_summary, create_unified_error,
    DEFAULT_CONFIG
)

from .base_adapter import ComparisonAdapter, ErrorMapper, PerformanceMonitor

from .direct_adapter import DirectComparisonAdapter, DirectModeErrorMapper
from .api_adapter import APIComparisonAdapter, APIModeErrorMapper
from .local_adapter import LocalComparisonAdapter, LocalModeErrorMapper, LocalServiceCompatibilityChecker

__all__ = [
    # 统一数据模型
    'UnifiedResult', 'UnifiedDifference', 'UnifiedProgress', 'UnifiedSummary',
    'UnifiedError', 'UnifiedConfig', 'ComparisonMode', 'DifferenceType',

    # 回调类型
    'ProgressCallback', 'ErrorCallback', 'CompletionCallback',

    # 工具函数
    'create_unified_difference', 'create_unified_summary', 'create_unified_error',

    # 基类
    'ComparisonAdapter', 'ErrorMapper', 'PerformanceMonitor',

    # 具体适配器
    'DirectComparisonAdapter', 'APIComparisonAdapter', 'LocalComparisonAdapter',

    # 错误映射器
    'DirectModeErrorMapper', 'APIModeErrorMapper', 'LocalModeErrorMapper',

    # 工具类
    'LocalServiceCompatibilityChecker',

    # 默认配置
    'DEFAULT_CONFIG'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'DB-Agent Team'
__description__ = '数据库比对统一适配器'
