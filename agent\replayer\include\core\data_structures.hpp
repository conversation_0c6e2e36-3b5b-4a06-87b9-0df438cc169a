#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <array>

namespace arkreplay {
namespace core {

// ==================== 基础类型定义 ====================

using Timestamp = std::chrono::high_resolution_clock::time_point;
using Duration = std::chrono::nanoseconds;
using MessageId = uint64_t;
using SessionId = uint64_t;
using SequenceNumber = uint64_t;

// ==================== 枚举定义 ====================

enum class ProtocolType : uint8_t {
    UNKNOWN = 0,
    KCXP = 1,
    FIX = 2,
    MID = 3
};

enum class MessageType : uint8_t {
    UNKNOWN = 0,
    REQUEST = 1,
    RESPONSE = 2,
    HEARTBEAT = 3,
    LOGON = 4,
    LOGOUT = 5
};

enum class ReplayStatus : uint8_t {
    PENDING = 0,
    RUNNING = 1,
    PAUSED = 2,
    COMPLETED = 3,
    FAILED = 4,
    CANCELLED = 5
};

enum class MessageStatus : uint8_t {
    PENDING = 0,
    SENT = 1,
    RECEIVED = 2,
    TIMEOUT = 3,
    ERROR = 4
};

// ==================== 核心数据结构 ====================

/**
 * 消息头结构 - 内存对齐优化
 * 总大小: 64字节，缓存行友好
 */
struct alignas(64) MessageHeader {
    MessageId message_id;           // 8字节 - 消息唯一标识
    SessionId session_id;           // 8字节 - 会话标识
    SequenceNumber sequence_num;    // 8字节 - 序列号
    Timestamp timestamp;            // 8字节 - 时间戳
    uint32_t body_length;           // 4字节 - 消息体长度
    ProtocolType protocol;          // 1字节 - 协议类型
    MessageType msg_type;           // 1字节 - 消息类型
    MessageStatus status;           // 1字节 - 消息状态
    uint8_t reserved[21];           // 21字节 - 保留字段，确保64字节对齐
    
    MessageHeader() noexcept 
        : message_id(0), session_id(0), sequence_num(0)
        , timestamp{}, body_length(0)
        , protocol(ProtocolType::UNKNOWN)
        , msg_type(MessageType::UNKNOWN)
        , status(MessageStatus::PENDING)
        , reserved{} {}
};

static_assert(sizeof(MessageHeader) == 64, "MessageHeader must be 64 bytes");

/**
 * 消息体结构 - 支持零拷贝
 */
class MessageBody {
private:
    std::unique_ptr<uint8_t[]> data_;
    size_t size_;
    size_t capacity_;

public:
    MessageBody() noexcept : data_(nullptr), size_(0), capacity_(0) {}
    
    explicit MessageBody(size_t capacity) 
        : data_(std::make_unique<uint8_t[]>(capacity))
        , size_(0), capacity_(capacity) {}
    
    // 移动构造函数 - 零拷贝
    MessageBody(MessageBody&& other) noexcept 
        : data_(std::move(other.data_))
        , size_(other.size_), capacity_(other.capacity_) {
        other.size_ = 0;
        other.capacity_ = 0;
    }
    
    MessageBody& operator=(MessageBody&& other) noexcept {
        if (this != &other) {
            data_ = std::move(other.data_);
            size_ = other.size_;
            capacity_ = other.capacity_;
            other.size_ = 0;
            other.capacity_ = 0;
        }
        return *this;
    }
    
    // 禁用拷贝构造和拷贝赋值
    MessageBody(const MessageBody&) = delete;
    MessageBody& operator=(const MessageBody&) = delete;
    
    uint8_t* data() noexcept { return data_.get(); }
    const uint8_t* data() const noexcept { return data_.get(); }
    size_t size() const noexcept { return size_; }
    size_t capacity() const noexcept { return capacity_; }
    
    void resize(size_t new_size) {
        if (new_size <= capacity_) {
            size_ = new_size;
        }
    }
    
    bool empty() const noexcept { return size_ == 0; }
};

/**
 * 完整消息结构
 */
struct Message {
    MessageHeader header;
    MessageBody body;
    
    Message() = default;
    Message(Message&&) = default;
    Message& operator=(Message&&) = default;
    
    // 禁用拷贝
    Message(const Message&) = delete;
    Message& operator=(const Message&) = delete;
};

/**
 * 会话信息结构
 */
struct SessionInfo {
    SessionId session_id;
    std::string connection_string;
    ProtocolType protocol;
    Timestamp created_at;
    Timestamp last_activity;
    std::atomic<bool> is_active;
    std::atomic<uint64_t> message_count;
    
    SessionInfo(SessionId id, const std::string& conn_str, ProtocolType proto)
        : session_id(id), connection_string(conn_str), protocol(proto)
        , created_at(std::chrono::high_resolution_clock::now())
        , last_activity(created_at), is_active(true), message_count(0) {}
};

/**
 * 回放配置结构
 */
struct ReplayConfig {
    double speed_multiplier = 1.0;     // 回放速度倍数
    bool preserve_timing = true;        // 是否保持原始时序
    size_t max_concurrent_sessions = 100; // 最大并发会话数
    Duration timeout = std::chrono::seconds(30); // 超时时间
    size_t buffer_size = 1024 * 1024;  // 缓冲区大小
    bool enable_metrics = true;         // 是否启用性能监控
    std::string log_level = "INFO";     // 日志级别
};

/**
 * 回放统计信息
 */
struct ReplayStatistics {
    std::atomic<uint64_t> total_messages{0};
    std::atomic<uint64_t> sent_messages{0};
    std::atomic<uint64_t> received_messages{0};
    std::atomic<uint64_t> failed_messages{0};
    std::atomic<uint64_t> timeout_messages{0};
    
    Timestamp start_time;
    std::atomic<Timestamp> last_update_time;
    
    ReplayStatistics() 
        : start_time(std::chrono::high_resolution_clock::now())
        , last_update_time(start_time) {}
    
    double get_success_rate() const noexcept {
        uint64_t total = total_messages.load();
        if (total == 0) return 0.0;
        return static_cast<double>(sent_messages.load()) / total * 100.0;
    }
    
    double get_throughput() const noexcept {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        if (duration.count() == 0) return 0.0;
        return static_cast<double>(sent_messages.load()) / duration.count();
    }
};

/**
 * 错误信息结构
 */
struct ErrorInfo {
    uint32_t error_code;
    std::string error_message;
    std::string error_details;
    Timestamp timestamp;
    MessageId related_message_id;
    
    ErrorInfo(uint32_t code, const std::string& msg, MessageId msg_id = 0)
        : error_code(code), error_message(msg), related_message_id(msg_id)
        , timestamp(std::chrono::high_resolution_clock::now()) {}
};

/**
 * 内存池配置
 */
struct MemoryPoolConfig {
    size_t initial_pool_size = 1024 * 1024;  // 1MB初始大小
    size_t max_pool_size = 100 * 1024 * 1024; // 100MB最大大小
    size_t block_size = 4096;                 // 4KB块大小
    bool enable_statistics = true;            // 启用统计
};

/**
 * 性能指标结构
 */
struct PerformanceMetrics {
    std::atomic<uint64_t> memory_allocated{0};
    std::atomic<uint64_t> memory_freed{0};
    std::atomic<uint64_t> cache_hits{0};
    std::atomic<uint64_t> cache_misses{0};
    std::atomic<uint64_t> network_bytes_sent{0};
    std::atomic<uint64_t> network_bytes_received{0};

    double get_cache_hit_rate() const noexcept {
        uint64_t total = cache_hits.load() + cache_misses.load();
        if (total == 0) return 0.0;
        return static_cast<double>(cache_hits.load()) / total * 100.0;
    }
};

} // namespace core
} // namespace arkreplay
