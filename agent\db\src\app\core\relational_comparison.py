"""
关系型数据库比对引擎

提供关系型数据库之间的数据比对功能。
"""
import time
import asyncio
import traceback
from typing import Dict, List, Any, Optional, Callable, Tuple

from app.models.db import DatabaseConnection
from app.models.task import (
    CompareConfig, TaskStats, ComparisonSummary,
    TaskStatus, AlgorithmType
)
from app.core.comparison_base import BaseComparisonEngine
from app.core.data_source import RelationalDBAdapter
# 算法相关导入已移至基类
from app.core.smart_partitioning import SmartPartitioningStrategy
from app.core.diff_analyzer import DifferenceAnalyzer
from app.models.logger import logger


class RelationalDBComparisonEngine(BaseComparisonEngine):
    """关系型数据库比对引擎"""

    def __init__(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig,
        progress_callback: Optional[Callable[[int, TaskStats], None]] = None
    ):
        """
        初始化关系型数据库比对引擎

        Args:
            source_conn: 源数据库连接信息
            target_conn: 目标数据库连接信息
            config: 比对配置
            progress_callback: 进度回调函数
        """
        super().__init__(source_conn, target_conn, config, progress_callback)

        # 数据源适配器
        self.source_adapter = RelationalDBAdapter(source_conn)
        self.target_adapter = RelationalDBAdapter(target_conn)

        # 算法初始化已在基类中完成，这里不需要重复

        # 初始化智能分区策略
        self.partitioning_strategy = SmartPartitioningStrategy()

        # 初始化差异分析器
        self.diff_analyzer = DifferenceAnalyzer()

        # 批处理大小
        self.batch_size = config.batch_size if config.batch_size else 1000

        # 查询缓存
        self._query_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0
        self._cache_enabled = True  # 可以通过配置控制是否启用缓存
        self._max_cache_size = 100  # 最大缓存条目数

        # 比对结果
        self._comparison_results = {
            "matched": [],
            "diff": [],
            "source_only": [],
            "target_only": []
        }

        # 进度更新阈值
        self.progress_threshold = 100

    async def initialize(self) -> bool:
        """
        初始化比对引擎

        Returns:
            bool: 初始化是否成功
        """
        await super().initialize()

        try:
            # 验证比对配置
            if not self._validate_config():
                logger.error("比对配置验证失败，终止比对")
                self.status = TaskStatus.FAILED
                return False

            # 连接数据源
            source_connected = await self.source_adapter.connect()
            if not source_connected:
                logger.error(f"无法连接到源数据库: {self.source_conn.host}:{self.source_conn.port}")
                self.status = TaskStatus.FAILED
                return False

            target_connected = await self.target_adapter.connect()
            if not target_connected:
                logger.error(f"无法连接到目标数据库: {self.target_conn.host}:{self.target_conn.port}")
                self.status = TaskStatus.FAILED
                return False

            # 全面的预检查机制（新增）
            if not await self._comprehensive_precheck():
                return False

            # 获取源和目标表的记录总数（用于进度计算）
            if not await self._get_record_counts():
                return False

            logger.info(f"源记录数: {self.stats.source_count}, 目标记录数: {self.stats.target_count}")

            # 计算进度阈值
            source_count = max(0, self.stats.source_count)
            target_count = max(0, self.stats.target_count)
            total_records = max(source_count, target_count)
            self.progress_threshold = min(max(int(total_records / 100), 10), 1000)

            # 初始化比对结果存储
            self._comparison_results = {
                "matched": [],
                "diff": [],
                "source_only": [],
                "target_only": []
            }

            return True
        except Exception as e:
            logger.exception(f"初始化比对引擎失败: {str(e)}")
            self.status = TaskStatus.FAILED
            return False

    def _validate_config(self) -> bool:
        """
        验证比对配置

        Returns:
            bool: 配置是否有效
        """
        # 检查模式
        if not hasattr(self.config, 'mode') or not self.config.mode:
            logger.error("未指定比对模式")
            return False

        # 检查表模式的配置
        if self.config.mode == "table":
            if not hasattr(self.config, 'source_table') or not self.config.source_table:
                logger.error("未指定源表名")
                return False

            if not hasattr(self.config, 'target_table') or not self.config.target_table:
                logger.error("未指定目标表名")
                return False

        # 检查查询模式的配置
        elif self.config.mode == "query":
            if not hasattr(self.config, 'source_query') or not self.config.source_query:
                logger.error("未指定源查询")
                return False

            if not hasattr(self.config, 'target_query') or not self.config.target_query:
                logger.error("未指定目标查询")
                return False

        # 检查主键
        if not hasattr(self.config, 'primary_keys') or not self.config.primary_keys:
            logger.warning("未指定主键，将使用全字段比对")

        # 检查比对字段
        if not hasattr(self.config, 'compare_fields') or not self.config.compare_fields:
            logger.warning("未指定比对字段，将使用所有字段进行比对")

        # 检查批处理大小
        if not hasattr(self.config, 'batch_size') or not self.config.batch_size:
            self.config.batch_size = 1000
            logger.info(f"未指定批处理大小，使用默认值: {self.config.batch_size}")

        return True

    async def run(self) -> ComparisonSummary:
        """
        执行比对操作

        Returns:
            ComparisonSummary: 比对结果摘要
        """
        try:
            # 初始化比对引擎
            if not await self.initialize():
                return self.build_summary()

            # 如果源和目标表均为空，无需比对
            if self.stats.source_count == 0 and self.stats.target_count == 0:
                logger.warning("源和目标表均为空，无需比对")
                return self.build_summary()

            # 智能选择并使用合适的算法进行比对
            algorithm_type = self._select_optimal_algorithm()

            if algorithm_type == AlgorithmType.HASH_BASED:
                # 使用基于哈希的比对算法
                logger.info("使用基于哈希的比对算法")
                await self._partitioned_comparison("基于哈希的比对算法")
            elif algorithm_type == AlgorithmType.TWO_PHASE:
                # 使用两阶段比对算法
                logger.info("使用两阶段比对算法")
                await self._partitioned_comparison("两阶段比对算法")
            else:
                # 使用默认的主键比对算法
                logger.info("使用默认的主键比对算法")
                await self._standard_comparison()

            # 构建并返回比对结果
            return self.build_summary()
        except Exception as e:
            logger.exception(f"比对过程中发生错误: {str(e)}")
            self.status = TaskStatus.FAILED

            # 确保所有必要的属性都存在
            if not hasattr(self.stats, 'matched_count') or self.stats.matched_count is None:
                self.stats.matched_count = 0

            if not hasattr(self.stats, 'source_only_count') or self.stats.source_only_count is None:
                self.stats.source_only_count = 0

            if not hasattr(self.stats, 'target_only_count') or self.stats.target_only_count is None:
                self.stats.target_only_count = 0

            if not hasattr(self.stats, 'diff_count') or self.stats.diff_count is None:
                self.stats.diff_count = 0

            # 检查是否已经设置了错误信息
            if not hasattr(self, '_error_info') or not self._error_info:
                # 根据异常类型设置相应的错误信息
                from app.core.error_utils import TableNotFoundError, SQLSyntaxError, DatabaseValidationError
                if isinstance(e, TableNotFoundError):
                    self._set_error_info("TABLE_NOT_FOUND", str(e),
                                       "请检查表名是否正确，确保表存在于指定的数据库中")
                elif isinstance(e, SQLSyntaxError):
                    self._set_error_info("SQL_SYNTAX_ERROR", str(e),
                                       "请检查SQL语句语法是否正确，特别注意表名、字段名和WHERE条件")
                elif isinstance(e, DatabaseValidationError):
                    self._set_error_info("DATABASE_VALIDATION_ERROR", str(e),
                                       "请检查数据库连接配置和权限设置")
                else:
                    self._set_error_info("COMPARISON_EXECUTION_ERROR", str(e),
                                       "数据比对执行过程中发生错误，请检查配置和日志")

            # 构建并返回比对结果
            return self.build_summary()
        finally:
            # 清理资源
            await self.cleanup()

    async def get_source_data(self, partition=None) -> List[Dict[str, Any]]:
        """
        获取源数据

        Args:
            partition: 分区信息，用于分批获取数据

        Returns:
            List[Dict[str, Any]]: 源数据列表
        """
        return await self._get_partition_data(True, partition)

    async def get_target_data(self, partition=None) -> List[Dict[str, Any]]:
        """
        获取目标数据

        Args:
            partition: 分区信息，用于分批获取数据

        Returns:
            List[Dict[str, Any]]: 目标数据列表
        """
        return await self._get_partition_data(False, partition)

    async def _get_partition_data(self, is_source: bool, partition_range=None) -> List[Dict[str, Any]]:
        """
        获取指定分区的数据

        Args:
            is_source: 是否是源数据库
            partition_range: 分区范围，为(start, end)元组或None

        Returns:
            List[Dict[str, Any]]: 分区数据列表
        """
        # 性能监控：记录查询开始时间
        query_start_time = time.time()
        self._performance_metrics['total_queries'] += 1

        mode = self.config.mode
        db_type = "源" if is_source else "目标"
        adapter = self.source_adapter if is_source else self.target_adapter

        try:
            # 构建查询
            if partition_range is None:
                # 无分区范围，使用普通查询
                if mode == "table":
                    table_name = self.config.source_table if is_source else self.config.target_table
                    where_clause = self.config.where_clause or "1=1"

                    # 构建字段列表
                    fields = "*"
                    compare_columns = getattr(self.config, 'compare_columns', None)
                    if compare_columns:
                        # 确保主键字段在比对字段中
                        all_fields = set(compare_columns)
                        for pk in self.config.primary_keys:
                            all_fields.add(pk)
                        fields = ", ".join(all_fields)

                    query = f"""
                    SELECT {fields} FROM {table_name}
                    WHERE {where_clause}
                    """

                    # 添加排序和限制
                    if self.config.primary_keys:
                        query += f" ORDER BY {', '.join(self.config.primary_keys)}"

                    if hasattr(self.config, 'limit') and self.config.limit:
                        query += f" LIMIT {self.config.limit}"
                else:  # query模式
                    base_query = self.config.source_query if is_source else self.config.target_query
                    # 移除查询末尾的分号
                    if base_query:
                        base_query = base_query.strip().rstrip(';')
                    query = base_query
            else:
                # 有分区范围，添加分区条件
                start, end = partition_range
                pk = self.config.primary_keys[0] if self.config.primary_keys else None  # 使用第一个主键作为分区键

                if not pk:
                    logger.warning(f"未指定主键，无法进行分区查询，将使用完整查询")
                    return await self.get_source_data() if is_source else await self.get_target_data()

                if mode == "table":
                    table_name = self.config.source_table if is_source else self.config.target_table
                    where_clause = self.config.where_clause or "1=1"

                    # 构建字段列表
                    fields = "*"
                    compare_columns = getattr(self.config, 'compare_columns', None)
                    if compare_columns:
                        # 确保主键字段在比对字段中
                        all_fields = set(compare_columns)
                        for pk_field in self.config.primary_keys:
                            all_fields.add(pk_field)
                        fields = ", ".join(all_fields)

                    # 构建分区条件
                    partition_condition = ""
                    if start is not None and end is not None:
                        partition_condition = f" AND {pk} >= {start} AND {pk} < {end}"
                    elif start is not None:
                        partition_condition = f" AND {pk} >= {start}"
                    elif end is not None:
                        partition_condition = f" AND {pk} < {end}"

                    query = f"""
                    SELECT {fields} FROM {table_name}
                    WHERE {where_clause}{partition_condition}
                    """

                    # 添加排序
                    if self.config.primary_keys:
                        query += f" ORDER BY {', '.join(self.config.primary_keys)}"
                else:  # query模式
                    base_query = self.config.source_query if is_source else self.config.target_query
                    # 移除查询末尾的分号
                    if base_query:
                        base_query = base_query.strip().rstrip(';')

                    # 构建分区条件
                    partition_condition = ""
                    if start is not None and end is not None:
                        partition_condition = f" WHERE t.{pk} >= {start} AND t.{pk} < {end}"
                    elif start is not None:
                        partition_condition = f" WHERE t.{pk} >= {start}"
                    elif end is not None:
                        partition_condition = f" WHERE t.{pk} < {end}"

                    query = f"""
                    SELECT * FROM ({base_query}) AS t
                    {partition_condition}
                    """

                    # 添加排序
                    if self.config.primary_keys:
                        query += f" ORDER BY {', '.join([f't.{pk}' for pk in self.config.primary_keys])}"

            # 检查缓存
            if self._cache_enabled:
                # 使用查询语句和数据库标签作为缓存键
                cache_key = f"{db_type}:{query}"
                if cache_key in self._query_cache:
                    self._cache_hits += 1
                    logger.debug(f"缓存命中: {db_type} 查询")

                    # 更新性能指标
                    query_end_time = time.time()
                    self._performance_metrics['query_time'] += (query_end_time - query_start_time)

                    return self._query_cache[cache_key]
                else:
                    self._cache_misses += 1

            # 执行查询
            logger.debug(f"执行{db_type}数据查询: {query}")
            result = await adapter.execute_query(query)

            # 缓存结果
            if self._cache_enabled:
                # 限制缓存大小
                if len(self._query_cache) >= self._max_cache_size:
                    # 简单的LRU策略：移除第一个条目
                    self._query_cache.pop(next(iter(self._query_cache)))

                # 只缓存小结果集，避免内存占用过大
                if len(result) <= 1000:
                    self._query_cache[cache_key] = result

            # 更新性能指标
            query_end_time = time.time()
            self._performance_metrics['query_time'] += (query_end_time - query_start_time)

            logger.debug(f"查询{db_type}数据完成，获取到 {len(result)} 条记录")
            return result
        except Exception as e:
            logger.error(f"获取{db_type}数据失败: {str(e)}")
            # 更新性能指标
            query_end_time = time.time()
            self._performance_metrics['query_time'] += (query_end_time - query_start_time)
            return []

    async def compare_data(self, source_data: List[Dict[str, Any]], target_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        比对数据

        Args:
            source_data: 源数据列表
            target_data: 目标数据列表

        Returns:
            Dict[str, Any]: 比对结果
        """
        # 性能监控：记录比对开始时间
        comparison_start_time = time.time()

        # 使用选择的算法进行比对
        comparison_results = await self.algorithm.compare(source_data, target_data, self.config)

        # 更新性能指标
        comparison_end_time = time.time()
        self._performance_metrics['comparison_time'] += (comparison_end_time - comparison_start_time)

        return comparison_results

    async def analyze_differences(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析差异

        Args:
            comparison_results: 比对结果

        Returns:
            Dict[str, Any]: 分析后的比对结果
        """
        # 性能监控：记录分析开始时间
        analysis_start_time = time.time()

        try:
            # 如果差异数量过大，只分析一部分以提高性能
            max_items_to_analyze = 1000

            # 准备差异项列表
            diff_items = []

            # 处理值不同的记录
            diff_count = 0
            for diff in comparison_results.get("diff", []):
                if diff_count >= max_items_to_analyze:
                    break

                # 转换为差异分析器需要的格式
                diff_item = {
                    "diff_type": "value",
                    "key": diff.get("key", {}),
                    "source_value": diff.get("source", {}),
                    "target_value": diff.get("target", {}),
                    "diffs": diff.get("diffs", [])
                }
                diff_items.append(diff_item)
                diff_count += 1

            # 处理仅源存在的记录
            source_only_count = 0
            for record in comparison_results.get("source_only", []):
                if source_only_count >= max_items_to_analyze // 2:
                    break

                diff_item = {
                    "diff_type": "source_only",
                    "key": record.get("key", {}),
                    "source_record": record.get("record", {})
                }
                diff_items.append(diff_item)
                source_only_count += 1

            # 处理仅目标存在的记录
            target_only_count = 0
            for record in comparison_results.get("target_only", []):
                if target_only_count >= max_items_to_analyze // 2:
                    break

                diff_item = {
                    "diff_type": "target_only",
                    "key": record.get("key", {}),
                    "target_record": record.get("record", {})
                }
                diff_items.append(diff_item)
                target_only_count += 1

            # 使用差异分析器分析差异
            if diff_items:
                # 记录实际分析的差异数量
                total_analyzed = len(diff_items)
                total_differences = (
                    len(comparison_results.get("diff", [])) +
                    len(comparison_results.get("source_only", [])) +
                    len(comparison_results.get("target_only", []))
                )

                # 如果差异数量过大，记录日志
                if total_differences > max_items_to_analyze:
                    logger.info(f"差异数量过大 ({total_differences})，仅分析前 {total_analyzed} 项以提高性能")

                # 执行批量分析
                analysis_results = self.diff_analyzer.batch_analyze(diff_items)

                # 记录分析结果
                if not hasattr(self, '_analysis_results'):
                    self._analysis_results = {
                        "items": [],
                        "summary": None
                    }

                # 添加分析结果
                self._analysis_results["items"].extend(analysis_results["items"])
                self._analysis_results["summary"] = analysis_results["summary"]

                # 更新分析结果摘要中的总差异数
                if self._analysis_results["summary"]:
                    self._analysis_results["summary"]["total_differences"] = total_differences

                logger.info(f"差异分析完成: 总差异={total_differences}, 分析样本={total_analyzed}, 严重程度={analysis_results['summary']['overall_severity']}")

                # 更新性能指标
                analysis_end_time = time.time()
                self._performance_metrics['analysis_time'] += (analysis_end_time - analysis_start_time)

                # 返回原始比对结果，保持兼容性
                return comparison_results
            else:
                # 更新性能指标
                analysis_end_time = time.time()
                self._performance_metrics['analysis_time'] += (analysis_end_time - analysis_start_time)

                return comparison_results
        except Exception as e:
            logger.warning(f"差异分析失败: {str(e)}")

            # 更新性能指标
            analysis_end_time = time.time()
            self._performance_metrics['analysis_time'] += (analysis_end_time - analysis_start_time)

            # 返回原始比对结果
            return comparison_results

    def build_summary(self) -> ComparisonSummary:
        """
        构建比对结果摘要

        Returns:
            ComparisonSummary: 比对结果摘要
        """
        # 计算匹配记录数（如果未设置）
        if not hasattr(self.stats, 'matched_count') or self.stats.matched_count == 0:
            self.stats.matched_count = max(0, self.stats.source_count - self.stats.diff_count)

        # 确保source_only_count和target_only_count属性存在
        if not hasattr(self.stats, 'source_only_count'):
            self.stats.source_only_count = 0

        if not hasattr(self.stats, 'target_only_count'):
            self.stats.target_only_count = 0

        # 记录性能指标
        self._log_performance_metrics()

        # 构建错误信息
        error_info = None
        if hasattr(self, '_error_info') and self._error_info:
            error_info = self._error_info.copy()
            # 添加额外的错误上下文信息
            error_info.update({
                "task_status": self.status.value if hasattr(self.status, 'value') else str(self.status),
                "source_table": getattr(self.config, 'source_table', None),
                "target_table": getattr(self.config, 'target_table', None),
                "comparison_mode": getattr(self.config, 'mode', None)
            })
        elif self.status == TaskStatus.FAILED:
            # 如果任务失败但没有设置错误信息，创建默认错误信息
            error_info = {
                "type": "UNKNOWN_ERROR",
                "message": "任务执行失败，但未捕获到具体错误信息",
                "is_fatal": True,
                "suggestion": "请检查日志获取更多信息，或联系管理员",
                "timestamp": time.time(),
                "task_status": self.status.value if hasattr(self.status, 'value') else str(self.status)
            }

        return ComparisonSummary(
            source_count=self.stats.source_count,
            target_count=self.stats.target_count,
            matched_count=self.stats.matched_count,
            diff_count=self.stats.diff_count,
            source_only_count=self.stats.source_only_count,
            target_only_count=self.stats.target_only_count,
            error=error_info
        )

    async def cleanup(self) -> None:
        """
        清理资源

        Returns:
            None
        """
        # 断开数据源连接
        await self.source_adapter.disconnect()
        await self.target_adapter.disconnect()

        # 调用父类的清理方法
        await super().cleanup()

    # _select_optimal_algorithm 方法已移至基类，这里不需要重复实现

    async def _get_total_count(self, is_source: bool) -> int:
        """
        获取表或查询的总记录数

        Args:
            is_source: 是否是源数据库

        Returns:
            int: 记录总数，如果获取失败则返回-1
        """
        # 性能监控：记录查询开始时间
        query_start_time = time.time()
        self._performance_metrics['total_queries'] += 1

        mode = self.config.mode
        db_type = "源" if is_source else "目标"
        adapter = self.source_adapter if is_source else self.target_adapter

        try:
            # 构建查询
            if mode == "table":
                table_name = self.config.source_table if is_source else self.config.target_table
                where_clause = self.config.where_clause or "1=1"

                query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            else:
                # 确保移除查询中的分号，避免DB2语法错误
                base_query = self.config.source_query if is_source else self.config.target_query
                # 移除查询末尾的分号
                base_query = base_query.strip().rstrip(';') if base_query else ""
                query = f"SELECT COUNT(*) FROM ({base_query}) AS t"

            # 检查缓存
            if self._cache_enabled:
                # 使用查询语句和数据库标签作为缓存键
                cache_key = f"{db_type}:count:{query}"
                if cache_key in self._query_cache:
                    self._cache_hits += 1
                    logger.debug(f"缓存命中: {db_type} 记录总数查询")

                    # 更新性能指标
                    query_end_time = time.time()
                    self._performance_metrics['query_time'] += (query_end_time - query_start_time)

                    return self._query_cache[cache_key]
                else:
                    self._cache_misses += 1

            # 执行查询
            result = await adapter.execute_scalar(query)

            # 缓存结果
            if self._cache_enabled and result != -1:
                # 限制缓存大小
                if len(self._query_cache) >= self._max_cache_size:
                    # 简单的LRU策略：移除第一个条目
                    self._query_cache.pop(next(iter(self._query_cache)))

                self._query_cache[cache_key] = result

            # 更新性能指标
            query_end_time = time.time()
            self._performance_metrics['query_time'] += (query_end_time - query_start_time)

            # 如果没有记录，返回0
            if result == -1:
                logger.error(f"获取{db_type}表记录总数失败")
                return -1

            # 记录结果
            count = result or 0
            logger.info(f"获取{db_type}表记录总数成功: {count}")
            return count
        except Exception as e:
            # 更新性能指标
            query_end_time = time.time()
            self._performance_metrics['query_time'] += (query_end_time - query_start_time)

            # 详细的SQL错误检测和分类
            error_msg = str(e).lower()

            # 检查表不存在错误
            if any(keyword in error_msg for keyword in [
                'sql0204n', 'undefined name', 'table or view does not exist',
                'table does not exist', 'relation does not exist', 'object name'
            ]):
                from app.core.error_utils import TableNotFoundError
                logger.error(f"获取{db_type}表记录总数失败 - 表不存在: {str(e)}")
                raise TableNotFoundError(f"表不存在: {str(e)}")

            # 检查SQL语法错误 - 增强DB2错误代码检测
            elif any(keyword in error_msg for keyword in [
                'sql0206n',  # 列不存在
                'sql0104n',  # 语法错误
                'sql0901n',  # SQL语句过长
                'sql0902n',  # 数据类型不匹配
                'sql0911n',  # 死锁
                'syntax error', 'sqlstate=42601', 'sqlstate=42703',
                'invalid identifier', 'invalid column name',
                'not valid in the context'
            ]):
                from app.core.error_utils import SQLSyntaxError
                logger.error(f"获取{db_type}表记录总数失败 - SQL语法错误: {str(e)}")
                raise SQLSyntaxError(f"SQL语法错误: {str(e)}", query)

            # 检查权限错误
            elif any(keyword in error_msg for keyword in [
                'sql0551n', 'sql0552n', 'access denied', 'permission denied',
                'insufficient privileges', 'authorization failure'
            ]):
                from app.core.error_utils import DatabaseValidationError
                logger.error(f"获取{db_type}表记录总数失败 - 权限错误: {str(e)}")
                raise DatabaseValidationError(f"权限错误: {str(e)}", "PERMISSION")

            # 检查连接错误
            elif any(keyword in error_msg for keyword in [
                'sql30081n', 'sql08001', 'connection', 'timeout', 'network',
                'communication link failure', 'connection refused'
            ]):
                from app.core.error_utils import DatabaseValidationError
                logger.error(f"获取{db_type}表记录总数失败 - 连接错误: {str(e)}")
                raise DatabaseValidationError(f"连接错误: {str(e)}", "CONNECTION")

            # 其他未分类错误
            else:
                logger.error(f"获取{db_type}表记录总数失败: {str(e)}")
                # 返回-1表示获取记录总数失败
                return -1

    async def _get_partition_data(self, is_source: bool, partition_range=None) -> List[Dict[str, Any]]:
        """
        获取指定分区的数据

        Args:
            is_source: 是否是源数据库
            partition_range: 分区范围，为(start, end)元组或None

        Returns:
            List[Dict[str, Any]]: 分区数据列表
        """
        mode = self.config.mode
        db_type = "源" if is_source else "目标"
        adapter = self.source_adapter if is_source else self.target_adapter

        try:
            # 构建查询
            if partition_range is None:
                # 无分区范围，使用普通查询
                if mode == "table":
                    table_name = self.config.source_table if is_source else self.config.target_table
                    where_clause = self.config.where_clause or "1=1"

                    query = f"""
                    SELECT * FROM {table_name}
                    WHERE {where_clause}
                    """
                else:  # query模式
                    base_query = self.config.source_query if is_source else self.config.target_query
                    # 移除查询末尾的分号
                    if base_query:
                        base_query = base_query.strip().rstrip(';')
                    query = base_query
            else:
                # 有分区范围，添加分区条件
                start, end = partition_range
                pk = self.config.primary_keys[0]  # 使用第一个主键作为分区键

                if mode == "table":
                    table_name = self.config.source_table if is_source else self.config.target_table
                    where_clause = self.config.where_clause or "1=1"

                    query = f"""
                    SELECT * FROM {table_name}
                    WHERE {where_clause} AND {pk} >= {start} AND {pk} < {end}
                    """
                else:  # query模式
                    base_query = self.config.source_query if is_source else self.config.target_query
                    # 移除查询末尾的分号
                    if base_query:
                        base_query = base_query.strip().rstrip(';')

                    query = f"""
                    SELECT * FROM ({base_query}) AS t
                    WHERE t.{pk} >= {start} AND t.{pk} < {end}
                    """

            # 执行查询
            return await adapter.execute_query(query)
        except Exception as e:
            logger.error(f"获取{db_type}分区数据失败: {str(e)}")
            return []

    async def _standard_comparison(self):
        """
        标准比对策略 - 适用于中小数据集

        步骤:
        1. 批量读取源数据
        2. 批量读取目标数据
        3. 内存中高效比对
        4. 生成差异结果
        """
        # 使用算法的查询语句获取数据
        source_query = await self.algorithm.get_query_statements(self.config, True)
        target_query = await self.algorithm.get_query_statements(self.config, False)

        logger.info(f"执行源数据查询: {source_query}")
        logger.info(f"执行目标数据查询: {target_query}")

        # 获取源数据和目标数据
        source_data = []
        target_data = []

        # 获取源数据
        try:
            source_data = await self.source_adapter.execute_query(source_query)
        except Exception as e:
            logger.error(f"源数据查询失败: {str(e)}")
            source_data = []

        # 获取目标数据
        try:
            target_data = await self.target_adapter.execute_query(target_query)
        except Exception as e:
            logger.error(f"目标数据查询失败: {str(e)}")
            target_data = []

        logger.info(f"获取到源数据 {len(source_data)} 条，目标数据 {len(target_data)} 条")

        # 使用选择的算法进行比对
        comparison_results = await self.compare_data(source_data, target_data)

        # 更新比对统计信息
        matched_count = len(comparison_results["matched"])
        diff_count = len(comparison_results["diff"])
        source_only_count = len(comparison_results["source_only"])
        target_only_count = len(comparison_results["target_only"])

        logger.info(f"完全匹配: {matched_count}, 有差异: {diff_count}, 仅源存在: {source_only_count}, 仅目标存在: {target_only_count}")

        self.stats.diff_count = diff_count + source_only_count + target_only_count
        self.stats.matched_count = matched_count
        self.stats.source_only_count = source_only_count
        self.stats.target_only_count = target_only_count

        # 使用差异分析器分析差异
        await self.analyze_differences(comparison_results)

        # 记录比对结果详情
        self._save_comparison_results(comparison_results)

        # 保存比对结果到任务数据中，供后续查询使用
        if hasattr(self, 'task_id') and self.task_id:
            self._save_results_to_task(comparison_results)

        # 更新进度
        await self._update_progress(100)

    async def _partitioned_comparison(self, algorithm_name="分区比对"):
        """
        通用分区比对方法 - 适用于大数据集

        将数据集分区后逐个处理，支持不同的比对算法

        Args:
            algorithm_name: 算法名称（用于日志）
        """
        # 记录使用的算法名称
        logger.info(f"使用{algorithm_name}进行分区比对")

        # 使用智能分区策略创建分区
        partitions = await self._create_partitions()
        logger.info(f"数据集已划分为 {len(partitions)} 个分区进行处理")

        total_partitions = len(partitions)
        completed_partitions = 0

        # 统计总体结果
        total_matched_count = 0
        total_diff_count = 0
        total_source_only_count = 0
        total_target_only_count = 0

        # 内存优化：记录处理过的记录数，定期触发垃圾回收
        processed_records_since_gc = 0
        gc_threshold = 100000  # 处理10万条记录后触发垃圾回收

        # 逐个处理分区
        for i, partition_range in enumerate(partitions):
            # 检查是否取消
            if self._cancel_requested:
                logger.info("检测到取消请求，停止比对")
                break

            # 检查是否暂停
            await self._pause_event.wait()

            logger.info(f"处理分区 {i+1}/{total_partitions}: {partition_range}")

            try:
                # 使用分区条件获取源和目标数据
                source_records = await self.get_source_data(partition_range)
                target_records = await self.get_target_data(partition_range)

                logger.info(f"分区 {i+1} 获取到源数据 {len(source_records)} 条，目标数据 {len(target_records)} 条")

                # 记录处理的记录数
                processed_records_since_gc += len(source_records) + len(target_records)

                # 使用选择的算法比对当前分区数据
                comparison_results = await self.compare_data(source_records, target_records)

                # 统计当前分区结果
                matched_count = len(comparison_results["matched"])
                diff_count = len(comparison_results["diff"])
                source_only_count = len(comparison_results["source_only"])
                target_only_count = len(comparison_results["target_only"])

                # 累加到总结果
                total_matched_count += matched_count
                total_diff_count += diff_count
                total_source_only_count += source_only_count
                total_target_only_count += target_only_count

                # 使用差异分析器分析差异
                await self.analyze_differences(comparison_results)

                # 记录当前分区的比对结果
                self._save_comparison_results(comparison_results)

                # 更新比对统计信息
                self.stats.processed_count += len(source_records)
                self.stats.diff_count = total_diff_count + total_source_only_count + total_target_only_count
                self.stats.matched_count = total_matched_count
                self.stats.source_only_count = total_source_only_count
                self.stats.target_only_count = total_target_only_count

                # 更新分区完成进度
                completed_partitions += 1
                partition_progress = int((completed_partitions / total_partitions) * 100)
                await self._update_progress(min(99, partition_progress))

                logger.info(f"分区 {i+1} 比对完成，匹配: {matched_count}, 差异: {diff_count}, 仅源: {source_only_count}, 仅目标: {target_only_count}")

                # 内存优化：定期触发垃圾回收
                if processed_records_since_gc >= gc_threshold:
                    import gc
                    gc.collect()
                    processed_records_since_gc = 0
                    logger.debug("触发垃圾回收以优化内存使用")

                # 释放不再需要的大对象引用
                source_records = None
                target_records = None
                comparison_results = None

            except Exception as e:
                logger.error(f"处理分区 {i+1} 时出错: {str(e)}")
                # 继续处理下一个分区，而不是终止整个比对过程
                continue

        # 完成所有分区后，更新进度为100%
        await self._update_progress(100)

    async def _create_partitions(self) -> List[Tuple[Any, Any]]:
        """
        创建数据分区

        Returns:
            List[Tuple[Any, Any]]: 分区范围列表，每个元素为(start, end)元组
        """
        # 性能监控：记录开始时间
        start_time = time.time()

        # 使用智能分区策略创建分区
        try:
            # 检查是否有主键
            if not hasattr(self.config, 'primary_keys') or not self.config.primary_keys:
                logger.warning("未指定主键，无法进行分区，将使用单一分区")
                return [(None, None)]  # 使用单一分区

            # 获取分区键的最小值和最大值
            pk = self.config.primary_keys[0]  # 使用第一个主键作为分区键

            # 构建查询
            if self.config.mode == "table":
                table_name = self.config.source_table
                where_clause = self.config.where_clause or "1=1"

                min_query = f"SELECT MIN({pk}) FROM {table_name} WHERE {where_clause}"
                max_query = f"SELECT MAX({pk}) FROM {table_name} WHERE {where_clause}"
            else:
                base_query = self.config.source_query
                # 移除查询末尾的分号
                if base_query:
                    base_query = base_query.strip().rstrip(';')

                min_query = f"SELECT MIN({pk}) FROM ({base_query}) AS t"
                max_query = f"SELECT MAX({pk}) FROM ({base_query}) AS t"

            # 执行查询获取最小值和最大值
            logger.info(f"正在获取分区键 {pk} 的最小值: {min_query}")
            min_val = await self.source_adapter.execute_scalar(min_query)

            logger.info(f"正在获取分区键 {pk} 的最大值: {max_query}")
            max_val = await self.source_adapter.execute_scalar(max_query)

            if min_val is None or max_val is None or min_val == max_val:
                logger.warning(f"无法获取有效的分区键范围 (min={min_val}, max={max_val})，使用单一分区")
                return [(None, None)]  # 使用单一分区

            logger.info(f"分区键 {pk} 的范围: {min_val} - {max_val}")

            # 根据数据量和范围确定分区数量
            source_count = self.stats.source_count

            # 使用智能分区策略创建分区
            partitions = self.partitioning_strategy.create_partitions(
                min_val, max_val, source_count, self.config
            )

            # 记录分区信息
            logger.info(f"创建了 {len(partitions)} 个分区")
            for i, (start, end) in enumerate(partitions[:5]):  # 只记录前5个分区
                logger.debug(f"分区 {i+1}: {start} - {end}")

            if len(partitions) > 5:
                logger.debug(f"... 共 {len(partitions)} 个分区")

            # 记录耗时
            end_time = time.time()
            logger.debug(f"创建分区耗时: {end_time - start_time:.2f}秒")

            return partitions
        except Exception as e:
            # 记录耗时
            end_time = time.time()
            logger.error(f"创建分区失败: {str(e)}, 耗时: {end_time - start_time:.2f}秒")

            # 使用单一分区
            return [(None, None)]

    def _save_comparison_results(self, comparison_results):
        """
        保存比对结果，供后续查询使用

        Args:
            comparison_results: 比对结果
        """
        # 获取当前结果数量，用于限制存储的结果数量
        current_diff_count = len(self._comparison_results["diff"])
        current_source_only_count = len(self._comparison_results["source_only"])
        current_target_only_count = len(self._comparison_results["target_only"])

        # 设置每种类型的最大存储记录数
        max_records_per_type = 10000

        # 转换差异记录格式为标准格式
        for diff in comparison_results.get("diff", []):
            # 检查是否达到存储上限
            if current_diff_count >= max_records_per_type:
                break

            # 标准化差异记录格式
            standardized_diff = self._standardize_diff_record(diff)
            self._comparison_results["diff"].append(standardized_diff)
            current_diff_count += 1

        # 处理仅源存在的记录
        for record in comparison_results.get("source_only", []):
            # 检查是否达到存储上限
            if current_source_only_count >= max_records_per_type:
                break

            self._comparison_results["source_only"].append({
                'key': record.get('key', {}),
                'record': record.get('record', {})
            })
            current_source_only_count += 1

        # 处理仅目标存在的记录
        for record in comparison_results.get("target_only", []):
            # 检查是否达到存储上限
            if current_target_only_count >= max_records_per_type:
                break

            self._comparison_results["target_only"].append({
                'key': record.get('key', {}),
                'record': record.get('record', {})
            })
            current_target_only_count += 1

        # 记录匹配的记录数(可选，根据需要可以不存储匹配记录以节省内存)
        matched_count = len(comparison_results.get("matched", []))
        logger.debug(f"保存比对结果: 匹配={matched_count}, 差异={current_diff_count}, "
                    f"仅源={current_source_only_count}, 仅目标={current_target_only_count}")

    def _standardize_diff_record(self, diff):
        """
        将差异记录标准化为统一格式

        Args:
            diff: 差异记录

        Returns:
            Dict[str, Any]: 标准化后的差异记录
        """
        # 如果已有格式化的差异字段，直接返回
        if 'diff_fields' in diff:
            return diff

        # 需要将原始差异格式转换为标准格式
        fields_diff = {}

        # 根据不同算法的输出格式进行适配
        if 'source' in diff and 'target' in diff:
            # 哈希比对算法格式
            source_record = diff.get('source', {})
            target_record = diff.get('target', {})
        elif 'source_record' in diff and 'target_record' in diff:
            # 标准比对算法格式
            source_record = diff.get('source_record', {})
            target_record = diff.get('target_record', {})
        else:
            # 未知格式，返回空记录
            return {
                'key': diff.get('key', {}),
                'diff_fields': {},
                'source_record': {},
                'target_record': {}
            }

        # 找出所有不同的字段
        all_fields = set(source_record.keys()) | set(target_record.keys())
        for field in all_fields:
            source_value = source_record.get(field)
            target_value = target_record.get(field)
            if source_value != target_value:
                fields_diff[field] = {
                    'source': source_value,
                    'target': target_value
                }

        # 返回标准化的差异记录
        return {
            'key': diff.get('key', {}),
            'diff_fields': fields_diff,
            'source_record': source_record,
            'target_record': target_record
        }

    async def _update_progress(self, progress: int):
        """
        更新进度并触发回调

        Args:
            progress: 进度值（0-100）
        """
        # 如果进度变化不大，跳过更新以减少开销
        if hasattr(self, '_last_progress_update'):
            last_progress = self._last_progress_update.get('progress', 0)
            last_update_time = self._last_progress_update.get('time', 0)
            current_time = time.time()

            # 如果进度变化小于2%且距离上次更新不足1秒，则跳过
            if (progress - last_progress < 2 and
                current_time - last_update_time < 1.0 and
                progress < 99):  # 但确保最后的进度更新不会被跳过
                return
        else:
            self._last_progress_update = {'progress': 0, 'time': 0}

        # 记录本次更新
        self._last_progress_update['progress'] = progress
        self._last_progress_update['time'] = time.time()

        # 更新进度
        self.progress = progress

        # 更新统计信息
        elapsed_time = int(time.time() - self.start_time) if self.start_time else 0
        self.stats.elapsed_time = elapsed_time

        # 预估剩余时间
        if progress > 0:
            self.stats.estimated_remaining_time = int(elapsed_time * (100 - progress) / progress)

        # 触发进度回调
        if self.progress_callback:
            try:
                # 检查回调函数是否是协程函数，如果是则使用await调用，否则直接调用
                if asyncio.iscoroutinefunction(self.progress_callback):
                    await self.progress_callback(progress, self.stats)
                else:
                    # 如果不是协程函数但返回协程对象，也需要等待
                    result = self.progress_callback(progress, self.stats)
                    if asyncio.iscoroutine(result):
                        await result
            except Exception as e:
                # 进度回调错误不应该影响主流程
                logger.warning(f"进度回调执行失败: {str(e)}")

        # 只在关键进度点记录日志，减少日志量
        if progress % 10 == 0 or progress >= 99:
            logger.info(f"比对进度: {progress}%, 已处理: {self.stats.processed_count}, 差异: {self.stats.diff_count}")
        else:
            logger.debug(f"比对进度: {progress}%, 已处理: {self.stats.processed_count}, 差异: {self.stats.diff_count}")

    def _save_results_to_task(self, comparison_results):
        """
        保存比对结果到任务数据中，供后续查询使用

        Args:
            comparison_results: 比对结果
        """
        try:
            # 如果有任务管理器，保存结果
            if hasattr(self, '_task_manager') and self._task_manager and hasattr(self, 'task_id') and self.task_id:
                # 获取当前任务数据
                if self.task_id in self._task_manager.tasks:
                    task_data = self._task_manager.tasks[self.task_id]

                    # 确保result字典存在
                    if "result" not in task_data:
                        task_data["result"] = {}

                    # 保存比对结果
                    task_data["result"]["comparison_results"] = comparison_results

                    logger.debug(f"已保存比对结果到任务 {self.task_id}: "
                               f"匹配={len(comparison_results.get('matched', []))}, "
                               f"差异={len(comparison_results.get('diff', []))}, "
                               f"仅源={len(comparison_results.get('source_only', []))}, "
                               f"仅目标={len(comparison_results.get('target_only', []))}")
                else:
                    logger.warning(f"任务 {self.task_id} 不存在于任务管理器中")
            else:
                logger.debug("未设置任务管理器或任务ID，跳过结果保存")
        except Exception as e:
            logger.exception(f"保存比对结果到任务失败: {str(e)}")

    def set_task_context(self, task_id: str, task_manager=None):
        """
        设置任务上下文信息

        Args:
            task_id: 任务ID
            task_manager: 任务管理器实例
        """
        self.task_id = task_id
        self._task_manager = task_manager
        logger.debug(f"已设置任务上下文: task_id={task_id}")

    async def _validate_table_existence(self) -> bool:
        """验证源表和目标表是否存在"""
        try:
            if self.config.mode == "table":
                source_table = self.config.source_table
                target_table = self.config.target_table

                # 检查源表是否存在
                try:
                    if not await self._check_table_exists(source_table, True):
                        logger.error(f"源表不存在: {source_table}")
                        self._set_error_info("TABLE_NOT_FOUND", f"源表不存在: {source_table}",
                                           "请检查源表名是否正确，确保表存在于指定的数据库中")
                        self.status = TaskStatus.FAILED
                        return False
                except Exception as e:
                    # 处理表存在性检查过程中的异常
                    from app.core.error_utils import TableNotFoundError, SQLSyntaxError, DatabaseValidationError
                    if isinstance(e, (TableNotFoundError, SQLSyntaxError, DatabaseValidationError)):
                        # 重新抛出自定义异常
                        raise e
                    else:
                        # 转换为表不存在错误
                        logger.error(f"源表存在性检查失败: {source_table}, {str(e)}")
                        self._set_error_info("TABLE_NOT_FOUND", f"源表不存在: {source_table} - {str(e)}",
                                           "请检查源表名是否正确，确保表存在于指定的数据库中")
                        self.status = TaskStatus.FAILED
                        return False

                # 检查目标表是否存在
                try:
                    if not await self._check_table_exists(target_table, False):
                        logger.error(f"目标表不存在: {target_table}")
                        self._set_error_info("TABLE_NOT_FOUND", f"目标表不存在: {target_table}",
                                           "请检查目标表名是否正确，确保表存在于指定的数据库中")
                        self.status = TaskStatus.FAILED
                        return False
                except Exception as e:
                    # 处理表存在性检查过程中的异常
                    from app.core.error_utils import TableNotFoundError, SQLSyntaxError, DatabaseValidationError
                    if isinstance(e, (TableNotFoundError, SQLSyntaxError, DatabaseValidationError)):
                        # 重新抛出自定义异常
                        raise e
                    else:
                        # 转换为表不存在错误
                        logger.error(f"目标表存在性检查失败: {target_table}, {str(e)}")
                        self._set_error_info("TABLE_NOT_FOUND", f"目标表不存在: {target_table} - {str(e)}",
                                           "请检查目标表名是否正确，确保表存在于指定的数据库中")
                        self.status = TaskStatus.FAILED
                        return False

            return True
        except Exception as e:
            logger.exception(f"表存在性验证失败: {str(e)}")
            self.status = TaskStatus.FAILED
            return False

    async def _check_table_exists(self, table_name: str, is_source: bool) -> bool:
        """检查表是否存在"""
        adapter = self.source_adapter if is_source else self.target_adapter

        try:
            # 使用数据库特定的表存在性检查查询
            if adapter.db_type.lower() == 'db2':
                if '.' in table_name:
                    schema, table = table_name.split('.', 1)
                else:
                    schema = adapter.connection.username.upper()
                    table = table_name

                query = """
                SELECT COUNT(*) FROM syscat.tables
                WHERE tabname = ? AND tabschema = ?
                """
                result = await adapter.execute_scalar(query)
                return result > 0
            elif adapter.db_type.lower() in ['mysql', 'mariadb']:
                database = adapter.connection.database
                query = """
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = ? AND table_name = ?
                """
                result = await adapter.execute_scalar(query)
                return result > 0
            elif adapter.db_type.lower() in ['postgresql', 'postgres']:
                if '.' in table_name:
                    schema, table = table_name.split('.', 1)
                else:
                    schema = 'public'
                    table = table_name

                query = """
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = ? AND table_name = ?
                """
                result = await adapter.execute_scalar(query)
                return result > 0
            else:
                # 对于其他数据库，使用通用方法
                query = f"SELECT COUNT(*) FROM {table_name} WHERE 1=0"
                await adapter.execute_scalar(query)
                return True

        except Exception as e:
            logger.error(f"检查表存在性失败: {table_name}, {str(e)}")
            # 重新抛出异常以便上层正确处理
            raise e

    async def _get_record_counts(self) -> bool:
        """获取源表和目标表记录总数"""
        try:
            self.stats.source_count = await self._get_total_count(True)

            self.stats.target_count = await self._get_total_count(False)

            logger.info(f"源记录数: {self.stats.source_count}, 目标记录数: {self.stats.target_count}")
            return True

        except Exception as e:
            from app.core.error_utils import TableNotFoundError, SQLSyntaxError, DatabaseValidationError

            # 详细的错误分类和处理
            if isinstance(e, TableNotFoundError):
                logger.error(f"表不存在错误: {str(e)}")
                self._set_error_info("TABLE_NOT_FOUND", str(e),
                                   "请检查表名是否正确，确保表存在于指定的数据库中")
                self.status = TaskStatus.FAILED
                return False
            elif isinstance(e, SQLSyntaxError):
                logger.error(f"SQL语法错误: {str(e)}")
                self._set_error_info("SQL_SYNTAX_ERROR", str(e),
                                   "请检查SQL语句语法是否正确，特别注意表名、字段名和WHERE条件")
                self.status = TaskStatus.FAILED
                return False
            elif isinstance(e, DatabaseValidationError):
                logger.error(f"数据库验证错误: {str(e)}")
                self._set_error_info("DATABASE_VALIDATION_ERROR", str(e),
                                   "请检查数据库连接配置和权限设置")
                self.status = TaskStatus.FAILED
                return False
            else:
                # 处理其他未分类的错误
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['connection', 'connect', 'timeout']):
                    logger.error(f"数据库连接错误: {str(e)}")
                    self._set_error_info("CONNECTION_ERROR", str(e),
                                       "请检查数据库连接配置和网络连接")
                elif any(keyword in error_msg for keyword in ['permission', 'access', 'denied', 'forbidden']):
                    logger.error(f"权限错误: {str(e)}")
                    self._set_error_info("PERMISSION_ERROR", str(e),
                                       "请检查数据库用户权限，确保有访问相关表的权限")
                else:
                    logger.exception(f"获取记录总数失败: {str(e)}")
                    self._set_error_info("UNKNOWN_ERROR", str(e),
                                       "请检查配置和日志获取更多信息，或联系管理员")

                self.status = TaskStatus.FAILED
                return False

    def _set_error_info(self, error_type: str, error_message: str, suggestion: str):
        """设置结构化错误信息"""
        # 使用错误消息处理器清理和优化错误消息
        from app.core.error_message_processor import ErrorMessageProcessor

        processed_error = ErrorMessageProcessor.process_error_message(error_message)

        self._error_info = {
            "type": processed_error.get("type", error_type),
            "message": processed_error.get("message", error_message),
            "is_fatal": True,
            "suggestion": processed_error.get("suggestion", suggestion),
            "timestamp": time.time(),
            "raw_message": error_message  # 保留原始消息用于调试
        }
        logger.debug(f"设置错误信息: {self._error_info['type']} - {self._error_info['message']}")

    def _get_error_suggestion(self, error_type: str, error_message: str) -> str:
        """根据错误类型和消息提供具体的解决建议"""
        error_msg_lower = error_message.lower()

        if error_type == "TABLE_NOT_FOUND":
            if "schema" in error_msg_lower:
                return "请检查表名格式是否正确（schema.table），确保schema存在且有访问权限"
            else:
                return "请检查表名是否正确，确保表存在于指定的数据库中"

        elif error_type == "SQL_SYNTAX_ERROR":
            if "sql0206n" in error_msg_lower:
                return "列名不存在，请检查字段名是否正确，注意大小写"
            elif "sql0104n" in error_msg_lower:
                return "SQL语法错误，请检查SQL语句的语法是否正确"
            elif "sql0901n" in error_msg_lower:
                return "SQL语句过长，请简化查询或分解为多个步骤"
            elif "sql0902n" in error_msg_lower:
                return "数据类型不匹配，请检查字段类型和比较值的类型"
            else:
                return "请检查SQL语句语法是否正确，特别注意表名、字段名和WHERE条件"

        elif error_type == "DATABASE_VALIDATION_ERROR":
            return "请检查数据库连接配置和权限设置"

        elif error_type == "CONNECTION_ERROR":
            return "请检查数据库连接配置和网络连接，确保数据库服务正常运行"

        elif error_type == "PERMISSION_ERROR":
            return "请检查数据库用户权限，确保有访问相关表的权限"

        else:
            return "请检查配置和日志获取更多信息，或联系管理员"

    async def _comprehensive_precheck(self) -> bool:
        """
        全面的预检查机制

        包括：
        1. 表名格式验证
        2. 表存在性验证
        3. SQL查询语法预验证
        4. 字段存在性验证
        5. 智能错误恢复建议

        Returns:
            bool: 预检查是否通过
        """
        try:
            # 1. 表名格式验证
            if not await self._validate_table_name_format():
                return False

            # 2. 表存在性验证
            if not await self._validate_table_existence():
                return False

            # 3. SQL查询语法预验证
            if not await self._validate_sql_syntax():
                return False

            # 4. 字段存在性验证
            if not await self._validate_field_existence():
                return False

            return True

        except Exception as e:
            logger.exception(f"预检查过程中发生错误: {str(e)}")

            # 检查是否已经设置了错误信息
            if not hasattr(self, '_error_info') or not self._error_info:
                # 根据异常类型设置相应的错误信息
                from app.core.error_utils import TableNotFoundError, SQLSyntaxError, DatabaseValidationError
                if isinstance(e, TableNotFoundError):
                    self._set_error_info("TABLE_NOT_FOUND", str(e),
                                       "请检查表名是否正确，确保表存在于指定的数据库中")
                elif isinstance(e, SQLSyntaxError):
                    self._set_error_info("SQL_SYNTAX_ERROR", str(e),
                                       "请检查SQL语句语法是否正确，特别注意表名、字段名和WHERE条件")
                elif isinstance(e, DatabaseValidationError):
                    self._set_error_info("DATABASE_VALIDATION_ERROR", str(e),
                                       "请检查数据库连接配置和权限设置")
                else:
                    self._set_error_info("PRECHECK_ERROR", str(e),
                                       "预检查失败，请检查配置和数据库连接")

            self.status = TaskStatus.FAILED
            return False

    async def _validate_table_name_format(self) -> bool:
        """验证表名格式的正确性"""
        try:
            if self.config.mode == "table":
                source_table = self.config.source_table
                target_table = self.config.target_table

                # 检查表名是否为空
                if not source_table or not target_table:
                    self._set_error_info("TABLE_NAME_FORMAT_ERROR",
                                       "源表或目标表名为空",
                                       "请确保源表和目标表名都已正确配置")
                    self.status = TaskStatus.FAILED
                    return False

                # 验证表名格式
                source_validation = self._validate_single_table_name(source_table, "源表")
                target_validation = self._validate_single_table_name(target_table, "目标表")

                if not source_validation["valid"]:
                    self._set_error_info("TABLE_NAME_FORMAT_ERROR",
                                       source_validation["error"],
                                       source_validation["suggestion"])
                    self.status = TaskStatus.FAILED
                    return False

                if not target_validation["valid"]:
                    self._set_error_info("TABLE_NAME_FORMAT_ERROR",
                                       target_validation["error"],
                                       target_validation["suggestion"])
                    self.status = TaskStatus.FAILED
                    return False

                # 提供智能修正建议
                if source_validation.get("auto_fix"):
                    logger.info(f"源表名建议修正: {source_table} -> {source_validation['auto_fix']}")

                if target_validation.get("auto_fix"):
                    logger.info(f"目标表名建议修正: {target_table} -> {target_validation['auto_fix']}")

            return True

        except Exception as e:
            logger.exception(f"表名格式验证失败: {str(e)}")
            self._set_error_info("TABLE_NAME_FORMAT_ERROR", str(e),
                               "表名格式验证失败，请检查表名配置")
            self.status = TaskStatus.FAILED
            return False

    def _validate_single_table_name(self, table_name: str, table_type: str) -> dict:
        """验证单个表名的格式"""
        result = {"valid": True, "error": None, "suggestion": None, "auto_fix": None}

        # 检查表名长度
        if len(table_name) > 128:
            result.update({
                "valid": False,
                "error": f"{table_type}名过长（超过128字符）",
                "suggestion": "请使用较短的表名"
            })
            return result

        # 检查是否包含非法字符
        import re
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$', table_name):
            result.update({
                "valid": False,
                "error": f"{table_type}名包含非法字符",
                "suggestion": "表名只能包含字母、数字和下划线，且必须以字母或下划线开头"
            })
            return result

        # 检查schema.table格式
        if '.' in table_name:
            parts = table_name.split('.')
            if len(parts) != 2:
                result.update({
                    "valid": False,
                    "error": f"{table_type}名格式错误，包含多个点号",
                    "suggestion": "表名格式应为 schema.table 或 table"
                })
                return result

            schema, table = parts
            if not schema or not table:
                result.update({
                    "valid": False,
                    "error": f"{table_type}名格式错误，schema或table部分为空",
                    "suggestion": "确保schema和table部分都不为空"
                })
                return result

        # 提供大小写修正建议
        if table_name != table_name.upper():
            result["auto_fix"] = table_name.upper()

        return result

    async def _validate_sql_syntax(self) -> bool:
        """SQL查询语法预验证"""
        try:
            if self.config.mode == "query":
                source_query = self.config.source_query
                target_query = self.config.target_query

                # 验证源查询语法
                if not await self._validate_single_sql_syntax(source_query, "源查询"):
                    return False

                # 验证目标查询语法
                if not await self._validate_single_sql_syntax(target_query, "目标查询"):
                    return False

            return True

        except Exception as e:
            logger.exception(f"SQL语法预验证失败: {str(e)}")
            self._set_error_info("SQL_SYNTAX_VALIDATION_ERROR", str(e),
                               "SQL语法预验证失败，请检查SQL语句")
            self.status = TaskStatus.FAILED
            return False

    async def _validate_single_sql_syntax(self, sql: str, query_type: str) -> bool:
        """验证单个SQL语句的语法"""
        if not sql or not sql.strip():
            self._set_error_info("SQL_SYNTAX_VALIDATION_ERROR",
                               f"{query_type}为空",
                               f"请确保{query_type}已正确配置")
            self.status = TaskStatus.FAILED
            return False

        # 基本语法检查
        sql_lower = sql.lower().strip()

        # 检查是否以SELECT开头
        if not sql_lower.startswith('select'):
            self._set_error_info("SQL_SYNTAX_VALIDATION_ERROR",
                               f"{query_type}必须以SELECT开头",
                               "请确保查询语句是有效的SELECT语句")
            self.status = TaskStatus.FAILED
            return False

        # 检查基本的SQL关键字配对
        if sql_lower.count('(') != sql_lower.count(')'):
            self._set_error_info("SQL_SYNTAX_VALIDATION_ERROR",
                               f"{query_type}中括号不匹配",
                               "请检查SQL语句中的括号是否正确配对")
            self.status = TaskStatus.FAILED
            return False

        # 检查是否包含FROM子句
        if 'from' not in sql_lower:
            self._set_error_info("SQL_SYNTAX_VALIDATION_ERROR",
                               f"{query_type}缺少FROM子句",
                               "请确保查询语句包含FROM子句")
            self.status = TaskStatus.FAILED
            return False

        return True

    async def _validate_field_existence(self) -> bool:
        """验证字段存在性"""
        try:
            # 验证主键字段
            if not await self._validate_primary_keys():
                return False

            # 验证比对字段
            if not await self._validate_compare_fields():
                return False

            return True

        except Exception as e:
            logger.exception(f"字段存在性验证失败: {str(e)}")
            self._set_error_info("FIELD_VALIDATION_ERROR", str(e),
                               "字段存在性验证失败，请检查字段配置")
            self.status = TaskStatus.FAILED
            return False

    async def _validate_primary_keys(self) -> bool:
        """验证主键字段是否存在"""
        # 使用安全的字段访问方式
        primary_keys = getattr(self.config, 'primary_keys', None)
        if not primary_keys:
            self._set_error_info("FIELD_VALIDATION_ERROR",
                               "未配置主键字段",
                               "请配置至少一个主键字段用于数据比对")
            self.status = TaskStatus.FAILED
            return False

        # 这里可以添加更详细的主键字段存在性检查
        # 通过查询数据库元数据来验证字段是否存在

        return True

    async def _validate_compare_fields(self) -> bool:
        """验证比对字段是否存在"""
        # 使用正确的字段名 compare_columns
        compare_columns = getattr(self.config, 'compare_columns', None)
        if not compare_columns:
            logger.warning("未配置比对字段，将比对所有字段")
            return True

        # 这里可以添加更详细的比对字段存在性检查
        # 通过查询数据库元数据来验证字段是否存在

        return True