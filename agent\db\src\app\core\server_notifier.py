"""
服务器通知模块

负责向 Server 发送任务状态更新和错误报告，确保 Server 能够及时获取任务执行情况。
"""
import httpx
import asyncio
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from app.core.config import settings
from app.models.logger import logger
from app.models.task import TaskStatus, TaskStatusResponse

# 自定义JSON编码器，处理datetime对象
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class ServerNotifier:
    """
    服务器通知客户端

    负责向 Server 发送任务状态更新和错误报告，支持多种通知方式。

    注意：当前Server端使用轮询方式获取任务状态，没有接收POST请求的API端点，
    因此通知功能已被禁用。如需启用，需要在Server端添加相应的API端点。
    """

    def __init__(self, server_url: Optional[str] = None):
        """
        初始化服务器通知客户端

        Args:
            server_url: Server API的基础URL，默认使用配置中的URL
        """
        self.server_url = server_url or settings.SERVER_API_BASE_URL
        if not self.server_url:
            logger.warning("未配置Server URL，通知功能将不可用")

        self.client = httpx.AsyncClient(timeout=10.0, follow_redirects=True)  # 设置超时为10秒，自动处理重定向
        self._pending_notifications = {}  # 待发送的通知队列
        self._notification_task = None  # 通知发送任务
        self._running = False  # 是否正在运行

    async def start(self):
        """启动通知服务"""
        if self._running:
            return

        self._running = True
        self._notification_task = asyncio.create_task(self._notification_worker())
        logger.info("服务器通知服务已启动")

    async def stop(self):
        """停止通知服务"""
        if not self._running:
            return

        self._running = False
        if self._notification_task:
            self._notification_task.cancel()
            try:
                await self._notification_task
            except asyncio.CancelledError:
                pass
            self._notification_task = None

        # 关闭HTTP客户端
        await self.client.aclose()
        logger.info("服务器通知服务已停止")

    async def notify_status_change(self, task_id: str, status: TaskStatus,
                                  task_response: Optional[TaskStatusResponse] = None,
                                  additional_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        通知任务状态变更

        Args:
            task_id: 任务ID
            status: 新的任务状态
            task_response: 完整的任务状态响应对象
            additional_data: 附加数据

        Returns:
            是否成功添加到通知队列
        """
        if not self.server_url:
            logger.debug(f"未配置Server URL，跳过状态变更通知: {task_id} -> {status}")
            return False

        # 构建通知数据
        notification_data = {
            "task_id": task_id,
            "status": str(status),  # 顶层状态字段，确保server端能正确获取
            "timestamp": datetime.now().isoformat(),
            "data": task_response.model_dump() if task_response else None
        }

        # 确保状态字段在顶层可用，避免server端获取空值
        if task_response:
            # 如果有完整的任务响应，也在顶层添加关键字段
            try:
                task_data = task_response.model_dump()
                notification_data.update({
                    "progress": task_data.get("progress", 0),
                    "message": task_data.get("message", f"任务状态已更新为 {status}"),
                    "stats": task_data.get("stats", {})
                })
                logger.debug(f"已添加任务状态详细信息到通知数据: {task_id}")
            except Exception as e:
                logger.warning(f"添加任务状态详细信息失败: {str(e)}")
                # 确保至少有基本的状态信息
                notification_data["message"] = f"任务状态已更新为 {status}"

        # 添加附加数据
        if additional_data:
            notification_data.update(additional_data)

        # 添加到通知队列
        key = f"status:{task_id}"
        self._pending_notifications[key] = notification_data
        logger.debug(f"已将状态变更添加到通知队列: {task_id} -> {status}")

        return True

    async def notify_task_progress(self, task_id: str, progress: int,
                                  stats: Optional[Dict[str, Any]] = None) -> bool:
        """
        通知任务进度更新

        Args:
            task_id: 任务ID
            progress: 进度百分比(0-100)
            stats: 任务统计信息

        Returns:
            是否成功添加到通知队列
        """
        if not self.server_url:
            return False

        # 构建通知数据
        notification_data = {
            "task_id": task_id,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }

        # 添加统计信息
        if stats:
            notification_data["stats"] = stats

        # 添加到通知队列
        key = f"progress:{task_id}"
        self._pending_notifications[key] = notification_data

        return True

    async def _notification_worker(self):
        """通知发送工作线程"""
        while self._running:
            try:
                # 检查是否有待发送的通知
                if not self._pending_notifications:
                    await asyncio.sleep(0.5)  # 无通知时短暂休眠
                    continue

                # 获取所有待发送的通知
                notifications = self._pending_notifications.copy()
                self._pending_notifications.clear()

                # 批量发送通知
                await self._send_notifications(notifications)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.exception(f"通知发送线程异常: {str(e)}")
                await asyncio.sleep(1)  # 发生异常时稍长休眠

    async def _send_notifications(self, notifications: Dict[str, Dict[str, Any]]):
        """
        发送通知到服务器

        Args:
            notifications: 待发送的通知字典
        """
        if not notifications:
            return

        # 按通知类型分组
        status_notifications = {}
        progress_notifications = {}

        for key, data in notifications.items():
            if key.startswith("status:"):
                task_id = key.split(":", 1)[1]
                status_notifications[task_id] = data
            elif key.startswith("progress:"):
                task_id = key.split(":", 1)[1]
                progress_notifications[task_id] = data

        # 发送状态通知
        if status_notifications:
            await self._send_status_notifications(status_notifications)

        # 发送进度通知
        if progress_notifications:
            await self._send_progress_notifications(progress_notifications)

    async def _send_status_notifications(self, notifications: Dict[str, Dict[str, Any]]):
        """发送状态变更通知"""
        # 检查是否配置了服务器URL
        if not self.server_url:
            logger.debug("未配置Server URL，跳过状态变更通知")
            return

        # 发送状态变更通知
        for task_id, data in notifications.items():
            try:
                # 构建API端点URL
                endpoint = f"{self.server_url}/api/database/tasks/{task_id}/status/update"

                # 发送通知，使用自定义JSON编码器
                json_data = json.dumps(data, cls=DateTimeEncoder)

                # 构建请求头，包含认证信息
                headers = {
                    "Content-Type": "application/json"
                }

                # 添加API密钥认证（如果配置了）
                api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
                if api_key and api_key != "DB-DUMMY-AGENT-KEY":
                    headers["Authorization"] = f"Bearer {api_key}"
                    logger.debug(f"使用API密钥认证: {api_key[:10]}...")
                else:
                    logger.debug("未配置有效的API密钥，跳过认证")

                response = await self.client.post(
                    endpoint,
                    content=json_data,
                    headers=headers,
                    timeout=5.0
                )

                if response.status_code == 200:
                    status = data.get('status', '').lower()
                    logger.info(f"任务状态更新通知发送成功: {task_id} -> {status}")
                else:
                    logger.warning(f"任务状态更新通知发送失败: {task_id}, 状态码: {response.status_code}")

            except Exception as e:
                logger.error(f"发送任务状态更新通知时发生异常: {task_id}, 错误: {str(e)}")

    async def _send_progress_notifications(self, notifications: Dict[str, Dict[str, Any]]):
        """发送进度更新通知"""
        # 检查是否配置了服务器URL
        if not self.server_url:
            logger.debug("未配置Server URL，跳过进度更新通知")
            return

        # 发送进度更新通知
        for task_id, data in notifications.items():
            try:
                # 构建API端点URL（可以使用状态更新端点，因为进度也是状态的一部分）
                endpoint = f"{self.server_url}/api/database/tasks/{task_id}/status/update"

                # 发送通知，使用自定义JSON编码器
                json_data = json.dumps(data, cls=DateTimeEncoder)

                # 构建请求头，包含认证信息
                headers = {
                    "Content-Type": "application/json"
                }

                # 添加API密钥认证（如果配置了）
                api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
                if api_key and api_key != "DB-DUMMY-AGENT-KEY":
                    headers["Authorization"] = f"Bearer {api_key}"
                    logger.debug(f"使用API密钥认证: {api_key[:10]}...")
                else:
                    logger.debug("未配置有效的API密钥，跳过认证")

                response = await self.client.post(
                    endpoint,
                    content=json_data,
                    headers=headers,
                    timeout=5.0
                )

                if response.status_code == 200:
                    progress = data.get('progress', 0)
                    logger.info(f"任务进度更新通知发送成功: {task_id} -> {progress}%")
                else:
                    logger.warning(f"任务进度更新通知发送失败: {task_id}, 状态码: {response.status_code}")

            except Exception as e:
                logger.error(f"发送任务进度更新通知时发生异常: {task_id}, 错误: {str(e)}")

# 全局服务器通知器实例
server_notifier = ServerNotifier()
