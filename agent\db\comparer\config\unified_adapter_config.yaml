# 统一适配器配置文件
# 控制三种比对模式的统一行为

# 基本配置
unified_comparison:
  # 是否启用统一比对模式
  enabled: true
  
  # 是否允许回退到传统模式
  fallback_to_legacy: true
  
  # 比对模式优先级（按顺序尝试）
  mode_priority:
    - api      # HTTP API模式
    - local    # 本地函数模式  
    - direct   # 内置比对模式

# 性能配置
performance:
  # 批处理大小
  batch_size: 1000
  
  # 连接超时时间（秒）
  connection_timeout: 30
  
  # 查询超时时间（秒）
  query_timeout: 60
  
  # 最大重试次数
  max_retries: 3
  
  # 重试延迟（秒）
  retry_delay: 2

# 进度反馈配置
progress:
  # 进度更新间隔（秒）
  update_interval: 2
  
  # 是否启用预估时间计算
  enable_estimation: true
  
  # 批量报告大小
  batch_report_size: 100

# 缓存配置
cache:
  # 是否启用结果缓存
  enabled: false
  
  # 缓存最大条目数
  max_size: 100
  
  # 缓存生存时间（秒）
  ttl: 3600

# 错误处理配置
error_handling:
  # 是否启用自动重试
  enable_auto_retry: true
  
  # 错误详情最大长度
  max_error_details: 1000
  
  # 是否记录详细错误堆栈
  log_stack_trace: true

# 调试配置
debug:
  # 调试模式
  enabled: false
  
  # 日志级别
  log_level: "INFO"
  
  # 是否启用性能监控
  enable_performance_monitoring: true
  
  # 是否记录详细的API调用
  log_api_calls: false

# 模式特定配置
modes:
  # 内置比对模式配置
  direct:
    # 是否启用连接池
    enable_connection_pool: false
    
    # 连接池大小
    connection_pool_size: 5
    
    # 是否启用批量处理优化
    enable_batch_optimization: true
  
  # HTTP API模式配置
  api:
    # API请求超时（秒）
    request_timeout: 300
    
    # 任务状态检查间隔（秒）
    status_check_interval: 2
    
    # 是否优先使用Legacy API
    prefer_legacy_api: false
    
    # 是否启用API响应缓存
    enable_response_cache: false
  
  # 本地函数模式配置
  local:
    # 异步执行超时（秒）
    async_timeout: 600
    
    # 是否启用并发处理
    enable_concurrent: false
    
    # 最大并发数
    max_concurrent: 3
    
    # 是否启用内存优化
    enable_memory_optimization: true

# 数据格式配置
data_format:
  # 统一差异记录格式
  unified_difference:
    # 是否包含元数据
    include_metadata: true
    
    # 是否格式化时间戳
    format_timestamps: true
    
    # 字段名映射
    field_mapping:
      id: "id"
      source_data: "source_data"
      target_data: "target_data"
      diff_fields: "diff_fields"
      diff_type: "diff_type"
      table_id: "table_id"
  
  # 统一结果格式
  unified_result:
    # 是否包含性能指标
    include_performance_metrics: true
    
    # 是否包含执行元数据
    include_execution_metadata: true

# 兼容性配置
compatibility:
  # Legacy API兼容性
  legacy_api:
    # 是否启用废弃警告
    enable_deprecation_warnings: true
    
    # 迁移指南URL
    migration_guide_url: "https://docs.example.com/migration"
    
    # 日落警告日期
    sunset_warning_date: "2024-12-31"
  
  # 向后兼容性
  backward_compatibility:
    # 是否保持原有接口
    maintain_original_interfaces: true
    
    # 是否支持原有数据格式
    support_legacy_formats: true

# 监控和日志配置
monitoring:
  # 是否启用指标收集
  enable_metrics: true
  
  # 指标收集间隔（秒）
  metrics_interval: 60
  
  # 是否启用健康检查
  enable_health_check: true
  
  # 健康检查间隔（秒）
  health_check_interval: 300

# 安全配置
security:
  # 是否启用敏感信息过滤
  filter_sensitive_data: true
  
  # 敏感字段列表
  sensitive_fields:
    - "password"
    - "token"
    - "secret"
    - "key"
  
  # 是否记录安全事件
  log_security_events: true

# 环境变量映射
environment_variables:
  # 启用统一比对
  ENABLE_UNIFIED_COMPARISON: "unified_comparison.enabled"
  
  # 回退到传统模式
  FALLBACK_TO_LEGACY: "unified_comparison.fallback_to_legacy"
  
  # 默认比对模式
  DEFAULT_COMPARISON_MODE: "unified_comparison.mode_priority[0]"
  
  # 批处理大小
  BATCH_SIZE: "performance.batch_size"
  
  # 连接超时
  CONNECTION_TIMEOUT: "performance.connection_timeout"
  
  # 查询超时
  QUERY_TIMEOUT: "performance.query_timeout"
  
  # 最大重试次数
  MAX_RETRIES: "performance.max_retries"
  
  # 调试模式
  DEBUG_MODE: "debug.enabled"
  
  # 日志级别
  LOG_LEVEL: "debug.log_level"

# 版本信息
version:
  config_version: "1.0.0"
  compatible_adapter_versions:
    - "1.0.0"
    - "1.0.1"
  
  last_updated: "2024-01-01"
  author: "DB-Agent Team"
