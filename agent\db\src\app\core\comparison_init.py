"""
比对引擎初始化模块

用于注册和初始化比对引擎。
"""
from app.core.comparison_base import DataSourceType
from app.core.comparison_factory import ComparisonEngineFactory
from app.core.relational_comparison import RelationalDBComparisonEngine
from app.models.logger import logger


def initialize_comparison_engines():
    """
    初始化并注册所有比对引擎

    Returns:
        None
    """
    try:
        # 导入跨数据库比对引擎
        from app.core.cross_db_comparison import CrossDBComparisonEngine

        # 注册所有关系型数据库比对引擎
        registered_count = 0

        for db_type in DataSourceType.RELATIONAL_DB_TYPES:
            for target_db_type in DataSourceType.RELATIONAL_DB_TYPES:
                # 如果源和目标数据库类型相同，使用标准关系型数据库比对引擎
                if db_type == target_db_type:
                    ComparisonEngineFactory.register_engine(
                        db_type,
                        target_db_type,
                        RelationalDBComparisonEngine
                    )
                    registered_count += 1
                else:
                    # 如果源和目标数据库类型不同，使用跨数据库比对引擎
                    ComparisonEngineFactory.register_engine(
                        db_type,
                        target_db_type,
                        CrossDBComparisonEngine
                    )
                    registered_count += 1

        # 注册通用关系型数据库比对引擎（向后兼容）
        ComparisonEngineFactory.register_engine(
            DataSourceType.RELATIONAL_DB,
            DataSourceType.RELATIONAL_DB,
            RelationalDBComparisonEngine
        )
        registered_count += 1

        # 注册通配符引擎（用于处理未明确注册的数据源类型组合）
        # 使用RelationalDBComparisonEngine作为默认引擎
        ComparisonEngineFactory.register_engine(
            "*",
            "*",
            RelationalDBComparisonEngine
        )
        registered_count += 1

        # 注册其他类型的比对引擎
        # TODO: 实现并注册其他类型的比对引擎（NoSQL、文件等）

        # 记录支持的数据源类型组合
        supported_combinations = ComparisonEngineFactory.get_supported_combinations()
        logger.info(f"📊 已注册的比对引擎数量: {len(supported_combinations)}")
        logger.info(f"📋 支持的关系型数据库类型: {', '.join(DataSourceType.RELATIONAL_DB_TYPES)}")
        logger.info(f"🔧 默认数据库类型: {DataSourceType.DEFAULT_DB}")
        logger.info(f"🌐 已启用跨数据库比对支持")

        # 特别检查DB2到DB2的组合
        db2_key = f"{DataSourceType.DB2}:{DataSourceType.DB2}"
        if db2_key in supported_combinations:
            logger.info(f"✅ DB2到DB2比对引擎已注册: {supported_combinations[db2_key]}")
        else:
            logger.warning(f"⚠️ DB2到DB2比对引擎未找到")

    except Exception as e:
        logger.error(f"❌ 比对引擎初始化失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise


# 在模块导入时自动初始化比对引擎
try:
    initialize_comparison_engines()
except Exception as e:
    logger.error(f"❌ 模块导入时比对引擎初始化失败: {str(e)}")
    # 不抛出异常，允许模块继续加载
