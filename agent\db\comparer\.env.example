# 统一适配器环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 统一比对模式配置
# ================================

# 是否启用统一比对模式（true/false）
ENABLE_UNIFIED_COMPARISON=true

# 是否允许回退到传统模式（true/false）
FALLBACK_TO_LEGACY=true

# 默认比对模式（auto/direct/api/local）
DEFAULT_COMPARISON_MODE=auto

# ================================
# 性能配置
# ================================

# 批处理大小
BATCH_SIZE=1000

# 连接超时时间（秒）
CONNECTION_TIMEOUT=30

# 查询超时时间（秒）
QUERY_TIMEOUT=60

# 最大重试次数
MAX_RETRIES=3

# 重试延迟（秒）
RETRY_DELAY=2

# ================================
# 进度反馈配置
# ================================

# 进度更新间隔（秒）
PROGRESS_UPDATE_INTERVAL=2

# 是否启用预估时间计算（true/false）
ENABLE_PROGRESS_ESTIMATION=true

# 批量报告大小
BATCH_REPORT_SIZE=100

# ================================
# 缓存配置
# ================================

# 是否启用结果缓存（true/false）
ENABLE_RESULT_CACHING=false

# 缓存最大条目数
CACHE_MAX_SIZE=100

# 缓存生存时间（秒）
CACHE_TTL=3600

# ================================
# 错误处理配置
# ================================

# 是否启用自动重试（true/false）
ENABLE_AUTO_RETRY=true

# 错误详情最大长度
MAX_ERROR_DETAILS=1000

# 是否记录详细错误堆栈（true/false）
LOG_STACK_TRACE=true

# ================================
# 调试配置
# ================================

# 调试模式（true/false）
DEBUG_MODE=false

# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO

# 是否启用性能监控（true/false）
ENABLE_PERFORMANCE_MONITORING=true

# 是否记录详细的API调用（true/false）
LOG_API_CALLS=false

# ================================
# 模式特定配置
# ================================

# 内置比对模式
ENABLE_CONNECTION_POOL=false
CONNECTION_POOL_SIZE=5
ENABLE_BATCH_OPTIMIZATION=true

# HTTP API模式
API_REQUEST_TIMEOUT=300
API_STATUS_CHECK_INTERVAL=2
PREFER_LEGACY_API=false
ENABLE_API_RESPONSE_CACHE=false

# 本地函数模式
LOCAL_ASYNC_TIMEOUT=600
ENABLE_LOCAL_CONCURRENT=false
MAX_LOCAL_CONCURRENT=3
ENABLE_MEMORY_OPTIMIZATION=true

# ================================
# Legacy API兼容性配置
# ================================

# 是否启用Legacy API兼容层（true/false）
ENABLE_LEGACY_API=true

# 是否启用废弃警告（true/false）
LEGACY_API_DEPRECATION_WARNING=true

# 迁移指南URL
MIGRATION_GUIDE_URL=https://docs.example.com/migration

# 日落警告日期
LEGACY_API_SUNSET_DATE=2024-12-31

# ================================
# 数据格式配置
# ================================

# 是否包含元数据（true/false）
INCLUDE_METADATA=true

# 是否格式化时间戳（true/false）
FORMAT_TIMESTAMPS=true

# 是否包含性能指标（true/false）
INCLUDE_PERFORMANCE_METRICS=true

# 是否包含执行元数据（true/false）
INCLUDE_EXECUTION_METADATA=true

# ================================
# 监控和日志配置
# ================================

# 是否启用指标收集（true/false）
ENABLE_METRICS=true

# 指标收集间隔（秒）
METRICS_INTERVAL=60

# 是否启用健康检查（true/false）
ENABLE_HEALTH_CHECK=true

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=300

# ================================
# 安全配置
# ================================

# 是否启用敏感信息过滤（true/false）
FILTER_SENSITIVE_DATA=true

# 是否记录安全事件（true/false）
LOG_SECURITY_EVENTS=true

# ================================
# 数据库连接配置
# ================================

# 最大并发连接数
MAX_CONCURRENT_CONNECTIONS=5

# 连接池超时（秒）
CONNECTION_POOL_TIMEOUT=30

# 是否启用连接验证（true/false）
ENABLE_CONNECTION_VALIDATION=true

# ================================
# 开发和测试配置
# ================================

# 是否启用测试模式（true/false）
TEST_MODE=false

# 测试数据库前缀
TEST_DB_PREFIX=test_

# 是否启用模拟模式（true/false）
MOCK_MODE=false

# 模拟延迟（毫秒）
MOCK_DELAY=100

# ================================
# 高级配置
# ================================

# 是否启用实验性功能（true/false）
ENABLE_EXPERIMENTAL_FEATURES=false

# 线程池大小
THREAD_POOL_SIZE=4

# 是否启用异步处理（true/false）
ENABLE_ASYNC_PROCESSING=true

# 内存限制（MB）
MEMORY_LIMIT=1024

# ================================
# 集成配置
# ================================

# 外部API基础URL
EXTERNAL_API_BASE_URL=http://localhost:8000

# API认证令牌
API_AUTH_TOKEN=your_token_here

# 是否启用SSL验证（true/false）
ENABLE_SSL_VERIFICATION=true

# 请求重试次数
REQUEST_RETRY_COUNT=3

# ================================
# 文件和路径配置
# ================================

# 日志文件路径
LOG_FILE_PATH=logs/unified_adapter.log

# 配置文件路径
CONFIG_FILE_PATH=config/unified_adapter_config.yaml

# 临时文件目录
TEMP_DIR=temp/

# 缓存目录
CACHE_DIR=cache/

# ================================
# 通知配置
# ================================

# 是否启用邮件通知（true/false）
ENABLE_EMAIL_NOTIFICATIONS=false

# SMTP服务器
SMTP_SERVER=smtp.example.com

# SMTP端口
SMTP_PORT=587

# 邮件发送者
EMAIL_FROM=<EMAIL>

# 邮件接收者
EMAIL_TO=<EMAIL>
