"""
SQL解析器模块

提供SQL语句解析和转换功能，支持不同数据库方言的转换。
"""
import re
from enum import Enum
from typing import Dict, List, Any, Optional, Tuple, Set, Union

from app.models.logger import logger


class SQLClauseType(Enum):
    """SQL子句类型"""
    SELECT = "SELECT"
    FROM = "FROM"
    WHERE = "WHERE"
    GROUP_BY = "GROUP BY"
    HAVING = "HAVING"
    ORDER_BY = "ORDER BY"
    LIMIT = "LIMIT"
    OFFSET = "OFFSET"
    JOIN = "JOIN"
    UNION = "UNION"
    SUBQUERY = "SUBQUERY"
    FUNCTION = "FUNCTION"
    EXPRESSION = "EXPRESSION"
    UNKNOWN = "UNKNOWN"


class SQLNode:
    """SQL语法树节点"""
    
    def __init__(self, node_type: SQLClauseType, content: str, parent=None):
        """
        初始化SQL语法树节点
        
        Args:
            node_type: 节点类型
            content: 节点内容
            parent: 父节点
        """
        self.node_type = node_type
        self.content = content
        self.parent = parent
        self.children = []
    
    def add_child(self, child):
        """
        添加子节点
        
        Args:
            child: 子节点
        """
        self.children.append(child)
        child.parent = self
    
    def __str__(self):
        return f"{self.node_type.value}: {self.content}"


class SQLParser:
    """SQL解析器"""
    
    def __init__(self, db_type: str):
        """
        初始化SQL解析器
        
        Args:
            db_type: 数据库类型
        """
        self.db_type = db_type.lower()
        
        # 初始化SQL关键字正则表达式
        self._init_regex_patterns()
    
    def _init_regex_patterns(self):
        """初始化SQL关键字正则表达式"""
        # 基本SQL子句模式
        self.clause_patterns = {
            SQLClauseType.SELECT: re.compile(r'SELECT\s+(.+?)(?:\s+FROM\s+|\s*$)', re.IGNORECASE | re.DOTALL),
            SQLClauseType.FROM: re.compile(r'FROM\s+(.+?)(?:\s+(?:WHERE|GROUP\s+BY|HAVING|ORDER\s+BY|LIMIT|OFFSET|UNION|$))', re.IGNORECASE | re.DOTALL),
            SQLClauseType.WHERE: re.compile(r'WHERE\s+(.+?)(?:\s+(?:GROUP\s+BY|HAVING|ORDER\s+BY|LIMIT|OFFSET|UNION|$))', re.IGNORECASE | re.DOTALL),
            SQLClauseType.GROUP_BY: re.compile(r'GROUP\s+BY\s+(.+?)(?:\s+(?:HAVING|ORDER\s+BY|LIMIT|OFFSET|UNION|$))', re.IGNORECASE | re.DOTALL),
            SQLClauseType.HAVING: re.compile(r'HAVING\s+(.+?)(?:\s+(?:ORDER\s+BY|LIMIT|OFFSET|UNION|$))', re.IGNORECASE | re.DOTALL),
            SQLClauseType.ORDER_BY: re.compile(r'ORDER\s+BY\s+(.+?)(?:\s+(?:LIMIT|OFFSET|UNION|$))', re.IGNORECASE | re.DOTALL),
            SQLClauseType.LIMIT: re.compile(r'LIMIT\s+(\d+)(?:\s+(?:OFFSET|UNION|$))', re.IGNORECASE),
            SQLClauseType.OFFSET: re.compile(r'OFFSET\s+(\d+)(?:\s+(?:UNION|$))', re.IGNORECASE),
            SQLClauseType.UNION: re.compile(r'UNION\s+(?:ALL\s+)?(.+)', re.IGNORECASE | re.DOTALL),
        }
        
        # JOIN子句模式
        self.join_pattern = re.compile(r'((?:INNER|LEFT|RIGHT|FULL|CROSS|OUTER)?\s*JOIN\s+.+?(?:ON\s+.+?)?)', re.IGNORECASE | re.DOTALL)
        
        # 子查询模式
        self.subquery_pattern = re.compile(r'\(\s*SELECT\s+.+?\)', re.IGNORECASE | re.DOTALL)
        
        # 函数调用模式
        self.function_pattern = re.compile(r'(\w+)\s*\((.+?)\)', re.IGNORECASE | re.DOTALL)
        
        # 表达式模式
        self.expression_pattern = re.compile(r'(.+?)(?:,|$)', re.IGNORECASE | re.DOTALL)
    
    def parse(self, query: str) -> SQLNode:
        """
        解析SQL查询语句
        
        Args:
            query: SQL查询语句
            
        Returns:
            SQLNode: SQL语法树根节点
        """
        # 创建根节点
        root = SQLNode(SQLClauseType.UNKNOWN, query)
        
        # 解析SELECT子句
        select_match = self.clause_patterns[SQLClauseType.SELECT].search(query)
        if select_match:
            select_content = select_match.group(1).strip()
            select_node = SQLNode(SQLClauseType.SELECT, select_content)
            root.add_child(select_node)
            
            # 解析SELECT列表
            self._parse_select_list(select_node)
        
        # 解析FROM子句
        from_match = self.clause_patterns[SQLClauseType.FROM].search(query)
        if from_match:
            from_content = from_match.group(1).strip()
            from_node = SQLNode(SQLClauseType.FROM, from_content)
            root.add_child(from_node)
            
            # 解析FROM子句中的表和JOIN
            self._parse_from_clause(from_node)
        
        # 解析WHERE子句
        where_match = self.clause_patterns[SQLClauseType.WHERE].search(query)
        if where_match:
            where_content = where_match.group(1).strip()
            where_node = SQLNode(SQLClauseType.WHERE, where_content)
            root.add_child(where_node)
            
            # 解析WHERE条件
            self._parse_where_clause(where_node)
        
        # 解析GROUP BY子句
        group_by_match = self.clause_patterns[SQLClauseType.GROUP_BY].search(query)
        if group_by_match:
            group_by_content = group_by_match.group(1).strip()
            group_by_node = SQLNode(SQLClauseType.GROUP_BY, group_by_content)
            root.add_child(group_by_node)
        
        # 解析HAVING子句
        having_match = self.clause_patterns[SQLClauseType.HAVING].search(query)
        if having_match:
            having_content = having_match.group(1).strip()
            having_node = SQLNode(SQLClauseType.HAVING, having_content)
            root.add_child(having_node)
            
            # 解析HAVING条件
            self._parse_having_clause(having_node)
        
        # 解析ORDER BY子句
        order_by_match = self.clause_patterns[SQLClauseType.ORDER_BY].search(query)
        if order_by_match:
            order_by_content = order_by_match.group(1).strip()
            order_by_node = SQLNode(SQLClauseType.ORDER_BY, order_by_content)
            root.add_child(order_by_node)
        
        # 解析LIMIT子句
        limit_match = self.clause_patterns[SQLClauseType.LIMIT].search(query)
        if limit_match:
            limit_content = limit_match.group(1).strip()
            limit_node = SQLNode(SQLClauseType.LIMIT, limit_content)
            root.add_child(limit_node)
        
        # 解析OFFSET子句
        offset_match = self.clause_patterns[SQLClauseType.OFFSET].search(query)
        if offset_match:
            offset_content = offset_match.group(1).strip()
            offset_node = SQLNode(SQLClauseType.OFFSET, offset_content)
            root.add_child(offset_node)
        
        # 解析UNION子句
        union_match = self.clause_patterns[SQLClauseType.UNION].search(query)
        if union_match:
            union_content = union_match.group(1).strip()
            union_node = SQLNode(SQLClauseType.UNION, union_content)
            root.add_child(union_node)
            
            # 递归解析UNION后的查询
            union_query_node = self.parse(union_content)
            union_node.add_child(union_query_node)
        
        return root
    
    def _parse_select_list(self, select_node: SQLNode) -> None:
        """
        解析SELECT列表
        
        Args:
            select_node: SELECT节点
        """
        select_content = select_node.content
        
        # 处理SELECT *
        if select_content.strip() == '*':
            return
        
        # 分割SELECT列表
        columns = self._split_expressions(select_content)
        
        for column in columns:
            # 检查是否是子查询
            if column.strip().startswith('(') and 'SELECT' in column.upper():
                subquery_node = SQLNode(SQLClauseType.SUBQUERY, column.strip())
                select_node.add_child(subquery_node)
                
                # 递归解析子查询
                subquery_content = column.strip()[1:-1]  # 去掉括号
                subquery_parsed = self.parse(subquery_content)
                subquery_node.add_child(subquery_parsed)
                continue
            
            # 检查是否是函数调用
            func_match = self.function_pattern.match(column.strip())
            if func_match:
                func_name = func_match.group(1)
                func_args = func_match.group(2)
                func_node = SQLNode(SQLClauseType.FUNCTION, f"{func_name}({func_args})")
                select_node.add_child(func_node)
                continue
            
            # 普通表达式
            expr_node = SQLNode(SQLClauseType.EXPRESSION, column.strip())
            select_node.add_child(expr_node)
    
    def _parse_from_clause(self, from_node: SQLNode) -> None:
        """
        解析FROM子句
        
        Args:
            from_node: FROM节点
        """
        from_content = from_node.content
        
        # 检查是否包含JOIN
        join_matches = self.join_pattern.finditer(from_content)
        for match in join_matches:
            join_content = match.group(1)
            join_node = SQLNode(SQLClauseType.JOIN, join_content)
            from_node.add_child(join_node)
        
        # 检查是否包含子查询
        subquery_matches = self.subquery_pattern.finditer(from_content)
        for match in subquery_matches:
            subquery_content = match.group(0)
            subquery_node = SQLNode(SQLClauseType.SUBQUERY, subquery_content)
            from_node.add_child(subquery_node)
            
            # 递归解析子查询
            inner_content = subquery_content[1:-1]  # 去掉括号
            subquery_parsed = self.parse(inner_content)
            subquery_node.add_child(subquery_parsed)
    
    def _parse_where_clause(self, where_node: SQLNode) -> None:
        """
        解析WHERE子句
        
        Args:
            where_node: WHERE节点
        """
        where_content = where_node.content
        
        # 检查是否包含子查询
        subquery_matches = self.subquery_pattern.finditer(where_content)
        for match in subquery_matches:
            subquery_content = match.group(0)
            subquery_node = SQLNode(SQLClauseType.SUBQUERY, subquery_content)
            where_node.add_child(subquery_node)
            
            # 递归解析子查询
            inner_content = subquery_content[1:-1]  # 去掉括号
            subquery_parsed = self.parse(inner_content)
            subquery_node.add_child(subquery_parsed)
        
        # 检查是否包含函数调用
        func_matches = self.function_pattern.finditer(where_content)
        for match in func_matches:
            func_name = match.group(1)
            func_args = match.group(2)
            func_node = SQLNode(SQLClauseType.FUNCTION, f"{func_name}({func_args})")
            where_node.add_child(func_node)
    
    def _parse_having_clause(self, having_node: SQLNode) -> None:
        """
        解析HAVING子句
        
        Args:
            having_node: HAVING节点
        """
        having_content = having_node.content
        
        # 检查是否包含子查询
        subquery_matches = self.subquery_pattern.finditer(having_content)
        for match in subquery_matches:
            subquery_content = match.group(0)
            subquery_node = SQLNode(SQLClauseType.SUBQUERY, subquery_content)
            having_node.add_child(subquery_node)
            
            # 递归解析子查询
            inner_content = subquery_content[1:-1]  # 去掉括号
            subquery_parsed = self.parse(inner_content)
            subquery_node.add_child(subquery_parsed)
        
        # 检查是否包含函数调用
        func_matches = self.function_pattern.finditer(having_content)
        for match in func_matches:
            func_name = match.group(1)
            func_args = match.group(2)
            func_node = SQLNode(SQLClauseType.FUNCTION, f"{func_name}({func_args})")
            having_node.add_child(func_node)
    
    def _split_expressions(self, expr_list: str) -> List[str]:
        """
        分割表达式列表
        
        Args:
            expr_list: 表达式列表字符串
            
        Returns:
            List[str]: 分割后的表达式列表
        """
        result = []
        current_expr = ""
        paren_level = 0
        quote_char = None
        
        for char in expr_list:
            if char == '(' and quote_char is None:
                paren_level += 1
                current_expr += char
            elif char == ')' and quote_char is None:
                paren_level -= 1
                current_expr += char
            elif char in ["'", '"'] and (quote_char is None or char == quote_char):
                # 处理引号
                if quote_char is None:
                    quote_char = char
                else:
                    quote_char = None
                current_expr += char
            elif char == ',' and paren_level == 0 and quote_char is None:
                # 只有在括号外且不在引号内时，才将逗号视为分隔符
                result.append(current_expr.strip())
                current_expr = ""
            else:
                current_expr += char
        
        # 添加最后一个表达式
        if current_expr.strip():
            result.append(current_expr.strip())
        
        return result
