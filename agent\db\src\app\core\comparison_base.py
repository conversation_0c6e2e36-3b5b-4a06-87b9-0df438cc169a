"""
比对引擎抽象基类和接口定义

提供数据比对引擎的抽象接口和基础实现，支持不同类型数据源的比对操作。
"""
import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Tuple

from app.models.task import (
    CompareConfig, TaskStats, ComparisonSummary,
    TaskStatus, AlgorithmType
)
from app.models.logger import logger


class DataSourceType(str):
    """数据源类型"""
    # 关系型数据库类型（与Server和前端保持一致）
    DB2 = "db2"                   # IBM DB2数据库（默认）
    ORACLE = "oracle"             # Oracle数据库
    GAUSSDB = "gaussdb"           # 华为GaussDB数据库
    SQLSERVER = "sqlserver"       # Microsoft SQL Server数据库
    POSTGRESQL = "postgresql"     # PostgreSQL数据库（统一使用postgresql）
    MYSQL = "mysql"               # MySQL数据库

    # 其他数据源类型
    NOSQL_DB = "nosql"            # NoSQL数据库
    FILE_SYSTEM = "file"          # 文件系统
    API = "api"                   # API数据源
    MEMORY = "memory"             # 内存数据

    # 通用类型（用于向后兼容）
    RELATIONAL_DB = "relational"  # 通用关系型数据库

    # 关系型数据库列表
    RELATIONAL_DB_TYPES = [DB2, ORACLE, GAUSSDB, SQLSERVER, POSTGRESQL, MYSQL]

    # 默认数据库类型
    DEFAULT_DB = DB2


class AbstractComparisonEngine(ABC):
    """比对引擎抽象基类"""

    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化比对引擎

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    async def run(self) -> ComparisonSummary:
        """
        执行比对操作

        Returns:
            ComparisonSummary: 比对结果摘要
        """
        pass

    @abstractmethod
    async def cancel(self) -> bool:
        """
        取消比对操作

        Returns:
            bool: 取消是否成功
        """
        pass

    @abstractmethod
    async def pause(self) -> bool:
        """
        暂停比对操作

        Returns:
            bool: 暂停是否成功
        """
        pass

    @abstractmethod
    async def resume(self) -> bool:
        """
        恢复比对操作

        Returns:
            bool: 恢复是否成功
        """
        pass

    @abstractmethod
    async def get_source_data(self, partition=None) -> List[Dict[str, Any]]:
        """
        获取源数据

        Args:
            partition: 分区信息，用于分批获取数据

        Returns:
            List[Dict[str, Any]]: 源数据列表
        """
        pass

    @abstractmethod
    async def get_target_data(self, partition=None) -> List[Dict[str, Any]]:
        """
        获取目标数据

        Args:
            partition: 分区信息，用于分批获取数据

        Returns:
            List[Dict[str, Any]]: 目标数据列表
        """
        pass

    @abstractmethod
    async def compare_data(self, source_data: List[Dict[str, Any]], target_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        比对数据

        Args:
            source_data: 源数据列表
            target_data: 目标数据列表

        Returns:
            Dict[str, Any]: 比对结果
        """
        pass

    @abstractmethod
    async def analyze_differences(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析差异

        Args:
            comparison_results: 比对结果

        Returns:
            Dict[str, Any]: 分析后的比对结果
        """
        pass

    @abstractmethod
    def build_summary(self) -> ComparisonSummary:
        """
        构建比对结果摘要

        Returns:
            ComparisonSummary: 比对结果摘要
        """
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """
        清理资源

        Returns:
            None
        """
        pass

    # 以下是为了兼容现有代码而添加的方法

    @abstractmethod
    async def _standard_comparison(self) -> None:
        """
        标准比对策略 - 适用于中小数据集

        Returns:
            None
        """
        pass

    @abstractmethod
    async def _partitioned_comparison(self, algorithm_name: str = "分区比对") -> None:
        """
        分区比对策略 - 适用于大数据集

        Args:
            algorithm_name: 算法名称，用于日志

        Returns:
            None
        """
        pass

    @abstractmethod
    async def _update_progress(self, progress: int) -> None:
        """
        更新进度并触发回调

        Args:
            progress: 进度值（0-100）

        Returns:
            None
        """
        pass

    @abstractmethod
    def _save_comparison_results(self, comparison_results: Dict[str, Any]) -> None:
        """
        保存比对结果

        Args:
            comparison_results: 比对结果

        Returns:
            None
        """
        pass

    @abstractmethod
    def _standardize_diff_record(self, diff: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化差异记录

        Args:
            diff: 差异记录

        Returns:
            Dict[str, Any]: 标准化后的差异记录
        """
        pass

    @property
    @abstractmethod
    def _all_results(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取所有比对结果

        Returns:
            Dict[str, List[Dict[str, Any]]]: 所有比对结果
        """
        pass


class BaseComparisonEngine(AbstractComparisonEngine):
    """比对引擎基础实现"""

    def __init__(
        self,
        source_conn: Any,
        target_conn: Any,
        config: CompareConfig,
        progress_callback: Optional[Callable[[int, TaskStats], None]] = None
    ):
        """
        初始化比对引擎基础实现

        Args:
            source_conn: 源连接信息
            target_conn: 目标连接信息
            config: 比对配置
            progress_callback: 进度回调函数
        """
        self.source_conn = source_conn
        self.target_conn = target_conn
        self.config = config
        self.progress_callback = progress_callback

        # 比对状态和统计信息
        self.stats = TaskStats(
            source_count=0,
            target_count=0,
            processed_count=0,
            diff_count=0,
            matched_count=0,
            source_only_count=0,
            target_only_count=0,
            elapsed_time=0,
            estimated_remaining_time=0
        )
        self.start_time = None
        self.progress = 0
        self.status = TaskStatus.PENDING
        self.task_id = None  # 用于错误报告

        # 内部运行状态
        self._cancel_requested = False
        self._pause_requested = False
        self._pause_event = asyncio.Event()
        self._pause_event.set()  # 默认不暂停

        # 性能监控
        self._performance_metrics = {
            'query_time': 0,
            'comparison_time': 0,
            'analysis_time': 0,
            'total_queries': 0
        }

        # 比对结果存储
        self._comparison_results = {
            "matched": [],
            "diff": [],
            "source_only": [],
            "target_only": []
        }

        # 进度更新控制
        self._last_progress_update = {'progress': 0, 'time': 0}

        # 初始化比对算法（延迟导入避免循环依赖）
        self.algorithm = None
        self._initialize_algorithm()

    async def initialize(self) -> bool:
        """
        初始化比对引擎

        Returns:
            bool: 初始化是否成功
        """
        self.start_time = time.time()
        self.status = TaskStatus.RUNNING
        return True

    async def cancel(self) -> bool:
        """
        取消比对操作

        Returns:
            bool: 取消是否成功
        """
        if self.status == TaskStatus.RUNNING:
            logger.info("请求取消比对任务")
            self._cancel_requested = True
            self.status = TaskStatus.CANCELED
            return True
        return False

    async def pause(self) -> bool:
        """
        暂停比对操作

        Returns:
            bool: 暂停是否成功
        """
        if self.status == TaskStatus.RUNNING and not self._pause_requested:
            logger.info("请求暂停比对任务")
            self._pause_requested = True
            self._pause_event.clear()
            self.status = TaskStatus.PAUSED
            return True
        return False

    async def resume(self) -> bool:
        """
        恢复比对操作

        Returns:
            bool: 恢复是否成功
        """
        if self.status == TaskStatus.PAUSED and self._pause_requested:
            logger.info("请求恢复比对任务")
            self._pause_requested = False
            self._pause_event.set()
            self.status = TaskStatus.RUNNING
            return True
        return False

    async def cleanup(self) -> None:
        """
        清理资源

        Returns:
            None
        """
        # 记录比对执行时间
        if self.start_time:
            self.execution_time = time.time() - self.start_time
            logger.info(f"比对完成, 耗时: {self.execution_time:.2f}秒, "
                       f"总记录数: {self.stats.source_count}/{self.stats.target_count}, "
                       f"差异记录数: {self.stats.diff_count}")

        # 记录性能指标
        self._log_performance_metrics()

    def _log_performance_metrics(self):
        """记录性能指标"""
        # 记录总执行时间
        if self.start_time:
            total_time = time.time() - self.start_time
            logger.info(f"性能指标摘要:")
            logger.info(f"  - 总执行时间: {total_time:.2f}秒")

            # 计算各阶段占比
            if total_time > 0:
                query_ratio = self._performance_metrics['query_time'] / total_time
                analysis_ratio = self._performance_metrics['analysis_time'] / total_time
                logger.info(f"  - 查询时间占比: {query_ratio:.2%}")
                logger.info(f"  - 分析时间占比: {analysis_ratio:.2%}")

    # 以下是为了兼容现有代码而实现的方法

    async def _standard_comparison(self) -> None:
        """
        标准比对策略 - 适用于中小数据集

        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现_standard_comparison方法")

    async def _partitioned_comparison(self, algorithm_name: str = "分区比对") -> None:
        """
        分区比对策略 - 适用于大数据集

        Args:
            algorithm_name: 算法名称，用于日志

        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现_partitioned_comparison方法")

    async def _update_progress(self, progress: int) -> None:
        """
        更新进度并触发回调

        Args:
            progress: 进度值（0-100）

        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现_update_progress方法")

    def _save_comparison_results(self, comparison_results: Dict[str, Any]) -> None:
        """
        保存比对结果

        Args:
            comparison_results: 比对结果

        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现_save_comparison_results方法")

    def _standardize_diff_record(self, diff: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化差异记录

        Args:
            diff: 差异记录

        Returns:
            Dict[str, Any]: 标准化后的差异记录

        子类必须实现此方法
        """
        raise NotImplementedError("子类必须实现_standardize_diff_record方法")

    @property
    def _all_results(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取所有比对结果

        Returns:
            Dict[str, List[Dict[str, Any]]]: 所有比对结果
        """
        if not hasattr(self, '_comparison_results'):
            self._comparison_results = {
                "matched": [],
                "diff": [],
                "source_only": [],
                "target_only": []
            }
        return self._comparison_results

    def _initialize_algorithm(self):
        """初始化比对算法"""
        try:
            # 延迟导入避免循环依赖
            from app.core.algorithms import ComparisonAlgorithmFactory, ComparisonAlgorithmType

            # 选择比对算法
            algorithm_type = None
            if hasattr(self.config, 'algorithm_type') and self.config.algorithm_type:
                # 将任务模型中的算法类型映射到算法模块中的类型
                algorithm_mapping = {
                    AlgorithmType.PRIMARY_KEY: ComparisonAlgorithmType.PRIMARY_KEY,
                    AlgorithmType.HASH_BASED: ComparisonAlgorithmType.HASH_BASED,
                    AlgorithmType.TWO_PHASE: ComparisonAlgorithmType.TWO_PHASE
                }
                algorithm_type = algorithm_mapping.get(self.config.algorithm_type)

            # 初始化比对算法
            self.algorithm = ComparisonAlgorithmFactory.get_algorithm(algorithm_type)
            logger.info(f"使用比对算法: {self.algorithm.__class__.__name__}")
        except Exception as e:
            logger.warning(f"初始化比对算法失败: {str(e)}")
            self.algorithm = None

    def _select_optimal_algorithm(self) -> AlgorithmType:
        """
        根据数据特征智能选择最优比对算法

        Returns:
            AlgorithmType: 选择的算法类型
        """
        # 如果用户明确指定了算法类型，优先使用用户指定的算法
        if hasattr(self.config, 'algorithm_type') and self.config.algorithm_type:
            logger.info(f"使用用户指定的算法: {self.config.algorithm_type}")
            return self.config.algorithm_type

        # 根据数据量选择算法
        source_count = self.stats.source_count
        target_count = self.stats.target_count
        total_count = max(source_count, target_count)

        # 数据量差异比例
        if source_count > 0 and target_count > 0:
            diff_ratio = abs(source_count - target_count) / max(source_count, target_count)
        else:
            diff_ratio = 0

        # 根据数据特征选择算法
        if total_count < 10000:
            # 小数据集使用标准比对算法
            logger.info("数据量较小，自动选择标准比对算法")
            return AlgorithmType.PRIMARY_KEY
        elif diff_ratio > 0.3:
            # 数据差异较大，使用两阶段比对算法
            logger.info(f"源目标数据量差异较大 ({diff_ratio:.2f})，自动选择两阶段比对算法")
            return AlgorithmType.TWO_PHASE
        elif total_count > 1000000:
            # 大数据集使用基于哈希的比对算法
            logger.info("数据量较大，自动选择基于哈希的比对算法")
            return AlgorithmType.HASH_BASED
        else:
            # 中等数据量，使用两阶段比对算法
            logger.info("数据量适中，自动选择两阶段比对算法")
            return AlgorithmType.TWO_PHASE
