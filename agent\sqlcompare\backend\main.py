#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI主应用程序
SQLCompare Backend API服务的入口点
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.core.config import get_settings
from backend.core.database import init_database
from backend.core.logging_config import setup_logging
from backend.core.dependencies import initialize_services, cleanup_services
from backend.api.v1.router import api_router
from backend.core.exceptions import APIException


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    # 启动时执行
    settings = get_settings()
    setup_logging(settings.log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("正在启动SQLCompare API服务...")
    
    # 初始化数据库
    await init_database()
    logger.info("数据库初始化完成")

    # 初始化所有服务
    await initialize_services()
    logger.info("服务初始化完成")

    yield

    # 关闭时执行
    logger.info("SQLCompare API服务正在关闭...")

    # 清理所有服务
    await cleanup_services()
    logger.info("服务清理完成")


def create_app() -> FastAPI:
    """创建FastAPI应用程序"""
    settings = get_settings()
    
    app = FastAPI(
        title="SQLCompare API",
        description="数据库比对任务管理API服务",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # 添加中间件
    setup_middleware(app, settings)
    
    # 添加路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 添加异常处理器
    setup_exception_handlers(app)
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "sqlcompare-api"}
    
    return app


def setup_middleware(app: FastAPI, settings):
    """设置中间件"""
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_hosts,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 受信任主机中间件
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts
        )


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(APIException)
    async def api_exception_handler(request: Request, exc: APIException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "服务器内部错误",
                    "details": str(exc) if get_settings().debug else None
                }
            }
        )


# 创建应用实例
app = create_app()


def main():
    """主函数"""
    settings = get_settings()
    
    uvicorn.run(
        "backend.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
