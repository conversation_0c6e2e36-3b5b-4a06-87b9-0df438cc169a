#!/bin/bash

# 数据比对器代理停止脚本
# 作者: 数据比对器代理开发团队
# 版本: 2.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 停止服务
stop_services() {
    log_step "停止数据比对器代理服务..."
    
    cd docker
    
    # 停止所有服务
    docker-compose down
    
    log_info "服务已停止"
}

# 清理资源（可选）
cleanup_resources() {
    read -p "是否清理所有数据卷？这将删除所有数据 (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_step "清理数据卷..."
        cd docker
        docker-compose down -v
        log_warn "所有数据已清理"
    else
        log_info "保留数据卷"
    fi
}

# 显示状态信息
show_status() {
    log_step "检查服务状态..."
    
    cd docker
    
    if docker-compose ps | grep -q "Up"; then
        log_warn "仍有服务在运行:"
        docker-compose ps
    else
        log_info "所有服务已停止"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "   数据比对器代理 v2.0 停止脚本"
    echo "========================================"
    echo ""
    
    stop_services
    show_status
    
    echo ""
    read -p "是否需要清理数据？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_resources
    fi
    
    echo ""
    log_info "数据比对器代理已停止"
    log_info "如需重新启动，请运行: ./scripts/start.sh"
}

# 执行主函数
main "$@"
