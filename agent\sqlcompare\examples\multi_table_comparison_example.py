"""
多表比对服务示例
展示如何使用新的多表比对API创建和执行复杂的比对任务
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from models.sqlalchemy_models import (
    Base, User, ComparisonTask, ComparisonTableRule, TaskStatus,
    ComparisonType, create_database_engine, create_tables, get_session_factory
)
from models.pydantic_models import TaskCreateDirect, TableRuleCreate
from adapters.service_sqlite_adapter import ServiceSqliteAdapter


class MultiTableComparisonService:
    """多表比对服务类"""
    
    def __init__(self, database_url: str = "sqlite:///multi_table_comparison.db"):
        self.engine = create_database_engine(database_url)
        create_tables(self.engine)
        self.SessionLocal = get_session_factory(self.engine)
        
    def create_multi_table_task(self, user_id: str, task_data: TaskCreateDirect) -> str:
        """创建多表比对任务"""
        with self.SessionLocal() as session:
            task_id = str(uuid.uuid4())
            
            # 创建主任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                task_name=task_data.task_name,
                description=task_data.description,
                comparison_type=task_data.comparison_type.value,
                source_conn=task_data.source_conn,
                target_conn=task_data.target_conn,
                comparison_rules=task_data.comparison_rules,
                batch_size=task_data.batch_size,
                timeout_seconds=task_data.timeout_seconds,
                priority=task_data.priority
            )
            session.add(task)
            session.flush()  # 获取task.id
            
            # 创建表规则
            for rule_data in task_data.sql_rules:
                rule_id = str(uuid.uuid4())
                table_rule = ComparisonTableRule(
                    rule_id=rule_id,
                    task_id=task_id,
                    table_id=rule_data.table_id,
                    table_name=rule_data.table_name or rule_data.table_id,
                    remark=rule_data.remark,
                    source_sql=rule_data.source_sql,
                    target_sql=rule_data.target_sql,
                    primary_keys=rule_data.primary_keys,
                    ignore_fields=rule_data.ignore_fields,
                    field_mappings=rule_data.field_mappings,
                    batch_size=rule_data.batch_size or task_data.batch_size,
                    timeout_seconds=rule_data.timeout_seconds or task_data.timeout_seconds,
                    priority=rule_data.priority or task_data.priority
                )
                session.add(table_rule)
            
            session.commit()
            return task_id
    
    def get_task_with_rules(self, task_id: str) -> Dict[str, Any]:
        """获取任务及其表规则"""
        with self.SessionLocal() as session:
            task = session.query(ComparisonTask).filter(
                ComparisonTask.task_id == task_id
            ).first()
            
            if not task:
                return {"error": "Task not found"}
            
            table_rules = session.query(ComparisonTableRule).filter(
                ComparisonTableRule.task_id == task_id
            ).all()
            
            return {
                "task_id": task.task_id,
                "task_name": task.task_name,
                "status": task.status,
                "progress_percentage": float(task.progress_percentage),
                "total_tables": len(table_rules),
                "completed_tables": len([r for r in table_rules if r.status == TaskStatus.COMPLETED.value]),
                "table_rules": [
                    {
                        "rule_id": rule.rule_id,
                        "table_id": rule.table_id,
                        "table_name": rule.table_name,
                        "remark": rule.remark,
                        "status": rule.status,
                        "progress_percentage": float(rule.progress_percentage),
                        "total_records": rule.total_records,
                        "processed_records": rule.processed_records,
                        "different_records": rule.different_records,
                        "execution_time_seconds": float(rule.execution_time_seconds),
                        "error_message": rule.error_message
                    }
                    for rule in table_rules
                ],
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }
    
    async def execute_multi_table_task(self, task_id: str) -> bool:
        """执行多表比对任务"""
        try:
            with self.SessionLocal() as session:
                # 获取任务和表规则
                task = session.query(ComparisonTask).filter(
                    ComparisonTask.task_id == task_id
                ).first()
                
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                table_rules = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.task_id == task_id
                ).order_by(ComparisonTableRule.priority.desc()).all()
                
                # 更新任务状态
                task.status = TaskStatus.RUNNING.value
                task.started_at = datetime.now()
                session.commit()
                
                # 按优先级顺序执行每个表的比对
                completed_tables = 0
                total_tables = len(table_rules)
                
                for i, table_rule in enumerate(table_rules):
                    try:
                        # 更新表规则状态
                        table_rule.status = TaskStatus.RUNNING.value
                        table_rule.started_at = datetime.now()
                        session.commit()
                        
                        # 执行单表比对
                        success = await self._execute_single_table_comparison(
                            session, task, table_rule
                        )
                        
                        if success:
                            table_rule.status = TaskStatus.COMPLETED.value
                            table_rule.completed_at = datetime.now()
                            table_rule.progress_percentage = 100.0
                            completed_tables += 1
                        else:
                            table_rule.status = TaskStatus.FAILED.value
                            table_rule.completed_at = datetime.now()
                        
                        # 更新任务整体进度
                        task.progress_percentage = (completed_tables / total_tables) * 100
                        task.current_step = f"已完成 {completed_tables}/{total_tables} 个表的比对"
                        
                        session.commit()
                        
                    except Exception as e:
                        table_rule.status = TaskStatus.FAILED.value
                        table_rule.error_message = str(e)
                        table_rule.completed_at = datetime.now()
                        session.commit()
                        print(f"表 {table_rule.table_id} 比对失败: {e}")
                
                # 完成任务
                if completed_tables == total_tables:
                    task.status = TaskStatus.COMPLETED.value
                    task.current_step = "所有表比对完成"
                else:
                    task.status = TaskStatus.FAILED.value
                    task.current_step = f"部分表比对失败，成功: {completed_tables}/{total_tables}"
                
                task.completed_at = datetime.now()
                task.progress_percentage = 100.0
                session.commit()
                
                return completed_tables == total_tables
                
        except Exception as e:
            print(f"执行多表比对任务失败: {e}")
            return False
    
    async def _execute_single_table_comparison(self, session, task: ComparisonTask, 
                                             table_rule: ComparisonTableRule) -> bool:
        """执行单表比对"""
        try:
            # 创建适配器
            adapter = ServiceSqliteAdapter(session, task.task_id)
            
            # 模拟比对过程
            print(f"开始比对表: {table_rule.table_id}")
            
            # 更新进度
            for progress in [10, 30, 50, 70, 90, 100]:
                table_rule.progress_percentage = progress
                table_rule.current_step = f"处理进度 {progress}%"
                session.commit()
                await asyncio.sleep(0.5)  # 模拟处理时间
            
            # 模拟结果统计
            table_rule.total_records = 10000
            table_rule.processed_records = 10000
            table_rule.different_records = 150
            table_rule.source_only_records = 25
            table_rule.target_only_records = 30
            table_rule.execution_time_seconds = 5.5
            
            print(f"完成比对表: {table_rule.table_id}")
            return True
            
        except Exception as e:
            print(f"单表比对失败: {e}")
            return False


# 使用示例
async def main():
    """主函数示例"""
    # 创建服务实例
    service = MultiTableComparisonService()
    
    # 创建用户
    user_data = {
        'user_id': 'multi_table_user',
        'username': '多表比对用户',
        'email': '<EMAIL>',
        'password': 'multi_hash',
        'role': 'user'
    }
    
    with service.SessionLocal() as session:
        try:
            user = User(**user_data)
            session.add(user)
            session.commit()
            print(f"创建用户成功: {user.user_id}")
        except Exception as e:
            print(f"用户可能已存在: {e}")
    
    # 创建多表比对任务
    task_data = TaskCreate(
        task_name='银行核心系统数据一致性检查',
        description='比对生产环境和灾备环境的核心业务表',
        comparison_type=ComparisonType.CONTENT,
        source_conn={
            'db_type': 'db2',
            'host': 'prod-db.bank.com',
            'port': 50000,
            'database': 'core_banking',
            'username': 'user',
            'password': 'password'
        },
        target_conn={
            'db_type': 'db2',
            'host': 'dr-db.bank.com',
            'port': 50000,
            'database': 'core_banking',
            'username': 'user',
            'password': 'password'
        },
        sql_rules=[
            TableRuleCreate(
                table_id='customer_accounts',
                table_name='customer_accounts',
                remark='客户账户表',
                source_sql='SELECT ACCOUNT_ID AS KEY, ca.* FROM customer_accounts ca WHERE status = \'ACTIVE\'',
                target_sql='SELECT ACCOUNT_ID AS KEY, ca.* FROM customer_accounts ca WHERE status = \'ACTIVE\'',
                primary_keys=['ACCOUNT_ID'],
                ignore_fields=['last_updated', 'sync_timestamp'],
                batch_size=50000,
                priority=9
            ),
            TableRuleCreate(
                table_id='transaction_records',
                table_name='transaction_records',
                remark='交易记录表',
                source_sql='SELECT TRANS_ID AS KEY, tr.* FROM transaction_records tr WHERE trans_date >= \'2024-01-01\'',
                target_sql='SELECT TRANS_ID AS KEY, tr.* FROM transaction_records tr WHERE trans_date >= \'2024-01-01\'',
                primary_keys=['TRANS_ID'],
                batch_size=30000,
                priority=8
            ),
            TableRuleCreate(
                table_id='loan_contracts',
                table_name='loan_contracts',
                remark='贷款合同表',
                source_sql='SELECT CONTRACT_ID AS KEY, lc.* FROM loan_contracts lc',
                target_sql='SELECT CONTRACT_ID AS KEY, lc.* FROM loan_contracts lc',
                primary_keys=['CONTRACT_ID'],
                batch_size=20000,
                priority=7
            )
        ],
        batch_size=20000,
        timeout_seconds=7200,
        priority=8
    )
    
    task_id = service.create_multi_table_task('multi_table_user', task_data)
    print(f"创建多表比对任务成功: {task_id}")
    
    # 查询任务状态
    task_info = service.get_task_with_rules(task_id)
    print(f"任务信息: {task_info}")
    
    # 执行任务
    print("开始执行多表比对任务...")
    success = await service.execute_multi_table_task(task_id)
    print(f"任务执行结果: {'成功' if success else '失败'}")
    
    # 查询最终结果
    final_info = service.get_task_with_rules(task_id)
    print(f"最终任务信息: {final_info}")


if __name__ == "__main__":
    asyncio.run(main())
