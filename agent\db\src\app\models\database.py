"""
数据库引擎模型

定义数据库引擎接口和实现，提供统一的数据库操作方法。
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod


class DatabaseEngine(ABC):
    """数据库引擎抽象基类"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接数据库"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """断开数据库连接"""
        pass
    
    @abstractmethod
    async def is_connected(self) -> bool:
        """检查是否已连接"""
        pass
    
    @abstractmethod
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行查询并返回结果集
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表，每行为一个字典
        """
        pass
    
    @abstractmethod
    async def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        执行查询并返回单个值
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果的第一行第一列的值
        """
        pass
    
    @abstractmethod
    async def execute_row(self, query: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, ...]:
        """
        执行查询并返回单行结果
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果的第一行，作为元组返回
        """
        pass
    
    @abstractmethod
    async def execute_nonquery(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """
        执行非查询操作并返回影响的行数
        
        Args:
            query: SQL语句
            params: 查询参数
            
        Returns:
            受影响的行数
        """
        pass
    
    @abstractmethod
    async def begin_transaction(self) -> None:
        """开始事务"""
        pass
    
    @abstractmethod
    async def commit(self) -> None:
        """提交事务"""
        pass
    
    @abstractmethod
    async def rollback(self) -> None:
        """回滚事务"""
        pass
    
    @abstractmethod
    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """
        获取表结构
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息
        """
        pass
    
    @abstractmethod
    async def get_primary_keys(self, table_name: str) -> List[str]:
        """
        获取表的主键列表
        
        Args:
            table_name: 表名
            
        Returns:
            主键列名列表
        """
        pass
    
    @abstractmethod
    async def get_column_names(self, table_name: str) -> List[str]:
        """
        获取表的所有列名
        
        Args:
            table_name: 表名
            
        Returns:
            列名列表
        """
        pass
    
    @abstractmethod
    async def acquire(self):
        """获取连接"""
        pass
    
    @abstractmethod
    def release(self):
        """释放连接"""
        pass
    
    @property
    @abstractmethod
    def db_type(self) -> str:
        """获取数据库类型"""
        pass
