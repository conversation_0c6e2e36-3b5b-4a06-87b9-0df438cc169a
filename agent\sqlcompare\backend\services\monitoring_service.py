#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控服务层
"""

import os
import sys
import psutil
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from reporters.sqlite_reporter import TaskManager

logger = logging.getLogger(__name__)


class MonitoringService:
    """监控服务类"""
    
    def __init__(self, db: Session, task_manager: TaskManager):
        self.db = db
        self.task_manager = task_manager
    
    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # 获取系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 检查数据库连接
            db_healthy = True
            try:
                if self.task_manager:
                    # 尝试执行一个简单的查询
                    self.task_manager.get_task_list(limit=1)
            except Exception:
                db_healthy = False
            
            # 计算整体健康状态
            overall_status = "healthy"
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90 or not db_healthy:
                overall_status = "unhealthy"
            elif cpu_percent > 70 or memory.percent > 70 or disk.percent > 70:
                overall_status = "warning"
            
            return {
                "status": overall_status,
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "database": "healthy" if db_healthy else "unhealthy",
                    "cpu": "healthy" if cpu_percent < 70 else "warning" if cpu_percent < 90 else "critical",
                    "memory": "healthy" if memory.percent < 70 else "warning" if memory.percent < 90 else "critical",
                    "disk": "healthy" if disk.percent < 70 else "warning" if disk.percent < 90 else "critical"
                },
                "metrics": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_free_gb": disk.free / (1024**3)
                }
            }
            
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
            cpu_freq = psutil.cpu_freq()
            
            # 内存信息
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # 网络信息
            network_io = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            return {
                "timestamp": datetime.now().isoformat(),
                "cpu": {
                    "count": cpu_count,
                    "percent_total": sum(cpu_percent) / len(cpu_percent),
                    "percent_per_core": cpu_percent,
                    "frequency_mhz": cpu_freq.current if cpu_freq else None
                },
                "memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "used_gb": memory.used / (1024**3),
                    "percent": memory.percent
                },
                "swap": {
                    "total_gb": swap.total / (1024**3),
                    "used_gb": swap.used / (1024**3),
                    "percent": swap.percent
                },
                "disk": {
                    "total_gb": disk_usage.total / (1024**3),
                    "used_gb": disk_usage.used / (1024**3),
                    "free_gb": disk_usage.free / (1024**3),
                    "percent": (disk_usage.used / disk_usage.total) * 100,
                    "read_bytes": disk_io.read_bytes if disk_io else 0,
                    "write_bytes": disk_io.write_bytes if disk_io else 0
                },
                "network": {
                    "bytes_sent": network_io.bytes_sent,
                    "bytes_recv": network_io.bytes_recv,
                    "packets_sent": network_io.packets_sent,
                    "packets_recv": network_io.packets_recv
                },
                "processes": {
                    "count": process_count
                }
            }
            
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            raise
    
    async def get_task_real_time_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务实时状态"""
        try:
            if not self.task_manager:
                return None
            
            # 获取任务信息
            task_info = self.task_manager.get_task(task_id)
            if not task_info:
                return None
            
            # 获取最新进度
            progress_logs = self.task_manager.get_task_progress(task_id, limit=1)
            latest_progress = progress_logs[0] if progress_logs else None
            
            # 计算实时指标
            total_records = task_info.get('total_records', 0)
            processed_records = task_info.get('processed_records', 0)
            progress_percentage = 0.0
            
            if total_records > 0:
                progress_percentage = (processed_records / total_records) * 100
            
            return {
                "task_id": task_id,
                "status": task_info['status'],
                "progress_percentage": progress_percentage,
                "processed_records": processed_records,
                "total_records": total_records,
                "current_step": latest_progress['step_name'] if latest_progress else None,
                "last_update": latest_progress['created_at'] if latest_progress else None,
                "execution_time": task_info.get('exec_time', 0),
                "differences_found": task_info.get('diff_records', 0),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取任务实时状态失败: {task_id}, {e}")
            raise
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        try:
            if not self.task_manager:
                return []
            
            # 获取运行中的任务
            running_tasks = self.task_manager.get_task_list(status="running", limit=100)
            
            active_tasks = []
            for task in running_tasks:
                task_status = await self.get_task_real_time_status(task['task_id'])
                if task_status:
                    active_tasks.append(task_status)
            
            return active_tasks
            
        except Exception as e:
            logger.error(f"获取活跃任务列表失败: {e}")
            raise
    
    async def get_performance_metrics(self, time_range: str = "1h") -> Dict[str, Any]:
        """获取性能指标"""
        try:
            # 模拟性能数据
            now = datetime.now()
            
            if time_range == "1h":
                start_time = now - timedelta(hours=1)
                data_points = 12  # 每5分钟一个点
            elif time_range == "6h":
                start_time = now - timedelta(hours=6)
                data_points = 24  # 每15分钟一个点
            elif time_range == "24h":
                start_time = now - timedelta(hours=24)
                data_points = 24  # 每小时一个点
            else:  # 7d
                start_time = now - timedelta(days=7)
                data_points = 28  # 每6小时一个点
            
            # 生成模拟数据
            time_series = []
            for i in range(data_points):
                timestamp = start_time + timedelta(seconds=(now - start_time).total_seconds() * i / data_points)
                time_series.append({
                    "timestamp": timestamp.isoformat(),
                    "cpu_percent": 20 + (i % 5) * 10,  # 模拟CPU使用率
                    "memory_percent": 30 + (i % 3) * 15,  # 模拟内存使用率
                    "active_tasks": max(0, 5 - (i % 8)),  # 模拟活跃任务数
                    "completed_tasks": i * 2,  # 模拟完成任务数
                    "throughput": 1000 + (i % 4) * 200  # 模拟吞吐量
                })
            
            return {
                "time_range": time_range,
                "start_time": start_time.isoformat(),
                "end_time": now.isoformat(),
                "data_points": data_points,
                "metrics": time_series
            }
            
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            raise
    
    async def get_system_alerts(self, severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取系统告警"""
        try:
            # 模拟告警数据
            alerts = [
                {
                    "id": "alert_001",
                    "severity": "warning",
                    "title": "CPU使用率较高",
                    "message": "系统CPU使用率超过70%",
                    "timestamp": datetime.now().isoformat(),
                    "acknowledged": False
                },
                {
                    "id": "alert_002",
                    "severity": "info",
                    "title": "任务完成",
                    "message": "比对任务 task_123 已完成",
                    "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
                    "acknowledged": True
                }
            ]
            
            if severity:
                alerts = [alert for alert in alerts if alert['severity'] == severity]
            
            return alerts
            
        except Exception as e:
            logger.error(f"获取系统告警失败: {e}")
            raise
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        try:
            # 这里应该实际更新告警状态
            logger.info(f"告警确认成功: {alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"确认告警失败: {alert_id}, {e}")
            raise
    
    async def get_system_logs(self, level: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取系统日志"""
        try:
            # 模拟日志数据
            logs = [
                {
                    "timestamp": datetime.now().isoformat(),
                    "level": "INFO",
                    "logger": "task_service",
                    "message": "任务创建成功: task_123",
                    "module": "backend.services.task_service"
                },
                {
                    "timestamp": (datetime.now() - timedelta(minutes=1)).isoformat(),
                    "level": "WARNING",
                    "logger": "monitoring_service",
                    "message": "CPU使用率较高: 75%",
                    "module": "backend.services.monitoring_service"
                }
            ]
            
            if level:
                logs = [log for log in logs if log['level'] == level]
            
            return logs[:limit]
            
        except Exception as e:
            logger.error(f"获取系统日志失败: {e}")
            raise
    
    async def get_task_logs(self, task_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取任务日志"""
        try:
            if not self.task_manager:
                return []
            
            # 获取任务进度日志
            progress_logs = self.task_manager.get_task_progress(task_id, limit=limit)
            
            # 转换为统一的日志格式
            task_logs = []
            for log in progress_logs:
                task_logs.append({
                    "timestamp": log['created_at'],
                    "level": "INFO",
                    "step": log['step_name'],
                    "progress": log['progress_percentage'],
                    "message": log.get('message', ''),
                    "processed_records": log.get('processed_records', 0),
                    "total_records": log.get('total_records', 0)
                })
            
            return task_logs
            
        except Exception as e:
            logger.error(f"获取任务日志失败: {task_id}, {e}")
            raise
