#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP API比对模式
通过REST API调用远程比对服务进行数据比对
"""

import os
import sys
import time
import uuid
import asyncio
import logging
from typing import Dict, Any, List, Optional

# 添加comparer目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
COMPARER_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))  # gui -> backend -> comparer
sys.path.insert(0, COMPARER_ROOT)

try:
    from .comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )
except ImportError:
    from comparison_interface import (
        ComparisonInterface, ComparisonConfig, ComparisonResult,
        ComparisonStatus, ComparisonModeFactory
    )

logger = logging.getLogger(__name__)


class APIComparisonMode(ComparisonInterface):
    """HTTP API比对模式
    
    特点：
    - 通过HTTP API调用远程比对服务
    - 支持Legacy API和新版本API
    - 适合分布式部署和大规模数据比对
    - 支持任务状态监控和进度跟踪
    """
    
    def __init__(self, gui_app, config: Optional[Dict[str, Any]] = None):
        super().__init__(gui_app, config)
        self.api_client = None
        self.base_url = config.get('base_url', 'http://localhost:8000') if config else 'http://localhost:8000'
        self.api_task_id = None
        self._monitoring_task = None
        
    @property
    def mode_name(self) -> str:
        return "api"
    
    @property
    def display_name(self) -> str:
        return "HTTP API比对"
    
    async def is_available(self) -> bool:
        """检查API比对模式是否可用"""
        try:
            return await self._check_api_availability()
        except Exception as e:
            logger.error(f"检查API比对可用性失败: {e}")
            return False
    
    async def _check_api_availability(self) -> bool:
        """检查API服务可用性"""
        try:
            return False
            # 获取或创建API客户端
            if not self.api_client:
                self.api_client = self._create_api_client()
            
            # 检测API能力
            if hasattr(self.api_client, '_detect_api_capabilities'):
                self.api_client._detect_api_capabilities()
            
            # 检查是否有可用的API端点
            return (self.api_client.legacy_api_available or 
                   self.api_client.backend_api_available)
            
        except Exception as e:
            logger.error(f"API可用性检查失败: {e}")
            return False
    
    def _create_api_client(self):
        """创建API客户端"""
        try:
            # 如果GUI应用有API客户端，使用它
            if hasattr(self.gui_app, 'api_client') and self.gui_app.api_client:
                return self.gui_app.api_client

            # 导入独立的APIClient类
            try:
                from api_client import APIClient
                return APIClient(self.base_url)
            except ImportError:
                # 回退到原始app.py中的APIClient
                try:
                    import sys
                    import os
                    comparer_dir = COMPARER_ROOT
                    app_path = os.path.join(comparer_dir, 'app.py')
                    if os.path.exists(app_path):
                        sys.path.insert(0, comparer_dir)
                        from app import APIClient
                        return APIClient(self.base_url)
                    else:
                        raise Exception("无法找到APIClient类")
                except Exception as e:
                    raise Exception(f"APIClient导入失败: {e}")

        except Exception as e:
            logger.error(f"创建API客户端失败: {e}")
            raise
    
    async def initialize(self) -> bool:
        """初始化API比对模式"""
        try:
            self.status = ComparisonStatus.INITIALIZING
            
            # 创建API客户端
            self.api_client = self._create_api_client()
            
            # 检查API可用性
            if not await self._check_api_availability():
                self._report_error("API服务不可用")
                self.status = ComparisonStatus.FAILED
                return False
            
            self.status = ComparisonStatus.IDLE
            return True
            
        except Exception as e:
            self._report_error(f"API比对模式初始化失败: {str(e)}")
            self.status = ComparisonStatus.FAILED
            return False
    
    async def execute_comparison(self, comparison_config: ComparisonConfig) -> ComparisonResult:
        """执行API比对"""
        self.start_time = time.time()
        self.status = ComparisonStatus.RUNNING
        self.current_task_id = f"api_{uuid.uuid4().hex[:8]}"
        
        try:
            # 验证配置
            is_valid, error_msg = await self.validate_config(comparison_config)
            if not is_valid:
                raise ValueError(error_msg)
            
            self._update_progress(0, "开始API比对")
            
            # 创建API比对任务
            self.api_task_id = await self._create_api_task(comparison_config)
            
            # 监控任务进度
            result = await self._monitor_api_task()
            
            # 创建最终结果
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time
            
            final_result = self._create_result(
                success=result.get('success', False),
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=result.get('completed_tables', 0),
                differences=result.get('differences', []),
                error_message=result.get('error_message')
            )
            
            self.last_result = final_result
            self.status = ComparisonStatus.COMPLETED if final_result.success else ComparisonStatus.FAILED
            self._update_progress(100, "API比对完成")
            self._report_completion(final_result)
            
            return final_result
            
        except Exception as e:
            self.end_time = time.time()
            execution_time = self.end_time - self.start_time if self.start_time else 0
            
            error_message = str(e)
            self._report_error(f"API比对失败: {error_message}")
            
            result = self._create_result(
                success=False,
                execution_time=execution_time,
                total_tables=len(comparison_config.tables),
                completed_tables=0,
                differences=[],
                error_message=error_message
            )
            
            self.last_result = result
            self.status = ComparisonStatus.FAILED
            return result
    
    async def _create_api_task(self, comparison_config: ComparisonConfig) -> str:
        """创建API比对任务"""
        try:
            self._update_progress(10, "创建API比对任务")
            
            # 构建API请求数据
            request_data = self._build_api_request(comparison_config)
            
            # 调用API创建任务
            response = self.api_client.create_comparison_task(request_data)
            
            # 检查响应
            if not self._is_api_response_successful(response):
                error_msg = self._extract_error_message_from_response(response)
                raise Exception(f"API任务创建失败: {error_msg}")
            
            # 提取任务ID
            task_id = self._extract_task_id_from_response(response)
            self._update_progress(20, f"API任务创建成功，任务ID: {task_id}")
            
            return task_id
            
        except Exception as e:
            raise Exception(f"创建API任务失败: {str(e)}")
    
    def _build_api_request(self, comparison_config: ComparisonConfig) -> Dict[str, Any]:
        """构建API请求数据"""
        try:
            # 根据API版本构建不同格式的请求
            if self.api_client.legacy_api_available:
                return self._build_legacy_api_request(comparison_config)
            else:
                return self._build_new_api_request(comparison_config)
                
        except Exception as e:
            raise Exception(f"构建API请求失败: {str(e)}")
    
    def _build_legacy_api_request(self, comparison_config: ComparisonConfig) -> Dict[str, Any]:
        """构建Legacy API请求"""
        # 取第一个表的配置作为主要配置
        first_table = comparison_config.tables[0] if comparison_config.tables else {}
        
        return {
            "source": {
                "type": comparison_config.source_config['db_type'],
                "host": comparison_config.source_config['host'],
                "port": comparison_config.source_config['port'],
                "username": comparison_config.source_config['username'],
                "password": comparison_config.source_config['password'],
                "database": comparison_config.source_config['database']
            },
            "target": {
                "type": comparison_config.target_config['db_type'],
                "host": comparison_config.target_config['host'],
                "port": comparison_config.target_config['port'],
                "username": comparison_config.target_config['username'],
                "password": comparison_config.target_config['password'],
                "database": comparison_config.target_config['database']
            },
            "config": {
                "mode": "query",
                "source_sql": first_table.get("sql_1", ""),
                "target_sql": first_table.get("sql_2", ""),
                "primary_keys": first_table.get("primary_keys", ["id"]),
                "batch_size": comparison_config.batch_size,
                "algorithm_type": "primary_key"
            }
        }
    
    def _build_new_api_request(self, comparison_config: ComparisonConfig) -> Dict[str, Any]:
        """构建新版本API请求"""
        first_table = comparison_config.tables[0] if comparison_config.tables else {}
        
        return {
            "source_connection": comparison_config.source_config,
            "target_connection": comparison_config.target_config,
            "comparison_type": "content",
            "table_name": first_table.get("table_id", "unknown"),
            "sql_rules": [
                {
                    "table_id": table["table_id"],
                    "source_sql": table["sql_1"],
                    "target_sql": table["sql_2"],
                    "remark": table.get("remark", "")
                }
                for table in comparison_config.tables
            ],
            "batch_size": comparison_config.batch_size,
            "timeout": comparison_config.timeout
        }
    
    async def _monitor_api_task(self) -> Dict[str, Any]:
        """监控API任务进度"""
        try:
            self._update_progress(30, "开始监控API任务进度")
            
            # 创建监控任务
            self._monitoring_task = asyncio.create_task(self._task_monitoring_loop())
            
            # 等待监控完成
            result = await self._monitoring_task
            
            return result
            
        except asyncio.CancelledError:
            self._update_progress(0, "API任务监控已取消")
            return {'success': False, 'error_message': '任务已取消'}
        except Exception as e:
            raise Exception(f"监控API任务失败: {str(e)}")
    
    async def _task_monitoring_loop(self) -> Dict[str, Any]:
        """任务监控循环"""
        max_wait_time = 3600  # 最大等待时间1小时
        check_interval = 5    # 检查间隔5秒
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            if self.cancel_requested:
                await self._cancel_api_task()
                return {'success': False, 'error_message': '用户取消任务'}
            
            try:
                # 获取任务状态
                status_response = await self._get_task_status()
                
                if self._is_task_completed(status_response):
                    # 任务完成，获取结果
                    return await self._get_task_result()
                elif self._is_task_failed(status_response):
                    # 任务失败
                    error_msg = self._extract_error_from_status(status_response)
                    return {'success': False, 'error_message': error_msg}
                else:
                    # 任务进行中，更新进度
                    progress = self._extract_progress_from_status(status_response)
                    self._update_progress(30 + int(progress * 0.6), f"API任务进行中: {progress:.1f}%")
                
            except Exception as e:
                logger.warning(f"获取任务状态失败: {e}")
            
            # 等待下次检查
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval
        
        # 超时
        return {'success': False, 'error_message': 'API任务执行超时'}
    
    async def _get_task_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            if hasattr(self.api_client, 'get_task_status'):
                return self.api_client.get_task_status(self.api_task_id)
            else:
                # 模拟状态检查
                return {'status': 'running', 'progress': 50}
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {'status': 'unknown'}
    
    async def _get_task_result(self) -> Dict[str, Any]:
        """获取任务结果"""
        try:
            if hasattr(self.api_client, 'get_task_result'):
                result = self.api_client.get_task_result(self.api_task_id)
                return self._convert_api_result(result)
            else:
                # 模拟结果
                return {
                    'success': True,
                    'completed_tables': 1,
                    'differences': []
                }
        except Exception as e:
            logger.error(f"获取任务结果失败: {e}")
            return {'success': False, 'error_message': str(e)}
    
    def _convert_api_result(self, api_result: Dict[str, Any]) -> Dict[str, Any]:
        """转换API结果格式"""
        try:
            # 根据API版本转换结果格式
            differences = []
            
            # 处理差异记录
            if 'differences' in api_result:
                for diff in api_result['differences']:
                    differences.append({
                        'id': diff.get('id', str(uuid.uuid4())),
                        'table_id': diff.get('table_id', 'unknown'),
                        'diff_type': diff.get('type', 'difference'),
                        'source_data': diff.get('source_data', {}),
                        'target_data': diff.get('target_data', {}),
                        'diff_fields': diff.get('diff_fields', [])
                    })
            
            return {
                'success': api_result.get('success', True),
                'completed_tables': api_result.get('completed_tables', 1),
                'differences': differences,
                'error_message': api_result.get('error_message')
            }
            
        except Exception as e:
            logger.error(f"转换API结果失败: {e}")
            return {'success': False, 'error_message': str(e)}
    
    def _is_task_completed(self, status_response: Dict[str, Any]) -> bool:
        """检查任务是否完成"""
        status = status_response.get('status', '').lower()
        return status in ['completed', 'finished', 'success']
    
    def _is_task_failed(self, status_response: Dict[str, Any]) -> bool:
        """检查任务是否失败"""
        status = status_response.get('status', '').lower()
        return status in ['failed', 'error', 'cancelled']
    
    def _extract_progress_from_status(self, status_response: Dict[str, Any]) -> float:
        """从状态响应中提取进度"""
        return status_response.get('progress', 0.0)
    
    def _extract_error_from_status(self, status_response: Dict[str, Any]) -> str:
        """从状态响应中提取错误信息"""
        return status_response.get('error', '任务执行失败')
    
    def _is_api_response_successful(self, response: Dict[str, Any]) -> bool:
        """检查API响应是否成功"""
        if not response:
            return False
        
        # Legacy API格式
        if "success" in response:
            return response.get("success", False)
        
        # 新版本API格式
        if "status" in response:
            return response.get("status") == "success"
        
        # 通用检查
        return "task_id" in response or ("data" in response and "task_id" in response["data"])
    
    def _extract_task_id_from_response(self, response: Dict[str, Any]) -> str:
        """从API响应中提取任务ID"""
        # Legacy API格式
        if "data" in response and "task_id" in response["data"]:
            return response["data"]["task_id"]
        
        # 新版本API格式
        if "task_id" in response:
            return response["task_id"]
        
        # 备用格式
        if "id" in response:
            return response["id"]
        
        raise ValueError("无法从API响应中提取任务ID")
    
    def _extract_error_message_from_response(self, response: Dict[str, Any]) -> str:
        """从API响应中提取错误信息"""
        if not response:
            return "未知错误"
        
        # Legacy API格式
        if "message" in response:
            return response["message"]
        
        # 新版本API格式
        if "error" in response:
            return response["error"]
        
        # 详细错误信息
        if "detail" in response:
            return response["detail"]
        
        return "API响应格式不识别"
    
    async def _cancel_api_task(self) -> None:
        """取消API任务"""
        try:
            if hasattr(self.api_client, 'cancel_task'):
                self.api_client.cancel_task(self.api_task_id)
        except Exception as e:
            logger.error(f"取消API任务失败: {e}")
    
    async def cancel_comparison(self) -> bool:
        """取消比对"""
        try:
            self.cancel_requested = True
            
            # 取消监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
            
            # 取消API任务
            if self.api_task_id:
                await self._cancel_api_task()
            
            self.status = ComparisonStatus.CANCELLED
            self._update_progress(0, "API比对已取消")
            return True
        except Exception as e:
            logger.error(f"取消API比对失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 取消监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
            
            self.status = ComparisonStatus.IDLE
            
        except Exception as e:
            logger.error(f"清理API比对资源失败: {e}")


# 注册API比对模式
try:
    ComparisonModeFactory.register_mode("api", APIComparisonMode)
except Exception as e:
    logger.error(f"❌ APIComparisonMode 注册失败: {e}")
