#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目路径管理模块
统一管理Backend API与原项目的路径关系
"""

import os
import sys
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class ProjectPaths:
    """项目路径管理器 - 统一管理所有路径配置"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        
        # 确定项目根目录
        self.backend_root = Path(__file__).parent.parent.resolve()
        self.project_root = self.backend_root.parent.resolve()
        
        # 验证项目结构
        self._validate_project_structure()
        
        # 添加项目路径到sys.path
        self._setup_python_path()
        
        logger.info(f"项目路径初始化完成:")
        logger.info(f"  Backend根目录: {self.backend_root}")
        logger.info(f"  项目根目录: {self.project_root}")
    
    def _validate_project_structure(self):
        """验证项目目录结构"""
        required_dirs = [
            'config',
            'connectors', 
            'core',
            'reporters',
            'utils'
        ]
        
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
        
        if missing_dirs:
            logger.warning(f"缺少以下目录: {missing_dirs}")
    
    def _setup_python_path(self):
        """设置Python模块搜索路径"""
        paths_to_add = [
            str(self.project_root),
            str(self.backend_root)
        ]
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
                logger.debug(f"添加到Python路径: {path}")
    
    # 核心目录路径
    @property
    def config_dir(self) -> Path:
        """配置文件目录"""
        return self.project_root / "config"
    
    @property
    def connectors_dir(self) -> Path:
        """连接器目录"""
        return self.project_root / "connectors"
    
    @property
    def core_dir(self) -> Path:
        """核心引擎目录"""
        return self.project_root / "core"
    
    @property
    def reporters_dir(self) -> Path:
        """报告器目录"""
        return self.project_root / "reporters"
    
    @property
    def utils_dir(self) -> Path:
        """工具类目录"""
        return self.project_root / "utils"
    
    # 配置文件路径
    @property
    def config_ini_file(self) -> Path:
        """Config.ini配置文件路径"""
        return self.config_dir / "Config.ini"
    
    @property
    def rules_xml_file(self) -> Path:
        """比对规则XML文件路径"""
        return self.config_dir / "kgdb2kgdb.xml"
    
    # 数据库文件路径
    @property
    def sqlite_database_file(self) -> Path:
        """SQLite数据库文件路径"""
        return self.project_root / "comparison_tasks.db"
    
    @property
    def api_database_file(self) -> Path:
        """API服务数据库文件路径"""
        return self.backend_root / "sqlcompare_api.db"
    
    # 日志目录
    @property
    def logs_dir(self) -> Path:
        """日志目录"""
        logs_dir = self.project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        return logs_dir
    
    @property
    def api_logs_dir(self) -> Path:
        """API日志目录"""
        api_logs_dir = self.backend_root / "logs"
        api_logs_dir.mkdir(exist_ok=True)
        return api_logs_dir
    
    # 临时文件目录
    @property
    def temp_dir(self) -> Path:
        """临时文件目录"""
        temp_dir = self.project_root / "temp"
        temp_dir.mkdir(exist_ok=True)
        return temp_dir
    
    @property
    def exports_dir(self) -> Path:
        """导出文件目录"""
        exports_dir = self.project_root / "exports"
        exports_dir.mkdir(exist_ok=True)
        return exports_dir
    
    # 上传文件目录
    @property
    def uploads_dir(self) -> Path:
        """上传文件目录"""
        uploads_dir = self.backend_root / "uploads"
        uploads_dir.mkdir(exist_ok=True)
        return uploads_dir
    
    # 工具方法
    def get_relative_path(self, file_path: Path, base_path: Optional[Path] = None) -> str:
        """获取相对路径"""
        if base_path is None:
            base_path = self.project_root
        
        try:
            return str(file_path.relative_to(base_path))
        except ValueError:
            return str(file_path)
    
    def ensure_dir_exists(self, dir_path: Path) -> Path:
        """确保目录存在"""
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    def get_config_file_path(self, filename: str) -> Path:
        """获取配置文件的完整路径"""
        return self.config_dir / filename
    
    def get_log_file_path(self, filename: str, api_log: bool = False) -> Path:
        """获取日志文件的完整路径"""
        if api_log:
            return self.api_logs_dir / filename
        else:
            return self.logs_dir / filename
    
    def get_temp_file_path(self, filename: str) -> Path:
        """获取临时文件的完整路径"""
        return self.temp_dir / filename
    
    def get_export_file_path(self, filename: str) -> Path:
        """获取导出文件的完整路径"""
        return self.exports_dir / filename
    
    def is_valid_project_root(self) -> bool:
        """验证是否为有效的项目根目录"""
        required_files = [
            'main.py',
            'requirements.txt'
        ]
        
        required_dirs = [
            'config',
            'core',
            'connectors',
            'reporters',
            'utils'
        ]
        
        # 检查必需文件
        for file_name in required_files:
            if not (self.project_root / file_name).exists():
                return False
        
        # 检查必需目录
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).is_dir():
                return False
        
        return True
    
    def __str__(self) -> str:
        return f"ProjectPaths(project_root={self.project_root}, backend_root={self.backend_root})"
    
    def __repr__(self) -> str:
        return self.__str__()


# 全局路径管理器实例
project_paths = ProjectPaths()


def get_project_paths() -> ProjectPaths:
    """获取项目路径管理器实例"""
    return project_paths


def setup_project_paths():
    """设置项目路径（用于初始化）"""
    paths = get_project_paths()
    
    if not paths.is_valid_project_root():
        logger.warning("项目根目录验证失败，可能影响功能正常运行")
    
    # 创建必要的目录
    paths.ensure_dir_exists(paths.logs_dir)
    paths.ensure_dir_exists(paths.api_logs_dir)
    paths.ensure_dir_exists(paths.temp_dir)
    paths.ensure_dir_exists(paths.exports_dir)
    paths.ensure_dir_exists(paths.uploads_dir)
    
    logger.info("项目路径设置完成")
    return paths


# 便捷函数
def get_config_path(filename: str = "Config.ini") -> str:
    """获取配置文件路径"""
    return str(project_paths.get_config_file_path(filename))


def get_database_path() -> str:
    """获取SQLite数据库路径"""
    return str(project_paths.sqlite_database_file)


def get_log_path(filename: str, api_log: bool = False) -> str:
    """获取日志文件路径"""
    return str(project_paths.get_log_file_path(filename, api_log))


if __name__ == "__main__":
    # 测试路径管理器
    paths = setup_project_paths()
    print(f"项目路径管理器: {paths}")
    print(f"配置文件: {paths.config_ini_file}")
    print(f"数据库文件: {paths.sqlite_database_file}")
    print(f"日志目录: {paths.logs_dir}")
    print(f"项目根目录有效: {paths.is_valid_project_root()}")
