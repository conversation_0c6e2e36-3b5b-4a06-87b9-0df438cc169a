"""
增强的SQL解析器模块

支持AS KEY语法解析，包括：
- 简单键值解析：SELECT field AS KEY
- 复合键值解析：SELECT field1||'-'||field2 AS KEY
- 函数表达式键值：SELECT CONCAT(field1, field2) AS KEY
- 向后兼容现有解析逻辑
"""

import re
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

from app.models.logger import logger

class KeyExpressionType(Enum):
    """键值表达式类型"""
    SIMPLE = "simple"           # 简单字段：field AS KEY
    CONCAT = "concat"           # 连接表达式：field1||field2 AS KEY
    FUNCTION = "function"       # 函数表达式：CONCAT(field1, field2) AS KEY
    COMPLEX = "complex"         # 复杂表达式：其他复杂表达式

@dataclass
class ComparisonKeyConfig:
    """比对键值配置"""
    key_expression: str                    # 完整的KEY表达式
    key_fields: List[str]                 # 组成KEY的字段列表
    is_composite: bool                    # 是否为复合键
    key_alias: str                        # KEY的别名
    expression_type: KeyExpressionType    # 表达式类型
    original_sql: str                     # 原始SQL语句
    parsed_successfully: bool             # 是否解析成功
    error_message: Optional[str] = None   # 错误信息

class EnhancedSQLParser:
    """增强的SQL解析器"""
    
    def __init__(self):
        """初始化SQL解析器"""
        self.key_patterns = self._compile_key_patterns()
        self.function_patterns = self._compile_function_patterns()
        
    def _compile_key_patterns(self) -> Dict[str, re.Pattern]:
        """编译AS KEY相关的正则表达式模式"""
        patterns = {
            # 标准AS KEY模式：SELECT ... AS KEY
            'standard_key': re.compile(
                r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*(?:,|\s+from)',
                re.IGNORECASE | re.DOTALL
            ),
            
            # 末尾AS KEY模式：SELECT ... AS KEY FROM
            'end_key': re.compile(
                r'select\s+(.+?)\s+as\s+(?:key|KEY)\s+from',
                re.IGNORECASE | re.DOTALL
            ),
            
            # 单独AS KEY模式：SELECT ... AS KEY$
            'single_key': re.compile(
                r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*$',
                re.IGNORECASE | re.DOTALL
            ),
            
            # 连接操作符模式：||
            'concat_operator': re.compile(r'\|\|'),
            
            # 字段分隔符模式
            'field_separator': re.compile(r"'\s*-\s*'|'\s*_\s*'|'\s*\|\s*'"),
            
            # 函数调用模式
            'function_call': re.compile(
                r'(\w+)\s*\(\s*([^)]+)\s*\)',
                re.IGNORECASE
            )
        }
        
        return patterns
    
    def _compile_function_patterns(self) -> Dict[str, re.Pattern]:
        """编译函数相关的正则表达式模式"""
        patterns = {
            # CONCAT函数
            'concat_func': re.compile(
                r'concat\s*\(\s*([^)]+)\s*\)',
                re.IGNORECASE
            ),
            
            # COALESCE函数
            'coalesce_func': re.compile(
                r'coalesce\s*\(\s*([^)]+)\s*\)',
                re.IGNORECASE
            ),
            
            # TRIM函数
            'trim_func': re.compile(
                r'trim\s*\(\s*([^)]+)\s*\)',
                re.IGNORECASE
            ),
            
            # UPPER/LOWER函数
            'case_func': re.compile(
                r'(upper|lower)\s*\(\s*([^)]+)\s*\)',
                re.IGNORECASE
            )
        }
        
        return patterns
    
    def extract_comparison_key(self, sql: str) -> ComparisonKeyConfig:
        """
        从SQL中提取比对键值配置
        
        Args:
            sql: SQL查询语句
            
        Returns:
            ComparisonKeyConfig: 比对键值配置
        """
        try:
            logger.debug(f"开始解析SQL中的AS KEY语法: {sql[:200]}...")
            
            # 清理SQL语句
            cleaned_sql = self._clean_sql(sql)
            
            # 尝试匹配AS KEY模式
            key_expression = self._extract_key_expression(cleaned_sql)
            
            if not key_expression:
                # 没有找到AS KEY，返回默认配置
                return self._create_default_config(cleaned_sql)
            
            # 解析键值表达式
            return self._parse_key_expression(key_expression, cleaned_sql)
            
        except Exception as e:
            logger.error(f"解析SQL AS KEY语法失败: {str(e)}")
            return ComparisonKeyConfig(
                key_expression="",
                key_fields=[],
                is_composite=False,
                key_alias="KEY",
                expression_type=KeyExpressionType.SIMPLE,
                original_sql=sql,
                parsed_successfully=False,
                error_message=str(e)
            )
    
    def _clean_sql(self, sql: str) -> str:
        """清理SQL语句"""
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', sql.strip())
        
        # 移除注释
        cleaned = re.sub(r'--.*$', '', cleaned, flags=re.MULTILINE)
        cleaned = re.sub(r'/\*.*?\*/', '', cleaned, flags=re.DOTALL)
        
        return cleaned
    
    def _extract_key_expression(self, sql: str) -> Optional[str]:
        """提取AS KEY表达式"""
        # 按优先级尝试不同的模式
        for pattern_name, pattern in self.key_patterns.items():
            if pattern_name.endswith('_key'):
                match = pattern.search(sql)
                if match:
                    key_expr = match.group(1).strip()
                    logger.debug(f"使用模式 {pattern_name} 匹配到KEY表达式: {key_expr}")
                    return key_expr
        
        return None
    
    def _parse_key_expression(self, key_expression: str, original_sql: str) -> ComparisonKeyConfig:
        """解析键值表达式"""
        try:
            # 判断表达式类型
            expression_type = self._determine_expression_type(key_expression)
            
            # 根据类型解析字段
            key_fields = self._extract_fields_from_expression(key_expression, expression_type)
            
            # 判断是否为复合键
            is_composite = len(key_fields) > 1
            
            return ComparisonKeyConfig(
                key_expression=key_expression,
                key_fields=key_fields,
                is_composite=is_composite,
                key_alias="KEY",
                expression_type=expression_type,
                original_sql=original_sql,
                parsed_successfully=True
            )
            
        except Exception as e:
            logger.error(f"解析键值表达式失败: {key_expression}, 错误: {str(e)}")
            return ComparisonKeyConfig(
                key_expression=key_expression,
                key_fields=[],
                is_composite=False,
                key_alias="KEY",
                expression_type=KeyExpressionType.SIMPLE,
                original_sql=original_sql,
                parsed_successfully=False,
                error_message=str(e)
            )
    
    def _determine_expression_type(self, expression: str) -> KeyExpressionType:
        """判断表达式类型"""
        # 检查是否包含函数调用
        if self.key_patterns['function_call'].search(expression):
            return KeyExpressionType.FUNCTION
        
        # 检查是否包含连接操作符
        if self.key_patterns['concat_operator'].search(expression):
            return KeyExpressionType.CONCAT
        
        # 检查是否为简单字段
        if re.match(r'^\w+$', expression.strip()):
            return KeyExpressionType.SIMPLE
        
        # 其他复杂表达式
        return KeyExpressionType.COMPLEX
    
    def _extract_fields_from_expression(self, expression: str, 
                                      expr_type: KeyExpressionType) -> List[str]:
        """从表达式中提取字段列表"""
        fields = []
        
        try:
            if expr_type == KeyExpressionType.SIMPLE:
                # 简单字段
                field = expression.strip()
                if field:
                    fields.append(field)
                    
            elif expr_type == KeyExpressionType.CONCAT:
                # 连接表达式：field1||'-'||field2
                fields = self._parse_concat_expression(expression)
                
            elif expr_type == KeyExpressionType.FUNCTION:
                # 函数表达式
                fields = self._parse_function_expression(expression)
                
            else:
                # 复杂表达式，尝试提取所有可能的字段名
                fields = self._parse_complex_expression(expression)
                
        except Exception as e:
            logger.error(f"提取字段失败: {expression}, 错误: {str(e)}")
            
        return fields
    
    def _parse_concat_expression(self, expression: str) -> List[str]:
        """解析连接表达式"""
        fields = []
        
        # 按||分割
        parts = self.key_patterns['concat_operator'].split(expression)
        
        for part in parts:
            part = part.strip()
            
            # 跳过字符串常量
            if part.startswith("'") and part.endswith("'"):
                continue
                
            # 跳过空白部分
            if not part:
                continue
                
            # 提取字段名
            field = self._extract_field_name(part)
            if field and field not in fields:
                fields.append(field)
        
        return fields
    
    def _parse_function_expression(self, expression: str) -> List[str]:
        """解析函数表达式"""
        fields = []
        
        # 尝试匹配各种函数
        for func_name, pattern in self.function_patterns.items():
            match = pattern.search(expression)
            if match:
                # 提取函数参数
                args_str = match.group(1) if len(match.groups()) >= 1 else match.group(2)
                
                # 解析参数中的字段
                arg_fields = self._parse_function_arguments(args_str)
                fields.extend(arg_fields)
                break
        
        # 如果没有匹配到已知函数，尝试通用函数解析
        if not fields:
            func_match = self.key_patterns['function_call'].search(expression)
            if func_match:
                args_str = func_match.group(2)
                fields = self._parse_function_arguments(args_str)
        
        return fields
    
    def _parse_function_arguments(self, args_str: str) -> List[str]:
        """解析函数参数中的字段"""
        fields = []
        
        # 简单的参数分割（按逗号）
        args = [arg.strip() for arg in args_str.split(',')]
        
        for arg in args:
            # 跳过字符串常量
            if arg.startswith("'") and arg.endswith("'"):
                continue
                
            # 提取字段名
            field = self._extract_field_name(arg)
            if field and field not in fields:
                fields.append(field)
        
        return fields
    
    def _parse_complex_expression(self, expression: str) -> List[str]:
        """解析复杂表达式"""
        fields = []
        
        # 使用正则表达式提取可能的字段名
        field_pattern = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b')
        matches = field_pattern.findall(expression)
        
        # 过滤掉SQL关键字
        sql_keywords = {
            'SELECT', 'FROM', 'WHERE', 'AND', 'OR', 'AS', 'KEY',
            'CONCAT', 'COALESCE', 'TRIM', 'UPPER', 'LOWER',
            'NULL', 'TRUE', 'FALSE'
        }
        
        for match in matches:
            if match.upper() not in sql_keywords and match not in fields:
                fields.append(match)
        
        return fields
    
    def _extract_field_name(self, field_expr: str) -> Optional[str]:
        """从字段表达式中提取字段名"""
        field_expr = field_expr.strip()
        
        # 移除表名前缀（如果有）
        if '.' in field_expr:
            parts = field_expr.split('.')
            field_expr = parts[-1]
        
        # 检查是否为有效的字段名
        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_expr):
            return field_expr
        
        return None
    
    def _create_default_config(self, sql: str) -> ComparisonKeyConfig:
        """创建默认配置（当没有找到AS KEY时）"""
        logger.info("未找到AS KEY语法，使用默认配置")
        
        return ComparisonKeyConfig(
            key_expression="",
            key_fields=["KEY"],  # 使用默认KEY字段
            is_composite=False,
            key_alias="KEY",
            expression_type=KeyExpressionType.SIMPLE,
            original_sql=sql,
            parsed_successfully=True
        )
    
    def validate_key_config(self, config: ComparisonKeyConfig) -> bool:
        """验证键值配置的有效性"""
        try:
            # 检查基本字段
            if not config.parsed_successfully:
                logger.warning(f"键值配置解析失败: {config.error_message}")
                return False
            
            # 检查字段列表
            if not config.key_fields:
                logger.warning("键值字段列表为空")
                return False
            
            # 检查字段名有效性
            for field in config.key_fields:
                if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field):
                    logger.warning(f"无效的字段名: {field}")
                    return False
            
            logger.debug(f"键值配置验证通过: {config.key_fields}")
            return True
            
        except Exception as e:
            logger.error(f"验证键值配置失败: {str(e)}")
            return False
    
    def get_key_expression_sql(self, config: ComparisonKeyConfig) -> str:
        """根据配置生成键值表达式SQL"""
        try:
            if not config.key_fields:
                return "NULL as KEY"
            
            if len(config.key_fields) == 1:
                # 简单键值
                return f"{config.key_fields[0]} as KEY"
            else:
                # 复合键值，使用连接操作
                if config.expression_type == KeyExpressionType.CONCAT:
                    # 保持原始表达式
                    return f"{config.key_expression} as KEY"
                else:
                    # 生成标准连接表达式
                    fields_expr = " || '-' || ".join(config.key_fields)
                    return f"{fields_expr} as KEY"
                    
        except Exception as e:
            logger.error(f"生成键值表达式SQL失败: {str(e)}")
            return "NULL as KEY"
