#pragma once

#include "core/data_structures.hpp"
#include "network/network_types.hpp"
#include <string>
#include <unordered_map>
#include <variant>
#include <optional>
#include <fstream>
#include <mutex>

namespace arkreplay {
namespace config {

// ==================== 配置值类型 ====================

/**
 * 配置值类型 - 支持多种数据类型
 */
using ConfigValue = std::variant<
    std::string,
    int64_t,
    double,
    bool,
    std::vector<std::string>
>;

/**
 * 配置节结构
 */
class ConfigSection {
private:
    std::unordered_map<std::string, ConfigValue> values_;
    mutable std::mutex section_mutex_;

public:
    ConfigSection() = default;
    
    /**
     * 设置配置值
     */
    template<typename T>
    void set(const std::string& key, const T& value) {
        std::lock_guard<std::mutex> lock(section_mutex_);
        values_[key] = value;
    }
    
    /**
     * 获取配置值
     */
    template<typename T>
    std::optional<T> get(const std::string& key) const {
        std::lock_guard<std::mutex> lock(section_mutex_);
        auto it = values_.find(key);
        if (it != values_.end()) {
            if (std::holds_alternative<T>(it->second)) {
                return std::get<T>(it->second);
            }
        }
        return std::nullopt;
    }
    
    /**
     * 获取配置值（带默认值）
     */
    template<typename T>
    T get_or_default(const std::string& key, const T& default_value) const {
        auto value = get<T>(key);
        return value ? *value : default_value;
    }
    
    /**
     * 检查键是否存在
     */
    bool has_key(const std::string& key) const {
        std::lock_guard<std::mutex> lock(section_mutex_);
        return values_.find(key) != values_.end();
    }
    
    /**
     * 获取所有键
     */
    std::vector<std::string> get_keys() const {
        std::lock_guard<std::mutex> lock(section_mutex_);
        std::vector<std::string> keys;
        keys.reserve(values_.size());
        for (const auto& pair : values_) {
            keys.push_back(pair.first);
        }
        return keys;
    }
    
    /**
     * 清除所有配置
     */
    void clear() {
        std::lock_guard<std::mutex> lock(section_mutex_);
        values_.clear();
    }
    
    /**
     * 获取配置数量
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(section_mutex_);
        return values_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(section_mutex_);
        return values_.empty();
    }
};

// ==================== 配置管理器 ====================

/**
 * 配置文件格式
 */
enum class ConfigFormat : uint8_t {
    INI = 0,
    JSON = 1,
    XML = 2,
    YAML = 3
};

/**
 * 配置管理器
 * 支持多种配置文件格式，线程安全
 */
class ConfigManager {
private:
    std::unordered_map<std::string, ConfigSection> sections_;
    mutable std::mutex config_mutex_;
    
    std::string config_file_path_;
    ConfigFormat format_;
    
    // 配置变更监听
    std::vector<std::function<void(const std::string&, const std::string&)>> change_listeners_;

public:
    ConfigManager() : format_(ConfigFormat::INI) {}
    explicit ConfigManager(const std::string& file_path, ConfigFormat format = ConfigFormat::INI)
        : config_file_path_(file_path), format_(format) {}
    
    /**
     * 加载配置文件
     */
    bool load_from_file(const std::string& file_path, ConfigFormat format = ConfigFormat::INI);
    
    /**
     * 保存配置到文件
     */
    bool save_to_file(const std::string& file_path = "") const;
    
    /**
     * 从字符串加载配置
     */
    bool load_from_string(const std::string& config_data, ConfigFormat format = ConfigFormat::INI);
    
    /**
     * 获取配置节
     */
    ConfigSection& get_section(const std::string& section_name) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return sections_[section_name];
    }
    
    const ConfigSection& get_section(const std::string& section_name) const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        static const ConfigSection empty_section;
        auto it = sections_.find(section_name);
        return it != sections_.end() ? it->second : empty_section;
    }
    
    /**
     * 检查节是否存在
     */
    bool has_section(const std::string& section_name) const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return sections_.find(section_name) != sections_.end();
    }
    
    /**
     * 获取所有节名
     */
    std::vector<std::string> get_section_names() const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        std::vector<std::string> names;
        names.reserve(sections_.size());
        for (const auto& pair : sections_) {
            names.push_back(pair.first);
        }
        return names;
    }
    
    /**
     * 便捷方法：直接获取配置值
     */
    template<typename T>
    std::optional<T> get_value(const std::string& section, const std::string& key) const {
        if (has_section(section)) {
            return get_section(section).get<T>(key);
        }
        return std::nullopt;
    }
    
    template<typename T>
    T get_value_or_default(const std::string& section, const std::string& key, const T& default_value) const {
        if (has_section(section)) {
            return get_section(section).get_or_default<T>(key, default_value);
        }
        return default_value;
    }
    
    /**
     * 便捷方法：直接设置配置值
     */
    template<typename T>
    void set_value(const std::string& section, const std::string& key, const T& value) {
        get_section(section).set(key, value);
        notify_change(section, key);
    }
    
    /**
     * 添加配置变更监听器
     */
    void add_change_listener(std::function<void(const std::string&, const std::string&)> listener) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        change_listeners_.push_back(std::move(listener));
    }
    
    /**
     * 清除所有配置
     */
    void clear() {
        std::lock_guard<std::mutex> lock(config_mutex_);
        sections_.clear();
    }
    
    /**
     * 合并另一个配置管理器的配置
     */
    void merge(const ConfigManager& other);

private:
    bool load_ini_format(const std::string& content);
    bool load_json_format(const std::string& content);
    bool load_xml_format(const std::string& content);
    bool load_yaml_format(const std::string& content);
    
    std::string save_ini_format() const;
    std::string save_json_format() const;
    std::string save_xml_format() const;
    std::string save_yaml_format() const;
    
    void notify_change(const std::string& section, const std::string& key);
    
    ConfigValue parse_value(const std::string& value_str);
    std::string value_to_string(const ConfigValue& value) const;
};

// ==================== 预定义配置结构 ====================

/**
 * 应用配置结构
 */
struct ApplicationConfig {
    std::string app_name = "ArkReplay-Agent";
    std::string version = "1.0.0";
    std::string log_level = "INFO";
    std::string log_file = "arkreplay.log";
    size_t max_log_size = 10 * 1024 * 1024; // 10MB
    size_t max_log_files = 5;
    bool enable_console_log = true;
    
    static ApplicationConfig from_config_manager(const ConfigManager& config) {
        ApplicationConfig app_config;
        app_config.app_name = config.get_value_or_default<std::string>("application", "name", app_config.app_name);
        app_config.version = config.get_value_or_default<std::string>("application", "version", app_config.version);
        app_config.log_level = config.get_value_or_default<std::string>("logging", "level", app_config.log_level);
        app_config.log_file = config.get_value_or_default<std::string>("logging", "file", app_config.log_file);
        app_config.max_log_size = config.get_value_or_default<int64_t>("logging", "max_size", app_config.max_log_size);
        app_config.max_log_files = config.get_value_or_default<int64_t>("logging", "max_files", app_config.max_log_files);
        app_config.enable_console_log = config.get_value_or_default<bool>("logging", "console", app_config.enable_console_log);
        return app_config;
    }
};

/**
 * 网络配置结构
 */
struct NetworkConfigSet {
    std::vector<network::NetworkConfig> connections;
    size_t default_buffer_size = 64 * 1024;
    std::chrono::milliseconds default_timeout{5000};
    bool enable_keepalive = true;
    size_t max_connections = 1000;
    
    static NetworkConfigSet from_config_manager(const ConfigManager& config) {
        NetworkConfigSet net_config;
        net_config.default_buffer_size = config.get_value_or_default<int64_t>("network", "buffer_size", net_config.default_buffer_size);
        net_config.default_timeout = std::chrono::milliseconds(
            config.get_value_or_default<int64_t>("network", "timeout_ms", net_config.default_timeout.count()));
        net_config.enable_keepalive = config.get_value_or_default<bool>("network", "keepalive", net_config.enable_keepalive);
        net_config.max_connections = config.get_value_or_default<int64_t>("network", "max_connections", net_config.max_connections);
        
        // 加载连接配置
        auto section_names = config.get_section_names();
        for (const auto& section_name : section_names) {
            if (section_name.starts_with("connection_")) {
                const auto& section = config.get_section(section_name);
                network::NetworkConfig conn_config;
                conn_config.host = section.get_or_default<std::string>("host", "localhost");
                conn_config.port = section.get_or_default<int64_t>("port", 8080);
                
                std::string type_str = section.get_or_default<std::string>("type", "tcp_client");
                if (type_str == "tcp_client") conn_config.type = network::ConnectionType::TCP_CLIENT;
                else if (type_str == "tcp_server") conn_config.type = network::ConnectionType::TCP_SERVER;
                else if (type_str == "udp") conn_config.type = network::ConnectionType::UDP;
                else if (type_str == "multicast") conn_config.type = network::ConnectionType::MULTICAST;
                
                net_config.connections.push_back(conn_config);
            }
        }
        
        return net_config;
    }
};

/**
 * 全局配置单例
 */
class GlobalConfig {
private:
    static std::unique_ptr<ConfigManager> instance_;
    static std::mutex instance_mutex_;

public:
    static ConfigManager& get_instance() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        if (!instance_) {
            instance_ = std::make_unique<ConfigManager>();
        }
        return *instance_;
    }
    
    static bool initialize(const std::string& config_file, ConfigFormat format = ConfigFormat::INI) {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        instance_ = std::make_unique<ConfigManager>();
        return instance_->load_from_file(config_file, format);
    }
    
    static void shutdown() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        instance_.reset();
    }
};

} // namespace config
} // namespace arkreplay
