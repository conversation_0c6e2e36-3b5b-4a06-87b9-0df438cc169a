"""
错误报告模块

负责收集和报告Agent中的错误信息，确保错误能够被正确传递到Server端。
实现统一的错误处理协议，使Server能够准确理解和处理错误。
"""
import httpx
import asyncio
import traceback
import time
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from app.core.config import settings
from app.models.logger import logger
from app.core.error_utils import ErrorCategory, ErrorSeverity, categorize_error, determine_severity, is_fatal_error

class ErrorProtocol:
    """错误处理协议，定义统一的错误格式和处理方法"""

    @staticmethod
    def categorize_error(error: Exception) -> ErrorCategory:
        """根据异常类型分类错误"""
        return categorize_error(error)

    @staticmethod
    def determine_severity(error: Exception, category: ErrorCategory = None) -> ErrorSeverity:
        """确定错误的严重程度"""
        return determine_severity(error, category)

    @staticmethod
    def is_fatal_error(error: Exception, severity: ErrorSeverity = None) -> bool:
        """
        判断错误是否为致命错误，需要立即停止任务

        Args:
            error: 异常对象
            severity: 错误严重程度，如果为None则自动判断

        Returns:
            是否为致命错误
        """
        return is_fatal_error(error, severity)

    @staticmethod
    def format_error(error: Exception, task_id: Optional[str] = None,
                    context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """格式化错误信息，生成标准错误报告"""
        # 获取错误类别和严重程度
        category = categorize_error(error)
        severity = determine_severity(error, category)

        # 判断是否为致命错误
        fatal = is_fatal_error(error, severity)

        # 构建错误报告
        error_report = {
            "timestamp": datetime.now().isoformat(),
            "type": type(error).__name__,
            "message": str(error),
            "traceback": traceback.format_exc(),
            "category": str(category),
            "severity": str(severity),
            "is_fatal": fatal,
            "task_id": task_id,
            "context": context or {}
        }

        return error_report

class ErrorReporter:
    """错误报告客户端，用于向Server端报告Agent中发生的错误"""

    def __init__(self, server_url: Optional[str] = None):
        """
        初始化错误报告客户端

        Args:
            server_url: Server API的基础URL，默认使用配置中的URL
        """
        self.server_url = server_url or settings.SERVER_API_BASE_URL
        if not self.server_url:
            logger.warning("未配置Server URL，错误报告功能将不可用")

        self.client = httpx.AsyncClient(timeout=10.0)  # 设置超时为10秒
        self._error_buffer = []  # 错误缓冲区，用于存储未能立即发送的错误
        self._buffer_lock = asyncio.Lock()  # 缓冲区锁，防止并发访问冲突
        self._retry_task = None  # 重试任务
        self._running = False  # 是否正在运行

    async def start(self):
        """启动错误报告服务"""
        if self._running:
            return

        self._running = True
        self._retry_task = asyncio.create_task(self._retry_worker())
        logger.info("错误报告服务已启动")

    async def stop(self):
        """停止错误报告服务"""
        if not self._running:
            return

        self._running = False
        if self._retry_task:
            self._retry_task.cancel()
            try:
                await self._retry_task
            except asyncio.CancelledError:
                pass
            self._retry_task = None

        # 关闭HTTP客户端
        await self.client.aclose()
        logger.info("错误报告服务已停止")

    async def report_task_error(self, task_id: str, error: Exception, context: Dict[str, Any] = None) -> bool:
        """
        向Server报告任务执行过程中的错误

        Args:
            task_id: 任务ID
            error: 发生的异常
            context: 错误发生时的上下文信息

        Returns:
            是否成功报告错误
        """
        if not self.server_url:
            logger.error(f"无法报告错误: Server URL未配置 (任务: {task_id})")
            return False

        # 使用错误处理协议格式化错误
        error_report = ErrorProtocol.format_error(error, task_id, context)

        # 获取错误类别和严重程度
        category = error_report["category"]
        severity = error_report["severity"]

        try:
            # 构建API端点URL
            endpoint = f"{self.server_url}/api/database/tasks/{task_id}/error"

            # 构建请求头，包含认证信息
            headers = {"Content-Type": "application/json"}
            api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            # 发送错误报告
            response = await self.client.post(endpoint, json=error_report, headers=headers, timeout=5.0)

            if response.status_code == 200:
                logger.info(f"任务错误报告发送成功: {task_id}, 类别: {category}, 严重程度: {severity}")
                return True
            else:
                logger.warning(f"任务错误报告发送失败: {task_id}, 状态码: {response.status_code}")
                # 添加到重试缓冲区
                await self._add_to_buffer(task_id, error_report, endpoint)
                return False

        except Exception as e:
            logger.error(f"发送任务错误报告时发生异常: {task_id}, 错误: {str(e)}")
            # 添加到重试缓冲区
            await self._add_to_buffer(task_id, error_report, endpoint)
            return False

    async def report_system_error(self, error: Exception, context: Dict[str, Any] = None) -> bool:
        """
        报告系统级错误（不与特定任务关联）

        Args:
            error: 发生的异常
            context: 错误发生时的上下文信息

        Returns:
            是否成功报告错误
        """
        if not self.server_url:
            logger.error(f"无法报告系统错误: Server URL未配置")
            return False

        # 使用错误处理协议格式化错误
        error_report = ErrorProtocol.format_error(error, None, context)

        # 获取错误类别和严重程度
        category = error_report["category"]
        severity = error_report["severity"]

        try:
            # 构建API端点URL
            endpoint = f"{self.server_url}/api/database/system/error"

            # 构建请求头，包含认证信息
            headers = {"Content-Type": "application/json"}
            api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            # 发送错误报告
            response = await self.client.post(endpoint, json=error_report, headers=headers, timeout=5.0)

            if response.status_code == 200:
                logger.info(f"系统错误报告发送成功: 类别: {category}, 严重程度: {severity}")
                return True
            else:
                logger.warning(f"系统错误报告发送失败: 状态码: {response.status_code}")
                # 添加到重试缓冲区
                await self._add_to_buffer(None, error_report, endpoint)
                return False

        except Exception as e:
            logger.error(f"发送系统错误报告时发生异常: 错误: {str(e)}")
            # 添加到重试缓冲区
            await self._add_to_buffer(None, error_report, endpoint)
            return False

    async def _add_to_buffer(self, task_id: Optional[str], error_report: Dict[str, Any], endpoint: str):
        """添加错误到重试缓冲区"""
        async with self._buffer_lock:
            # 限制缓冲区大小，防止内存溢出
            if len(self._error_buffer) >= 100:
                self._error_buffer.pop(0)  # 移除最旧的错误

            self._error_buffer.append({
                "task_id": task_id,
                "error_report": error_report,
                "endpoint": endpoint,
                "retry_count": 0,
                "next_retry": time.time() + 5  # 5秒后重试
            })

    async def _retry_worker(self):
        """重试发送错误报告的工作线程"""
        while self._running:
            try:
                # 检查是否有需要重试的错误
                retry_items = []
                current_time = time.time()

                async with self._buffer_lock:
                    # 找出需要重试的错误
                    for item in self._error_buffer:
                        if item["next_retry"] <= current_time:
                            retry_items.append(item)

                    # 从缓冲区中移除将要重试的错误
                    if retry_items:
                        self._error_buffer = [item for item in self._error_buffer if item not in retry_items]

                # 重试发送错误报告
                for item in retry_items:
                    await self._retry_send_error(item)

                # 短暂休眠
                await asyncio.sleep(1)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.exception(f"错误重试线程异常: {str(e)}")
                await asyncio.sleep(5)  # 发生异常时稍长休眠

    async def _retry_send_error(self, item: Dict[str, Any]):
        """重试发送错误报告"""
        task_id = item["task_id"]
        error_report = item["error_report"]
        endpoint = item["endpoint"]
        retry_count = item["retry_count"]

        try:
            # 构建请求头，包含认证信息
            headers = {"Content-Type": "application/json"}
            api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            # 发送错误报告
            response = await self.client.post(endpoint, json=error_report, headers=headers, timeout=5.0)

            if response.status_code == 200:
                logger.info(f"错误报告重试成功: {task_id}, 重试次数: {retry_count+1}")
                return
            else:
                logger.warning(f"错误报告重试失败: {task_id}, 重试次数: {retry_count+1}, 状态码: {response.status_code}")

        except Exception as e:
            logger.error(f"错误报告重试异常: {task_id}, 重试次数: {retry_count+1}, 错误: {str(e)}")

        # 如果重试失败且未达到最大重试次数，重新添加到缓冲区
        if retry_count < 2:  # 最多重试3次
            item["retry_count"] = retry_count + 1
            async with self._buffer_lock:
                self._error_buffer.append(item)
        else:
            logger.error(f"错误报告重试达到最大次数，放弃: {task_id}")

    async def close(self):
        """关闭错误报告服务"""
        await self.stop()

        # 尝试发送所有缓冲的错误
        async with self._buffer_lock:
            if self._error_buffer:
                logger.info(f"关闭前尝试发送 {len(self._error_buffer)} 个缓冲的错误报告")
                for item in self._error_buffer:
                    try:
                        # 构建请求头，包含认证信息
                        headers = {"Content-Type": "application/json"}
                        api_key = getattr(settings, 'DB_AGENT_API_KEY', None)
                        if api_key:
                            headers["Authorization"] = f"Bearer {api_key}"

                        await self.client.post(
                            item["endpoint"],
                            json=item["error_report"],
                            headers=headers,
                            timeout=2.0
                        )
                    except Exception:
                        pass  # 忽略关闭时的错误

        # 关闭HTTP客户端
        await self.client.aclose()


# 全局错误报告器实例
error_reporter = ErrorReporter()

async def report_error(task_id: str, error: Exception, context: Dict[str, Any] = None) -> bool:
    """
    报告任务错误的便捷函数

    Args:
        task_id: 任务ID
        error: 发生的异常
        context: 错误发生时的上下文信息

    Returns:
        是否成功报告错误
    """
    # 检查是否为致命错误
    is_fatal = ErrorProtocol.is_fatal_error(error)

    # 如果是致命错误，需要处理任务终止
    if is_fatal:
        # 记录日志
        logger.critical(f"检测到致命错误，任务ID: {task_id}, 错误: {str(error)}")

        # 导入任务管理器（避免循环导入）
        try:
            from app.core.task_manager import task_manager
            # 异步处理致命错误，不阻塞当前函数
            asyncio.create_task(task_manager.handle_fatal_error(task_id, error, context))
        except Exception as e:
            logger.error(f"处理致命错误时发生异常: {str(e)}")

    # 无论是否为致命错误，都报告错误
    return await error_reporter.report_task_error(task_id, error, context)

async def report_system_error(error: Exception, context: Dict[str, Any] = None) -> bool:
    """
    报告系统级错误的便捷函数（不与特定任务关联）

    Args:
        error: 发生的异常
        context: 错误发生时的上下文信息

    Returns:
        是否成功报告错误
    """
    # 检查是否为致命错误
    is_fatal = ErrorProtocol.is_fatal_error(error)

    # 如果是致命错误，记录关键日志
    if is_fatal:
        logger.critical(f"检测到系统级致命错误: {str(error)}")

        # 如果上下文中包含任务ID，尝试处理任务终止
        task_id = context.get("task_id") if context else None
        if task_id:
            try:
                from app.core.task_manager import task_manager
                # 异步处理致命错误，不阻塞当前函数
                asyncio.create_task(task_manager.handle_fatal_error(task_id, error, context))
            except Exception as e:
                logger.error(f"处理系统级致命错误时发生异常: {str(e)}")

    # 无论是否为致命错误，都报告错误
    return await error_reporter.report_system_error(error, context)

# 这些函数已经从error_utils导入，不需要再定义