# connectors/base_connector.py
from abc import ABC, abstractmethod
from typing import Iterator
from core.models import Record

class BaseConnector(ABC, Iterator[Record]):
    """所有数据源连接器的抽象基类。"""

    @abstractmethod
    def connect(self):
        """建立到数据源的连接。"""
        pass

    @abstractmethod
    def close(self):
        """关闭到数据源的连接。"""
        pass

    @abstractmethod
    def fetch_data(self) -> Iterator[Record]:
        """从数据源获取数据，并以 Record 对象的形式返回一个迭代器。"""
        pass

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def __iter__(self) -> Iterator[Record]:
        return self.fetch_data()

    @abstractmethod
    def __next__(self) -> Record:
        pass