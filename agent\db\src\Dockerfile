# 使用多阶段构建，减小最终镜像体积

# 基础阶段：安装系统依赖和数据库客户端
FROM debian:bullseye-slim AS base

# 防止交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖和数据库客户端
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 基础工具
    curl ca-certificates gnupg2 lsb-release wget unixodbc unixodbc-dev \
    # MySQL客户端
    default-libmysqlclient-dev \
    # PostgreSQL客户端
    libpq-dev \
    # SQLite
    libsqlite3-dev \
    # freetds for MSSQL
    freetds-dev freetds-bin tdsodbc \
    # Python构建依赖
    python3-dev python3-pip build-essential \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Oracle Instant Client 安装
# 注意：需要手动下载Oracle Instant Client或从官方仓库获取
RUN mkdir -p /opt/oracle \
    && wget https://download.oracle.com/otn_software/linux/instantclient/214000/instantclient-basic-linux.x64-********.0dbru.zip -O /tmp/oracle-instantclient.zip \
    && unzip /tmp/oracle-instantclient.zip -d /opt/oracle \
    && rm /tmp/oracle-instantclient.zip \
    && echo "/opt/oracle/instantclient_21_4" > /etc/ld.so.conf.d/oracle-instantclient.conf \
    && ldconfig

# SQL Server ODBC驱动 
# 安装Microsoft ODBC Driver for SQL Server
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql18 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# DB2 CLIDRIVER 安装
# 注意：需要手动下载DB2 CLIDRIVER或从官方仓库获取
RUN mkdir -p /opt/ibm \
    && wget https://public.dhe.ibm.com/ibmdl/export/pub/software/data/db2/drivers/odbc_cli/linuxx64_odbc_cli.tar.gz -O /tmp/db2_clidriver.tar.gz \
    && tar -xzf /tmp/db2_clidriver.tar.gz -C /opt/ibm \
    && rm /tmp/db2_clidriver.tar.gz \
    && echo "/opt/ibm/clidriver/lib" > /etc/ld.so.conf.d/db2-clidriver.conf \
    && ldconfig

# 创建Python虚拟环境
FROM base AS python-deps

WORKDIR /app

# 创建虚拟环境并安装依赖
RUN python3 -m pip install --upgrade pip \
    && python3 -m pip install virtualenv \
    && python3 -m virtualenv /opt/venv

# 激活虚拟环境
ENV PATH="/opt/venv/bin:$PATH"

# 拷贝并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 最终阶段
FROM base AS runtime

WORKDIR /app

# 复制虚拟环境
COPY --from=python-deps /opt/venv /opt/venv

# 设置环境变量
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    # Oracle 环境变量
    LD_LIBRARY_PATH="/opt/oracle/instantclient_21_4:${LD_LIBRARY_PATH}" \
    # DB2 环境变量
    DB2_CLI_DRIVER_INSTALL_PATH="/opt/ibm/clidriver" \
    # 服务配置
    PORT=8000 \
    MAX_WORKERS=4 \
    WORKER_TIMEOUT=120

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动服务
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"] 