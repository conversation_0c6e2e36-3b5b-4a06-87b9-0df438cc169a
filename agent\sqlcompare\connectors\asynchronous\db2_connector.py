# connectors/asynchronous/db2_connector.py
import asyncio
from typing import Dict, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor

from .base_connector import AsyncDatabaseConnector
from core.models import Record
from utils.imdb_manager import configure_db2_driver
import ibm_db


class AsyncDB2Connector(AsyncDatabaseConnector):
    """异步DB2数据库连接器"""

    def __init__(self, db_config: Dict[str, Any], query: str, **kwargs):
        """
        初始化异步DB2连接器

        Args:
            db_config: DB2数据库配置
            query: SQL查询语句
            **kwargs: 其他参数
        """
        # DB2特定配置
        self.max_workers = kwargs.pop('max_workers', 2)  # 线程池大小

        super().__init__(db_config, query, **kwargs)
        self.batch_size = kwargs.get('batch_size', 10000) # 流式处理的批次大小
        self._connection = None
        self._thread_pool = None
        self._connection_string = self._build_connection_string()
        self.connection_timeout = kwargs.get('connection_timeout', 30)

        # 性能统计
        self._stats = {
            'total_fetched': 0,
            'fetch_time': 0.0,
            'connection_time': 0.0,
            'query_optimization_time': 0.0
        }

        # 优化的查询缓存
        self._optimized_query_cache = {}
        
    def _build_connection_string(self) -> str:
        """构建DB2连接字符串"""
        return (
            f"DATABASE={self.db_config['schema']};"
            f"HOSTNAME={self.db_config['ip']};"
            f"PORT={self.db_config['port']};"
            f"PROTOCOL=TCPIP;"
            f"UID={self.db_config['user_name']};"
            f"PWD={self.db_config['password']};"
        )
    
    async def connect(self) -> bool:
        """建立异步DB2连接"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 创建线程池用于执行同步的DB2操作
            self._thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
            
            # 在线程池中执行连接操作
            self._connection = await asyncio.get_event_loop().run_in_executor(
                self._thread_pool,
                self._sync_connect
            )
            
            if self._connection:
                self._is_connected = True
                connection_time = asyncio.get_event_loop().time() - start_time
                self.stats['connection_time'] = connection_time
                self.logger.info(f"异步DB2连接成功: {self.db_config['ip']}, 耗时: {connection_time:.2f}秒")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"异步DB2连接失败: {e}")
            self._record_error(e)
            return False
    
    def _sync_connect(self):
        """同步连接方法（在线程池中执行）"""
        try:
            return ibm_db.connect(self._connection_string, "", "")
        except Exception as e:
            self.logger.error(f"DB2同步连接失败: {e}")
            raise
    
    async def disconnect(self) -> bool:
        """断开异步DB2连接"""
        try:
            if self._connection:
                # 在线程池中执行断开连接操作
                await asyncio.get_event_loop().run_in_executor(
                    self._thread_pool,
                    ibm_db.close,
                    self._connection
                )
                self._connection = None
            
            if self._thread_pool:
                self._thread_pool.shutdown(wait=True)
                self._thread_pool = None
            
            self._is_connected = False
            self.logger.info(f"异步DB2连接已断开: {self.db_config['ip']}")
            return True
            
        except Exception as e:
            self.logger.error(f"异步DB2断开连接失败: {e}")
            self._record_error(e)
            return False

    async def get_record_count(self) -> int:
        """
        获取查询结果的精确记录数量（异步版本）

        Returns:
            记录数量
        """
        if not self._is_connected or not self._connection:
            raise ConnectionError("数据库未连接。")

        try:
            # 从原始查询构造COUNT查询
            original_query = self.query.strip()

            # 移除ORDER BY子句（COUNT查询不需要排序）
            query_upper = original_query.upper()
            order_by_pos = query_upper.rfind("ORDER BY")
            if order_by_pos != -1:
                base_query = original_query[:order_by_pos].strip()
            else:
                base_query = original_query

            # 移除FETCH FIRST限制（COUNT查询需要统计全部记录）
            fetch_first_pos = query_upper.rfind("FETCH FIRST")
            if fetch_first_pos != -1:
                base_query = original_query[:fetch_first_pos].strip()

            # 构造COUNT查询
            count_query = f"SELECT COUNT(*) AS record_count FROM ({base_query}) AS count_subquery"

            print(f"📊 执行异步COUNT查询: {count_query}")

            # 异步执行COUNT查询
            result = await asyncio.get_event_loop().run_in_executor(
                self._thread_pool,
                self._sync_execute_count_query,
                count_query
            )

            print(f"✅ 异步查询记录数量: {result:,}")
            return result

        except Exception as e:
            print(f"❌ 异步COUNT查询失败: {e}")
            raise

    def _sync_execute_count_query(self, count_query: str) -> int:
        """同步执行COUNT查询（在线程池中执行）"""
        try:
            stmt = ibm_db.exec_immediate(self._connection, count_query)
            row = ibm_db.fetch_tuple(stmt)

            if row:
                count = int(row[0])
                return count
            else:
                raise Exception("COUNT查询返回空结果")

        except Exception as e:
            self.logger.error(f"同步COUNT查询失败: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """测试异步DB2连接"""
        if not self._is_connected or not self._connection:
            return False
        
        try:
            # 执行简单的测试查询
            test_query = "SELECT 1 FROM SYSIBM.SYSDUMMY1"
            stmt = await asyncio.get_event_loop().run_in_executor(
                self._thread_pool,
                lambda: ibm_db.exec_immediate(self._connection, test_query)
            )
            result = ibm_db.fetch_tuple(stmt)
            return result is not None
        except Exception as e:
            self.logger.error(f"DB2连接测试失败: {e}")
            self._record_error(e)
            return False
    
    async def fetch_data_async(self):
        """
        异步获取数据，返回异步迭代器
        """
        if not self._is_connected or not self._connection:
            raise ConnectionError("DB2数据库未连接")

        # 获取优化的查询（带缓存）
        optimized_query = self._get_streaming_query()

        # 使用异步流式处理
        async for record in self._fetch_streaming_data_async(optimized_query):
            yield record

    def _get_streaming_query(self) -> str:
        """获取优化的数据获取查询（带缓存）"""
        cache_key = f"fetch_{hash(self.query)}"

        if cache_key in self._optimized_query_cache:
            return self._optimized_query_cache[cache_key]

        # 智能处理ORDER BY子句，确保数据按KEY排序
        query_upper = self.query.upper()
        order_by_pos = query_upper.rfind("ORDER BY")

        if order_by_pos != -1:
            # 如果用户提供了显式的ORDER BY，则使用它
            order_by_clause = self.query[order_by_pos:]
            base_query = self.query[:order_by_pos].strip()
        else:
            # 没有ORDER BY时，自动按第一列排序
            order_by_clause = "ORDER BY 1"
            base_query = self.query.strip()

        # 构建优化查询（不使用分页，直接流式处理）
        optimized_query = f"{base_query} {order_by_clause}"

        # 缓存优化查询
        self._optimized_query_cache[cache_key] = optimized_query

        return optimized_query

    async def _fetch_streaming_data_async(self, optimized_query: str):
        """异步流式读取数据"""
        try:
            # 在线程池中执行同步的流式读取
            async for record in self._async_generator_wrapper(optimized_query):
                yield record

        except Exception as e:
            self.logger.error(f"异步流式读取失败: {e}")
            raise

    async def _async_generator_wrapper(self, optimized_query: str):
        """将同步生成器包装为异步生成器"""
        # 在线程池中执行查询和数据获取
        loop = asyncio.get_event_loop()

        # 使用队列在线程和异步之间传递数据
        queue = asyncio.Queue(maxsize=self.batch_size)

        async def producer():
            """在线程池中生成数据"""
            try:
                await loop.run_in_executor(
                    self._thread_pool,
                    self._sync_fetch_streaming_data,
                    optimized_query,
                    queue
                )
            except Exception as e:
                await queue.put(('error', e))
            finally:
                await queue.put(('done', None))

        # 启动生产者任务
        producer_task = asyncio.create_task(producer())

        try:
            while True:
                # 异步获取数据
                item_type, data = await queue.get()

                if item_type == 'record':
                    yield data
                elif item_type == 'error':
                    raise data
                elif item_type == 'done':
                    break

        finally:
            # 确保生产者任务完成
            if not producer_task.done():
                producer_task.cancel()
                try:
                    await producer_task
                except asyncio.CancelledError:
                    pass

    def _sync_fetch_streaming_data(self, optimized_query: str, queue):
        """在线程池中执行的同步流式数据获取"""
        import time

        stmt = None
        try:
            start_time = time.perf_counter()

            # 执行查询
            stmt = ibm_db.exec_immediate(self._connection, optimized_query)
            query_exec_time = time.perf_counter() - start_time
            self.logger.info(f"查询执行完成: {query_exec_time:.3f}s - {self.db_config['ip']}")

            if not stmt:
                raise Exception("查询执行失败")

            # 预计算列信息
            num_fields = ibm_db.num_fields(stmt)
            if num_fields == 0:
                self.logger.warning("查询返回0个字段")
                return

            # 优化列信息获取
            columns = tuple(ibm_db.field_name(stmt, i) for i in range(num_fields))
            value_columns = columns[1:] if len(columns) > 1 else ()

            # 流式读取数据
            batch_count = 0
            total_rows = 0
            fetch_start = time.perf_counter()

            while True:
                # 批量获取数据
                batch_rows = ibm_db.fetchmany(stmt, self.batch_size)
                if not batch_rows:
                    break

                batch_count += 1

                # 处理批次数据
                for row_tuple in batch_rows:
                    total_rows += 1

                    # 构建Record对象
                    if len(row_tuple) >= 1:
                        key = str(row_tuple[0]) if row_tuple[0] is not None else ""

                        if len(value_columns) > 0:
                            value = {col: (str(row_tuple[i+1]) if row_tuple[i+1] is not None else "")
                                   for i, col in enumerate(value_columns)}
                        else:
                            value = {}

                        record = Record(key=key, value=value)

                        # 将记录放入队列（同步方式）
                        asyncio.run_coroutine_threadsafe(
                            queue.put(('record', record)),
                            asyncio.get_event_loop()
                        ).result()

            fetch_time = time.perf_counter() - fetch_start
            self.logger.info(f"流式读取完成: {total_rows} 行, {batch_count} 批次, 总耗时: {fetch_time:.3f}s")

            # 更新统计
            self._stats['total_fetched'] += total_rows
            self._stats['fetch_time'] += fetch_time

        except Exception as e:
            self.logger.error(f"同步流式读取失败: {e}")
            # 将错误放入队列
            asyncio.run_coroutine_threadsafe(
                queue.put(('error', e)),
                asyncio.get_event_loop()
            ).result()
        finally:
            if stmt:
                try:
                    ibm_db.free_result(stmt)
                except:
                    pass
    
    def _build_streaming_query(self, base_query: str) -> str:
        """构建流式查询，智能处理ORDER BY子句"""
        # 智能处理ORDER BY子句，确保数据按KEY排序
        query_upper = base_query.upper()
        order_by_pos = query_upper.rfind("ORDER BY")

        if order_by_pos != -1:
            # 如果用户提供了显式的ORDER BY，则使用它
            order_by_clause = base_query[order_by_pos:]
            base_query = base_query[:order_by_pos].strip()
            print(f"✅ 检测到用户指定的ORDER BY: {order_by_clause}")
        else:
            # 没有ORDER BY时，自动按第一列（KEY）排序
            order_by_clause = "ORDER BY 1"  # 第一列通常是KEY
            base_query = base_query.strip()
            print(f"⚠️  未检测到ORDER BY子句，自动添加: {order_by_clause}")
            print(f"💡 为确保归并比对算法正确性，数据将按第一列(KEY)排序")

        # 构建单次大查询
        return f"{base_query} {order_by_clause}"

    async def fetch_all_data(self, shared_dict: Dict[str, Dict[str, str]] = None, data_source: str = 't1') -> Dict[str, Dict[str, str]]:
        """
        一次性获取所有数据到内存字典 (异步版本)

        Args:
            shared_dict: 共享的内存字典，如果提供则直接更新此字典
            data_source: 数据源标识，'t1'表示第一个数据源，'t2'表示第二个数据源

        Returns:
            字典格式: {key: {'t1': str_value, 't2': str_value}}
        """
        if not self._is_connected or not self._connection:
            raise ConnectionError("数据库未连接。")

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._thread_pool,
            self._sync_fetch_all_data,
            shared_dict,
            data_source
        )

    def _sync_fetch_all_data(self, shared_dict: Optional[Dict[str, Dict[str, str]]], data_source: str) -> Dict[str, Dict[str, str]]:
        """同步版本的 fetch_all_data，在线程池中执行"""
        if shared_dict is None:
            data_dict = {}
        else:
            data_dict = shared_dict

        try:
            query_start = time.perf_counter()
            stmt = ibm_db.exec_immediate(self._connection, self.query)
            query_exec_time = time.perf_counter() - query_start
            self.logger.info(f"查询执行完成: {query_exec_time:.3f}s - {self.db_config['ip']}")

            if not stmt:
                raise Exception("查询执行失败")

            num_fields = ibm_db.num_fields(stmt)
            if num_fields == 0:
                return data_dict
            columns = [ibm_db.field_name(stmt, i) for i in range(num_fields)]

            total_rows = 0
            fetch_start = time.perf_counter()

            while True:
                row = ibm_db.fetch_assoc(stmt)
                if not row:
                    break

                total_rows += 1
                key_column = columns[0]
                key = str(row[key_column]) if row[key_column] is not None else ""
                row_str = str(row)

                if data_source == 't1':
                    data_dict[key] = {'t1': row_str, 't2': ''}
                else:
                    if key in data_dict:
                        item = data_dict[key]
                        if item['t1'] == row_str:
                            del data_dict[key]
                    else:
                        item = {'t1': '', 't2': row_str}
                        data_dict[key] = item

            ibm_db.free_result(stmt)
            fetch_time = time.perf_counter() - fetch_start

            if data_source == 't1':
                self.logger.info(f"数据源A: {total_rows:,}, 查询: {query_exec_time:.3f}s, 存储: {fetch_time:.3f}s")
            else:
                logger.info(f"数据源B: {total_rows:,}, 查询: {query_exec_time:.3f}s, 存储: {fetch_time:.3f}s")

            return data_dict

        except Exception as e:
            self.logger.error(f"同步全量数据获取失败: {e}")
            raise

    async def get_table_info(self) -> Optional[Dict[str, Any]]:
        """
        获取表信息
        
        Returns:
            表信息字典
        """
        try:
            # 从查询中提取表名（简单实现）
            query_upper = self.query.upper()
            if "FROM" in query_upper:
                # 这是一个简化的表名提取，实际应用中可能需要更复杂的解析
                from_pos = query_upper.find("FROM")
                after_from = self.query[from_pos + 4:].strip()
                table_name = after_from.split()[0]
                
                # 查询表的列信息
                columns_query = f"""
                SELECT COLNAME, TYPENAME, LENGTH, SCALE
                FROM SYSCAT.COLUMNS
                WHERE TABNAME = '{table_name.upper()}'
                ORDER BY COLNO
                """
                stmt = await asyncio.get_event_loop().run_in_executor(
                    self._thread_pool,
                    lambda: ibm_db.exec_immediate(self._connection, columns_query)
                )
                
                columns_result = []
                while True:
                    row = ibm_db.fetch_tuple(stmt)
                    if not row:
                        break
                    columns_result.append(row)
                
                return {
                    'table_name': table_name,
                    'columns': [
                        {
                            'name': row[0],
                            'type': row[1],
                            'length': row[2],
                            'scale': row[3]
                        }
                        for row in columns_result
                    ]
                }
            
            return None
            
        except Exception as e:
            self.logger.warning(f"获取表信息失败: {e}")
            return None
    
    async def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        base_info = await super().get_connection_info()
        base_info.update({
            'database_type': 'DB2',
            'ip': self.db_config['ip'],
            'port': self.db_config['port'],
            'database': self.db_config['database'],
            'max_workers': self.max_workers,
            'connection_timeout': self.connection_timeout
        })
        return base_info


class AsyncDB2ConnectorFactory:
    """异步DB2连接器工厂"""
    
    @staticmethod
    def create_connector(
        db_config: Dict[str, Any],
        query: str,
        batch_size: int = 10000,
        max_workers: int = 2,
        connection_timeout: float = 30.0
    ) -> AsyncDB2Connector:
        """
        创建异步DB2连接器

        Args:
            db_config: 数据库配置
            query: SQL查询语句
            batch_size: 批量处理大小
            max_workers: 线程池大小
            connection_timeout: 连接超时时间

        Returns:
            异步DB2连接器实例
        """
        return AsyncDB2Connector(
            db_config=db_config,
            query=query,
            batch_size=batch_size,
            max_workers=max_workers,
            connection_timeout=connection_timeout
        )
    
    @staticmethod
    def create_from_config(config_dict: Dict[str, Any]) -> AsyncDB2Connector:
        """
        从配置字典创建连接器
        
        Args:
            config_dict: 包含所有配置的字典
            
        Returns:
            异步DB2连接器实例
        """
        db_config = config_dict['db_config']
        query = config_dict['query']
        
        # 提取可选参数
        kwargs = {
            'batch_size': config_dict.get('batch_size', 10000),
            'max_workers': config_dict.get('max_workers', 2),
            'connection_timeout': config_dict.get('connection_timeout', 30)
        }

        return AsyncDB2Connector(db_config, query, **kwargs)


