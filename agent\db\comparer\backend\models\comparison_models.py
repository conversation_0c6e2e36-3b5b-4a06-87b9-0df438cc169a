"""
比对相关数据模型
"""
import os
import sys
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Float, Boolean
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)
from comparison.connectors.sqlalchemy_enginer import Base

class ComparisonTypeEnum(str, Enum):
    """比对类型枚举"""
    STRUCTURE = "structure"
    CONTENT = "content"


class TaskStatusEnum(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# SQLAlchemy模型
class ComparisonTask(Base):
    """比对任务表"""
    __tablename__ = "comparison_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), unique=True, index=True, nullable=False)
    comparison_type = Column(String(20), nullable=False)
    source_connection = Column(JSON, nullable=False)
    target_connection = Column(JSON, nullable=False)
    table_name = Column(String(100), nullable=False)
    sql_rules = Column(JSON, nullable=True)
    batch_size = Column(Integer, default=1000)
    timeout = Column(Integer, default=3600)
    status = Column(String(20), default="pending")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)


class ComparisonResult(Base):
    """比对结果表"""
    __tablename__ = "comparison_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), index=True, nullable=False)
    comparison_type = Column(String(20), nullable=False)
    source_info = Column(JSON, nullable=False)
    target_info = Column(JSON, nullable=False)
    total_records = Column(Integer, default=0)
    matched_records = Column(Integer, default=0)
    different_records = Column(Integer, default=0)
    source_only_records = Column(Integer, default=0)
    target_only_records = Column(Integer, default=0)
    execution_time = Column(Float, default=0.0)
    differences = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class ComparisonRule(Base):
    """比对规则表"""
    __tablename__ = "comparison_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    rule_id = Column(String(50), unique=True, index=True, nullable=False)
    table_id = Column(String(100), nullable=False)
    remark = Column(String(200), nullable=True)
    sql_1 = Column(Text, nullable=False)
    sql_2 = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# Pydantic模型
class DatabaseConnectionBase(BaseModel):
    """数据库连接基础模型"""
    db_type: str = Field(..., description="数据库类型")
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口号")
    database: str = Field(..., description="数据库名")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class ComparisonTaskCreate(BaseModel):
    """创建比对任务模型"""
    task_id: str = Field(..., description="任务ID")
    comparison_type: ComparisonTypeEnum = Field(..., description="比对类型")
    source_connection: Dict[str, Any] = Field(..., description="源数据库连接")
    target_connection: Dict[str, Any] = Field(..., description="目标数据库连接")
    table_name: str = Field(..., description="表名")
    sql_rules: Optional[Dict[str, str]] = Field(None, description="SQL规则")
    batch_size: int = Field(1000, description="批处理大小")
    timeout: int = Field(3600, description="超时时间(秒)")


class ComparisonTaskResponse(BaseModel):
    """比对任务响应模型"""
    id: int
    task_id: str
    comparison_type: str
    table_name: str
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True


class ComparisonResultResponse(BaseModel):
    """比对结果响应模型"""
    id: int
    task_id: str
    comparison_type: str
    source_info: Dict[str, Any]
    target_info: Dict[str, Any]
    total_records: int
    matched_records: int
    different_records: int
    source_only_records: int
    target_only_records: int
    execution_time: float
    differences: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class ComparisonRuleCreate(BaseModel):
    """创建比对规则模型"""
    table_id: str = Field(..., description="表ID")
    remark: Optional[str] = Field(None, description="备注")
    sql_1: str = Field(..., description="源端SQL")
    sql_2: str = Field(..., description="目标端SQL")


class ComparisonRuleResponse(BaseModel):
    """比对规则响应模型"""
    id: int
    rule_id: str
    table_id: str
    remark: Optional[str] = None
    sql_1: str
    sql_2: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ComparisonStatistics(BaseModel):
    """比对统计模型"""
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    average_execution_time: float = Field(..., description="平均执行时间")
    success_rate: float = Field(..., description="成功率")


class DifferenceDetail(BaseModel):
    """差异详情模型"""
    type: str = Field(..., description="差异类型")
    key: Optional[str] = Field(None, description="记录键值")
    column_name: Optional[str] = Field(None, description="字段名")
    source_value: Optional[Any] = Field(None, description="源端值")
    target_value: Optional[Any] = Field(None, description="目标端值")
    description: str = Field(..., description="差异描述")


class ComparisonProgress(BaseModel):
    """比对进度模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatusEnum = Field(..., description="任务状态")
    progress_percentage: float = Field(..., description="进度百分比")
    current_step: str = Field(..., description="当前步骤")
    processed_records: int = Field(0, description="已处理记录数")
    total_records: int = Field(0, description="总记录数")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    estimated_completion: Optional[datetime] = Field(None, description="预计完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
