#pragma once

#include "core/data_structures.hpp"
#include "memory/memory_pool.hpp"
#include <string>
#include <vector>
#include <queue>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <functional>

namespace arkreplay {
namespace network {

// ==================== 网络连接类型 ====================

enum class ConnectionType : uint8_t {
    UNKNOWN = 0,
    TCP_CLIENT = 1,
    TCP_SERVER = 2,
    UDP = 3,
    MULTICAST = 4
};

enum class ConnectionState : uint8_t {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    DISCONNECTING = 3,
    ERROR = 4
};

// ==================== 网络配置结构 ====================

/**
 * 网络连接配置
 */
struct NetworkConfig {
    std::string host;
    uint16_t port;
    ConnectionType type;
    
    // TCP特定配置
    bool tcp_nodelay = true;
    bool tcp_keepalive = true;
    uint32_t tcp_keepalive_idle = 60;
    uint32_t tcp_keepalive_interval = 10;
    uint32_t tcp_keepalive_count = 3;
    
    // 缓冲区配置
    size_t send_buffer_size = 64 * 1024;    // 64KB
    size_t recv_buffer_size = 64 * 1024;    // 64KB
    
    // 超时配置
    std::chrono::milliseconds connect_timeout{5000};
    std::chrono::milliseconds send_timeout{3000};
    std::chrono::milliseconds recv_timeout{3000};
    
    // 重连配置
    bool auto_reconnect = true;
    uint32_t max_reconnect_attempts = 5;
    std::chrono::milliseconds reconnect_interval{1000};
    
    NetworkConfig() : type(ConnectionType::TCP_CLIENT), port(0) {}
    
    NetworkConfig(const std::string& h, uint16_t p, ConnectionType t = ConnectionType::TCP_CLIENT)
        : host(h), port(p), type(t) {}
};

// ==================== 网络事件结构 ====================

enum class NetworkEventType : uint8_t {
    CONNECTED = 0,
    DISCONNECTED = 1,
    DATA_RECEIVED = 2,
    DATA_SENT = 3,
    ERROR_OCCURRED = 4,
    TIMEOUT = 5
};

/**
 * 网络事件结构
 */
struct NetworkEvent {
    NetworkEventType type;
    core::SessionId session_id;
    std::string error_message;
    size_t bytes_transferred;
    core::Timestamp timestamp;
    
    NetworkEvent(NetworkEventType t, core::SessionId sid = 0)
        : type(t), session_id(sid), bytes_transferred(0)
        , timestamp(std::chrono::high_resolution_clock::now()) {}
};

// ==================== 网络缓冲区 ====================

/**
 * 高性能网络缓冲区
 * 特点：
 * 1. 零拷贝设计
 * 2. 环形缓冲区
 * 3. 线程安全
 * 4. 内存池集成
 */
class NetworkBuffer {
private:
    std::unique_ptr<uint8_t[]> buffer_;
    size_t capacity_;
    std::atomic<size_t> read_pos_{0};
    std::atomic<size_t> write_pos_{0};
    std::atomic<size_t> size_{0};
    mutable std::mutex mutex_;

public:
    explicit NetworkBuffer(size_t capacity)
        : buffer_(std::make_unique<uint8_t[]>(capacity)), capacity_(capacity) {}
    
    ~NetworkBuffer() = default;
    
    // 禁用拷贝，允许移动
    NetworkBuffer(const NetworkBuffer&) = delete;
    NetworkBuffer& operator=(const NetworkBuffer&) = delete;
    NetworkBuffer(NetworkBuffer&&) = default;
    NetworkBuffer& operator=(NetworkBuffer&&) = default;
    
    /**
     * 写入数据
     * @param data 数据指针
     * @param length 数据长度
     * @return 实际写入的字节数
     */
    size_t write(const uint8_t* data, size_t length) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t available = capacity_ - size_.load();
        size_t to_write = std::min(length, available);
        
        if (to_write == 0) return 0;
        
        size_t write_pos = write_pos_.load();
        
        // 处理环形缓冲区的边界情况
        if (write_pos + to_write <= capacity_) {
            std::memcpy(buffer_.get() + write_pos, data, to_write);
        } else {
            size_t first_part = capacity_ - write_pos;
            std::memcpy(buffer_.get() + write_pos, data, first_part);
            std::memcpy(buffer_.get(), data + first_part, to_write - first_part);
        }
        
        write_pos_.store((write_pos + to_write) % capacity_);
        size_.fetch_add(to_write);
        
        return to_write;
    }
    
    /**
     * 读取数据
     * @param data 输出缓冲区
     * @param length 要读取的长度
     * @return 实际读取的字节数
     */
    size_t read(uint8_t* data, size_t length) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t available = size_.load();
        size_t to_read = std::min(length, available);
        
        if (to_read == 0) return 0;
        
        size_t read_pos = read_pos_.load();
        
        // 处理环形缓冲区的边界情况
        if (read_pos + to_read <= capacity_) {
            std::memcpy(data, buffer_.get() + read_pos, to_read);
        } else {
            size_t first_part = capacity_ - read_pos;
            std::memcpy(data, buffer_.get() + read_pos, first_part);
            std::memcpy(data + first_part, buffer_.get(), to_read - first_part);
        }
        
        read_pos_.store((read_pos + to_read) % capacity_);
        size_.fetch_sub(to_read);
        
        return to_read;
    }
    
    /**
     * 查看数据（不移动读指针）
     * @param data 输出缓冲区
     * @param length 要查看的长度
     * @return 实际查看的字节数
     */
    size_t peek(uint8_t* data, size_t length) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t available = size_.load();
        size_t to_peek = std::min(length, available);
        
        if (to_peek == 0) return 0;
        
        size_t read_pos = read_pos_.load();
        
        if (read_pos + to_peek <= capacity_) {
            std::memcpy(data, buffer_.get() + read_pos, to_peek);
        } else {
            size_t first_part = capacity_ - read_pos;
            std::memcpy(data, buffer_.get() + read_pos, first_part);
            std::memcpy(data + first_part, buffer_.get(), to_peek - first_part);
        }
        
        return to_peek;
    }
    
    size_t size() const { return size_.load(); }
    size_t capacity() const { return capacity_; }
    size_t available_space() const { return capacity_ - size_.load(); }
    bool empty() const { return size_.load() == 0; }
    bool full() const { return size_.load() == capacity_; }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        read_pos_.store(0);
        write_pos_.store(0);
        size_.store(0);
    }
};

// ==================== 网络连接统计 ====================

/**
 * 网络连接统计信息
 */
struct NetworkStatistics {
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint64_t> messages_sent{0};
    std::atomic<uint64_t> messages_received{0};
    std::atomic<uint64_t> connection_count{0};
    std::atomic<uint64_t> disconnection_count{0};
    std::atomic<uint64_t> error_count{0};
    std::atomic<uint64_t> timeout_count{0};
    
    core::Timestamp start_time;
    std::atomic<core::Timestamp> last_activity;
    
    NetworkStatistics() 
        : start_time(std::chrono::high_resolution_clock::now())
        , last_activity(start_time) {}
    
    double get_send_rate() const {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        if (duration.count() == 0) return 0.0;
        return static_cast<double>(bytes_sent.load()) / duration.count();
    }
    
    double get_receive_rate() const {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        if (duration.count() == 0) return 0.0;
        return static_cast<double>(bytes_received.load()) / duration.count();
    }
    
    double get_error_rate() const {
        uint64_t total_messages = messages_sent.load() + messages_received.load();
        if (total_messages == 0) return 0.0;
        return static_cast<double>(error_count.load()) / total_messages * 100.0;
    }
};

// ==================== 网络连接接口 ====================

/**
 * 网络连接回调函数类型
 */
using NetworkEventCallback = std::function<void(const NetworkEvent&)>;
using DataReceivedCallback = std::function<void(core::SessionId, const uint8_t*, size_t)>;

/**
 * 网络连接基类
 */
class NetworkConnection {
protected:
    core::SessionId session_id_;
    NetworkConfig config_;
    std::atomic<ConnectionState> state_;
    NetworkStatistics stats_;
    NetworkEventCallback event_callback_;
    DataReceivedCallback data_callback_;

public:
    NetworkConnection(core::SessionId id, const NetworkConfig& config)
        : session_id_(id), config_(config), state_(ConnectionState::DISCONNECTED) {}
    
    virtual ~NetworkConnection() = default;
    
    // 纯虚函数
    virtual bool connect() = 0;
    virtual void disconnect() = 0;
    virtual bool send(const uint8_t* data, size_t length) = 0;
    virtual bool is_connected() const = 0;
    
    // 通用接口
    core::SessionId get_session_id() const { return session_id_; }
    ConnectionState get_state() const { return state_.load(); }
    const NetworkConfig& get_config() const { return config_; }
    const NetworkStatistics& get_statistics() const { return stats_; }
    
    void set_event_callback(NetworkEventCallback callback) {
        event_callback_ = std::move(callback);
    }
    
    void set_data_callback(DataReceivedCallback callback) {
        data_callback_ = std::move(callback);
    }

protected:
    void notify_event(const NetworkEvent& event) {
        if (event_callback_) {
            event_callback_(event);
        }
    }
    
    void notify_data_received(const uint8_t* data, size_t length) {
        if (data_callback_) {
            data_callback_(session_id_, data, length);
        }
        stats_.bytes_received.fetch_add(length);
        stats_.messages_received.fetch_add(1);
        stats_.last_activity.store(std::chrono::high_resolution_clock::now());
    }
    
    void update_send_stats(size_t bytes) {
        stats_.bytes_sent.fetch_add(bytes);
        stats_.messages_sent.fetch_add(1);
        stats_.last_activity.store(std::chrono::high_resolution_clock::now());
    }
};

} // namespace network
} // namespace arkreplay
