#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库引擎工厂
完全抽象 SQLAlchemy 的使用，让上层模块与 SQLAlchemy 解耦
提供抽象接口，隐藏 SQLAlchemy 的具体实现细节

架构设计：
- comparison层: 数据库基础设施（工厂、连接器、抽象接口）
- backend层: 系统业务逻辑（任务管理、结果存储）
"""
import os
import time
import logging
from datetime import datetime
from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Dict, Any, Optional, Generator, Protocol, runtime_checkable
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool, StaticPool
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, declarative_base

logger = logging.getLogger(__name__)

# ================================
# 抽象的ORM基础设施 - 隐藏SQLAlchemy实现细节
# ================================

# 创建抽象的Base类，用于模型定义
Base = declarative_base()

# ================================
# 抽象接口定义 - 隐藏 SQLAlchemy 实现
# ================================

@runtime_checkable
class SessionProtocol(Protocol):
    """数据库会话协议 - 简化的抽象接口"""

    def add(self, instance):
        """添加对象到会话"""
        ...

    def commit(self):
        """提交事务"""
        ...

    def rollback(self):
        """回滚事务"""
        ...

    def close(self):
        """关闭会话"""
        ...

    def query(self, *entities):
        """查询对象"""
        ...

class AbstractDatabaseManager(ABC):
    """抽象数据库管理器 - 完全隐藏 SQLAlchemy 实现"""

    @abstractmethod
    def initialize(self):
        """初始化数据库连接"""
        pass

    @abstractmethod
    def get_session_context(self) -> Generator[SessionProtocol, None, None]:
        """获取数据库会话上下文"""
        pass

    @abstractmethod
    def get_session(self) -> SessionProtocol:
        """获取数据库会话"""
        pass

    @abstractmethod
    def close(self):
        """关闭数据库连接"""
        pass

class DatabaseEngineConfig:
    """数据库引擎配置基类"""

    def __init__(self, database_url: str):
        self.database_url = database_url
        self.is_sqlite = database_url.startswith('sqlite')
        self.db_type = self._detect_db_type()

    def _detect_db_type(self) -> str:
        """检测数据库类型"""
        url_lower = self.database_url.lower()
        if 'sqlite' in url_lower:
            return 'sqlite'
        elif 'postgresql' in url_lower or 'postgres' in url_lower:
            return 'postgresql'
        elif 'mysql' in url_lower:
            return 'mysql'
        elif 'oracle' in url_lower:
            return 'oracle'
        elif 'db2' in url_lower:
            return 'db2'
        elif 'gaussdb' in url_lower:
            return 'gaussdb'
        else:
            return 'unknown'

    def get_base_config(self) -> Dict[str, Any]:
        """获取基础配置"""
        return {
            'pool_pre_ping': True,
            'echo': False,
            'future': True  # 启用SQLAlchemy 2.0风格
        }

class SystemDatabaseEngineConfig(DatabaseEngineConfig):
    """系统数据库引擎配置 - 针对ORM操作和长连接优化"""

    def get_engine_config(self, pool_size: int = 10, max_overflow: int = 20,
                         debug: bool = False) -> Dict[str, Any]:
        """获取系统数据库引擎配置"""
        config = self.get_base_config()
        config.update({
            'echo': debug,
            'pool_recycle': 3600,  # 1小时回收连接，适合长连接
        })

        if self.is_sqlite:
            # SQLite配置 - 适合开发环境
            config.update({
                'poolclass': StaticPool,
                'connect_args': {
                    "check_same_thread": False,  # 允许多线程访问
                    "timeout": 20                # 超时时间
                }
            })
        else:
            # 生产数据库配置
            config.update({
                'poolclass': QueuePool,
                'pool_size': pool_size,
                'max_overflow': max_overflow,
                'pool_timeout': 30,                    # 获取连接超时
                'pool_reset_on_return': 'commit',      # 连接返回时重置状态
                'echo_pool': debug                     # 连接池日志
            })

        return config

class BusinessDatabaseEngineConfig(DatabaseEngineConfig):
    """业务数据库引擎配置 - 针对原生SQL查询和临时连接优化"""

    def get_engine_config(self, pool_size: int = 5, max_overflow: int = 10,
                         debug: bool = False) -> Dict[str, Any]:
        """获取业务数据库引擎配置"""
        config = self.get_base_config()
        config.update({
            'echo': debug,
            'poolclass': QueuePool,
            'pool_size': pool_size,
            'max_overflow': max_overflow,
            'pool_pre_ping': True,     # 预先ping连接
            'pool_recycle': 1800,      # 30分钟回收，适合临时连接
            'pool_timeout': 5,         # 更短的超时，快速失败
        })

        # 针对不同数据库类型的特殊配置
        if self.db_type == 'db2':
            # DB2特殊配置
            config.update({
                'pool_size': 1,              # 单一连接，避免并发问题
                'max_overflow': 0,           # 禁用溢出连接
                'pool_recycle': 180,         # 3分钟回收，更频繁刷新
                'pool_reset_on_return': 'commit'
            })
        elif self.db_type == 'oracle':
            # Oracle特殊配置
            config.update({
                'pool_size': max(3, pool_size),  # Oracle最少3个连接
                'pool_recycle': 3600             # Oracle可以使用更长的回收时间
            })

        return config

# ================================
# 具体实现类 - 实现抽象接口，隐藏 SQLAlchemy 细节
# ================================

class SystemDatabaseManager(AbstractDatabaseManager):
    """
    系统数据库管理器的具体实现
    实现抽象接口，完全隐藏 SQLAlchemy 的实现细节
    """

    def __init__(self, database_url: str, pool_size: int = 10, max_overflow: int = 20, debug: bool = False):
        self.database_url = database_url
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.debug = debug
        self._engine: Optional[Engine] = None
        self._session_factory = None
        self._initialized_at = None

        # 连接统计信息
        self._connection_stats = {
            'total_connections': 0,
            'successful_connections': 0,
            'failed_connections': 0,
            'last_test_time': None,
            'last_test_result': None,
            'session_created': 0,
            'session_errors': 0
        }

    def initialize(self):
        """初始化系统数据库连接"""
        try:           
            # 使用工厂创建引擎，隐藏 SQLAlchemy 细节
            self._engine = DatabaseEngineFactory.create_system_engine(
                database_url=self.database_url,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                debug=self.debug
            )

            # 使用工厂创建会话工厂，隐藏 sessionmaker 类型
            self._session_factory = DatabaseEngineFactory.create_session_factory(
                engine=self._engine,
                autocommit=False,
                autoflush=False
            )

            self._initialized_at = datetime.now()
        except Exception as e:
            logger.error(f"系统数据库初始化失败: {e}")
            raise

    @contextmanager
    def get_session_context(self) -> Generator[SessionProtocol, None, None]:
        """
        获取系统数据库会话上下文
        返回抽象的 DatabaseSessionProtocol，隐藏 SQLAlchemy Session
        """
        if not self._session_factory:
            raise RuntimeError("系统数据库未初始化")

        session = self._session_factory()
        start_time = time.time()
        operation_id = f"sys_session_{int(start_time * 1000)}"

        try:
            yield session

            # 提交事务
            session.commit()
            duration = time.time() - start_time

        except Exception as e:
            # 回滚事务
            try:
                session.rollback()
                duration = time.time() - start_time
                logger.warning(f"系统数据库会话回滚: {operation_id}, 耗时: {duration:.3f}秒, 错误: {e}")
            except Exception as rollback_error:
                logger.error(f"系统数据库回滚失败: {operation_id}, 回滚错误: {rollback_error}")

            # 重新抛出原始异常
            raise

        finally:
            # 确保会话关闭
            try:
                session.close()
                total_duration = time.time() - start_time
                if total_duration > 1.0:  # 记录耗时较长的操作
                    logger.info(f"系统数据库会话关闭: {operation_id}, 总耗时: {total_duration:.3f}秒")
            except Exception as close_error:
                logger.error(f"关闭系统数据库会话失败: {operation_id}, 错误: {close_error}")

    def get_session(self) -> SessionProtocol:
        """
        获取系统数据库会话
        返回抽象的 DatabaseSessionProtocol，隐藏 SQLAlchemy Session
        """
        if not self._session_factory:
            raise RuntimeError("系统数据库未初始化")

        try:
            session = self._session_factory()
            self._connection_stats['session_created'] += 1
            self._connection_stats['total_connections'] += 1
            self._connection_stats['successful_connections'] += 1
            return session  # session 实现了 SessionProtocol
        except Exception as e:
            self._connection_stats['session_errors'] += 1
            self._connection_stats['total_connections'] += 1
            self._connection_stats['failed_connections'] += 1
            logger.error(f"创建系统数据库会话失败: {e}")
            raise

    def test_connection(self) -> bool:
        """
        测试系统数据库连接
        返回抽象的连接测试结果，隐藏 SQLAlchemy 实现细节
        """

        try:
            if not self._engine:
                logger.error("系统数据库引擎未初始化")
                return False

            start_time = time.time()

            # 使用工厂方法测试连接，隐藏 SQLAlchemy 细节
            DatabaseEngineFactory._test_engine_connection(self._engine, self.database_url)

            # 更新连接统计
            duration = time.time() - start_time
            self._connection_stats['last_test_time'] = datetime.now()
            self._connection_stats['last_test_result'] = True

            logger.info(f"系统数据库连接测试成功，耗时: {duration:.3f}秒")
            return True

        except Exception as e:
            # 更新连接统计
            self._connection_stats['last_test_time'] = datetime.now()
            self._connection_stats['last_test_result'] = False

            logger.error(f"系统数据库连接测试失败: {e}")
            return False

    def create_all_tables(self):
        """
        创建所有数据库表
        通过抽象接口创建表，隐藏SQLAlchemy实现细节
        """
        try:
            if not self._engine:
                raise RuntimeError("系统数据库引擎未初始化")

            # 使用Base的metadata创建所有表
            Base.metadata.create_all(bind=self._engine)
            logger.info("系统数据库表创建完成")

        except Exception as e:
            logger.error(f"创建系统数据库表失败: {e}")
            raise

    def close(self):
        """关闭系统数据库连接"""
        try:
            if self._engine:
                self._engine.dispose()
                self._engine = None
                self._session_factory = None
            logger.info("系统数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭系统数据库连接失败: {e}")

class DatabaseEngineFactory:
    """
    统一的数据库引擎工厂类
    """

    @staticmethod
    def create_system_engine(database_url: str, pool_size: int = 10,
                           max_overflow: int = 20, debug: bool = False) -> Engine:
        """
        创建系统数据库引擎
        专门用于系统元数据存储：ORM操作、事务管理、长连接
        """

        try:
            # 确保SQLite目录存在
            DatabaseEngineFactory._ensure_sqlite_directory(database_url)

            # 获取系统数据库配置
            config_manager = SystemDatabaseEngineConfig(database_url)
            engine_config = config_manager.get_engine_config(pool_size, max_overflow, debug)

            # 创建引擎
            engine = create_engine(database_url, **engine_config)

            # 测试连接
            DatabaseEngineFactory._test_engine_connection(engine, database_url)

            return engine

        except Exception as e:
            logger.error(f"创建系统数据库引擎失败: {e}")
            raise

    @staticmethod
    def create_business_engine(database_url: str, pool_size: int = 5,
                             max_overflow: int = 10, debug: bool = False) -> Engine:
        """
        创建业务数据库引擎
        专门用于业务数据查询：原生SQL、临时连接、批量查询
        """

        try:
            # 获取业务数据库配置
            config_manager = BusinessDatabaseEngineConfig(database_url)
            engine_config = config_manager.get_engine_config(pool_size, max_overflow, debug)

            # 创建引擎
            engine = create_engine(database_url, **engine_config)

            # 测试连接
            DatabaseEngineFactory._test_engine_connection(engine, database_url)

            return engine

        except Exception as e:
            logger.error(f"创建业务数据库引擎失败: {e}")
            raise

    @staticmethod
    def create_session_factory(engine: Engine, autocommit: bool = False,
                             autoflush: bool = False) -> sessionmaker:
        """创建会话工厂"""
        return sessionmaker(
            autocommit=autocommit,
            autoflush=autoflush,
            bind=engine
        )

    @staticmethod
    def _ensure_sqlite_directory(database_url: str):
        """确保SQLite数据库目录存在"""
        if database_url.startswith('sqlite') and ':///' in database_url:
            db_path = database_url.split(':///', 1)[1]
            if db_path != ':memory:':
                db_dir = os.path.dirname(db_path)
                if db_dir and not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)

    @staticmethod
    def _test_engine_connection(engine: Engine, database_url: str):
        """测试引擎连接"""
        with engine.connect() as conn:
            # 根据数据库类型选择测试SQL
            if 'sqlite' in database_url:
                conn.execute(text("SELECT 1"))
            elif 'db2' in database_url:
                conn.execute(text("SELECT 1 FROM SYSIBM.SYSDUMMY1"))
            elif 'oracle' in database_url:
                conn.execute(text("SELECT 1 FROM DUAL"))
            else:
                conn.execute(text("SELECT 1"))

# 便捷函数
def create_system_database_engine(database_url: str, **kwargs) -> Engine:
    """创建系统数据库引擎的便捷函数"""
    return DatabaseEngineFactory.create_system_engine(database_url, **kwargs)

def create_business_database_engine(database_url: str, **kwargs) -> Engine:
    """创建业务数据库引擎的便捷函数"""
    return DatabaseEngineFactory.create_business_engine(database_url, **kwargs)