# 数据比对器代理环境配置示例
# 复制此文件为 .env 并修改相应配置

# 应用基础配置
APP_NAME=数据比对器代理
VERSION=2.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
DATABASE_URL=**********************************************************/comparison_db
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://:redis_pass@redis:6379/0
REDIS_CACHE_TTL=3600

# Celery配置
CELERY_BROKER_URL=redis://:redis_pass@redis:6379/1
CELERY_RESULT_BACKEND=redis://:redis_pass@redis:6379/2

# 文件存储配置
UPLOAD_DIR=./uploads
RESULT_DIR=./results
MAX_FILE_SIZE=104857600

# 比对配置
DEFAULT_BATCH_SIZE=1000
MAX_CONCURRENT_TASKS=5
COMPARISON_TIMEOUT=3600

# Legacy API兼容性配置
ENABLE_LEGACY_API=true
LEGACY_API_DEPRECATION_WARNING=true
LEGACY_API_SUNSET_DATE=2024-06-01
MIGRATION_GUIDE_URL=https://docs.example.com/migration

# API版本配置
DEFAULT_API_VERSION=v1
SUPPORTED_API_VERSIONS=v1,legacy

# 兼容性监控配置
ENABLE_LEGACY_MONITORING=true
LEGACY_CACHE_TTL=300
MIGRATION_LOG_LEVEL=INFO

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 支持的数据库类型
SUPPORTED_DB_TYPES=oracle,mysql,postgresql,sqlserver,db2,gaussdb
