#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较指挥器 (Comparison Director)
专门用于解决多层抽象开销性能瓶颈，实现4-6秒快速比较

设计原则：
- 复用现有的 comparison/connectors/ 基础设施
- 简化调用链路，消除不必要的抽象层
- 保持架构一致性，最小化代码变更
- 渐进式集成，向后兼容

架构兼容性：
- 使用现有的 DatabaseConnectorFactory 创建连接器
- 复用现有的数据库配置和连接逻辑
- 保持与现有比较引擎的接口兼容性
- 最小化对现有代码的影响
"""
import time
import logging
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ComparisonDirectorResult:
    """比较指挥器结果 - 兼容现有结果格式"""
    task_id: str
    table_name: str
    total_source_records: int
    total_target_records: int
    matched_records: int
    different_records: int
    differences: List[Dict[str, Any]]
    execution_time: float
    status: str
    error_message: Optional[str] = None

class ComparisonDirector:
    """
    比较指挥器 - 性能优化版本

    核心优化策略：
    1. 复用现有连接器基础设施，避免重复实现
    2. 简化调用链路，减少抽象层开销
    3. 直接同步执行，消除线程+异步嵌套
    4. 轻量级进度更新，避免阻塞

    架构兼容性：
    - 使用现有的 DatabaseConnectorFactory
    - 保持与现有配置系统的兼容性
    - 最小化对现有代码的影响
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        # 延迟导入，避免循环依赖
        self._connector_factory = None

    def _get_connector_factory(self):
        """延迟获取连接器工厂"""
        if self._connector_factory is None:
            try:
                from comparison.connectors.database_connector import DatabaseConnectorFactory
                self._connector_factory = DatabaseConnectorFactory()
            except ImportError as e:
                self.logger.error(f"无法导入数据库连接器工厂: {e}")
                raise RuntimeError("数据库连接器基础设施不可用")
        return self._connector_factory

    def run_optimized_comparison(self, source_config, target_config, table_config,
                               progress_callback: Optional[Callable[[int, str], None]] = None) -> ComparisonDirectorResult:
        """
        执行优化的数据库比较 - 复用现有基础设施

        Args:
            source_config: 源数据库配置
            target_config: 目标数据库配置
            table_config: 表配置信息
            progress_callback: 进度回调函数（可选）

        Returns:
            ComparisonDirectorResult: 比较结果
        """
        start_time = time.time()
        task_id = f"director_task_{int(start_time * 1000)}"
        table_name = table_config.get('table_id', 'unknown')

        try:
            self.logger.info(f"🚀 开始优化比较: {table_name}")

            # 阶段1: 创建连接器 (10%)
            self._update_progress(progress_callback, 10, "创建数据库连接器")
            source_connector = self._create_optimized_connector(source_config)
            target_connector = self._create_optimized_connector(target_config)

            # 阶段2: 获取源数据 (30%)
            self._update_progress(progress_callback, 30, "获取源数据")
            source_sql = table_config.get('sql_1', '').strip()
            source_data = self._fetch_data_optimized(source_connector, source_sql)

            # 阶段3: 获取目标数据 (60%)
            self._update_progress(progress_callback, 60, "获取目标数据")
            target_sql = table_config.get('sql_2', '').strip()
            target_data = self._fetch_data_optimized(target_connector, target_sql)

            # 阶段4: 执行比较 (80%)
            self._update_progress(progress_callback, 80, "执行数据比较")
            differences = self._compare_data_optimized(source_data, target_data)

            # 阶段5: 清理资源 (100%)
            self._update_progress(progress_callback, 100, "比较完成")
            self._cleanup_connector(source_connector)
            self._cleanup_connector(target_connector)

            execution_time = time.time() - start_time

            # 计算统计信息
            total_records = len(source_data) + len(target_data)
            different_count = len(differences)
            matched_count = total_records - different_count

            result = ComparisonDirectorResult(
                task_id=task_id,
                table_name=table_name,
                total_source_records=len(source_data),
                total_target_records=len(target_data),
                matched_records=matched_count,
                different_records=different_count,
                differences=differences,
                execution_time=execution_time,
                status="completed"
            )

            self.logger.info(f"✅ 优化比较完成: {table_name}, 耗时: {execution_time:.2f}秒")
            return result

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"比较失败: {str(e)}"
            self.logger.error(f"❌ {table_name} {error_msg}")

            return ComparisonDirectorResult(
                task_id=task_id,
                table_name=table_name,
                total_source_records=0,
                total_target_records=0,
                matched_records=0,
                different_records=0,
                differences=[],
                execution_time=execution_time,
                status="failed",
                error_message=error_msg
            )

    def _update_progress(self, callback: Optional[Callable], progress: int, message: str):
        """更新进度 - 轻量级实现"""
        if callback:
            try:
                callback(progress, message)
            except Exception as e:
                self.logger.warning(f"进度回调失败: {e}")

    def _create_optimized_connector(self, db_config):
        """
        创建优化的数据库连接器 - 复用现有基础设施

        优化点：
        - 使用现有的 DatabaseConnectorFactory
        - 简化连接参数，专注性能
        - 避免重复实现数据库驱动逻辑
        """
        try:
            connector_factory = self._get_connector_factory()

            # 根据数据库类型创建连接器
            db_type = getattr(db_config, 'type', 'unknown').lower()
            connector = connector_factory.create_connector(db_type)

            # 创建简化的连接配置
            connection_info = self._create_connection_info(db_config)

            # 建立连接（同步方式，避免异步开销）
            self._connect_sync(connector, connection_info)

            return connector

        except Exception as e:
            self.logger.error(f"创建优化连接器失败: {e}")
            raise RuntimeError(f"数据库连接失败: {e}")

    def _create_connection_info(self, db_config):
        """创建连接信息对象 - 兼容现有连接器接口"""
        # 创建兼容现有连接器的连接信息对象
        class ConnectionInfo:
            def __init__(self, config):
                self.db_type = getattr(config, 'type', 'unknown').lower()
                self.host = getattr(config, 'host', 'localhost')
                self.port = getattr(config, 'port', 3306)
                self.username = getattr(config, 'username', '')
                self.password = getattr(config, 'password', '')
                self.database = getattr(config, 'database', '')

        return ConnectionInfo(db_config)

    def _connect_sync(self, connector, connection_info):
        """
        同步连接数据库 - 避免异步开销

        优化点：
        - 将异步连接转换为同步执行
        - 减少事件循环创建开销
        - 简化连接流程
        """
        try:
            # 如果连接器有异步连接方法，转换为同步执行
            if hasattr(connector, 'connect') and asyncio.iscoroutinefunction(connector.connect):
                # 创建临时事件循环执行异步连接
                loop = asyncio.new_event_loop()
                try:
                    loop.run_until_complete(connector.connect(connection_info))
                finally:
                    loop.close()
            elif hasattr(connector, 'connect'):
                # 直接调用同步连接方法
                connector.connect(connection_info)
            else:
                raise RuntimeError("连接器不支持连接操作")

        except Exception as e:
            self.logger.error(f"同步连接失败: {e}")
            raise

    def _fetch_data_optimized(self, connector, sql: str) -> List[Dict[str, Any]]:
        """
        优化的数据获取 - 复用现有连接器

        优化点：
        - 使用现有连接器的查询方法
        - 同步执行，避免异步开销
        - 批量获取，减少网络往返
        """
        if not sql or not sql.strip():
            return []

        try:
            # 如果连接器有异步查询方法，转换为同步执行
            if hasattr(connector, 'execute_query') and asyncio.iscoroutinefunction(connector.execute_query):
                loop = asyncio.new_event_loop()
                try:
                    result = loop.run_until_complete(connector.execute_query(sql))
                finally:
                    loop.close()
                return result
            elif hasattr(connector, 'execute_query'):
                # 直接调用同步查询方法
                return connector.execute_query(sql)
            else:
                # 回退到原生SQL执行
                return self._execute_raw_sql(connector, sql)

        except Exception as e:
            self.logger.error(f"数据获取失败: {sql[:100]}... 错误: {e}")
            return []

    def _execute_raw_sql(self, connector, sql: str) -> List[Dict[str, Any]]:
        """
        执行原生SQL - 回退方案

        当连接器不支持标准查询方法时的回退实现
        """
        try:
            # 尝试获取连接器的原生连接对象
            if hasattr(connector, 'session') and connector.session:
                # 使用SQLAlchemy会话执行原生SQL
                from sqlalchemy import text
                result = connector.session.execute(text(sql))

                # 转换结果为字典列表
                columns = result.keys()
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]

            elif hasattr(connector, 'engine') and connector.engine:
                # 使用SQLAlchemy引擎执行原生SQL
                from sqlalchemy import text
                with connector.engine.connect() as conn:
                    result = conn.execute(text(sql))
                    columns = result.keys()
                    rows = result.fetchall()
                    return [dict(zip(columns, row)) for row in rows]
            else:
                self.logger.warning("无法获取连接器的原生连接对象")
                return []

        except Exception as e:
            self.logger.error(f"原生SQL执行失败: {e}")
            return []

    def _cleanup_connector(self, connector):
        """清理连接器资源"""
        try:
            if hasattr(connector, 'disconnect') and asyncio.iscoroutinefunction(connector.disconnect):
                # 异步断开连接
                loop = asyncio.new_event_loop()
                try:
                    loop.run_until_complete(connector.disconnect())
                finally:
                    loop.close()
            elif hasattr(connector, 'disconnect'):
                # 同步断开连接
                connector.disconnect()
            elif hasattr(connector, 'close'):
                # 通用关闭方法
                connector.close()
        except Exception as e:
            self.logger.warning(f"清理连接器失败: {e}")

    def _compare_data_optimized(self, source_data: List[Dict],
                              target_data: List[Dict]) -> List[Dict[str, Any]]:
        """
        优化的数据比较算法

        优化点：
        - 简化比较逻辑
        - 使用字典快速查找
        - 最小化内存分配
        """
        differences = []

        # 创建快速查找映射
        source_map = {}
        target_map = {}

        # 处理源数据 - 使用多种键策略
        for record in source_data:
            key = self._generate_record_key(record)
            source_map[key] = record

        # 处理目标数据
        for record in target_data:
            key = self._generate_record_key(record)
            target_map[key] = record

        # 快速差异检测
        all_keys = set(source_map.keys()) | set(target_map.keys())

        for key in all_keys:
            source_record = source_map.get(key)
            target_record = target_map.get(key)

            if source_record and not target_record:
                differences.append({
                    'type': 'source_only',
                    'key': key,
                    'source_record': source_record,
                    'target_record': None,
                    'description': f"记录仅存在于源端: {key}"
                })
            elif target_record and not source_record:
                differences.append({
                    'type': 'target_only',
                    'key': key,
                    'source_record': None,
                    'target_record': target_record,
                    'description': f"记录仅存在于目标端: {key}"
                })
            elif source_record and target_record:
                # 快速内容比较
                if not self._records_equal(source_record, target_record):
                    differences.append({
                        'type': 'data_mismatch',
                        'key': key,
                        'source_record': source_record,
                        'target_record': target_record,
                        'description': f"记录内容不匹配: {key}",
                        'field_differences': self._get_field_differences(source_record, target_record)
                    })

        return differences

    def _get_field_differences(self, source_record: Dict[str, Any],
                             target_record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取字段级差异 - 兼容现有差异格式"""
        field_diffs = []

        # 获取所有字段
        all_fields = set(source_record.keys()) | set(target_record.keys())

        for field in all_fields:
            source_value = source_record.get(field)
            target_value = target_record.get(field)

            # 处理None值比较
            if source_value != target_value:
                field_diffs.append({
                    'field': field,
                    'source_value': source_value,
                    'target_value': target_value
                })

        return field_diffs

    def _generate_record_key(self, record: Dict[str, Any]) -> str:
        """
        生成记录键 - 支持多种键策略

        优先级：
        1. KEY字段
        2. ID字段
        3. 第一个字段
        4. 记录哈希
        """
        # 尝试常见的主键字段
        for key_field in ['KEY', 'key', 'ID', 'id', 'pk', 'PK']:
            if key_field in record and record[key_field] is not None:
                return str(record[key_field])

        # 使用第一个非空字段
        for key, value in record.items():
            if value is not None:
                return f"{key}:{value}"

        # 最后使用记录哈希
        return str(hash(str(sorted(record.items()))))

    def _records_equal(self, record1: Dict[str, Any], record2: Dict[str, Any]) -> bool:
        """快速记录比较"""
        if len(record1) != len(record2):
            return False

        for key, value1 in record1.items():
            if key not in record2:
                return False

            value2 = record2[key]

            # 处理None值比较
            if value1 is None and value2 is None:
                continue
            if value1 is None or value2 is None:
                return False

            # 简单值比较
            if value1 != value2:
                return False

        return True

    def _close_direct_connection(self, connection):
        """关闭直接数据库连接"""
        try:
            if hasattr(connection, 'close'):
                connection.close()
            else:  # DB2
                import ibm_db
                ibm_db.close(connection)
        except Exception as e:
            self.logger.warning(f"关闭连接失败: {e}")
