"""
Legacy API兼容性数据模型

定义与原版本API兼容的请求和响应模型
"""
from typing import Any, Optional, Dict, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class DatabaseType(str, Enum):
    """数据库类型枚举（与原版本保持一致）"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    DB2 = "db2"
    GAUSSDB = "gaussdb"
    MONGODB = "mongodb"


class ConnectionParameters(BaseModel):
    """数据库连接的额外参数"""
    charset: Optional[str] = None
    service_name: Optional[str] = None
    sid: Optional[str] = None
    ssl_mode: Optional[str] = None
    db_schema: Optional[str] = None

    model_config = {
        "extra": "allow"
    }


class DatabaseConnection(BaseModel):
    """数据库连接请求模型（原版本兼容）"""
    type: DatabaseType
    host: str
    port: int
    username: str
    password: str = Field(..., description="数据库密码")
    database: str = Field(..., description="数据库名称或服务名")
    parameters: Optional[ConnectionParameters] = Field(
        default_factory=ConnectionParameters,
        description="数据库特定的连接参数"
    )

    @property
    def db_type(self) -> DatabaseType:
        """获取数据库类型（兼容性属性）"""
        return self.type


class ConnectionTestRequest(BaseModel):
    """数据库连接测试请求模型（原版本兼容）"""
    config: DatabaseConnection = Field(..., description="数据库连接配置")
    timeout: Optional[int] = Field(None, description="连接超时时间（秒）")


class ConnectionTestResponse(BaseModel):
    """数据库连接测试响应模型（原版本兼容）"""
    success: bool = Field(..., description="连接测试是否成功")
    message: str = Field(..., description="连接测试结果消息")
    elapsed_time: float = Field(..., description="测试耗时（秒）")
    version: Optional[str] = Field(None, description="数据库版本")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据信息")


class TaskPriority(str, Enum):
    """任务优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class AlgorithmType(str, Enum):
    """比对算法类型"""
    PRIMARY_KEY = "primary_key"
    HASH = "hash"
    FULL_SCAN = "full_scan"


class CompareMode(str, Enum):
    """比对模式"""
    TABLE = "table"
    SQL = "sql"


class CompareConfig(BaseModel):
    """比对配置（原版本兼容）"""
    mode: CompareMode = Field(..., description="比对模式")
    source_table: Optional[str] = Field(None, description="源表名")
    target_table: Optional[str] = Field(None, description="目标表名")
    source_sql: Optional[str] = Field(None, description="源SQL查询")
    target_sql: Optional[str] = Field(None, description="目标SQL查询")
    
    # 通用配置
    primary_keys: List[str] = Field(..., min_items=1, description="主键字段列表")
    compare_columns: Optional[List[str]] = Field(None, description="比对的字段列表")
    ignore_columns: Optional[List[str]] = Field(None, description="忽略比对的字段列表")
    where_clause: Optional[str] = Field(None, description="过滤条件")
    limit: Optional[int] = Field(None, gt=0, description="限制比对的记录数")
    batch_size: int = Field(1000, gt=0, description="处理批次大小")
    
    # 算法配置
    algorithm_type: AlgorithmType = Field(AlgorithmType.PRIMARY_KEY, description="比对算法类型")
    
    # 高级配置
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="任务完成后的回调URL")


class CompareTaskRequest(BaseModel):
    """比对任务请求模型（原版本兼容）"""
    source: DatabaseConnection = Field(..., description="源数据库连接配置")
    target: DatabaseConnection = Field(..., description="目标数据库连接配置")
    config: CompareConfig = Field(..., description="比对配置")


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    code: int = Field(default=200, description="响应代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间戳")


class TaskResponse(BaseResponse):
    """任务响应模型（原版本兼容）"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress: Optional[int] = Field(None, description="任务进度(0-100)")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class TaskStatusResponse(BaseResponse):
    """任务状态响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: Optional[float] = Field(None, description="任务进度(0-100)")
    created_at: str = Field(..., description="创建时间")
    started_at: Optional[str] = Field(None, description="开始时间")
    completed_at: Optional[str] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class ComparisonSummary(BaseModel):
    """比对摘要"""
    total_records: int = Field(..., description="总记录数")
    matched_records: int = Field(..., description="匹配记录数")
    different_records: int = Field(..., description="差异记录数")
    source_only_records: int = Field(..., description="仅源端存在的记录数")
    target_only_records: int = Field(..., description="仅目标端存在的记录数")
    execution_time: float = Field(..., description="执行时间(秒)")


class TaskResultResponse(BaseResponse):
    """任务结果响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    summary: Optional[ComparisonSummary] = Field(None, description="比对摘要")
    details: Optional[List[Dict[str, Any]]] = Field(None, description="比对详情")
    created_at: str = Field(..., description="创建时间")
    completed_at: Optional[str] = Field(None, description="完成时间")


class ComparisonResponse(BaseResponse):
    """数据比对响应模型（用于直接比对）"""
    summary: Optional[ComparisonSummary] = Field(None, description="比对摘要")
    details: Optional[List[Dict[str, Any]]] = Field(None, description="比对详情")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(default=False, description="操作失败")
    error_code: str = Field(..., description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
