"""
Legacy API适配器服务

提供完整的Legacy API到新版本API的适配功能，包括数据转换、错误处理和性能优化
"""
import os
import sys
# 获取当前脚本的目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 将项目根目录添加到Python路径中，使得backend和comparison模块可以被正确导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)
import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

from backend.api.legacy.models import (
    CompareTaskRequest, ConnectionTestRequest, DatabaseConnection,
    TaskResponse, TaskStatusResponse, TaskResultResponse, ComparisonResponse,
    ConnectionTestResponse, TaskStatus, ComparisonSummary
)
from backend.api.legacy.converters import LegacyConverter
from backend.services.comparison_service import ComparisonService
from backend.models.comparison_models import ComparisonTaskCreate

logger = logging.getLogger(__name__)


class LegacyAdapterService:
    """Legacy API适配器服务"""
    
    def __init__(self):
        self.comparison_service = ComparisonService()
        self.converter = LegacyConverter()
        self.direct_comparison_cache = {}  # 直接比对结果缓存
        
    async def test_database_connection(self, request: ConnectionTestRequest) -> ConnectionTestResponse:
        """测试数据库连接（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 数据库连接测试 - {request.config.type}@{request.config.host}")
            
            start_time = time.time()
            
            # 转换为新版本格式
            new_connection = self.converter.convert_connection_test_request(request)
            
            # 调用新版本连接测试服务
            # TODO: 实际调用新版本的连接测试API
            # 这里先模拟实现
            success, message, metadata = await self._test_connection_internal(new_connection)
            
            elapsed_time = time.time() - start_time
            
            # 构建响应
            result = {
                "success": success,
                "message": message,
                "elapsed_time": elapsed_time,
                "version": metadata.get("version", "Unknown") if metadata else "Unknown",
                "metadata": metadata or {}
            }
            
            # 转换为Legacy格式
            legacy_response = self.converter.convert_connection_test_response(result)
            
            logger.info(f"Legacy适配器: 连接测试完成 - 成功: {success}, 耗时: {elapsed_time:.2f}s")
            return legacy_response
            
        except Exception as e:
            logger.error(f"Legacy适配器: 连接测试失败 - {str(e)}")
            raise
    
    async def create_comparison_task(self, request: CompareTaskRequest) -> TaskResponse:
        """创建比对任务（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 创建比对任务 - 源: {request.source.type}@{request.source.host}")
            
            # 转换为新版本格式
            new_request_data = self.converter.convert_task_request_to_new_format(request)
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建新版本任务对象
            new_task = ComparisonTaskCreate(
                task_id=task_id,
                **new_request_data
            )
            
            # 验证任务
            is_valid, error_msg = await self.comparison_service.validate_task(new_task)
            if not is_valid:
                raise ValueError(f"任务验证失败: {error_msg}")
            
            # 创建任务
            created_task = await self.comparison_service.create_task(new_task)
            
            # 异步启动任务执行
            asyncio.create_task(self._execute_task_async(task_id))
            
            # 转换为Legacy格式
            legacy_response = self.converter.convert_new_task_response_to_legacy(created_task)
            
            logger.info(f"Legacy适配器: 任务创建成功 - ID: {task_id}")
            return legacy_response
            
        except Exception as e:
            logger.error(f"Legacy适配器: 任务创建失败 - {str(e)}")
            raise
    
    async def get_task_status(self, task_id: str) -> TaskStatusResponse:
        """获取任务状态（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 查询任务状态 - ID: {task_id}")
            
            # 获取任务状态
            task = await self.comparison_service.get_task(task_id)
            
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 转换为Legacy格式
            legacy_response = self.converter.convert_new_status_to_legacy(task)
            
            logger.info(f"Legacy适配器: 任务状态查询成功 - ID: {task_id}, 状态: {task.status}")
            return legacy_response
            
        except Exception as e:
            logger.error(f"Legacy适配器: 任务状态查询失败 - {str(e)}")
            raise
    
    async def get_task_result(self, task_id: str) -> TaskResultResponse:
        """获取任务结果（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 获取任务结果 - ID: {task_id}")
            
            # 获取任务信息
            task = await self.comparison_service.get_task(task_id)
            
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 获取比对结果
            result = await self.comparison_service.get_comparison_result(task_id)
            
            # 转换为Legacy格式
            legacy_response = self.converter.convert_new_result_to_legacy(task, result)
            
            logger.info(f"Legacy适配器: 任务结果获取成功 - ID: {task_id}")
            return legacy_response
            
        except Exception as e:
            logger.error(f"Legacy适配器: 任务结果获取失败 - {str(e)}")
            raise
    
    async def compare_data_directly(self, request: CompareTaskRequest) -> ComparisonResponse:
        """直接同步比对数据（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 直接比对 - 源: {request.source.type}@{request.source.host}")
            
            start_time = time.time()
            
            # 生成缓存键
            cache_key = self._generate_cache_key(request)
            
            # 检查缓存
            if cache_key in self.direct_comparison_cache:
                cached_result = self.direct_comparison_cache[cache_key]
                if time.time() - cached_result["timestamp"] < 300:  # 5分钟缓存
                    logger.info("Legacy适配器: 使用缓存结果")
                    return cached_result["response"]
            
            # 转换为新版本格式
            new_request_data = self.converter.convert_task_request_to_new_format(request)
            
            # 生成临时任务ID
            temp_task_id = f"direct_{uuid.uuid4().hex[:8]}"
            
            # 创建临时任务对象
            temp_task = ComparisonTaskCreate(
                task_id=temp_task_id,
                **new_request_data
            )
            
            # 验证任务
            is_valid, error_msg = await self.comparison_service.validate_task(temp_task)
            if not is_valid:
                raise ValueError(f"比对配置验证失败: {error_msg}")
            
            # 执行直接比对
            result = await self._execute_direct_comparison(temp_task)
            
            execution_time = time.time() - start_time
            
            # 构建比对结果
            comparison_result = {
                "success": True,
                "message": "数据比对完成",
                "summary": {
                    "total_records": result.get("total_records", 0),
                    "matched_records": result.get("matched_records", 0),
                    "different_records": result.get("different_records", 0),
                    "source_only_records": result.get("source_only_records", 0),
                    "target_only_records": result.get("target_only_records", 0),
                    "execution_time": execution_time
                },
                "details": result.get("differences", []),
                "data": {
                    "task_id": temp_task_id,
                    "comparison_type": new_request_data["comparison_type"]
                }
            }
            
            # 转换为Legacy格式
            legacy_response = self.converter.convert_direct_comparison_result(comparison_result)
            
            # 缓存结果
            self.direct_comparison_cache[cache_key] = {
                "response": legacy_response,
                "timestamp": time.time()
            }
            
            # 清理过期缓存
            self._cleanup_cache()
            
            logger.info(f"Legacy适配器: 直接比对完成 - 耗时: {execution_time:.2f}s")
            return legacy_response
            
        except Exception as e:
            logger.error(f"Legacy适配器: 直接比对失败 - {str(e)}")
            raise
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务（Legacy API适配）"""
        try:
            logger.info(f"Legacy适配器: 取消任务 - ID: {task_id}")
            
            # 获取任务
            task = await self.comparison_service.get_task(task_id)
            
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 取消任务
            success = await self.comparison_service.cancel_task(task_id)
            
            if success:
                logger.info(f"Legacy适配器: 任务取消成功 - ID: {task_id}")
                return {
                    "success": True,
                    "message": f"任务 {task_id} 已取消",
                    "code": 200,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise ValueError(f"任务取消失败: {task_id}")
                
        except Exception as e:
            logger.error(f"Legacy适配器: 任务取消失败 - {str(e)}")
            raise
    
    async def _test_connection_internal(self, connection: Dict[str, Any]) -> tuple[bool, str, Optional[Dict[str, Any]]]:
        """内部连接测试实现"""
        try:
            # TODO: 实际实现数据库连接测试
            # 这里先模拟实现
            await asyncio.sleep(0.1)  # 模拟连接时间
            
            db_type = connection.get("db_type", "unknown")
            host = connection.get("host", "unknown")
            
            # 模拟连接成功
            return True, f"成功连接到 {db_type} 数据库 {host}", {
                "version": "Mock Version 1.0",
                "connection_type": db_type,
                "server_info": f"{db_type} Server"
            }
            
        except Exception as e:
            return False, f"连接失败: {str(e)}", None
    
    async def _execute_task_async(self, task_id: str):
        """异步执行任务"""
        try:
            logger.info(f"Legacy适配器: 开始异步执行任务 - ID: {task_id}")
            
            # 调用新版本的任务执行服务
            await self.comparison_service.execute_task(task_id)
            
            logger.info(f"Legacy适配器: 任务执行完成 - ID: {task_id}")
            
        except Exception as e:
            logger.error(f"Legacy适配器: 任务执行失败 - ID: {task_id}, 错误: {str(e)}")
    
    async def _execute_direct_comparison(self, task: ComparisonTaskCreate) -> Dict[str, Any]:
        """执行直接比对"""
        try:
            # TODO: 实际实现直接比对逻辑
            # 这里先模拟实现
            await asyncio.sleep(0.5)  # 模拟比对时间
            
            # 模拟比对结果
            return {
                "total_records": 1000,
                "matched_records": 950,
                "different_records": 30,
                "source_only_records": 10,
                "target_only_records": 10,
                "differences": [
                    {
                        "type": "difference",
                        "key": "user_001",
                        "column": "email",
                        "source_value": "<EMAIL>",
                        "target_value": "<EMAIL>"
                    },
                    {
                        "type": "source_only",
                        "key": "user_002",
                        "record": {"id": "user_002", "name": "John Doe"}
                    }
                ]
            }
            
        except Exception as e:
            logger.error(f"Legacy适配器: 直接比对执行失败 - {str(e)}")
            raise
    
    def _generate_cache_key(self, request: CompareTaskRequest) -> str:
        """生成缓存键"""
        import hashlib
        
        # 构建缓存键的组成部分
        key_parts = [
            request.source.type.value,
            request.source.host,
            str(request.source.port),
            request.source.database,
            request.target.type.value,
            request.target.host,
            str(request.target.port),
            request.target.database,
            request.config.mode.value,
            str(request.config.source_table or ""),
            str(request.config.target_table or ""),
            str(request.config.source_sql or ""),
            str(request.config.target_sql or ""),
            str(request.config.primary_keys),
            str(request.config.compare_columns or []),
            str(request.config.batch_size)
        ]
        
        # 生成哈希
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, value in self.direct_comparison_cache.items()
            if current_time - value["timestamp"] > 300  # 5分钟过期
        ]
        
        for key in expired_keys:
            del self.direct_comparison_cache[key]
        
        if expired_keys:
            logger.info(f"Legacy适配器: 清理了 {len(expired_keys)} 个过期缓存项")


# 全局适配器实例
legacy_adapter_service = LegacyAdapterService()
