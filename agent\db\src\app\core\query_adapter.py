"""
跨数据库查询适配器

提供跨数据库查询适配功能，处理不同数据库之间的语法差异。
"""
import re
from typing import Dict, List, Any, Optional, Tuple

from app.models.logger import logger


class QueryAdapter:
    """跨数据库查询适配器"""

    def __init__(self, source_db_type: str, target_db_type: str):
        """
        初始化跨数据库查询适配器

        Args:
            source_db_type: 源数据库类型
            target_db_type: 目标数据库类型
        """
        self.source_db_type = source_db_type.lower()
        self.target_db_type = target_db_type.lower()

        # 初始化函数映射
        self._init_function_mappings()

        # 初始化语法映射
        self._init_syntax_mappings()

        logger.debug(f"初始化跨数据库查询适配器: {source_db_type} -> {target_db_type}")

    def _init_function_mappings(self):
        """初始化函数映射"""
        # 日期函数映射
        self.date_functions = {
            # 当前日期
            "db2": {
                "current_date": "CURRENT DATE",
                "current_time": "CURRENT TIME",
                "current_timestamp": "CURRENT TIMESTAMP",
                "extract_year": "YEAR({0})",
                "extract_month": "MONTH({0})",
                "extract_day": "DAY({0})",
                "date_add": "({0} + {1} DAYS)",
                "date_diff": "DAYS({0}) - DAYS({1})",
                "date_format": "VARCHAR_FORMAT({0}, '{1}')",
                "to_date": "DATE({0})"
            },
            "oracle": {
                "current_date": "SYSDATE",
                "current_time": "SYSTIMESTAMP",
                "current_timestamp": "SYSTIMESTAMP",
                "extract_year": "EXTRACT(YEAR FROM {0})",
                "extract_month": "EXTRACT(MONTH FROM {0})",
                "extract_day": "EXTRACT(DAY FROM {0})",
                "date_add": "({0} + {1})",
                "date_diff": "({0} - {1})",
                "date_format": "TO_CHAR({0}, '{1}')",
                "to_date": "TO_DATE({0}, '{1}')"
            },
            "gaussdb": {
                "current_date": "CURRENT_DATE",
                "current_time": "CURRENT_TIME",
                "current_timestamp": "CURRENT_TIMESTAMP",
                "extract_year": "EXTRACT(YEAR FROM {0})",
                "extract_month": "EXTRACT(MONTH FROM {0})",
                "extract_day": "EXTRACT(DAY FROM {0})",
                "date_add": "({0} + INTERVAL '{1} DAY')",
                "date_diff": "DATE_PART('day', {0} - {1})",
                "date_format": "TO_CHAR({0}, '{1}')",
                "to_date": "TO_DATE({0}, '{1}')"
            },
            "sqlserver": {
                "current_date": "CONVERT(DATE, GETDATE())",
                "current_time": "CONVERT(TIME, GETDATE())",
                "current_timestamp": "GETDATE()",
                "extract_year": "YEAR({0})",
                "extract_month": "MONTH({0})",
                "extract_day": "DAY({0})",
                "date_add": "DATEADD(DAY, {1}, {0})",
                "date_diff": "DATEDIFF(DAY, {1}, {0})",
                "date_format": "FORMAT({0}, '{1}')",
                "to_date": "CONVERT(DATE, {0}, {1})"
            },
            "postgres": {
                "current_date": "CURRENT_DATE",
                "current_time": "CURRENT_TIME",
                "current_timestamp": "CURRENT_TIMESTAMP",
                "extract_year": "EXTRACT(YEAR FROM {0})",
                "extract_month": "EXTRACT(MONTH FROM {0})",
                "extract_day": "EXTRACT(DAY FROM {0})",
                "date_add": "({0} + INTERVAL '{1} DAY')",
                "date_diff": "DATE_PART('day', {0} - {1})",
                "date_format": "TO_CHAR({0}, '{1}')",
                "to_date": "TO_DATE({0}, '{1}')"
            },
            "mysql": {
                "current_date": "CURDATE()",
                "current_time": "CURTIME()",
                "current_timestamp": "NOW()",
                "extract_year": "YEAR({0})",
                "extract_month": "MONTH({0})",
                "extract_day": "DAY({0})",
                "date_add": "DATE_ADD({0}, INTERVAL {1} DAY)",
                "date_diff": "DATEDIFF({0}, {1})",
                "date_format": "DATE_FORMAT({0}, '{1}')",
                "to_date": "STR_TO_DATE({0}, '{1}')"
            }
        }

        # 字符串函数映射
        self.string_functions = {
            "db2": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTR({0}, {1}, {2})",
                "length": "LENGTH({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            },
            "oracle": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTR({0}, {1}, {2})",
                "length": "LENGTH({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            },
            "gaussdb": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTRING({0} FROM {1} FOR {2})",
                "length": "LENGTH({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            },
            "sqlserver": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTRING({0}, {1}, {2})",
                "length": "LEN({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            },
            "postgres": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTRING({0} FROM {1} FOR {2})",
                "length": "LENGTH({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            },
            "mysql": {
                "concat": "CONCAT({0}, {1})",
                "substring": "SUBSTRING({0}, {1}, {2})",
                "length": "LENGTH({0})",
                "upper": "UPPER({0})",
                "lower": "LOWER({0})",
                "trim": "TRIM({0})",
                "replace": "REPLACE({0}, {1}, {2})"
            }
        }

        # 数值函数映射
        self.numeric_functions = {
            "db2": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEILING({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "MOD({0}, {1})"
            },
            "oracle": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEIL({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "MOD({0}, {1})"
            },
            "gaussdb": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEIL({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "MOD({0}, {1})"
            },
            "sqlserver": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEILING({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "({0} % {1})"
            },
            "postgres": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEIL({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "MOD({0}, {1})"
            },
            "mysql": {
                "round": "ROUND({0}, {1})",
                "ceil": "CEILING({0})",
                "floor": "FLOOR({0})",
                "abs": "ABS({0})",
                "mod": "MOD({0}, {1})"
            }
        }

    def _init_syntax_mappings(self):
        """初始化语法映射"""
        # 分页语法映射
        self.pagination_syntax = {
            "db2": "FETCH FIRST {limit} ROWS ONLY",
            "oracle": "FETCH FIRST {limit} ROWS ONLY",
            "gaussdb": "LIMIT {limit}",
            "sqlserver": "TOP {limit}",
            "postgres": "LIMIT {limit}",
            "mysql": "LIMIT {limit}"
        }

        # 偏移语法映射
        self.offset_syntax = {
            "db2": "OFFSET {offset} ROWS",
            "oracle": "OFFSET {offset} ROWS",
            "gaussdb": "OFFSET {offset}",
            "sqlserver": "OFFSET {offset} ROWS",
            "postgres": "OFFSET {offset}",
            "mysql": "OFFSET {offset}"
        }

        # 分页+偏移语法映射
        self.pagination_offset_syntax = {
            "db2": "OFFSET {offset} ROWS FETCH FIRST {limit} ROWS ONLY",
            "oracle": "OFFSET {offset} ROWS FETCH FIRST {limit} ROWS ONLY",
            "gaussdb": "LIMIT {limit} OFFSET {offset}",
            "sqlserver": "OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY",
            "postgres": "LIMIT {limit} OFFSET {offset}",
            "mysql": "LIMIT {offset}, {limit}"
        }

        # 字符串连接运算符
        self.concat_operator = {
            "db2": "||",
            "oracle": "||",
            "gaussdb": "||",
            "sqlserver": "+",
            "postgres": "||",
            "mysql": "CONCAT"
        }

        # 日期格式映射
        self.date_format_mapping = {
            "db2": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH": "HH", "MI": "MI", "SS": "SS"
            },
            "oracle": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH": "HH24", "MI": "MI", "SS": "SS"
            },
            "gaussdb": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH": "HH24", "MI": "MI", "SS": "SS"
            },
            "sqlserver": {
                "YYYY": "yyyy", "MM": "MM", "DD": "dd",
                "HH": "HH", "MI": "mm", "SS": "ss"
            },
            "postgres": {
                "YYYY": "YYYY", "MM": "MM", "DD": "DD",
                "HH": "HH24", "MI": "MI", "SS": "SS"
            },
            "mysql": {
                "YYYY": "%Y", "MM": "%m", "DD": "%d",
                "HH": "%H", "MI": "%i", "SS": "%s"
            }
        }

    def adapt_query(self, query: str, source_to_target: bool = True) -> str:
        """
        适配查询语句

        Args:
            query: 原始查询语句
            source_to_target: 是否从源数据库适配到目标数据库

        Returns:
            str: 适配后的查询语句
        """
        if not query:
            return query

        # 确定源和目标数据库类型
        from_db = self.source_db_type if source_to_target else self.target_db_type
        to_db = self.target_db_type if source_to_target else self.source_db_type

        # 如果源和目标数据库类型相同，无需适配
        if from_db == to_db:
            return query

        # 移除查询末尾的分号
        query = query.strip()
        if query.endswith(';'):
            query = query[:-1]

        # 检查是否是简单查询
        is_simple_query = self._is_simple_query(query)

        if is_simple_query:
            # 对于简单查询，使用正则表达式适配
            # 适配分页语法
            query = self._adapt_pagination(query, from_db, to_db)

            # 适配日期函数
            query = self._adapt_date_functions(query, from_db, to_db)

            # 适配字符串函数
            query = self._adapt_string_functions(query, from_db, to_db)

            # 适配数值函数
            query = self._adapt_numeric_functions(query, from_db, to_db)

            # 适配字符串连接运算符
            query = self._adapt_concat_operator(query, from_db, to_db)
        else:
            # 对于复杂查询，使用SQL转换器
            try:
                from app.core.sql_transformer import SQLTransformer
                transformer = SQLTransformer(from_db, to_db)
                query = transformer.transform(query)
            except ImportError:
                # 如果SQL转换器不可用，回退到正则表达式适配
                logger.warning("SQL转换器不可用，回退到正则表达式适配")

                # 适配分页语法
                query = self._adapt_pagination(query, from_db, to_db)

                # 适配日期函数
                query = self._adapt_date_functions(query, from_db, to_db)

                # 适配字符串函数
                query = self._adapt_string_functions(query, from_db, to_db)

                # 适配数值函数
                query = self._adapt_numeric_functions(query, from_db, to_db)

                # 适配字符串连接运算符
                query = self._adapt_concat_operator(query, from_db, to_db)

        return query

    def _is_simple_query(self, query: str) -> bool:
        """
        判断是否是简单查询

        Args:
            query: 查询语句

        Returns:
            bool: 是否是简单查询
        """
        # 检查是否包含子查询
        if re.search(r'\(\s*SELECT\s+', query, re.IGNORECASE):
            return False

        # 检查是否包含UNION
        if re.search(r'\s+UNION\s+', query, re.IGNORECASE):
            return False

        # 检查是否包含JOIN
        if re.search(r'\s+JOIN\s+', query, re.IGNORECASE):
            return False

        # 检查是否包含GROUP BY
        if re.search(r'\s+GROUP\s+BY\s+', query, re.IGNORECASE):
            return False

        # 检查是否包含HAVING
        if re.search(r'\s+HAVING\s+', query, re.IGNORECASE):
            return False

        # 检查是否包含复杂的WHERE条件
        if re.search(r'\s+WHERE\s+.+?(?:AND|OR|NOT|IN|EXISTS|BETWEEN|LIKE|IS\s+NULL|IS\s+NOT\s+NULL)', query, re.IGNORECASE):
            return False

        # 检查是否包含窗口函数
        if re.search(r'OVER\s*\(', query, re.IGNORECASE):
            return False

        # 检查是否包含CASE表达式
        if re.search(r'CASE\s+WHEN', query, re.IGNORECASE):
            return False

        # 检查是否包含WITH子句
        if re.search(r'WITH\s+\w+\s+AS\s*\(', query, re.IGNORECASE):
            return False

        # 如果以上条件都不满足，则认为是简单查询
        return True

    def _adapt_pagination(self, query: str, from_db: str, to_db: str) -> str:
        """
        适配分页语法

        Args:
            query: 原始查询语句
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的查询语句
        """
        # 适配LIMIT子句
        if from_db != to_db:
            # 处理LIMIT子句
            if " LIMIT " in query.upper():
                # 提取LIMIT值
                limit_match = re.search(r'LIMIT\s+(\d+)', query, re.IGNORECASE)
                if limit_match:
                    limit_value = limit_match.group(1)

                    # 根据目标数据库类型替换LIMIT子句
                    if to_db == "db2" or to_db == "oracle":
                        # 替换为FETCH FIRST n ROWS ONLY
                        query = re.sub(r'LIMIT\s+\d+', f"FETCH FIRST {limit_value} ROWS ONLY", query, flags=re.IGNORECASE)
                    elif to_db == "sqlserver":
                        # 检查是否有ORDER BY子句
                        if " ORDER BY " in query.upper():
                            # 如果有ORDER BY子句，使用OFFSET 0 ROWS FETCH NEXT n ROWS ONLY
                            query = re.sub(r'LIMIT\s+\d+', f"OFFSET 0 ROWS FETCH NEXT {limit_value} ROWS ONLY", query, flags=re.IGNORECASE)
                        else:
                            # 如果没有ORDER BY子句，使用TOP n
                            # 找到SELECT关键字的位置
                            select_pos = query.upper().find("SELECT")
                            if select_pos >= 0:
                                # 插入TOP子句
                                query = query[:select_pos+6] + f" TOP {limit_value} " + query[select_pos+6:]
                                # 移除LIMIT子句
                                query = re.sub(r'LIMIT\s+\d+', "", query, flags=re.IGNORECASE)

            # 处理FETCH FIRST子句
            elif "FETCH FIRST" in query.upper() and "ROWS ONLY" in query.upper():
                # 提取FETCH FIRST值
                fetch_match = re.search(r'FETCH FIRST\s+(\d+)\s+ROWS ONLY', query, re.IGNORECASE)
                if fetch_match:
                    fetch_value = fetch_match.group(1)

                    # 根据目标数据库类型替换FETCH FIRST子句
                    if to_db in ["gaussdb", "postgres", "mysql"]:
                        # 替换为LIMIT n
                        query = re.sub(r'FETCH FIRST\s+\d+\s+ROWS ONLY', f"LIMIT {fetch_value}", query, flags=re.IGNORECASE)
                    elif to_db == "sqlserver":
                        # 检查是否有ORDER BY子句
                        if " ORDER BY " in query.upper():
                            # 如果有ORDER BY子句，使用OFFSET 0 ROWS FETCH NEXT n ROWS ONLY
                            query = re.sub(r'FETCH FIRST\s+\d+\s+ROWS ONLY', f"OFFSET 0 ROWS FETCH NEXT {fetch_value} ROWS ONLY", query, flags=re.IGNORECASE)
                        else:
                            # 如果没有ORDER BY子句，使用TOP n
                            # 找到SELECT关键字的位置
                            select_pos = query.upper().find("SELECT")
                            if select_pos >= 0:
                                # 插入TOP子句
                                query = query[:select_pos+6] + f" TOP {fetch_value} " + query[select_pos+6:]
                                # 移除FETCH FIRST子句
                                query = re.sub(r'FETCH FIRST\s+\d+\s+ROWS ONLY', "", query, flags=re.IGNORECASE)

            # 处理TOP子句
            elif "TOP " in query.upper() and "SELECT " in query.upper():
                # 提取TOP值
                top_match = re.search(r'SELECT\s+TOP\s+(\d+)', query, re.IGNORECASE)
                if top_match:
                    top_value = top_match.group(1)

                    # 根据目标数据库类型替换TOP子句
                    if to_db in ["gaussdb", "postgres", "mysql"]:
                        # 替换为LIMIT n
                        query = re.sub(r'TOP\s+\d+', "", query, flags=re.IGNORECASE)
                        query += f" LIMIT {top_value}"
                    elif to_db in ["db2", "oracle"]:
                        # 替换为FETCH FIRST n ROWS ONLY
                        query = re.sub(r'TOP\s+\d+', "", query, flags=re.IGNORECASE)
                        query += f" FETCH FIRST {top_value} ROWS ONLY"

        return query

    def _adapt_date_functions(self, query: str, from_db: str, to_db: str) -> str:
        """
        适配日期函数

        Args:
            query: 原始查询语句
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的查询语句
        """
        # 获取源和目标数据库的日期函数映射
        from_date_funcs = self.date_functions.get(from_db, {})
        to_date_funcs = self.date_functions.get(to_db, {})

        # 如果源和目标数据库的日期函数映射相同，无需适配
        if from_date_funcs == to_date_funcs:
            return query

        # 1. 适配当前日期函数
        # 例如：DB2的CURRENT DATE -> Oracle的SYSDATE
        if from_db != to_db:
            # 当前日期
            if from_date_funcs.get("current_date") in query:
                query = query.replace(
                    from_date_funcs.get("current_date"),
                    to_date_funcs.get("current_date")
                )

            # 当前时间
            if from_date_funcs.get("current_time") in query:
                query = query.replace(
                    from_date_funcs.get("current_time"),
                    to_date_funcs.get("current_time")
                )

            # 当前时间戳
            if from_date_funcs.get("current_timestamp") in query:
                query = query.replace(
                    from_date_funcs.get("current_timestamp"),
                    to_date_funcs.get("current_timestamp")
                )

        # 2. 适配日期格式化函数
        # 例如：DB2的VARCHAR_FORMAT -> Oracle的TO_CHAR
        if from_db == "db2" and to_db == "oracle":
            # DB2: VARCHAR_FORMAT(date, 'YYYY-MM-DD') -> Oracle: TO_CHAR(date, 'YYYY-MM-DD')
            query = re.sub(
                r'VARCHAR_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
                lambda m: f"TO_CHAR({m.group(1)}, '{self._adapt_date_format(m.group(2), from_db, to_db)}')",
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: TO_CHAR(date, 'YYYY-MM-DD') -> DB2: VARCHAR_FORMAT(date, 'YYYY-MM-DD')
            query = re.sub(
                r'TO_CHAR\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
                lambda m: f"VARCHAR_FORMAT({m.group(1)}, '{self._adapt_date_format(m.group(2), from_db, to_db)}')",
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["db2", "oracle"] and to_db == "mysql":
            # DB2/Oracle: TO_CHAR/VARCHAR_FORMAT(date, 'YYYY-MM-DD') -> MySQL: DATE_FORMAT(date, '%Y-%m-%d')
            pattern = r'(VARCHAR_FORMAT|TO_CHAR)\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)'
            query = re.sub(
                pattern,
                lambda m: f"DATE_FORMAT({m.group(2)}, '{self._adapt_date_format(m.group(3), from_db, to_db)}')",
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "mysql" and to_db in ["db2", "oracle"]:
            # MySQL: DATE_FORMAT(date, '%Y-%m-%d') -> DB2/Oracle: TO_CHAR/VARCHAR_FORMAT(date, 'YYYY-MM-DD')
            to_func = "VARCHAR_FORMAT" if to_db == "db2" else "TO_CHAR"
            query = re.sub(
                r'DATE_FORMAT\s*\(\s*([^,]+)\s*,\s*\'([^\']+)\'\s*\)',
                lambda m: f"{to_func}({m.group(1)}, '{self._adapt_date_format(m.group(2), from_db, to_db)}')",
                query,
                flags=re.IGNORECASE
            )

        # 3. 适配日期提取函数
        # 例如：DB2的YEAR(date) -> Oracle的EXTRACT(YEAR FROM date)
        if from_db == "db2" and to_db in ["oracle", "postgres", "gaussdb"]:
            # DB2: YEAR(date) -> Oracle/Postgres: EXTRACT(YEAR FROM date)
            query = re.sub(
                r'YEAR\s*\(\s*([^)]+)\s*\)',
                r'EXTRACT(YEAR FROM \1)',
                query,
                flags=re.IGNORECASE
            )

            # DB2: MONTH(date) -> Oracle/Postgres: EXTRACT(MONTH FROM date)
            query = re.sub(
                r'MONTH\s*\(\s*([^)]+)\s*\)',
                r'EXTRACT(MONTH FROM \1)',
                query,
                flags=re.IGNORECASE
            )

            # DB2: DAY(date) -> Oracle/Postgres: EXTRACT(DAY FROM date)
            query = re.sub(
                r'DAY\s*\(\s*([^)]+)\s*\)',
                r'EXTRACT(DAY FROM \1)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["oracle", "postgres", "gaussdb"] and to_db == "db2":
            # Oracle/Postgres: EXTRACT(YEAR FROM date) -> DB2: YEAR(date)
            query = re.sub(
                r'EXTRACT\s*\(\s*YEAR\s+FROM\s+([^)]+)\s*\)',
                r'YEAR(\1)',
                query,
                flags=re.IGNORECASE
            )

            # Oracle/Postgres: EXTRACT(MONTH FROM date) -> DB2: MONTH(date)
            query = re.sub(
                r'EXTRACT\s*\(\s*MONTH\s+FROM\s+([^)]+)\s*\)',
                r'MONTH(\1)',
                query,
                flags=re.IGNORECASE
            )

            # Oracle/Postgres: EXTRACT(DAY FROM date) -> DB2: DAY(date)
            query = re.sub(
                r'EXTRACT\s*\(\s*DAY\s+FROM\s+([^)]+)\s*\)',
                r'DAY(\1)',
                query,
                flags=re.IGNORECASE
            )

        # 4. 适配日期加减函数
        # 例如：DB2的(date + 1 DAY) -> Oracle的(date + 1)
        if from_db == "db2" and to_db == "oracle":
            # DB2: (date + 1 DAYS) -> Oracle: (date + 1)
            query = re.sub(
                r'\(\s*([^+]+)\s*\+\s*(\d+)\s*DAYS\s*\)',
                r'(\1 + \2)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: (date + 1) -> DB2: (date + 1 DAYS)
            query = re.sub(
                r'\(\s*([^+]+)\s*\+\s*(\d+)\s*\)',
                r'(\1 + \2 DAYS)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["db2", "oracle"] and to_db in ["postgres", "gaussdb"]:
            # DB2/Oracle: (date + 1 DAYS)/(date + 1) -> Postgres: (date + INTERVAL '1 DAY')
            pattern = r'\(\s*([^+]+)\s*\+\s*(\d+)(?:\s*DAYS)?\s*\)'
            query = re.sub(
                pattern,
                r'(\1 + INTERVAL \'\2 DAY\')',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["postgres", "gaussdb"] and to_db == "db2":
            # Postgres: (date + INTERVAL '1 DAY') -> DB2: (date + 1 DAYS)
            query = re.sub(
                r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
                r'(\1 + \2 DAYS)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["postgres", "gaussdb"] and to_db == "oracle":
            # Postgres: (date + INTERVAL '1 DAY') -> Oracle: (date + 1)
            query = re.sub(
                r'\(\s*([^+]+)\s*\+\s*INTERVAL\s*\'(\d+)\s*DAY\'\s*\)',
                r'(\1 + \2)',
                query,
                flags=re.IGNORECASE
            )

        # 5. 适配日期差异函数
        # 例如：DB2的DAYS(date1) - DAYS(date2) -> Oracle的(date1 - date2)
        if from_db == "db2" and to_db == "oracle":
            # DB2: DAYS(date1) - DAYS(date2) -> Oracle: (date1 - date2)
            query = re.sub(
                r'DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)',
                r'(\1 - \2)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: (date1 - date2) -> DB2: DAYS(date1) - DAYS(date2)
            query = re.sub(
                r'\(\s*([^-]+)\s*-\s*([^)]+)\s*\)',
                r'DAYS(\1) - DAYS(\2)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["db2", "oracle"] and to_db in ["postgres", "gaussdb"]:
            # DB2/Oracle: DAYS(date1) - DAYS(date2)/(date1 - date2) -> Postgres: DATE_PART('day', date1 - date2)
            pattern = r'(?:DAYS\s*\(\s*([^)]+)\s*\)\s*-\s*DAYS\s*\(\s*([^)]+)\s*\)|\(\s*([^-]+)\s*-\s*([^)]+)\s*\))'

            def replace_date_diff(match):
                if match.group(1) and match.group(2):  # DB2 format
                    return f"DATE_PART('day', {match.group(1)} - {match.group(2)})"
                else:  # Oracle format
                    return f"DATE_PART('day', {match.group(3)} - {match.group(4)})"

            query = re.sub(pattern, replace_date_diff, query, flags=re.IGNORECASE)

        # 6. 适配日期转换函数
        # 例如：DB2的DATE(string) -> Oracle的TO_DATE(string, 'YYYY-MM-DD')
        if from_db == "db2" and to_db == "oracle":
            # DB2: DATE(string) -> Oracle: TO_DATE(string, 'YYYY-MM-DD')
            query = re.sub(
                r'DATE\s*\(\s*([^)]+)\s*\)',
                r"TO_DATE(\1, 'YYYY-MM-DD')",
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: TO_DATE(string, 'YYYY-MM-DD') -> DB2: DATE(string)
            query = re.sub(
                r'TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\)',
                r'DATE(\1)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["db2", "oracle"] and to_db == "mysql":
            # DB2: DATE(string) -> MySQL: STR_TO_DATE(string, '%Y-%m-%d')
            # Oracle: TO_DATE(string, 'YYYY-MM-DD') -> MySQL: STR_TO_DATE(string, '%Y-%m-%d')
            pattern = r'(?:DATE\s*\(\s*([^)]+)\s*\)|TO_DATE\s*\(\s*([^,]+)\s*,\s*\'[^\']+\'\s*\))'

            def replace_to_date(match):
                if match.group(1):  # DB2 format
                    return f"STR_TO_DATE({match.group(1)}, '%Y-%m-%d')"
                else:  # Oracle format
                    return f"STR_TO_DATE({match.group(2)}, '%Y-%m-%d')"

            query = re.sub(pattern, replace_to_date, query, flags=re.IGNORECASE)

        return query

    def _adapt_string_functions(self, query: str, from_db: str, to_db: str) -> str:
        """
        适配字符串函数

        Args:
            query: 原始查询语句
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的查询语句
        """
        # 获取源和目标数据库的字符串函数映射
        from_string_funcs = self.string_functions.get(from_db, {})
        to_string_funcs = self.string_functions.get(to_db, {})

        # 如果源和目标数据库的字符串函数映射相同，无需适配
        if from_string_funcs == to_string_funcs:
            return query

        # 1. 适配字符串连接函数
        # 例如：DB2/Oracle的CONCAT(str1, str2) -> MySQL的CONCAT(str1, str2)
        # 注意：MySQL的CONCAT可以接受多个参数，而其他数据库的CONCAT只接受两个参数
        if from_db in ["db2", "oracle", "postgres", "gaussdb"] and to_db == "mysql":
            # 递归处理嵌套的CONCAT调用
            # 例如：CONCAT(CONCAT(a, b), c) -> CONCAT(a, b, c)
            def expand_concat(match):
                arg1 = match.group(1).strip()
                arg2 = match.group(2).strip()

                # 检查arg1是否是CONCAT调用
                concat_match1 = re.search(r'CONCAT\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)', arg1, re.IGNORECASE)
                if concat_match1:
                    inner_arg1 = concat_match1.group(1).strip()
                    inner_arg2 = concat_match1.group(2).strip()
                    arg1 = f"{inner_arg1}, {inner_arg2}"

                # 检查arg2是否是CONCAT调用
                concat_match2 = re.search(r'CONCAT\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)', arg2, re.IGNORECASE)
                if concat_match2:
                    inner_arg1 = concat_match2.group(1).strip()
                    inner_arg2 = concat_match2.group(2).strip()
                    arg2 = f"{inner_arg1}, {inner_arg2}"

                return f"CONCAT({arg1}, {arg2})"

            # 递归处理所有CONCAT调用
            prev_query = ""
            while prev_query != query:
                prev_query = query
                query = re.sub(
                    r'CONCAT\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
                    expand_concat,
                    query,
                    flags=re.IGNORECASE
                )

        # 2. 适配子字符串函数
        # 例如：DB2/Oracle的SUBSTR(str, start, length) -> Postgres的SUBSTRING(str FROM start FOR length)
        if from_db in ["db2", "oracle", "sqlserver", "mysql"] and to_db in ["postgres", "gaussdb"]:
            # DB2/Oracle/SQL Server/MySQL: SUBSTR/SUBSTRING(str, start, length) -> Postgres: SUBSTRING(str FROM start FOR length)
            pattern = r'(?:SUBSTR|SUBSTRING)\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)'
            query = re.sub(
                pattern,
                r'SUBSTRING(\1 FROM \2 FOR \3)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["postgres", "gaussdb"] and to_db in ["db2", "oracle", "sqlserver", "mysql"]:
            # Postgres: SUBSTRING(str FROM start FOR length) -> DB2/Oracle/SQL Server/MySQL: SUBSTRING(str, start, length)
            query = re.sub(
                r'SUBSTRING\s*\(\s*([^F]+)\s*FROM\s*([^F]+)\s*FOR\s*([^)]+)\s*\)',
                r'SUBSTRING(\1, \2, \3)',
                query,
                flags=re.IGNORECASE
            )

        # 3. 适配字符串长度函数
        # 例如：DB2/Oracle/Postgres/MySQL的LENGTH(str) -> SQL Server的LEN(str)
        if from_db in ["db2", "oracle", "postgres", "gaussdb", "mysql"] and to_db == "sqlserver":
            # DB2/Oracle/Postgres/MySQL: LENGTH(str) -> SQL Server: LEN(str)
            query = re.sub(
                r'LENGTH\s*\(\s*([^)]+)\s*\)',
                r'LEN(\1)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "sqlserver" and to_db in ["db2", "oracle", "postgres", "gaussdb", "mysql"]:
            # SQL Server: LEN(str) -> DB2/Oracle/Postgres/MySQL: LENGTH(str)
            query = re.sub(
                r'LEN\s*\(\s*([^)]+)\s*\)',
                r'LENGTH(\1)',
                query,
                flags=re.IGNORECASE
            )

        # 4. 适配字符串替换函数
        # 所有数据库的REPLACE函数参数相同，无需适配

        # 5. 适配字符串大小写转换函数
        # 所有数据库的UPPER和LOWER函数参数相同，无需适配

        # 6. 适配字符串修剪函数
        # 所有数据库的TRIM函数参数相同，无需适配

        return query

    def _adapt_numeric_functions(self, query: str, from_db: str, to_db: str) -> str:
        """
        适配数值函数

        Args:
            query: 原始查询语句
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的查询语句
        """
        # 获取源和目标数据库的数值函数映射
        from_numeric_funcs = self.numeric_functions.get(from_db, {})
        to_numeric_funcs = self.numeric_functions.get(to_db, {})

        # 如果源和目标数据库的数值函数映射相同，无需适配
        if from_numeric_funcs == to_numeric_funcs:
            return query

        # 1. 适配四舍五入函数
        # 所有数据库的ROUND函数参数相同，无需适配

        # 2. 适配向上取整函数
        # 例如：DB2/SQL Server的CEILING(num) -> Oracle/Postgres/MySQL的CEIL(num)
        if from_db in ["db2", "sqlserver"] and to_db in ["oracle", "postgres", "gaussdb", "mysql"]:
            # DB2/SQL Server: CEILING(num) -> Oracle/Postgres/MySQL: CEIL(num)
            query = re.sub(
                r'CEILING\s*\(\s*([^)]+)\s*\)',
                r'CEIL(\1)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db in ["oracle", "postgres", "gaussdb", "mysql"] and to_db in ["db2", "sqlserver"]:
            # Oracle/Postgres/MySQL: CEIL(num) -> DB2/SQL Server: CEILING(num)
            query = re.sub(
                r'CEIL\s*\(\s*([^)]+)\s*\)',
                r'CEILING(\1)',
                query,
                flags=re.IGNORECASE
            )

        # 3. 适配向下取整函数
        # 所有数据库的FLOOR函数参数相同，无需适配

        # 4. 适配绝对值函数
        # 所有数据库的ABS函数参数相同，无需适配

        # 5. 适配取模函数
        # 例如：DB2/Oracle/Postgres/MySQL的MOD(num1, num2) -> SQL Server的(num1 % num2)
        if from_db in ["db2", "oracle", "postgres", "gaussdb", "mysql"] and to_db == "sqlserver":
            # DB2/Oracle/Postgres/MySQL: MOD(num1, num2) -> SQL Server: (num1 % num2)
            query = re.sub(
                r'MOD\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
                r'(\1 % \2)',
                query,
                flags=re.IGNORECASE
            )
        elif from_db == "sqlserver" and to_db in ["db2", "oracle", "postgres", "gaussdb", "mysql"]:
            # SQL Server: (num1 % num2) -> DB2/Oracle/Postgres/MySQL: MOD(num1, num2)
            query = re.sub(
                r'\(\s*([^%]+)\s*%\s*([^)]+)\s*\)',
                r'MOD(\1, \2)',
                query,
                flags=re.IGNORECASE
            )

        return query

    def _adapt_date_format(self, format_str: str, from_db: str, to_db: str) -> str:
        """
        适配日期格式字符串

        Args:
            format_str: 原始日期格式字符串
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的日期格式字符串
        """
        # 获取源和目标数据库的日期格式映射
        from_format = self.date_format_mapping.get(from_db, {})
        to_format = self.date_format_mapping.get(to_db, {})

        # 如果源和目标数据库的日期格式映射相同，无需适配
        if from_format == to_format:
            return format_str

        # 特殊处理MySQL格式到其他数据库格式
        if from_db == "mysql" and to_db in ["db2", "oracle", "postgres", "gaussdb", "sqlserver"]:
            # MySQL: %Y-%m-%d %H:%i:%s -> DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS
            format_str = format_str.replace("%Y", "YYYY")
            format_str = format_str.replace("%m", "MM")
            format_str = format_str.replace("%d", "DD")
            format_str = format_str.replace("%H", "HH24")
            format_str = format_str.replace("%i", "MI")
            format_str = format_str.replace("%s", "SS")

            # SQL Server特殊处理
            if to_db == "sqlserver":
                format_str = format_str.replace("HH24", "HH")
                format_str = format_str.replace("MI", "mm")
                format_str = format_str.replace("SS", "ss")

            return format_str

        # 特殊处理其他数据库格式到MySQL格式
        elif to_db == "mysql" and from_db in ["db2", "oracle", "postgres", "gaussdb", "sqlserver"]:
            # DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS -> MySQL: %Y-%m-%d %H:%i:%s
            format_str = format_str.replace("YYYY", "%Y")
            format_str = format_str.replace("MM", "%m")
            format_str = format_str.replace("DD", "%d")
            format_str = format_str.replace("HH24", "%H")
            format_str = format_str.replace("HH", "%H")
            format_str = format_str.replace("MI", "%i")
            format_str = format_str.replace("SS", "%s")

            # SQL Server特殊处理
            if from_db == "sqlserver":
                format_str = format_str.replace("yyyy", "%Y")
                format_str = format_str.replace("mm", "%i")
                format_str = format_str.replace("ss", "%s")

            return format_str

        # 处理SQL Server和其他数据库之间的转换
        elif from_db == "sqlserver" and to_db in ["db2", "oracle", "postgres", "gaussdb"]:
            # SQL Server: yyyy-MM-dd HH:mm:ss -> DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS
            format_str = format_str.replace("yyyy", "YYYY")
            format_str = format_str.replace("MM", "MM")
            format_str = format_str.replace("dd", "DD")
            format_str = format_str.replace("HH", "HH24")
            format_str = format_str.replace("mm", "MI")
            format_str = format_str.replace("ss", "SS")

            return format_str
        elif to_db == "sqlserver" and from_db in ["db2", "oracle", "postgres", "gaussdb"]:
            # DB2/Oracle/Postgres: YYYY-MM-DD HH24:MI:SS -> SQL Server: yyyy-MM-dd HH:mm:ss
            format_str = format_str.replace("YYYY", "yyyy")
            format_str = format_str.replace("MM", "MM")
            format_str = format_str.replace("DD", "dd")
            format_str = format_str.replace("HH24", "HH")
            format_str = format_str.replace("MI", "mm")
            format_str = format_str.replace("SS", "ss")

            return format_str

        # 处理Oracle和DB2之间的转换
        elif from_db == "oracle" and to_db == "db2":
            # Oracle: HH24 -> DB2: HH
            format_str = format_str.replace("HH24", "HH")

            return format_str
        elif from_db == "db2" and to_db == "oracle":
            # DB2: HH -> Oracle: HH24
            format_str = format_str.replace("HH", "HH24")

            return format_str

        # 默认情况下，不做转换
        return format_str

    def _adapt_concat_operator(self, query: str, from_db: str, to_db: str) -> str:
        """
        适配字符串连接运算符

        Args:
            query: 原始查询语句
            from_db: 源数据库类型
            to_db: 目标数据库类型

        Returns:
            str: 适配后的查询语句
        """
        # 获取源和目标数据库的字符串连接运算符
        from_concat = self.concat_operator.get(from_db)
        to_concat = self.concat_operator.get(to_db)

        # 如果源和目标数据库的字符串连接运算符不同
        if from_concat != to_concat:
            # 如果源数据库使用||运算符，目标数据库使用+运算符
            if from_concat == "||" and to_concat == "+":
                # 使用正则表达式替换字符串连接运算符，避免替换其他地方的||
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*\|\|\s*([\'"])([^\'"]*)(\4)',
                    r'\1\2\3 + \4\5\6',
                    query
                )
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*\|\|\s*([^\'"\s]+)',
                    r'\1\2\3 + \4',
                    query
                )
                query = re.sub(
                    r'([^\'"\s]+)\s*\|\|\s*([\'"])([^\'"]*)(\2)',
                    r'\1 + \2\3\4',
                    query
                )
                query = re.sub(
                    r'([^\'"\s]+)\s*\|\|\s*([^\'"\s]+)',
                    r'\1 + \2',
                    query
                )
            # 如果源数据库使用+运算符，目标数据库使用||运算符
            elif from_concat == "+" and to_concat == "||":
                # 使用正则表达式替换字符串连接运算符，避免替换数值加法运算符
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*\+\s*([\'"])([^\'"]*)(\4)',
                    r'\1\2\3 || \4\5\6',
                    query
                )
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*\+\s*([^\'"\s]+)',
                    r'\1\2\3 || \4',
                    query
                )
                query = re.sub(
                    r'([^\'"\s]+)\s*\+\s*([\'"])([^\'"]*)(\2)',
                    r'\1 || \2\3\4',
                    query
                )
            # 如果源数据库使用||或+运算符，目标数据库使用CONCAT函数
            elif to_concat == "CONCAT" and from_concat in ["||", "+"]:
                # 使用正则表达式替换字符串连接运算符为CONCAT函数
                concat_op = r"\|\|" if from_concat == "||" else r"\+"

                # 处理两个字符串字面量的连接
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*' + concat_op + r'\s*([\'"])([^\'"]*)(\4)',
                    r'CONCAT(\1\2\3, \4\5\6)',
                    query
                )

                # 处理字符串字面量和标识符的连接
                query = re.sub(
                    r'([\'"])([^\'"]*)(\1)\s*' + concat_op + r'\s*([^\'"\s]+)',
                    r'CONCAT(\1\2\3, \4)',
                    query
                )

                # 处理标识符和字符串字面量的连接
                query = re.sub(
                    r'([^\'"\s]+)\s*' + concat_op + r'\s*([\'"])([^\'"]*)(\2)',
                    r'CONCAT(\1, \2\3\4)',
                    query
                )

                # 处理两个标识符的连接
                query = re.sub(
                    r'([^\'"\s]+)\s*' + concat_op + r'\s*([^\'"\s]+)',
                    r'CONCAT(\1, \2)',
                    query
                )

            # 如果源数据库使用CONCAT函数，目标数据库使用||或+运算符
            elif from_concat == "CONCAT" and to_concat in ["||", "+"]:
                # 使用正则表达式替换CONCAT函数为字符串连接运算符
                concat_op = " || " if to_concat == "||" else " + "

                # 处理两个参数的CONCAT函数
                query = re.sub(
                    r'CONCAT\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)',
                    r'\1' + concat_op + r'\2',
                    query,
                    flags=re.IGNORECASE
                )

                # 处理多个参数的CONCAT函数（MySQL特有）
                # 这里需要更复杂的解析，简单实现可能会有问题
                # 暂时不实现

        return query
