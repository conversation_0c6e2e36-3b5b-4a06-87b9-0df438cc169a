"""
Legacy API路由器

提供与原版本API兼容的路由配置
"""
from fastapi import APIRouter
from .endpoints import router as legacy_endpoints

# 创建Legacy API路由器
legacy_router = APIRouter()

# 包含所有Legacy端点
legacy_router.include_router(
    legacy_endpoints,
    prefix="",  # 不添加前缀，保持原版本路径
    tags=["Legacy API - 兼容性支持"]
)

# 添加API信息端点
@legacy_router.get("/")
async def legacy_api_info():
    """Legacy API信息"""
    return {
        "name": "Legacy API Compatibility Layer",
        "version": "1.0.0",
        "description": "提供与原版本API的兼容性支持",
        "supported_endpoints": [
            "POST /api/database/connection/test/",
            "POST /api/tasks/compare",
            "GET /api/tasks/{task_id}/status",
            "GET /api/tasks/{task_id}/result",
            "POST /api/compare_data",
            "DELETE /api/tasks/{task_id}"
        ],
        "migration_guide": "/docs/migration-guide",
        "new_api_docs": "/docs"
    }


@legacy_router.get("/health")
async def legacy_health_check():
    """Legacy API健康检查"""
    return {
        "status": "healthy",
        "api_version": "legacy-v1",
        "compatibility_layer": "active",
        "message": "Legacy API兼容层运行正常"
    }
