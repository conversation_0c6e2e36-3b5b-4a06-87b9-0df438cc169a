import os
import sys
import time
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, os.path.dirname(PROJECT_DIR))
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field, model_validator
from app.models.task import DatabaseConfig
from app.core.db_utils import test_connection
from app.models.logger import logger

router = APIRouter()

class ConnectionTestRequest(BaseModel):
    """数据库连接测试请求模型"""
    config: DatabaseConfig = Field(..., description="数据库连接配置")
    timeout: Optional[int] = Field(None, description="连接超时时间（秒）")
    
    # 添加模型验证器，支持直接的数据库连接参数
    @model_validator(mode='before')
    @classmethod
    def validate_input(cls, data):
        """支持直接接受数据库连接参数，而不需要嵌套在config字段中"""
        if isinstance(data, dict) and "config" not in data and "type" in data:
            # 检测到扁平格式的请求，构建嵌套格式
            db_fields = [
                "type", "host", "port", "username", "password", "database", 
                "path", "uri", "ssl", "options", "parameters"
            ]
            config_data = {k: v for k, v in data.items() if k in db_fields}
            timeout = data.get("timeout")
            
            # 创建嵌套格式的数据
            return {"config": config_data, "timeout": timeout}
        return data


class ConnectionTestResponse(BaseModel):
    """数据库连接测试响应模型"""
    success: bool = Field(..., description="连接测试是否成功")
    message: str = Field(..., description="连接测试结果消息")
    elapsed_time: float = Field(..., description="测试耗时（秒）")
    version: Optional[str] = Field(None, description="数据库版本")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据信息")


@router.post("/test/", response_model=ConnectionTestResponse, 
             summary="测试数据库连接",
             description="测试指定数据库连接配置是否能成功连接到目标数据库")
async def test_db_connection(
    request: ConnectionTestRequest
) -> Dict[str, Any]:
    """
    测试数据库连接

    根据提供的数据库配置参数测试数据库连接，返回测试结果、数据库版本和元数据信息。
    
    Args:
        request: 包含数据库配置和超时设置的请求
    
    Returns:
        包含连接测试结果的响应对象
    
    Raises:
        HTTPException: 连接测试过程发生异常时抛出
    """
    logger.info(f"收到数据库连接测试请求 - 类型: {request.config.type}, 主机: {request.config.host}")
    
    start_time = time.time()
    
    try:
        # 调用连接测试工具函数
        test_result = await test_connection(request.config, request.timeout)
        
        elapsed_time = time.time() - start_time
        
        # 构建响应结果
        response = {
            "success": test_result["success"],
            "message": test_result["message"],
            "elapsed_time": elapsed_time,
            "version": test_result.get("version"),
            "metadata": test_result.get("metadata")
        }
        
        if test_result["success"]:
            logger.info(f"数据库连接测试成功 - 类型: {request.config.type}, "
                       f"主机: {request.config.host}, 耗时: {elapsed_time:.2f}秒, "
                       f"版本: {test_result.get('version', '未知')}")
        else:
            logger.warning(f"数据库连接测试失败 - 类型: {request.config.type}, "
                          f"主机: {request.config.host}, 耗时: {elapsed_time:.2f}秒, "
                          f"原因: {test_result['message']}")
        
        return response
        
    except Exception as e:
        logger.exception(f"数据库连接测试发生异常 - 类型: {request.config.type}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"连接测试异常: {str(e)}"
        ) 