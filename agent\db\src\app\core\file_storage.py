"""
文件存储管理器模块

负责管理比对结果的文件存储，包括：
- 创建任务工作空间
- 差异数据分离存储
- 文件锁机制
- 目录结构管理
"""

import json
import asyncio
import aiofiles
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from contextlib import asynccontextmanager
import logging

from app.models.logger import logger

class ComparisonFileStorage:
    """比对结果文件存储管理器"""

    def __init__(self, base_path: str = "comparison_results"):
        """
        初始化文件存储管理器

        Args:
            base_path: 基础存储路径
        """
        self.base_path = Path(base_path)
        self.file_locks = {}  # 文件锁字典
        self._ensure_base_directory()

    def _ensure_base_directory(self):
        """确保基础目录存在"""
        try:
            self.base_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"文件存储基础目录已创建: {self.base_path}")
        except Exception as e:
            logger.error(f"创建基础目录失败: {str(e)}")
            raise

    def create_task_workspace(self, task_id: str) -> Path:
        """
        为任务创建独立的工作空间

        Args:
            task_id: 任务ID

        Returns:
            Path: 工作空间路径
        """
        try:
            workspace = self.base_path / task_id

            # 创建目录结构
            directories = ['metadata', 'data', 'indexes', 'logs', 'temp']
            for dir_name in directories:
                dir_path = workspace / dir_name
                dir_path.mkdir(parents=True, exist_ok=True)

            # 设置工作空间权限
            self._set_workspace_permissions(workspace)

            logger.info(f"任务工作空间已创建: {workspace}")
            return workspace

        except Exception as e:
            logger.error(f"创建任务工作空间失败 - 任务ID: {task_id}, 错误: {str(e)}")
            raise

    def _set_workspace_permissions(self, workspace_path: Path):
        """设置工作空间权限"""
        try:
            # 设置目录权限为755
            os.chmod(workspace_path, 0o755)
            for subdir in workspace_path.iterdir():
                if subdir.is_dir():
                    os.chmod(subdir, 0o755)
        except Exception as e:
            logger.warning(f"设置工作空间权限失败: {str(e)}")

    @asynccontextmanager
    async def _acquire_file_lock(self, file_path: Path):
        """获取文件锁"""
        lock_key = str(file_path)

        # 如果锁不存在，创建新锁
        if lock_key not in self.file_locks:
            self.file_locks[lock_key] = asyncio.Lock()

        lock = self.file_locks[lock_key]

        try:
            await lock.acquire()
            yield
        finally:
            lock.release()

    async def write_differences(self, task_id: str, diff_type: str,
                              records: List[Dict]) -> str:
        """
        写入差异记录到文件

        Args:
            task_id: 任务ID
            diff_type: 差异类型 (source_only, target_only, differences)
            records: 记录列表

        Returns:
            str: 文件路径
        """
        try:
            workspace = self.base_path / task_id / 'data'
            file_path = workspace / f"{diff_type}.jsonl"

            # 使用文件锁确保并发安全
            async with self._acquire_file_lock(file_path):
                async with aiofiles.open(file_path, 'a', encoding='utf-8') as f:
                    for record in records:
                        # 添加时间戳
                        record['_timestamp'] = datetime.now().isoformat()
                        line = json.dumps(record, ensure_ascii=False) + '\n'
                        await f.write(line)

            logger.debug(f"写入 {len(records)} 条 {diff_type} 记录到文件: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"写入差异记录失败 - 任务ID: {task_id}, 类型: {diff_type}, 错误: {str(e)}")
            raise

    async def write_metadata(self, task_id: str, metadata_type: str,
                           data: Dict[str, Any]) -> str:
        """
        写入元数据文件

        Args:
            task_id: 任务ID
            metadata_type: 元数据类型 (task_info, comparison_config, key_config, etc.)
            data: 元数据内容

        Returns:
            str: 文件路径
        """
        try:
            workspace = self.base_path / task_id / 'metadata'
            file_path = workspace / f"{metadata_type}.json"

            # 添加元数据时间戳
            data['_created_at'] = datetime.now().isoformat()
            data['_metadata_type'] = metadata_type

            async with self._acquire_file_lock(file_path):
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    content = json.dumps(data, ensure_ascii=False, indent=2)
                    await f.write(content)

            logger.debug(f"写入元数据文件: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"写入元数据失败 - 任务ID: {task_id}, 类型: {metadata_type}, 错误: {str(e)}")
            raise

    async def write_summary(self, task_id: str, summary_data: Dict[str, Any]) -> str:
        """
        写入统计摘要文件

        Args:
            task_id: 任务ID
            summary_data: 摘要数据

        Returns:
            str: 文件路径
        """
        try:
            workspace = self.base_path / task_id / 'data'
            file_path = workspace / 'summary.json'

            # 添加摘要元信息
            summary_data.update({
                '_generated_at': datetime.now().isoformat(),
                '_task_id': task_id,
                '_version': '1.0'
            })

            async with self._acquire_file_lock(file_path):
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    content = json.dumps(summary_data, ensure_ascii=False, indent=2)
                    await f.write(content)

            logger.info(f"写入统计摘要文件: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"写入统计摘要失败 - 任务ID: {task_id}, 错误: {str(e)}")
            raise

    async def read_file(self, task_id: str, file_type: str,
                       subdir: str = 'data') -> Optional[List[Dict]]:
        """
        读取文件内容

        Args:
            task_id: 任务ID
            file_type: 文件类型
            subdir: 子目录名

        Returns:
            List[Dict]: 文件内容列表
        """
        try:
            workspace = self.base_path / task_id / subdir

            if file_type.endswith('.json'):
                file_path = workspace / file_type
                if not file_path.exists():
                    return None

                async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    return json.loads(content)
            else:
                # JSONL文件
                file_path = workspace / f"{file_type}.jsonl"
                if not file_path.exists():
                    return None

                records = []
                async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                    async for line in f:
                        line = line.strip()
                        if line:
                            try:
                                records.append(json.loads(line))
                            except json.JSONDecodeError:
                                logger.warning(f"跳过无效JSON行: {line[:100]}")
                                continue

                return records

        except Exception as e:
            logger.error(f"读取文件失败 - 任务ID: {task_id}, 文件类型: {file_type}, 错误: {str(e)}")
            return None

    def get_workspace_info(self, task_id: str) -> Dict[str, Any]:
        """
        获取工作空间信息

        Args:
            task_id: 任务ID

        Returns:
            Dict: 工作空间信息
        """
        try:
            workspace = self.base_path / task_id

            if not workspace.exists():
                return {'exists': False}

            info = {
                'exists': True,
                'workspace_path': str(workspace),
                'created_time': datetime.fromtimestamp(workspace.stat().st_ctime).isoformat(),
                'directories': {},
                'files': {}
            }

            # 统计各目录下的文件
            for subdir in ['metadata', 'data', 'indexes', 'logs']:
                subdir_path = workspace / subdir
                if subdir_path.exists():
                    files = list(subdir_path.glob('*'))
                    info['directories'][subdir] = {
                        'file_count': len(files),
                        'files': [f.name for f in files if f.is_file()]
                    }

            return info

        except Exception as e:
            logger.error(f"获取工作空间信息失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return {'exists': False, 'error': str(e)}

    async def cleanup_workspace(self, task_id: str, retention_days: int = 7) -> bool:
        """
        清理任务工作空间

        Args:
            task_id: 任务ID
            retention_days: 保留天数

        Returns:
            bool: 是否成功清理
        """
        try:
            workspace = self.base_path / task_id

            if not workspace.exists():
                return True

            # 检查是否超过保留期限
            workspace_age = datetime.now().timestamp() - workspace.stat().st_ctime
            if workspace_age < retention_days * 24 * 3600:
                logger.info(f"工作空间未到清理时间: {task_id}")
                return False

            # 压缩重要文件到归档目录
            await self._archive_important_files(task_id)

            # 删除临时文件
            temp_dir = workspace / 'temp'
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)

            logger.info(f"工作空间清理完成: {task_id}")
            return True

        except Exception as e:
            logger.error(f"清理工作空间失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return False

    async def _archive_important_files(self, task_id: str):
        """归档重要文件"""
        try:
            workspace = self.base_path / task_id
            archive_dir = self.base_path / 'archive' / task_id
            archive_dir.mkdir(parents=True, exist_ok=True)

            # 归档摘要文件和元数据
            important_files = [
                'data/summary.json',
                'metadata/task_info.json',
                'metadata/comparison_config.json'
            ]

            for file_path in important_files:
                src_file = workspace / file_path
                if src_file.exists():
                    dst_file = archive_dir / file_path
                    dst_file.parent.mkdir(parents=True, exist_ok=True)

                    import shutil
                    shutil.copy2(src_file, dst_file)

            logger.info(f"重要文件已归档: {task_id}")

        except Exception as e:
            logger.error(f"归档文件失败 - 任务ID: {task_id}, 错误: {str(e)}")

    def get_file_path(self, task_id: str, file_type: str, subdir: str = 'data') -> Path:
        """
        获取文件路径

        Args:
            task_id: 任务ID
            file_type: 文件类型
            subdir: 子目录

        Returns:
            Path: 文件路径
        """
        workspace = self.base_path / task_id / subdir
        if file_type.endswith('.json'):
            return workspace / file_type
        else:
            return workspace / f"{file_type}.jsonl"

    def file_exists(self, task_id: str, file_type: str, subdir: str = 'data') -> bool:
        """
        检查文件是否存在

        Args:
            task_id: 任务ID
            file_type: 文件类型
            subdir: 子目录

        Returns:
            bool: 文件是否存在
        """
        file_path = self.get_file_path(task_id, file_type, subdir)
        return file_path.exists()

    async def get_file_stats(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务文件统计信息

        Args:
            task_id: 任务ID

        Returns:
            Dict: 文件统计信息
        """
        try:
            workspace = self.base_path / task_id
            if not workspace.exists():
                return {'exists': False}

            stats = {
                'exists': True,
                'task_id': task_id,
                'workspace_path': str(workspace),
                'file_counts': {},
                'total_size': 0
            }

            # 统计各类型文件
            data_dir = workspace / 'data'
            if data_dir.exists():
                for file_type in ['source_only', 'target_only', 'differences']:
                    file_path = data_dir / f"{file_type}.jsonl"
                    if file_path.exists():
                        # 计算文件大小
                        file_size = file_path.stat().st_size
                        stats['total_size'] += file_size

                        # 计算行数
                        line_count = 0
                        with open(file_path, 'r', encoding='utf-8') as f:
                            for _ in f:
                                line_count += 1

                        stats['file_counts'][file_type] = {
                            'records': line_count,
                            'size_bytes': file_size,
                            'size_mb': round(file_size / 1024 / 1024, 2)
                        }

            return stats

        except Exception as e:
            logger.error(f"获取文件统计失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return {'exists': False, 'error': str(e)}
