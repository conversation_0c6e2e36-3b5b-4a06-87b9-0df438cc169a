#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比对引擎模块初始化
确保所有引擎类型被正确注册到工厂中
"""

# 导入基础类
from .base_engine import (
    BaseComparisonEngine,
    ComparisonEngineFactory,
    ComparisonTask,
    ComparisonResult,
    ComparisonType,
    DifferenceType,
    DatabaseConnection
)

# 导入并注册具体引擎实现
# 这些导入会触发引擎的自动注册
from .content_engine import ContentComparisonEngine
from .structure_engine import StructureComparisonEngine

# 验证引擎注册
def verify_engine_registration():
    """验证引擎注册状态"""
    supported_types = ComparisonEngineFactory.get_supported_types()
    print(f"已注册的比对引擎类型: {[t.value for t in supported_types]}")
    
    # 确保关键引擎类型已注册
    required_types = [ComparisonType.CONTENT, ComparisonType.STRUCTURE]
    for comp_type in required_types:
        if comp_type not in supported_types:
            print(f"警告: {comp_type.value} 引擎未注册")
        else:
            print(f"✓ {comp_type.value} 引擎已注册")

# 在模块加载时验证注册状态（仅在调试模式下）
import os
if os.getenv('DEBUG_MODE', 'false').lower() == 'true':
    verify_engine_registration()

# 导出主要类和函数
__all__ = [
    'BaseComparisonEngine',
    'ComparisonEngineFactory', 
    'ComparisonTask',
    'ComparisonResult',
    'ComparisonType',
    'DifferenceType',
    'DatabaseConnection',
    'ContentComparisonEngine',
    'StructureComparisonEngine',
    'verify_engine_registration'
]
