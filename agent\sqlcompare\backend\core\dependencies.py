#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖注入容器 - 统一管理服务实例，避免重复创建
"""

import os
import sys
import logging
from functools import lru_cache
from typing import Optional
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入主项目服务
from services.sqlalchemy_service import SQLAlchemyComparisonService

logger = logging.getLogger(__name__)


def get_database_url() -> str:
    """
    获取数据库URL - 从环境变量或配置文件读取，避免硬编码
    """
    # 优先从环境变量读取
    database_url = os.getenv('SQLCOMPARE_DATABASE_URL')
    
    if database_url:
        logger.info("使用环境变量中的数据库URL")
        return database_url
    
    # 从配置文件读取
    try:
        from backend.core.config import get_settings
        settings = get_settings()
        database_url = settings.database_url
        logger.info("使用配置文件中的数据库URL")
        return database_url
    except Exception as e:
        logger.warning(f"无法从配置文件读取数据库URL: {e}")
    
    # 默认值
    default_url = "sqlite:///./sqlcompare.db"
    logger.info(f"使用默认数据库URL: {default_url}")
    return default_url


@lru_cache()
def get_sqlalchemy_service() -> SQLAlchemyComparisonService:
    """
    获取SQLAlchemy服务单例 - 应用生命周期内复用
    """
    try:
        database_url = get_database_url()
        service = SQLAlchemyComparisonService(database_url)
        logger.info("SQLAlchemy服务单例创建成功")
        return service
    except Exception as e:
        logger.error(f"创建SQLAlchemy服务失败: {e}")
        raise


def get_comparison_service():
    """
    获取比对服务实例 - 每次创建新实例，避免循环导入
    """
    try:
        from backend.services.comparison_service import ComparisonService
        return ComparisonService(get_database_url())
    except Exception as e:
        logger.error(f"创建比对服务失败: {e}")
        raise


def get_task_service():
    """
    获取任务服务实例 - 每次创建新实例，避免循环导入
    """
    try:
        from backend.services.task_service import TaskService
        return TaskService(get_database_url())
    except Exception as e:
        logger.error(f"创建任务服务失败: {e}")
        raise


def get_config_service():
    """
    获取配置服务实例 - 每次创建新实例，避免循环导入
    """
    try:
        from backend.services.config_service import ConfigService
        return ConfigService(get_database_url())
    except Exception as e:
        logger.error(f"创建配置服务失败: {e}")
        raise


def get_result_service():
    """
    获取结果服务实例 - 每次创建新实例，避免循环导入
    """
    try:
        from backend.services.result_service import ResultService
        return ResultService(get_database_url())
    except Exception as e:
        logger.error(f"创建结果服务失败: {e}")
        raise


def get_current_user_id() -> str:
    """
    获取当前用户ID - 从认证上下文获取
    TODO: 实现真正的JWT认证
    """
    # 优先从环境变量读取（用于测试）
    user_id = os.getenv('SQLCOMPARE_USER_ID')
    if user_id:
        return user_id
    
    # 默认用户（开发环境）
    return "default_user"


def clear_service_cache():
    """
    清理服务缓存 - 用于测试或重新初始化
    """
    try:
        get_sqlalchemy_service.cache_clear()
        logger.info("服务缓存清理完成")
    except Exception as e:
        logger.error(f"清理服务缓存失败: {e}")


def get_service_status() -> dict:
    """
    获取服务状态信息 - 用于健康检查
    """
    try:
        status = {
            "database_url": get_database_url(),
            "services": {
                "sqlalchemy_service": "initialized" if get_sqlalchemy_service.cache_info().currsize > 0 else "not_initialized",
                "comparison_service": "available",
                "task_service": "available",
                "config_service": "available",
                "result_service": "available"
            }
        }
        return status
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        return {"error": str(e)}


# 应用启动时的初始化函数
async def initialize_services():
    """
    应用启动时初始化核心服务
    """
    try:
        logger.info("开始初始化核心服务...")

        # 预热SQLAlchemy服务
        get_sqlalchemy_service()

        logger.info("核心服务初始化完成")

    except Exception as e:
        logger.error(f"服务初始化失败: {e}")
        raise


# 应用关闭时的清理函数
async def cleanup_services():
    """
    应用关闭时清理所有服务
    """
    try:
        logger.info("开始清理所有服务...")

        # 清理服务缓存
        clear_service_cache()

        logger.info("所有服务清理完成")

    except Exception as e:
        logger.error(f"服务清理失败: {e}")
