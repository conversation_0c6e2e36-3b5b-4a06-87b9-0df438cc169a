"""
响应模型定义
"""
from typing import Any, Optional, Dict, List
from pydantic import BaseModel, Field
from datetime import datetime


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    code: int = Field(default=200, description="响应代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间戳")


class DataResponse(BaseResponse):
    """数据响应模型"""
    data: Optional[Any] = Field(None, description="响应数据")


class ListResponse(BaseResponse):
    """列表响应模型"""
    data: List[Any] = Field(default_factory=list, description="响应数据列表")
    total: int = Field(default=0, description="总数量")
    page: int = Field(default=1, description="当前页码")
    size: int = Field(default=20, description="每页大小")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(default=False, description="操作失败")
    error_code: str = Field(..., description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class ComparisonResponse(DataResponse):
    """数据比对响应模型"""
    summary: Optional[Dict[str, Any]] = Field(None, description="比对摘要")
    details: Optional[List[Dict[str, Any]]] = Field(None, description="比对详情")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)")


class TaskResponse(DataResponse):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress: Optional[int] = Field(None, description="任务进度(0-100)")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    task_id: Optional[str] = Field(None, description="任务ID")
    data: Optional[Dict[str, Any]] = Field(None, description="消息数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="消息时间戳")
