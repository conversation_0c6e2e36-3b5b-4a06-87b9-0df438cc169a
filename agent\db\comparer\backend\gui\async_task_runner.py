#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步任务运行器模块
提供线程+协程混合模型的封装，简化异步任务的执行和管理
"""

import asyncio
import time
import threading
import logging
import traceback
import uuid
from typing import Any, Callable, Optional, Dict, Union, Awaitable
from dataclasses import dataclass
from enum import Enum
import tkinter as tk

logger = logging.getLogger(__name__)


class ErrorLevel(Enum):
    """错误级别枚举"""
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class TaskResult:
    """任务执行结果"""
    success: bool
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    task_id: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class TaskMetrics:
    """任务性能指标"""
    task_id: str
    start_time: float
    end_time: float
    execution_time: float
    success: bool
    error_type: Optional[str] = None
    memory_usage: Optional[float] = None


class ThreadSafeErrorHandler:
    """线程安全的错误处理器
    
    处理跨线程的异常传递，避免lambda闭包变量捕获问题
    """
    
    def __init__(self, gui_app):
        """初始化错误处理器
        
        Args:
            gui_app: GUI应用实例，需要有log_message方法和root属性
        """
        self.gui_app = gui_app
        self._error_count = 0
        self._warning_count = 0
        
    def handle_error(self, error: Union[str, Exception], 
                    level: ErrorLevel = ErrorLevel.ERROR,
                    context: str = "",
                    show_dialog: bool = False) -> None:
        """处理错误信息
        
        Args:
            error: 错误信息或异常对象
            level: 错误级别
            context: 错误上下文信息
            show_dialog: 是否显示错误对话框
        """
        try:
            # 格式化错误信息
            if isinstance(error, Exception):
                error_msg = str(error)
                error_type = type(error).__name__
            else:
                error_msg = str(error)
                error_type = "Unknown"
            
            # 添加上下文信息
            if context:
                full_message = f"{context}: {error_msg}"
            else:
                full_message = error_msg
            
            # 根据错误级别选择图标和前缀
            level_icons = {
                ErrorLevel.WARNING: "⚠️",
                ErrorLevel.ERROR: "❌", 
                ErrorLevel.CRITICAL: "🚨"
            }
            
            icon = level_icons.get(level, "❌")
            formatted_message = f"{icon} {full_message}"
            
            # 更新错误计数
            if level == ErrorLevel.WARNING:
                self._warning_count += 1
            else:
                self._error_count += 1
            
            # 安全地传递错误信息到GUI线程
            self._safe_gui_callback(
                lambda msg=formatted_message: self.gui_app.log_message(msg)
            )
            
            # 显示错误对话框（如果需要）
            if show_dialog and level != ErrorLevel.WARNING:
                self._safe_gui_callback(
                    lambda msg=full_message: self._show_error_dialog(msg, level)
                )
            
            # 记录到日志
            log_level = {
                ErrorLevel.WARNING: logging.WARNING,
                ErrorLevel.ERROR: logging.ERROR,
                ErrorLevel.CRITICAL: logging.CRITICAL
            }.get(level, logging.ERROR)
            
            logger.log(log_level, f"{context}: {error_msg}", exc_info=isinstance(error, Exception))
            
        except Exception as e:
            # 错误处理器本身出错时的后备处理
            logger.critical(f"错误处理器失败: {e}")
    
    def _safe_gui_callback(self, callback: Callable) -> None:
        """安全的GUI回调，避免lambda闭包问题"""
        try:
            if hasattr(self.gui_app, 'root') and self.gui_app.root:
                self.gui_app.root.after(0, callback)
        except Exception as e:
            logger.error(f"GUI回调失败: {e}")
    
    def _show_error_dialog(self, message: str, level: ErrorLevel) -> None:
        """显示错误对话框"""
        try:
            from tkinter import messagebox
            
            title_map = {
                ErrorLevel.ERROR: "错误",
                ErrorLevel.CRITICAL: "严重错误"
            }
            
            title = title_map.get(level, "错误")
            messagebox.showerror(title, message)
            
        except Exception as e:
            logger.error(f"显示错误对话框失败: {e}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计信息"""
        return {
            "error_count": self._error_count,
            "warning_count": self._warning_count,
            "total_count": self._error_count + self._warning_count
        }
    
    def reset_stats(self) -> None:
        """重置错误统计"""
        self._error_count = 0
        self._warning_count = 0


class AsyncTaskRunner:
    """异步任务运行器
    
    封装事件循环的创建、管理和销毁逻辑，提供统一的异步任务执行接口
    """
    
    _metrics_history: Dict[str, TaskMetrics] = {}
    _active_tasks: Dict[str, asyncio.Task] = {}
    
    @staticmethod
    def run_async_task(async_func: Callable[..., Awaitable[Any]], 
                      *args, 
                      timeout: Optional[float] = None,
                      task_id: Optional[str] = None,
                      error_handler: Optional[ThreadSafeErrorHandler] = None,
                      **kwargs) -> TaskResult:
        """运行异步任务
        
        Args:
            async_func: 要执行的异步函数
            *args: 传递给异步函数的位置参数
            timeout: 超时时间（秒），None表示无超时
            task_id: 任务ID，用于跟踪和取消
            error_handler: 错误处理器
            **kwargs: 传递给异步函数的关键字参数
            
        Returns:
            TaskResult: 任务执行结果
        """
        # 生成任务ID
        if task_id is None:
            task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        start_time = time.time()
        loop = None
        
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 创建任务
            task = loop.create_task(async_func(*args, **kwargs))
            AsyncTaskRunner._active_tasks[task_id] = task
            
            # 执行任务（带超时控制）
            if timeout:
                result = loop.run_until_complete(
                    asyncio.wait_for(task, timeout=timeout)
                )
            else:
                result = loop.run_until_complete(task)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录性能指标
            metrics = TaskMetrics(
                task_id=task_id,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                success=True
            )
            AsyncTaskRunner._metrics_history[task_id] = metrics
            
            logger.info(f"异步任务 {task_id} 执行成功，耗时: {execution_time:.3f}秒")
            
            return TaskResult(
                success=True,
                result=result,
                execution_time=execution_time,
                task_id=task_id,
                metadata={"metrics": metrics}
            )
            
        except asyncio.TimeoutError as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            error_msg = f"任务 {task_id} 执行超时 (>{timeout}秒)"
            logger.warning(error_msg)
            
            if error_handler:
                error_handler.handle_error(error_msg, ErrorLevel.WARNING)
            
            return TaskResult(
                success=False,
                error=e,
                execution_time=execution_time,
                task_id=task_id
            )
            
        except asyncio.CancelledError as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            error_msg = f"任务 {task_id} 被取消"
            logger.info(error_msg)
            
            return TaskResult(
                success=False,
                error=e,
                execution_time=execution_time,
                task_id=task_id
            )
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            error_msg = f"任务 {task_id} 执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # 记录失败的性能指标
            metrics = TaskMetrics(
                task_id=task_id,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                success=False,
                error_type=type(e).__name__
            )
            AsyncTaskRunner._metrics_history[task_id] = metrics
            
            if error_handler:
                error_handler.handle_error(e, ErrorLevel.ERROR, f"任务 {task_id}")
            
            return TaskResult(
                success=False,
                error=e,
                execution_time=execution_time,
                task_id=task_id,
                metadata={"metrics": metrics}
            )
            
        finally:
            # 清理资源
            try:
                if task_id in AsyncTaskRunner._active_tasks:
                    del AsyncTaskRunner._active_tasks[task_id]
                
                if loop and not loop.is_closed():
                    loop.close()
                    
            except Exception as e:
                logger.error(f"清理事件循环失败: {e}")
    
    @staticmethod
    def cancel_task(task_id: str) -> bool:
        """取消指定的任务
        
        Args:
            task_id: 要取消的任务ID
            
        Returns:
            bool: 取消是否成功
        """
        try:
            if task_id in AsyncTaskRunner._active_tasks:
                task = AsyncTaskRunner._active_tasks[task_id]
                task.cancel()
                logger.info(f"任务 {task_id} 取消请求已发送")
                return True
            else:
                logger.warning(f"任务 {task_id} 不存在或已完成")
                return False
                
        except Exception as e:
            logger.error(f"取消任务 {task_id} 失败: {e}")
            return False
    
    @staticmethod
    def get_active_tasks() -> Dict[str, str]:
        """获取当前活跃的任务列表
        
        Returns:
            Dict[str, str]: 任务ID到任务状态的映射
        """
        active_tasks = {}
        for task_id, task in AsyncTaskRunner._active_tasks.items():
            if task.done():
                status = "completed"
            elif task.cancelled():
                status = "cancelled"
            else:
                status = "running"
            active_tasks[task_id] = status
        
        return active_tasks
    
    @staticmethod
    def get_metrics_summary() -> Dict[str, Any]:
        """获取性能指标摘要
        
        Returns:
            Dict[str, Any]: 性能指标摘要
        """
        if not AsyncTaskRunner._metrics_history:
            return {"total_tasks": 0}
        
        metrics_list = list(AsyncTaskRunner._metrics_history.values())
        successful_tasks = [m for m in metrics_list if m.success]
        failed_tasks = [m for m in metrics_list if not m.success]
        
        total_execution_time = sum(m.execution_time for m in metrics_list)
        avg_execution_time = total_execution_time / len(metrics_list) if metrics_list else 0
        
        return {
            "total_tasks": len(metrics_list),
            "successful_tasks": len(successful_tasks),
            "failed_tasks": len(failed_tasks),
            "success_rate": len(successful_tasks) / len(metrics_list) * 100 if metrics_list else 0,
            "total_execution_time": total_execution_time,
            "average_execution_time": avg_execution_time,
            "fastest_task": min(metrics_list, key=lambda m: m.execution_time).execution_time if metrics_list else 0,
            "slowest_task": max(metrics_list, key=lambda m: m.execution_time).execution_time if metrics_list else 0
        }
    
    @staticmethod
    def clear_metrics_history() -> None:
        """清空性能指标历史"""
        AsyncTaskRunner._metrics_history.clear()
        logger.info("性能指标历史已清空")


# ============================================================================
# 便利函数层 - 简化API使用
# ============================================================================

def run_async_task(async_func: Callable[..., Awaitable[Any]],
                  *args,
                  timeout: Optional[float] = None,
                  task_id: Optional[str] = None,
                  gui_app=None,
                  **kwargs) -> TaskResult:
    """便利函数：运行异步任务

    这是AsyncTaskRunner.run_async_task的简化包装，自动创建错误处理器

    Args:
        async_func: 要执行的异步函数
        *args: 传递给异步函数的位置参数
        timeout: 超时时间（秒），None表示无超时
        task_id: 任务ID，用于跟踪和取消
        gui_app: GUI应用实例，用于自动创建错误处理器
        **kwargs: 传递给异步函数的关键字参数

    Returns:
        TaskResult: 任务执行结果

    Example:
        result = run_async_task(my_async_func, arg1="value1", timeout=60.0, gui_app=self)
        if result.success:
            print(f"任务成功: {result.result}")
        else:
            print(f"任务失败: {result.error}")
    """
    error_handler = ThreadSafeErrorHandler(gui_app) if gui_app else None
    return AsyncTaskRunner.run_async_task(
        async_func, *args,
        timeout=timeout,
        task_id=task_id,
        error_handler=error_handler,
        **kwargs
    )


def run_async_in_thread(async_func: Callable[..., Awaitable[Any]],
                       gui_app,
                       *args,
                       timeout: Optional[float] = None,
                       task_id: Optional[str] = None,
                       on_success: Optional[Callable[[Any], None]] = None,
                       on_error: Optional[Callable[[Exception], None]] = None,
                       on_finally: Optional[Callable[[], None]] = None,
                       **kwargs) -> threading.Thread:
    """便利函数：在线程中运行异步任务

    自动处理线程创建、异步任务执行和GUI回调

    Args:
        async_func: 要执行的异步函数
        gui_app: GUI应用实例
        *args: 传递给异步函数的位置参数
        timeout: 超时时间（秒）
        task_id: 任务ID
        on_success: 成功回调函数，接收结果作为参数
        on_error: 错误回调函数，接收异常作为参数
        on_finally: 最终回调函数，无论成功失败都会执行
        **kwargs: 传递给异步函数的关键字参数

    Returns:
        threading.Thread: 创建的线程

    Example:
        def on_success(result):
            print(f"任务成功: {result}")

        def on_error(error):
            print(f"任务失败: {error}")

        thread = run_async_in_thread(
            my_async_func,
            gui_app,
            "value1",  # 位置参数
            timeout=60.0,
            on_success=on_success,
            on_error=on_error,
            arg1="value1"  # 关键字参数
        )
    """
    def thread_target():
        """线程执行目标函数"""
        try:
            # 执行异步任务
            result = run_async_task(
                async_func, *args,
                timeout=timeout,
                task_id=task_id,
                gui_app=gui_app,
                **kwargs
            )

            # 处理结果回调
            if result.success and on_success:
                gui_app.root.after(0, lambda r=result.result: on_success(r))
            elif not result.success and on_error:
                gui_app.root.after(0, lambda e=result.error: on_error(e))

        except Exception as e:
            # 线程级别的异常处理
            logger.error(f"线程执行异常: {e}", exc_info=True)
            if on_error:
                gui_app.root.after(0, lambda ex=e: on_error(ex))

        finally:
            # 执行最终回调
            if on_finally:
                gui_app.root.after(0, on_finally)

    thread = threading.Thread(target=thread_target, daemon=True)
    thread.start()
    return thread


def create_error_handler(gui_app: Optional[Any] = None) -> ThreadSafeErrorHandler:
    """便利函数：创建错误处理器

    Args:
        gui_app: GUI应用实例

    Returns:
        ThreadSafeErrorHandler: 错误处理器实例

    Example:
        error_handler = create_error_handler(self)
        error_handler.handle_error("测试错误", ErrorLevel.WARNING)
    """
    return ThreadSafeErrorHandler(gui_app)


# ============================================================================
# 专用包装器函数 - 针对特定场景的优化
# ============================================================================

def async_comparison_task(gui_app,
                         comparison_mode,
                         config,
                         timeout: Optional[float] = 600.0,
                         on_success: Optional[Callable[[Any], None]] = None,
                         on_error: Optional[Callable[[Exception], None]] = None,
                         on_finally: Optional[Callable[[], None]] = None) -> threading.Thread:
    """专门用于数据库比对的异步任务包装器

    针对数据库比对场景优化的便利函数，包含合适的默认超时时间和任务ID

    Args:
        gui_app: GUI应用实例
        comparison_mode: 比对模式实例
        config: 比对配置
        timeout: 超时时间（秒），默认10分钟
        on_success: 成功回调函数
        on_error: 错误回调函数
        on_finally: 最终回调函数

    Returns:
        threading.Thread: 创建的线程

    Example:
        def on_success(result):
            self._process_comparison_result(result)

        def on_error(error):
            self.log_message(f"比对失败: {error}")

        thread = async_comparison_task(
            self,
            self.current_mode,
            config,
            on_success=on_success,
            on_error=on_error
        )
    """
    return run_async_in_thread(
        comparison_mode.execute_comparison,
        gui_app,
        config,  # 将config作为位置参数传递给*args
        timeout=timeout,
        task_id=f"comparison_{int(time.time())}",
        on_success=on_success,
        on_error=on_error,
        on_finally=on_finally
    )


def simple_async_task(gui_app,
                     async_func: Callable[..., Awaitable[Any]],
                     *args, **kwargs) -> threading.Thread:
    """最简单的异步任务执行函数

    提供最简化的API，使用默认配置执行异步任务

    Args:
        gui_app: GUI应用实例
        async_func: 要执行的异步函数
        *args, **kwargs: 传递给异步函数的参数

    Returns:
        threading.Thread: 创建的线程

    Example:
        # 最简单的使用方式
        thread = simple_async_task(self, my_async_func, arg1="value1")
    """
    return run_async_in_thread(
        async_func,
        gui_app,
        *args,
        timeout=300.0,  # 默认5分钟超时
        **kwargs
    )


# ============================================================================
# 使用示例和测试函数
# ============================================================================

async def example_async_task(duration: float = 1.0, should_fail: bool = False) -> str:
    """示例异步任务

    Args:
        duration: 任务持续时间
        should_fail: 是否应该失败

    Returns:
        str: 任务结果
    """
    await asyncio.sleep(duration)

    if should_fail:
        raise Exception("示例任务故意失败")

    return f"任务完成，耗时 {duration} 秒"


def demo_async_task_runner():
    """演示异步任务运行器的使用"""
    print("🚀 异步任务运行器演示")
    print("=" * 50)

    # 示例1：使用便利函数执行成功的任务
    print("\n📝 示例1：使用便利函数执行成功的任务")
    result1 = run_async_task(
        example_async_task,
        duration=0.5,
        should_fail=False,
        task_id="demo_task_1"
    )
    print(f"结果: success={result1.success}, result={result1.result}, time={result1.execution_time:.3f}s")

    # 示例2：使用便利函数执行失败的任务
    print("\n📝 示例2：使用便利函数执行失败的任务")
    result2 = run_async_task(
        example_async_task,
        duration=0.3,
        should_fail=True,
        task_id="demo_task_2"
    )
    print(f"结果: success={result2.success}, error={result2.error}, time={result2.execution_time:.3f}s")

    # 示例3：使用便利函数执行超时的任务
    print("\n📝 示例3：使用便利函数执行超时的任务")
    result3 = run_async_task(
        example_async_task,
        duration=2.0,
        should_fail=False,
        timeout=1.0,
        task_id="demo_task_3"
    )
    print(f"结果: success={result3.success}, error={type(result3.error).__name__}, time={result3.execution_time:.3f}s")

    # 显示性能指标
    print("\n📊 性能指标摘要:")
    metrics = AsyncTaskRunner.get_metrics_summary()
    for key, value in metrics.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.3f}")
        else:
            print(f"   {key}: {value}")


if __name__ == "__main__":
    # 运行演示
    demo_async_task_runner()
