"""
数据库管理API端点
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from pydantic import BaseModel
import logging
import time

from comparison.connectors.database_connector import DatabaseConnectorFactory
from comparison.engines.base_engine import DatabaseConnection

router = APIRouter()
logger = logging.getLogger(__name__)


class DatabaseConnectionTest(BaseModel):
    """数据库连接测试模型"""
    db_type: str
    host: str
    port: int
    database: str
    username: str
    password: str


class ConnectionTestResponse(BaseModel):
    """连接测试响应模型"""
    success: bool
    message: str
    connection_time: float
    error_details: str = None


@router.post("/test-connection", response_model=ConnectionTestResponse)
async def test_database_connection(connection: DatabaseConnectionTest):
    """测试数据库连接"""
    start_time = time.time()
    
    try:
        # 创建连接器
        connector_factory = DatabaseConnectorFactory()
        connector = connector_factory.create_connector(connection.db_type)
        
        # 创建连接信息对象
        conn_info = DatabaseConnection(
            db_type=connection.db_type,
            host=connection.host,
            port=connection.port,
            database=connection.database,
            username=connection.username,
            password=connection.password
        )
        
        # 测试连接
        await connector.connect(conn_info)
        await connector.disconnect()
        
        connection_time = time.time() - start_time
        
        logger.info(f"数据库连接测试成功: {connection.db_type}://{connection.host}:{connection.port}/{connection.database}")
        
        return ConnectionTestResponse(
            success=True,
            message="数据库连接成功",
            connection_time=round(connection_time, 3)
        )
        
    except ValueError as e:
        # 不支持的数据库类型
        logger.error(f"不支持的数据库类型: {connection.db_type}")
        raise HTTPException(status_code=400, detail=f"不支持的数据库类型: {connection.db_type}")
        
    except Exception as e:
        connection_time = time.time() - start_time
        error_msg = str(e)
        
        logger.error(f"数据库连接测试失败: {error_msg}")
        
        return ConnectionTestResponse(
            success=False,
            message="数据库连接失败",
            connection_time=round(connection_time, 3),
            error_details=error_msg
        )


@router.get("/supported-types")
async def get_supported_database_types():
    """获取支持的数据库类型"""
    try:
        connector_factory = DatabaseConnectorFactory()
        supported_types = connector_factory.get_supported_types()
        
        return {
            "supported_types": supported_types,
            "total": len(supported_types)
        }
        
    except Exception as e:
        logger.error(f"获取支持的数据库类型失败: {e}")
        raise HTTPException(status_code=500, detail="获取支持的数据库类型失败")


@router.get("/connection-template/{db_type}")
async def get_connection_template(db_type: str):
    """获取指定数据库类型的连接模板"""
    templates = {
        "mysql": {
            "db_type": "mysql",
            "host": "localhost",
            "port": 3306,
            "database": "your_database",
            "username": "your_username",
            "password": "your_password"
        },
        "postgresql": {
            "db_type": "postgresql",
            "host": "localhost",
            "port": 5432,
            "database": "your_database",
            "username": "your_username",
            "password": "your_password"
        },
        "oracle": {
            "db_type": "oracle",
            "host": "localhost",
            "port": 1521,
            "database": "your_service_name",
            "username": "your_username",
            "password": "your_password"
        },
        "sqlserver": {
            "db_type": "sqlserver",
            "host": "localhost",
            "port": 1433,
            "database": "your_database",
            "username": "your_username",
            "password": "your_password"
        },
        "db2": {
            "db_type": "db2",
            "host": "localhost",
            "port": 50000,
            "database": "your_database",
            "username": "your_username",
            "password": "your_password"
        },
        "gaussdb": {
            "db_type": "gaussdb",
            "host": "localhost",
            "port": 5432,
            "database": "your_database",
            "username": "your_username",
            "password": "your_password"
        }
    }
    
    if db_type.lower() not in templates:
        raise HTTPException(status_code=404, detail=f"不支持的数据库类型: {db_type}")
    
    return templates[db_type.lower()]
