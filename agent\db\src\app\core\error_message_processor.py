"""
错误消息处理器

负责将原始的数据库错误转换为用户友好的错误消息
"""

import re
import logging
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class ErrorMessageProcessor:
    """错误消息处理器"""
    
    # DB2错误代码映射
    DB2_ERROR_PATTERNS = {
        'SQL0204N': {
            'type': 'TABLE_NOT_FOUND',
            'pattern': r'SQL0204N\s+"([^"]+)"\s+is\s+an\s+undefined\s+name',
            'message_template': '表 "{table_name}" 不存在',
            'suggestion': '请检查表名是否正确，确保表存在于指定的数据库中'
        },
        'SQL0206N': {
            'type': 'COLUMN_NOT_FOUND',
            'pattern': r'SQL0206N\s+"([^"]+)"\s+is\s+not\s+valid\s+in\s+the\s+context',
            'message_template': '列 "{column_name}" 不存在或在当前上下文中无效',
            'suggestion': '请检查列名是否正确，注意大小写和表别名'
        },
        'SQL0104N': {
            'type': 'SQL_SYNTAX_ERROR',
            'pattern': r'SQL0104N\s+An\s+unexpected\s+token\s+"([^"]+)"\s+was\s+found',
            'message_template': 'SQL语法错误：发现意外的标记 "{token}"',
            'suggestion': '请检查SQL语句语法是否正确'
        },
        'SQL0551N': {
            'type': 'PERMISSION_ERROR',
            'pattern': r'SQL0551N\s+"([^"]+)"\s+does\s+not\s+have\s+the\s+required\s+authorization',
            'message_template': '用户 "{user}" 没有执行此操作的权限',
            'suggestion': '请联系数据库管理员授予相应权限'
        },
        'SQL30081N': {
            'type': 'CONNECTION_ERROR',
            'pattern': r'SQL30081N\s+A\s+communication\s+error\s+has\s+been\s+detected',
            'message_template': '数据库通信错误',
            'suggestion': '请检查网络连接和数据库服务状态'
        }
    }
    
    # 通用错误模式
    GENERIC_ERROR_PATTERNS = {
        'connection': {
            'type': 'CONNECTION_ERROR',
            'keywords': ['connection', 'connect', 'timeout', 'refused', 'unreachable'],
            'message': '数据库连接失败',
            'suggestion': '请检查数据库连接配置和网络状态'
        },
        'permission': {
            'type': 'PERMISSION_ERROR',
            'keywords': ['permission', 'access', 'denied', 'forbidden', 'unauthorized'],
            'message': '权限不足',
            'suggestion': '请检查数据库用户权限'
        },
        'syntax': {
            'type': 'SQL_SYNTAX_ERROR',
            'keywords': ['syntax error', 'invalid syntax', 'parse error'],
            'message': 'SQL语法错误',
            'suggestion': '请检查SQL语句语法'
        }
    }
    
    @classmethod
    def process_error_message(cls, raw_error: str) -> Dict[str, str]:
        """
        处理原始错误消息，返回用户友好的错误信息
        
        Args:
            raw_error: 原始错误消息
            
        Returns:
            包含type, message, suggestion的字典
        """
        try:
            # 清理原始错误消息
            cleaned_error = cls._clean_raw_error(raw_error)
            
            # 尝试匹配DB2特定错误
            db2_result = cls._match_db2_error(cleaned_error)
            if db2_result:
                return db2_result
            
            # 尝试匹配通用错误模式
            generic_result = cls._match_generic_error(cleaned_error)
            if generic_result:
                return generic_result
            
            # 如果都没有匹配，返回清理后的通用错误
            return {
                'type': 'UNKNOWN_ERROR',
                'message': cls._extract_core_message(cleaned_error),
                'suggestion': '请检查配置和日志获取更多信息，或联系管理员'
            }
            
        except Exception as e:
            logger.error(f"处理错误消息时发生异常: {str(e)}")
            return {
                'type': 'MESSAGE_PROCESSING_ERROR',
                'message': '错误消息处理失败',
                'suggestion': '请联系管理员'
            }
    
    @classmethod
    def _clean_raw_error(cls, raw_error: str) -> str:
        """清理原始错误消息，移除重复和无用信息"""
        if not raw_error:
            return ""
        
        # 移除重复的前缀
        cleaned = raw_error
        
        # 移除 "任务执行失败:" 前缀
        cleaned = re.sub(r'^任务执行失败:\s*', '', cleaned)
        
        # 移除重复的 "表不存在:" 前缀
        cleaned = re.sub(r'^表不存在:\s*表不存在:\s*', '表不存在: ', cleaned)
        
        # 移除Python异常类型前缀
        cleaned = re.sub(r'^[a-zA-Z_][a-zA-Z0-9_.]*::\w+:\s*', '', cleaned)
        
        # 移除 "Statement Execute Failed:" 前缀
        cleaned = re.sub(r'Statement\s+Execute\s+Failed:\s*', '', cleaned)
        
        # 移除驱动程序信息
        cleaned = re.sub(r'\[IBM\]\[CLI Driver\]\[DB2/[^\]]+\]\s*', '', cleaned)
        
        # 移除SQLSTATE和SQLCODE信息
        cleaned = re.sub(r'\s+SQLSTATE=\w+\s+SQLCODE=-?\d+', '', cleaned)
        
        return cleaned.strip()
    
    @classmethod
    def _match_db2_error(cls, error_message: str) -> Optional[Dict[str, str]]:
        """匹配DB2特定错误"""
        for error_code, config in cls.DB2_ERROR_PATTERNS.items():
            if error_code in error_message:
                match = re.search(config['pattern'], error_message, re.IGNORECASE)
                if match:
                    # 提取匹配的参数
                    if error_code == 'SQL0204N':
                        table_name = match.group(1)
                        message = config['message_template'].format(table_name=table_name)
                    elif error_code == 'SQL0206N':
                        column_name = match.group(1)
                        message = config['message_template'].format(column_name=column_name)
                    elif error_code == 'SQL0104N':
                        token = match.group(1)
                        message = config['message_template'].format(token=token)
                    elif error_code == 'SQL0551N':
                        user = match.group(1)
                        message = config['message_template'].format(user=user)
                    else:
                        message = config['message_template']
                    
                    return {
                        'type': config['type'],
                        'message': message,
                        'suggestion': config['suggestion']
                    }
                else:
                    # 如果模式不匹配，使用通用消息
                    return {
                        'type': config['type'],
                        'message': config['message_template'].replace('"{table_name}"', '').replace('"{column_name}"', '').replace('"{token}"', '').replace('"{user}"', ''),
                        'suggestion': config['suggestion']
                    }
        
        return None
    
    @classmethod
    def _match_generic_error(cls, error_message: str) -> Optional[Dict[str, str]]:
        """匹配通用错误模式"""
        error_lower = error_message.lower()
        
        for pattern_name, config in cls.GENERIC_ERROR_PATTERNS.items():
            if any(keyword in error_lower for keyword in config['keywords']):
                return {
                    'type': config['type'],
                    'message': config['message'],
                    'suggestion': config['suggestion']
                }
        
        return None
    
    @classmethod
    def _extract_core_message(cls, error_message: str) -> str:
        """提取核心错误消息"""
        if not error_message:
            return "未知错误"
        
        # 如果消息太长，截取前100个字符
        if len(error_message) > 100:
            return error_message[:100] + "..."
        
        return error_message
    
    @classmethod
    def is_table_not_found_error(cls, error_message: str) -> bool:
        """检查是否为表不存在错误"""
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        return any(pattern in error_lower for pattern in [
            'sql0204n',
            'undefined name',
            'table or view does not exist',
            'table does not exist',
            'relation does not exist'
        ])
    
    @classmethod
    def is_sql_syntax_error(cls, error_message: str) -> bool:
        """检查是否为SQL语法错误"""
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        return any(pattern in error_lower for pattern in [
            'sql0206n',
            'sql0104n',
            'syntax error',
            'sqlstate=42601',
            'sqlstate=42703',
            'invalid identifier',
            'invalid column name'
        ])
    
    @classmethod
    def is_connection_error(cls, error_message: str) -> bool:
        """检查是否为连接错误"""
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        return any(pattern in error_lower for pattern in [
            'sql30081n',
            'sql08001',
            'connection',
            'timeout',
            'network',
            'communication link failure',
            'connection refused'
        ])
