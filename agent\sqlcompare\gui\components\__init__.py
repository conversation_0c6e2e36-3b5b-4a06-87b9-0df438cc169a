#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件模块包

包含所有页面组件的定义和导出
"""

# 导出所有页面组件类
__all__ = [
    'BasePage',
    'DatasourceConfigPage',
    'OutputConfigPage',
    'ComparisonRulesPage',
    'TablesOverviewPage',
    'TableComparisonPage'
]

# 延迟导入，避免循环导入问题
def get_base_page():
    """获取基础页面类"""
    from .base_page import BasePage
    return BasePage

def get_datasource_config_page():
    """获取数据源配置页面类"""
    from .datasource_config_page import DatasourceConfigPage
    return DatasourceConfigPage

def get_output_config_page():
    """获取输出配置页面类"""
    from .output_config_page import OutputConfigPage
    return OutputConfigPage

def get_comparison_rules_page():
    """获取比对规则配置页面类"""
    from .comparison_rules_page import ComparisonRulesPage
    return ComparisonRulesPage

def get_tables_overview_page():
    """获取表概览页面类"""
    from .tables_overview_page import TablesOverviewPage
    return TablesOverviewPage

def get_table_comparison_page():
    """获取单表比对结果页面类"""
    from .table_comparison_page import TableComparisonPage
    return TableComparisonPage
