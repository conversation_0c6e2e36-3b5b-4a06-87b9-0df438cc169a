"""
Legacy API兼容性端点

实现与原版本API兼容的端点，提供无缝迁移支持
"""
import os
import sys
# 获取当前脚本的目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 将项目根目录添加到Python路径中，使得backend和comparison模块可以被正确导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(SCRIPT_DIR)))
sys.path.insert(0, PROJECT_ROOT)
from fastapi import APIRouter, HTTPException, Response
import logging

from .models import (
    CompareTaskRequest, ConnectionTestRequest, ConnectionTestResponse,
    TaskResponse, TaskStatusResponse, TaskResultResponse, ComparisonResponse
)
from .converters import LegacyConverter
from backend.services.legacy_adapter_service import legacy_adapter_service

logger = logging.getLogger(__name__)
router = APIRouter()


def add_legacy_headers(response: Response):
    """添加Legacy API版本标识头"""
    response.headers["X-API-Version"] = "legacy-v1"
    response.headers["X-Legacy-Support"] = "true"
    response.headers["X-Migration-Notice"] = "This is a legacy API. Please migrate to /api/v1/"


@router.post("/database/connection/test/", response_model=ConnectionTestResponse)
async def test_database_connection(
    request: ConnectionTestRequest,
    response: Response
):
    """
    测试数据库连接 (Legacy API兼容)

    原版本API路径: POST /api/database/connection/test/
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 数据库连接测试请求 - 类型: {request.config.type}, 主机: {request.config.host}")

        # 使用适配器服务
        legacy_response = await legacy_adapter_service.test_database_connection(request)

        logger.info(f"Legacy API: 连接测试成功 - 耗时: {legacy_response.elapsed_time:.2f}秒")
        return legacy_response

    except Exception as e:
        logger.error(f"Legacy API: 连接测试失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="CONNECTION_TEST_FAILED",
            message=f"数据库连接测试失败: {str(e)}",
            details={"connection_type": request.config.type.value},
            status_code=400
        )
        raise HTTPException(status_code=400, detail=error_response.model_dump())


@router.post("/tasks/compare", response_model=TaskResponse)
async def create_comparison_task(
    request: CompareTaskRequest,
    response: Response
):
    """
    创建数据比对任务 (Legacy API兼容)

    原版本API路径: POST /api/tasks/compare
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 创建比对任务请求 - 源: {request.source.type}@{request.source.host}, "
                   f"目标: {request.target.type}@{request.target.host}")

        # 使用适配器服务
        legacy_response = await legacy_adapter_service.create_comparison_task(request)

        logger.info(f"Legacy API: 任务创建成功 - ID: {legacy_response.task_id}")
        return legacy_response

    except ValueError as e:
        logger.error(f"Legacy API: 任务创建失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_VALIDATION_FAILED",
            message=str(e),
            status_code=400
        )
        raise HTTPException(status_code=400, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"Legacy API: 任务创建失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_CREATION_FAILED",
            message=f"任务创建失败: {str(e)}",
            status_code=500
        )
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.get("/tasks/{task_id}/status", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str,
    response: Response
):
    """
    查询任务状态 (Legacy API兼容)

    原版本API路径: GET /api/tasks/{task_id}/status
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 查询任务状态 - ID: {task_id}")

        # 使用适配器服务
        legacy_response = await legacy_adapter_service.get_task_status(task_id)

        logger.info(f"Legacy API: 任务状态查询成功 - ID: {task_id}, 状态: {legacy_response.status}")
        return legacy_response

    except ValueError as e:
        logger.error(f"Legacy API: 任务状态查询失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_NOT_FOUND",
            message=str(e),
            status_code=404
        )
        raise HTTPException(status_code=404, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"Legacy API: 任务状态查询失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="STATUS_QUERY_FAILED",
            message=f"任务状态查询失败: {str(e)}",
            status_code=500
        )
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.get("/tasks/{task_id}/result", response_model=TaskResultResponse)
async def get_task_result(
    task_id: str,
    response: Response
):
    """
    获取任务结果 (Legacy API兼容)

    原版本API路径: GET /api/tasks/{task_id}/result
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 获取任务结果 - ID: {task_id}")

        # 使用适配器服务
        legacy_response = await legacy_adapter_service.get_task_result(task_id)

        logger.info(f"Legacy API: 任务结果获取成功 - ID: {task_id}")
        return legacy_response

    except ValueError as e:
        logger.error(f"Legacy API: 任务结果获取失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_NOT_FOUND",
            message=str(e),
            status_code=404
        )
        raise HTTPException(status_code=404, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"Legacy API: 任务结果获取失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="RESULT_QUERY_FAILED",
            message=f"任务结果获取失败: {str(e)}",
            status_code=500
        )
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.post("/compare_data", response_model=ComparisonResponse)
async def compare_data_directly(
    request: CompareTaskRequest,
    response: Response
):
    """
    直接同步比对数据 (Legacy API兼容)

    原版本API路径: POST /api/compare_data
    注意: 此API适用于小型数据集的同步比对，大型数据集请使用任务API
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 直接比对请求 - 源: {request.source.type}@{request.source.host}, "
                   f"目标: {request.target.type}@{request.target.host}")

        # 使用适配器服务
        legacy_response = await legacy_adapter_service.compare_data_directly(request)

        logger.info(f"Legacy API: 直接比对完成 - 耗时: {legacy_response.execution_time:.2f}秒")
        return legacy_response

    except ValueError as e:
        logger.error(f"Legacy API: 直接比对失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_VALIDATION_FAILED",
            message=str(e),
            status_code=400
        )
        raise HTTPException(status_code=400, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"Legacy API: 直接比对失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="DIRECT_COMPARISON_FAILED",
            message=f"直接比对失败: {str(e)}",
            status_code=500
        )
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    response: Response
):
    """
    取消任务 (Legacy API兼容)

    原版本API路径: DELETE /api/tasks/{task_id}
    """
    add_legacy_headers(response)

    try:
        logger.info(f"Legacy API: 取消任务 - ID: {task_id}")

        # 使用适配器服务
        result = await legacy_adapter_service.cancel_task(task_id)

        logger.info(f"Legacy API: 任务取消成功 - ID: {task_id}")
        return result

    except ValueError as e:
        logger.error(f"Legacy API: 任务取消失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_NOT_FOUND",
            message=str(e),
            status_code=404
        )
        raise HTTPException(status_code=404, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"Legacy API: 任务取消失败 - {str(e)}")
        error_response = LegacyConverter.create_error_response(
            error_code="TASK_CANCEL_ERROR",
            message=f"任务取消异常: {str(e)}",
            status_code=500
        )
        raise HTTPException(status_code=500, detail=error_response.model_dump())
