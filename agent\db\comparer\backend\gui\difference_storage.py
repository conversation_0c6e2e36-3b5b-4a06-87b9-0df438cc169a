#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
差异数据持久化存储模块
提供海量差异数据的高效存储和查询功能
"""

import os
import sys
import sqlite3
import json
import time
import uuid
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from contextlib import contextmanager
import threading

# 配置日志
logger = logging.getLogger(__name__)

# 系统数据库配置
def get_system_database_path():
    """获取系统数据库路径"""
    try:
        # 尝试导入系统配置
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))

        # 临时添加路径
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        database_url = None
        try:
            from backend.core.config import settings
            database_url = settings.DATABASE_URL
            logger.info(f"成功导入系统配置: {database_url}")
        except ImportError as e:
            logger.warning(f"无法导入系统配置: {e}")
            # 回退到默认配置
            database_url = "sqlite:///./comparison.db"
        except Exception as e:
            logger.warning(f"导入系统配置时出错: {e}")
            database_url = "sqlite:///./comparison.db"

        # 解析SQLite URL
        if database_url and database_url.startswith('sqlite:///'):
            db_file = database_url.replace('sqlite:///', '')
            if db_file.startswith('./'):
                # 相对路径：相对于comparer目录
                comparer_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                db_path = os.path.join(comparer_dir, db_file[2:])  # 去掉 './'
            else:
                # 绝对路径
                db_path = db_file

            resolved_path = os.path.abspath(db_path)
            logger.info(f"解析的数据库路径: {resolved_path}")
            return resolved_path
        else:
            # 非SQLite或解析失败，使用默认路径
            logger.warning(f"使用默认数据库路径，原URL: {database_url}")
            comparer_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            default_path = os.path.join(comparer_dir, "comparison.db")
            return os.path.abspath(default_path)

    except Exception as e:
        logger.error(f"获取系统数据库路径失败: {e}")
        # 最终回退：在comparer目录下创建comparison.db
        try:
            comparer_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            fallback_path = os.path.join(comparer_dir, "comparison.db")
            logger.info(f"使用回退路径: {fallback_path}")
            return os.path.abspath(fallback_path)
        except Exception as e2:
            logger.error(f"回退路径也失败: {e2}")
            # 最后的回退：当前目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            final_path = os.path.join(current_dir, "comparison.db")
            logger.info(f"使用最终回退路径: {final_path}")
            return final_path

logger = logging.getLogger(__name__)


@dataclass
class DifferenceRecord:
    """差异记录数据类"""
    id: str
    session_id: str
    table_id: str
    record_id: str
    diff_type: str
    diff_fields: List[str]
    source_data: Dict[str, Any]
    target_data: Dict[str, Any]
    created_at: float
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ComparisonSession:
    """比对会话数据类"""
    session_id: str
    task_id: str
    mode: str
    status: str
    total_differences: int
    created_at: float
    completed_at: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


class DifferenceStorageManager:
    """差异数据存储管理器
    
    特点：
    - SQLite数据库存储，支持大量数据
    - 批量写入优化，提高性能
    - 事务管理和错误恢复
    - 分页查询支持
    - 历史记录管理
    """
    
    def __init__(self, db_path: str = None):
        """初始化存储管理器

        Args:
            db_path: 数据库文件路径，默认使用系统配置的数据库
        """
        if db_path is None:
            # 使用系统配置的数据库路径
            db_path = get_system_database_path()
            logger.info(f"使用系统配置数据库: {db_path}")

        self.db_path = db_path
        self._lock = threading.Lock()

        # 简单的性能优化配置
        self._init_performance_config()

        # 详细的路径和权限检查
        logger.info(f"数据库路径: {self.db_path}")
        logger.info(f"数据库目录: {os.path.dirname(self.db_path)}")
        logger.info(f"数据库文件存在: {os.path.exists(self.db_path)}")

        if os.path.exists(self.db_path):
            file_size = os.path.getsize(self.db_path)
            logger.info(f"数据库文件大小: {file_size} 字节")

        # 检查目录权限
        db_dir = os.path.dirname(self.db_path)
        if os.path.exists(db_dir):
            logger.info(f"数据库目录可写: {os.access(db_dir, os.W_OK)}")
        else:
            logger.warning(f"数据库目录不存在: {db_dir}")
            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"创建数据库目录: {db_dir}")

        self._init_database()

        # 初始化后再次检查文件大小
        if os.path.exists(self.db_path):
            file_size = os.path.getsize(self.db_path)
            logger.info(f"初始化后数据库文件大小: {file_size} 字节")

        logger.info(f"差异存储管理器初始化完成，数据库路径: {self.db_path}")


    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with self._get_connection() as conn:
                # 创建比对会话表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS comparison_sessions (
                        session_id TEXT PRIMARY KEY,
                        task_id TEXT NOT NULL,
                        mode TEXT NOT NULL,
                        status TEXT NOT NULL,
                        total_differences INTEGER DEFAULT 0,
                        created_at REAL NOT NULL,
                        completed_at REAL,
                        metadata TEXT
                    )
                """)
                
                # 创建差异记录表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS difference_records (
                        id TEXT PRIMARY KEY,
                        session_id TEXT NOT NULL,
                        table_id TEXT NOT NULL,
                        record_id TEXT NOT NULL,
                        diff_type TEXT NOT NULL,
                        diff_fields TEXT NOT NULL,
                        source_data TEXT NOT NULL,
                        target_data TEXT NOT NULL,
                        created_at REAL NOT NULL,
                        metadata TEXT,
                        FOREIGN KEY (session_id) REFERENCES comparison_sessions (session_id)
                    )
                """)
                
                # 创建索引以提高查询性能
                conn.execute("CREATE INDEX IF NOT EXISTS idx_session_id ON difference_records (session_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_table_id ON difference_records (table_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_diff_type ON difference_records (diff_type)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON difference_records (created_at)")
                
                conn.commit()

                # 验证表是否创建成功
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                logger.info(f"数据库表结构初始化完成，创建的表: {tables}")

                # 验证数据库文件大小
                file_size = os.path.getsize(self.db_path)
                logger.info(f"数据库文件大小: {file_size} 字节")

        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise
    
    @contextmanager
    def _get_connection(self, data_size_estimate: int = 0):
        """获取数据库连接（上下文管理器）- 智能性能优化版"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=60.0)
            conn.row_factory = sqlite3.Row  # 启用字典式访问

            # 智能SQLite性能优化配置
            self._apply_smart_sqlite_config(conn, data_size_estimate)

            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def _apply_smart_sqlite_config(self, conn, data_size_estimate=0):
        """应用智能SQLite配置"""
        try:
            # 基础配置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA temp_store=MEMORY")

            # 根据系统配置调整缓存大小
            conn.execute(f"PRAGMA cache_size={self.cache_size}")

            # 根据数据大小调整同步模式
            if data_size_estimate > 50000:
                # 大数据集：最大性能模式
                conn.execute("PRAGMA synchronous=OFF")
                conn.execute("PRAGMA wal_autocheckpoint=5000")
                logger.debug("使用大数据集优化配置")
            else:
                # 正常模式：平衡安全性和性能
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA wal_autocheckpoint=2000")

            # 内存映射大小根据系统内存调整
            mmap_size = min(self.cache_size * 10000, 1073741824)  # 最大1GB
            conn.execute(f"PRAGMA mmap_size={mmap_size}")

            # 优化查询计划
            conn.execute("PRAGMA optimize")

            logger.debug(f"SQLite配置已应用: cache_size={self.cache_size}, mmap_size={mmap_size}")

        except Exception as e:
            logger.warning(f"应用SQLite配置失败: {e}")
            # 继续使用默认配置
    
    def create_session(self, task_id: str, mode: str, metadata: Dict[str, Any] = None) -> str:
        """创建新的比对会话
        
        Args:
            task_id: 任务ID
            mode: 比对模式
            metadata: 元数据
            
        Returns:
            str: 会话ID
        """
        session_id = f"session_{uuid.uuid4().hex[:12]}"
        
        try:
            logger.debug(f"准备创建会话: session_id={session_id}, task_id={task_id}, mode={mode}")

            with self._lock:
                with self._get_connection() as conn:
                    conn.execute("""
                        INSERT INTO comparison_sessions
                        (session_id, task_id, mode, status, created_at, metadata)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        session_id, task_id, mode, "running",
                        time.time(), json.dumps(metadata) if metadata else None
                    ))
                    conn.commit()

            logger.info(f"创建比对会话: {session_id}")
            return session_id

        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            logger.error(f"会话参数: session_id={session_id}, task_id={task_id}, mode={mode}, metadata={metadata}")
            raise
    
    def batch_insert_differences(self, session_id: str, differences: List[Dict[str, Any]],
                                batch_size: int = None, progress_callback=None) -> int:
        """批量插入差异记录 - 智能性能优化版

        Args:
            session_id: 会话ID
            differences: 差异记录列表
            batch_size: 批量大小（None时自动选择）
            progress_callback: 进度回调函数 (current, total, message)

        Returns:
            int: 插入的记录数
        """
        if not differences:
            return 0

        # 智能选择批量大小
        if batch_size is None:
            batch_size = self._get_optimal_batch_size(len(differences))

        # 判断是否使用分块存储
        use_chunked = self._should_use_chunked_storage(differences)

        if use_chunked:
            logger.info(f"数据量较大，使用分块存储: {len(differences)} 条记录")
            return self._chunked_batch_insert(session_id, differences, batch_size, progress_callback)
        else:
            logger.info(f"数据量适中，使用直接存储: {len(differences)} 条记录")
            return self._direct_batch_insert(session_id, differences, batch_size, progress_callback)

    def _direct_batch_insert(self, session_id: str, differences: List[Dict[str, Any]],
                            batch_size: int, progress_callback=None) -> int:
        """直接批量插入（适用于小到中等数据集）"""
        total_inserted = 0
        start_time = time.time()

        try:
            with self._lock:
                with self._get_connection(len(differences)) as conn:
                    conn.execute("BEGIN IMMEDIATE")

                    try:
                        insert_sql = """
                            INSERT INTO difference_records
                            (id, session_id, table_id, record_id, diff_type,
                             diff_fields, source_data, target_data, created_at, metadata)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """

                        current_time = time.time()
                        records = self._prepare_batch_records(differences, session_id, current_time)

                        # 一次性插入所有记录
                        conn.executemany(insert_sql, records)
                        total_inserted = len(records)

                        # 更新会话统计
                        conn.execute("""
                            UPDATE comparison_sessions
                            SET total_differences = total_differences + ?
                            WHERE session_id = ?
                        """, (total_inserted, session_id))

                        conn.commit()
                        conn.execute("PRAGMA wal_checkpoint(FULL)")

                        # 进度回调
                        if progress_callback:
                            progress_callback(total_inserted, total_inserted, "存储完成")

                    except Exception as e:
                        conn.rollback()
                        raise

            elapsed = time.time() - start_time
            rate = total_inserted / elapsed if elapsed > 0 else 0
            self._record_performance(elapsed, total_inserted)

            logger.info(f"直接批量插入完成，会话 {session_id}，共 {total_inserted} 条记录，"
                       f"耗时 {elapsed:.2f}秒，速度: {rate:.0f} 条/秒")
            return total_inserted

        except Exception as e:
            logger.error(f"直接批量插入失败: {e}")
            raise

    def _chunked_batch_insert(self, session_id: str, differences: List[Dict[str, Any]],
                             batch_size: int, progress_callback=None) -> int:
        """分块批量插入（适用于大数据集）"""
        total_inserted = 0
        start_time = time.time()

        try:
            with self._lock:
                with self._get_connection(len(differences)) as conn:
                    conn.execute("BEGIN IMMEDIATE")

                    try:
                        insert_sql = """
                            INSERT INTO difference_records
                            (id, session_id, table_id, record_id, diff_type,
                             diff_fields, source_data, target_data, created_at, metadata)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """

                        current_time = time.time()

                        # 分块处理
                        for i in range(0, len(differences), batch_size):
                            batch = differences[i:i + batch_size]
                            records = self._prepare_batch_records(batch, session_id, current_time)

                            conn.executemany(insert_sql, records)
                            total_inserted += len(records)

                            # 进度回调
                            if progress_callback and total_inserted % 1000 == 0:
                                progress = int((total_inserted / len(differences)) * 80) + 10
                                progress_callback(total_inserted, len(differences),
                                                f"已存储 {total_inserted}/{len(differences)} 条记录")

                            # 定期触发垃圾回收
                            self._trigger_gc_if_needed()

                            # 释放批次内存
                            del batch, records

                        # 更新会话统计
                        conn.execute("""
                            UPDATE comparison_sessions
                            SET total_differences = total_differences + ?
                            WHERE session_id = ?
                        """, (total_inserted, session_id))

                        conn.commit()
                        conn.execute("PRAGMA wal_checkpoint(FULL)")

                    except Exception as e:
                        conn.rollback()
                        raise

            elapsed = time.time() - start_time
            rate = total_inserted / elapsed if elapsed > 0 else 0
            self._record_performance(elapsed, total_inserted)

            logger.info(f"分块批量插入完成，会话 {session_id}，共 {total_inserted} 条记录，"
                       f"耗时 {elapsed:.2f}秒，速度: {rate:.0f} 条/秒")
            return total_inserted

        except Exception as e:
            logger.error(f"分块批量插入失败: {e}")
            raise

    def _prepare_batch_records(self, batch: List[Dict[str, Any]], session_id: str, current_time: float) -> List[tuple]:
        """快速准备批量记录数据

        Args:
            batch: 批量数据
            session_id: 会话ID
            current_time: 当前时间戳

        Returns:
            List[tuple]: 准备好的记录元组列表
        """
        records = []

        # 使用更精确的时间戳和UUID确保唯一性
        import time
        precise_time = time.time()

        for idx, diff in enumerate(batch):
            # 生成唯一记录ID：使用UUID + 精确时间戳 + 索引
            record_id = f"diff_{uuid.uuid4().hex[:8]}_{int(precise_time * 1000000)}_{idx:06d}"

            # 快速JSON序列化（使用默认参数）
            diff_fields_json = json.dumps(diff.get('diff_fields', []), separators=(',', ':'))
            source_data_json = json.dumps(diff.get('source_data', {}), separators=(',', ':'))
            target_data_json = json.dumps(diff.get('target_data', {}), separators=(',', ':'))

            metadata = diff.get('metadata')
            metadata_json = json.dumps(metadata, separators=(',', ':')) if metadata else None

            records.append((
                record_id,
                session_id,
                diff.get('table_id', ''),
                diff.get('id', ''),
                diff.get('diff_type', ''),
                diff_fields_json,
                source_data_json,
                target_data_json,
                current_time,
                metadata_json
            ))

            # 微小延迟确保时间戳唯一性
            precise_time += 0.000001

        return records
    
    def complete_session(self, session_id: str, status: str = "completed"):
        """完成比对会话
        
        Args:
            session_id: 会话ID
            status: 最终状态
        """
        try:
            with self._lock:
                with self._get_connection() as conn:
                    conn.execute("""
                        UPDATE comparison_sessions
                        SET status = ?, completed_at = ?
                        WHERE session_id = ?
                    """, (status, time.time(), session_id))
                    conn.commit()

                    # 强制WAL检查点，确保所有数据写入主数据库文件
                    conn.execute("PRAGMA wal_checkpoint(FULL)")

                    # 验证数据是否正确写入
                    cursor = conn.execute("SELECT COUNT(*) FROM difference_records WHERE session_id = ?", (session_id,))
                    record_count = cursor.fetchone()[0]
                    logger.info(f"会话 {session_id} 验证: 共有 {record_count} 条差异记录")

            logger.info(f"会话 {session_id} 已完成，状态: {status}")
            
        except Exception as e:
            logger.error(f"完成会话失败: {e}")
            raise

    def get_differences_paginated(self, session_id: str, page: int = 1, page_size: int = 100,
                                 table_filter: str = None, diff_type_filter: str = None) -> Tuple[List[Dict[str, Any]], int]:
        """分页查询差异记录

        Args:
            session_id: 会话ID
            page: 页码（从1开始）
            page_size: 每页记录数
            table_filter: 表名过滤
            diff_type_filter: 差异类型过滤

        Returns:
            Tuple[List[Dict], int]: (差异记录列表, 总记录数)
        """
        try:
            with self._get_connection() as conn:
                # 构建查询条件
                where_conditions = ["session_id = ?"]
                params = [session_id]

                if table_filter:
                    where_conditions.append("table_id LIKE ?")
                    params.append(f"%{table_filter}%")

                if diff_type_filter:
                    where_conditions.append("diff_type = ?")
                    params.append(diff_type_filter)

                where_clause = " AND ".join(where_conditions)

                # 查询总记录数
                count_query = f"SELECT COUNT(*) FROM difference_records WHERE {where_clause}"
                total_count = conn.execute(count_query, params).fetchone()[0]

                # 分页查询
                offset = (page - 1) * page_size
                data_query = f"""
                    SELECT id, session_id, table_id, record_id, diff_type,
                           diff_fields, source_data, target_data, created_at, metadata
                    FROM difference_records
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                """

                rows = conn.execute(data_query, params + [page_size, offset]).fetchall()

                # 转换为字典格式
                differences = []
                for row in rows:
                    diff = {
                        'id': row['record_id'],
                        'table_id': row['table_id'],
                        'diff_type': row['diff_type'],
                        'diff_fields': json.loads(row['diff_fields']),
                        'source_data': json.loads(row['source_data']),
                        'target_data': json.loads(row['target_data']),
                        'created_at': row['created_at'],
                        'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                    }
                    differences.append(diff)

                logger.debug(f"分页查询完成，页码: {page}, 每页: {page_size}, 总数: {total_count}")
                return differences, total_count

        except Exception as e:
            logger.error(f"分页查询差异记录失败: {e}")
            raise

    def get_difference_by_id(self, session_id: str, record_id: str) -> Optional[Dict[str, Any]]:
        """根据记录ID获取完整差异记录

        Args:
            session_id: 会话ID
            record_id: 记录ID

        Returns:
            Optional[Dict]: 差异记录或None
        """
        try:
            with self._get_connection() as conn:
                row = conn.execute("""
                    SELECT id, session_id, table_id, record_id, diff_type,
                           diff_fields, source_data, target_data, created_at, metadata
                    FROM difference_records
                    WHERE session_id = ? AND record_id = ?
                """, (session_id, record_id)).fetchone()

                if row:
                    return {
                        'id': row['record_id'],
                        'table_id': row['table_id'],
                        'diff_type': row['diff_type'],
                        'diff_fields': json.loads(row['diff_fields']),
                        'source_data': json.loads(row['source_data']),
                        'target_data': json.loads(row['target_data']),
                        'created_at': row['created_at'],
                        'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                    }
                return None

        except Exception as e:
            logger.error(f"查询差异记录失败: {e}")
            raise

    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息

        Args:
            session_id: 会话ID

        Returns:
            Optional[Dict]: 会话信息或None
        """
        try:
            with self._get_connection() as conn:
                row = conn.execute("""
                    SELECT session_id, task_id, mode, status, total_differences,
                           created_at, completed_at, metadata
                    FROM comparison_sessions
                    WHERE session_id = ?
                """, (session_id,)).fetchone()

                if row:
                    return {
                        'session_id': row['session_id'],
                        'task_id': row['task_id'],
                        'mode': row['mode'],
                        'status': row['status'],
                        'total_differences': row['total_differences'],
                        'created_at': row['created_at'],
                        'completed_at': row['completed_at'],
                        'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                    }
                return None

        except Exception as e:
            logger.error(f"查询会话信息失败: {e}")
            raise

    def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """列出最近的会话

        Args:
            limit: 返回数量限制

        Returns:
            List[Dict]: 会话列表
        """
        try:
            with self._get_connection() as conn:
                rows = conn.execute("""
                    SELECT session_id, task_id, mode, status, total_differences,
                           created_at, completed_at, metadata
                    FROM comparison_sessions
                    ORDER BY created_at DESC
                    LIMIT ?
                """, (limit,)).fetchall()

                sessions = []
                for row in rows:
                    sessions.append({
                        'session_id': row['session_id'],
                        'task_id': row['task_id'],
                        'mode': row['mode'],
                        'status': row['status'],
                        'total_differences': row['total_differences'],
                        'created_at': row['created_at'],
                        'completed_at': row['completed_at'],
                        'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                    })

                return sessions

        except Exception as e:
            logger.error(f"列出会话失败: {e}")
            raise

    def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """清理旧的会话数据

        Args:
            days_old: 保留天数

        Returns:
            int: 删除的会话数
        """
        try:
            cutoff_time = time.time() - (days_old * 24 * 3600)

            with self._lock:
                with self._get_connection() as conn:
                    # 获取要删除的会话
                    old_sessions = conn.execute("""
                        SELECT session_id FROM comparison_sessions
                        WHERE created_at < ?
                    """, (cutoff_time,)).fetchall()

                    if not old_sessions:
                        return 0

                    session_ids = [row[0] for row in old_sessions]

                    # 删除差异记录
                    conn.execute("""
                        DELETE FROM difference_records
                        WHERE session_id IN ({})
                    """.format(','.join('?' * len(session_ids))), session_ids)

                    # 删除会话
                    conn.execute("""
                        DELETE FROM comparison_sessions
                        WHERE session_id IN ({})
                    """.format(','.join('?' * len(session_ids))), session_ids)

                    conn.commit()

                    logger.info(f"清理了 {len(session_ids)} 个旧会话")
                    return len(session_ids)

        except Exception as e:
            logger.error(f"清理旧会话失败: {e}")
            raise

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            with self._get_connection() as conn:
                # 会话统计
                session_count = conn.execute("SELECT COUNT(*) FROM comparison_sessions").fetchone()[0]

                # 差异记录统计
                diff_count = conn.execute("SELECT COUNT(*) FROM difference_records").fetchone()[0]

                # 数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0

                return {
                    'session_count': session_count,
                    'difference_count': diff_count,
                    'database_size_mb': round(db_size / (1024 * 1024), 2),
                    'database_path': self.db_path
                }

        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            raise

    def _init_performance_config(self):
        """初始化简单的性能配置"""
        try:
            import psutil

            # 获取系统内存信息
            memory_gb = psutil.virtual_memory().total / (1024**3)

            # 根据系统内存设置配置
            if memory_gb >= 8:
                # 高性能配置
                self.memory_threshold_mb = 200
                self.chunk_size = 8000
                self.cache_size = 50000  # 约200MB
                logger.info("使用高性能存储配置")
            elif memory_gb >= 4:
                # 默认配置
                self.memory_threshold_mb = 100
                self.chunk_size = 5000
                self.cache_size = 25000  # 约100MB
                logger.info("使用默认存储配置")
            else:
                # 保守配置
                self.memory_threshold_mb = 50
                self.chunk_size = 2000
                self.cache_size = 10000  # 约40MB
                logger.info("使用保守存储配置")

            # 性能监控
            self.operation_times = []
            self.gc_counter = 0

        except ImportError:
            # 如果psutil不可用，使用默认配置
            self.memory_threshold_mb = 100
            self.chunk_size = 5000
            self.cache_size = 25000
            self.operation_times = []
            self.gc_counter = 0
            logger.warning("psutil不可用，使用默认配置")

    def _estimate_memory_usage(self, differences):
        """简单估算内存使用量（MB）"""
        if not differences:
            return 0

        # 简单估算：每条记录约1KB
        estimated_mb = len(differences) * 1024 / (1024 * 1024)
        return estimated_mb

    def _should_use_chunked_storage(self, differences):
        """判断是否应该使用分块存储"""
        estimated_memory = self._estimate_memory_usage(differences)
        return estimated_memory > self.memory_threshold_mb

    def _trigger_gc_if_needed(self):
        """根据需要触发垃圾回收"""
        self.gc_counter += 1
        if self.gc_counter >= 5000:  # 每5000次操作触发一次
            import gc
            gc.collect()
            self.gc_counter = 0
            logger.debug("触发垃圾回收")

    def _get_optimal_batch_size(self, total_records):
        """获取最优批量大小"""
        if total_records < 1000:
            return min(total_records, 500)
        elif total_records < 10000:
            return min(self.chunk_size // 2, 2500)
        else:
            return self.chunk_size

    def _record_performance(self, elapsed_time, record_count):
        """记录性能数据"""
        self.operation_times.append(elapsed_time)
        # 只保留最近20次记录
        if len(self.operation_times) > 20:
            self.operation_times = self.operation_times[-20:]

        # 计算平均性能
        if len(self.operation_times) >= 5:
            avg_time = sum(self.operation_times) / len(self.operation_times)
            avg_rate = record_count / avg_time if avg_time > 0 else 0
            logger.debug(f"平均存储性能: {avg_rate:.0f} 条/秒")
