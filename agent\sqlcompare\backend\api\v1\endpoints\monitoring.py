#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控API端点
"""

import os
import sys
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import json
import asyncio
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from backend.core.database import get_db, get_task_manager
from backend.models.base import APIResponse
from backend.services.monitoring_service import MonitoringService
from reporters.sqlite_reporter import TaskManager

router = APIRouter()
logger = logging.getLogger(__name__)


def get_monitoring_service(
    db: Session = Depends(get_db),
    task_manager: TaskManager = Depends(get_task_manager)
) -> MonitoringService:
    """获取监控服务实例"""
    return MonitoringService(db, task_manager)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.task_subscribers: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # 从任务订阅中移除
        for task_id, subscribers in self.task_subscribers.items():
            if websocket in subscribers:
                subscribers.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """广播消息"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播WebSocket消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_task_update(self, task_id: str, message: str):
        """发送任务更新消息"""
        if task_id in self.task_subscribers:
            disconnected = []
            for connection in self.task_subscribers[task_id]:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"发送任务更新消息失败: {e}")
                    disconnected.append(connection)
            
            # 清理断开的连接
            for connection in disconnected:
                self.task_subscribers[task_id].remove(connection)
    
    def subscribe_task(self, task_id: str, websocket: WebSocket):
        """订阅任务更新"""
        if task_id not in self.task_subscribers:
            self.task_subscribers[task_id] = []
        self.task_subscribers[task_id].append(websocket)


# 全局连接管理器
manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 通用监控"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理不同类型的消息
            if message_data.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong", "timestamp": message_data.get("timestamp")}),
                    websocket
                )
            elif message_data.get("type") == "subscribe_task":
                task_id = message_data.get("task_id")
                if task_id:
                    manager.subscribe_task(task_id, websocket)
                    await manager.send_personal_message(
                        json.dumps({"type": "subscribed", "task_id": task_id}),
                        websocket
                    )
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket)


@router.websocket("/ws/tasks/{task_id}")
async def task_websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket端点 - 任务特定监控"""
    await manager.connect(websocket)
    manager.subscribe_task(task_id, websocket)
    
    try:
        # 发送初始任务状态
        monitoring_service = MonitoringService(None, get_task_manager())
        initial_status = await monitoring_service.get_task_real_time_status(task_id)
        if initial_status:
            await manager.send_personal_message(
                json.dumps({
                    "type": "task_status",
                    "task_id": task_id,
                    "data": initial_status
                }),
                websocket
            )
        
        while True:
            # 定期发送任务更新
            await asyncio.sleep(5)  # 每5秒更新一次
            
            current_status = await monitoring_service.get_task_real_time_status(task_id)
            if current_status:
                await manager.send_personal_message(
                    json.dumps({
                        "type": "task_update",
                        "task_id": task_id,
                        "data": current_status
                    }),
                    websocket
                )
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"任务WebSocket错误: {e}")
        manager.disconnect(websocket)


@router.get("/health", response_model=Dict[str, Any])
async def health_check(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """健康检查"""
    try:
        health_status = await monitoring_service.get_system_health()
        return health_status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", response_model=Dict[str, Any])
async def get_system_metrics(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取系统指标"""
    try:
        metrics = await monitoring_service.get_system_metrics()
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/status", response_model=Dict[str, Any])
async def get_task_real_time_status(
    task_id: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取任务实时状态"""
    try:
        status = await monitoring_service.get_task_real_time_status(task_id)
        if not status:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        return status
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/active", response_model=List[Dict[str, Any]])
async def get_active_tasks(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取活跃任务列表"""
    try:
        active_tasks = await monitoring_service.get_active_tasks()
        return active_tasks
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance", response_model=Dict[str, Any])
async def get_performance_metrics(
    time_range: Optional[str] = "1h",  # 1h, 6h, 24h, 7d
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取性能指标"""
    try:
        metrics = await monitoring_service.get_performance_metrics(time_range)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts", response_model=List[Dict[str, Any]])
async def get_system_alerts(
    severity: Optional[str] = None,  # low, medium, high, critical
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取系统告警"""
    try:
        alerts = await monitoring_service.get_system_alerts(severity)
        return alerts
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/alerts/{alert_id}/acknowledge", response_model=APIResponse)
async def acknowledge_alert(
    alert_id: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """确认告警"""
    try:
        success = await monitoring_service.acknowledge_alert(alert_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"告警 {alert_id} 不存在")
        
        return APIResponse.success_response(message="告警确认成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs", response_model=List[Dict[str, Any]])
async def get_system_logs(
    level: Optional[str] = None,  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    limit: int = 100,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取系统日志"""
    try:
        logs = await monitoring_service.get_system_logs(level, limit)
        return logs
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/logs", response_model=List[Dict[str, Any]])
async def get_task_logs(
    task_id: str,
    limit: int = 50,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取任务日志"""
    try:
        logs = await monitoring_service.get_task_logs(task_id, limit)
        return logs
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 用于发送实时更新的辅助函数
async def send_task_progress_update(task_id: str, progress_data: Dict[str, Any]):
    """发送任务进度更新"""
    message = json.dumps({
        "type": "progress_update",
        "task_id": task_id,
        "data": progress_data
    })
    await manager.send_task_update(task_id, message)


async def send_system_alert(alert_data: Dict[str, Any]):
    """发送系统告警"""
    message = json.dumps({
        "type": "system_alert",
        "data": alert_data
    })
    await manager.broadcast(message)
