"""
依赖注入模块
提供FastAPI依赖注入所需的工厂函数和单例实例。
采用单例模式确保服务实例在应用生命周期内只被创建一次。
"""
from typing import Optional
from fastapi import Depends

from app.core.task_manager import TaskManager
from app.models.logger import logger

# 全局单例实例
_task_manager: Optional[TaskManager] = None

async def initialize_services():
    """
    初始化全局服务实例

    在应用启动时调用，确保服务实例被正确初始化
    """
    global _task_manager

    logger.info("初始化全局服务实例")

    # 初始化比对引擎
    try:
        from app.core.comparison_init import initialize_comparison_engines
        initialize_comparison_engines()
        logger.info("比对引擎初始化完成")
    except Exception as e:
        logger.error(f"比对引擎初始化失败: {str(e)}")

    # 初始化任务管理器
    if _task_manager is None:
        _task_manager = TaskManager()
        await _task_manager.initialize()
        logger.info("任务管理器初始化完成")

async def get_task_manager() -> TaskManager:
    """
    获取任务管理器实例

    用于FastAPI依赖注入系统，确保返回单例实例
    """
    global _task_manager

    if _task_manager is None:
        logger.warning("任务管理器尚未初始化，正在创建新实例")
        _task_manager = TaskManager()
        await _task_manager.initialize()

    return _task_manager

# get_comparison_service 函数已移除，使用 unified_comparison_service 替代
