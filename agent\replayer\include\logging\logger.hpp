#pragma once

#include "core/data_structures.hpp"
#include <string>
#include <fstream>
#include <sstream>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>

namespace arkreplay {
namespace logging {

// ==================== 日志级别 ====================

enum class LogLevel : uint8_t {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

/**
 * 日志级别转换函数
 */
inline std::string log_level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO:  return "INFO";
        case LogLevel::WARN:  return "WARN";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

inline LogLevel string_to_log_level(const std::string& level_str) {
    if (level_str == "TRACE") return LogLevel::TRACE;
    if (level_str == "DEBUG") return LogLevel::DEBUG;
    if (level_str == "INFO")  return LogLevel::INFO;
    if (level_str == "WARN")  return LogLevel::WARN;
    if (level_str == "ERROR") return LogLevel::ERROR;
    if (level_str == "FATAL") return LogLevel::FATAL;
    return LogLevel::INFO; // 默认级别
}

// ==================== 日志记录结构 ====================

/**
 * 日志记录结构
 */
struct LogRecord {
    LogLevel level;
    core::Timestamp timestamp;
    std::string logger_name;
    std::string message;
    std::string file_name;
    int line_number;
    std::string function_name;
    std::thread::id thread_id;
    
    LogRecord(LogLevel lvl, const std::string& name, const std::string& msg,
              const std::string& file = "", int line = 0, const std::string& func = "")
        : level(lvl), timestamp(std::chrono::high_resolution_clock::now())
        , logger_name(name), message(msg), file_name(file), line_number(line)
        , function_name(func), thread_id(std::this_thread::get_id()) {}
};

// ==================== 日志格式化器 ====================

/**
 * 日志格式化器基类
 */
class LogFormatter {
public:
    virtual ~LogFormatter() = default;
    virtual std::string format(const LogRecord& record) = 0;
};

/**
 * 默认日志格式化器
 * 格式: [时间戳] [级别] [线程ID] [日志器名称] 消息 (文件:行号)
 */
class DefaultFormatter : public LogFormatter {
public:
    std::string format(const LogRecord& record) override {
        std::ostringstream oss;
        
        // 时间戳
        auto time_t = std::chrono::system_clock::to_time_t(
            std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                record.timestamp - std::chrono::high_resolution_clock::now() + 
                std::chrono::system_clock::now()));
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            record.timestamp.time_since_epoch()) % 1000;
        
        oss << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        oss << "." << std::setfill('0') << std::setw(3) << ms.count() << "] ";
        
        // 日志级别
        oss << "[" << log_level_to_string(record.level) << "] ";
        
        // 线程ID
        oss << "[" << record.thread_id << "] ";
        
        // 日志器名称
        if (!record.logger_name.empty()) {
            oss << "[" << record.logger_name << "] ";
        }
        
        // 消息
        oss << record.message;
        
        // 文件和行号
        if (!record.file_name.empty() && record.line_number > 0) {
            oss << " (" << record.file_name << ":" << record.line_number << ")";
        }
        
        return oss.str();
    }
};

/**
 * JSON格式化器
 */
class JsonFormatter : public LogFormatter {
public:
    std::string format(const LogRecord& record) override {
        std::ostringstream oss;
        oss << "{";
        oss << "\"timestamp\":\"" << std::chrono::duration_cast<std::chrono::milliseconds>(
            record.timestamp.time_since_epoch()).count() << "\",";
        oss << "\"level\":\"" << log_level_to_string(record.level) << "\",";
        oss << "\"logger\":\"" << record.logger_name << "\",";
        oss << "\"message\":\"" << escape_json(record.message) << "\",";
        oss << "\"thread\":\"" << record.thread_id << "\"";
        if (!record.file_name.empty()) {
            oss << ",\"file\":\"" << record.file_name << "\",";
            oss << "\"line\":" << record.line_number;
        }
        oss << "}";
        return oss.str();
    }

private:
    std::string escape_json(const std::string& str) {
        std::string result;
        result.reserve(str.length());
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default: result += c; break;
            }
        }
        return result;
    }
};

// ==================== 日志输出器 ====================

/**
 * 日志输出器基类
 */
class LogAppender {
public:
    virtual ~LogAppender() = default;
    virtual void append(const LogRecord& record) = 0;
    virtual void flush() = 0;
};

/**
 * 控制台输出器
 */
class ConsoleAppender : public LogAppender {
private:
    std::unique_ptr<LogFormatter> formatter_;
    mutable std::mutex console_mutex_;

public:
    explicit ConsoleAppender(std::unique_ptr<LogFormatter> formatter = std::make_unique<DefaultFormatter>())
        : formatter_(std::move(formatter)) {}
    
    void append(const LogRecord& record) override {
        std::lock_guard<std::mutex> lock(console_mutex_);
        std::string formatted = formatter_->format(record);
        
        if (record.level >= LogLevel::ERROR) {
            std::cerr << formatted << std::endl;
        } else {
            std::cout << formatted << std::endl;
        }
    }
    
    void flush() override {
        std::lock_guard<std::mutex> lock(console_mutex_);
        std::cout.flush();
        std::cerr.flush();
    }
};

/**
 * 文件输出器
 */
class FileAppender : public LogAppender {
private:
    std::unique_ptr<LogFormatter> formatter_;
    std::ofstream file_stream_;
    std::string file_path_;
    size_t max_file_size_;
    size_t max_files_;
    size_t current_file_size_;
    mutable std::mutex file_mutex_;

public:
    FileAppender(const std::string& file_path, size_t max_size = 10 * 1024 * 1024, size_t max_files = 5,
                 std::unique_ptr<LogFormatter> formatter = std::make_unique<DefaultFormatter>())
        : formatter_(std::move(formatter)), file_path_(file_path)
        , max_file_size_(max_size), max_files_(max_files), current_file_size_(0) {
        open_file();
    }
    
    ~FileAppender() {
        if (file_stream_.is_open()) {
            file_stream_.close();
        }
    }
    
    void append(const LogRecord& record) override {
        std::lock_guard<std::mutex> lock(file_mutex_);
        
        if (!file_stream_.is_open()) {
            open_file();
        }
        
        std::string formatted = formatter_->format(record);
        file_stream_ << formatted << std::endl;
        current_file_size_ += formatted.length() + 1;
        
        if (current_file_size_ >= max_file_size_) {
            rotate_files();
        }
    }
    
    void flush() override {
        std::lock_guard<std::mutex> lock(file_mutex_);
        if (file_stream_.is_open()) {
            file_stream_.flush();
        }
    }

private:
    void open_file() {
        file_stream_.open(file_path_, std::ios::app);
        if (file_stream_.is_open()) {
            file_stream_.seekp(0, std::ios::end);
            current_file_size_ = file_stream_.tellp();
        }
    }
    
    void rotate_files() {
        file_stream_.close();
        
        // 删除最老的文件
        std::string oldest_file = file_path_ + "." + std::to_string(max_files_ - 1);
        std::remove(oldest_file.c_str());
        
        // 重命名现有文件
        for (size_t i = max_files_ - 1; i > 0; --i) {
            std::string old_name = file_path_ + "." + std::to_string(i - 1);
            std::string new_name = file_path_ + "." + std::to_string(i);
            std::rename(old_name.c_str(), new_name.c_str());
        }
        
        // 重命名当前文件
        std::string backup_name = file_path_ + ".0";
        std::rename(file_path_.c_str(), backup_name.c_str());
        
        // 重新打开文件
        current_file_size_ = 0;
        open_file();
    }
};

// ==================== 异步日志器 ====================

/**
 * 异步日志器
 * 使用独立线程处理日志输出，避免阻塞主线程
 */
class AsyncLogger {
private:
    std::string logger_name_;
    LogLevel min_level_;
    std::vector<std::unique_ptr<LogAppender>> appenders_;
    
    // 异步处理
    std::queue<LogRecord> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::thread worker_thread_;
    std::atomic<bool> should_stop_{false};
    
    // 统计信息
    std::atomic<uint64_t> total_logs_{0};
    std::atomic<uint64_t> dropped_logs_{0};
    static constexpr size_t MAX_QUEUE_SIZE = 10000;

public:
    explicit AsyncLogger(const std::string& name, LogLevel min_level = LogLevel::INFO)
        : logger_name_(name), min_level_(min_level) {
        worker_thread_ = std::thread(&AsyncLogger::worker_function, this);
    }
    
    ~AsyncLogger() {
        stop();
    }
    
    // 禁用拷贝和移动
    AsyncLogger(const AsyncLogger&) = delete;
    AsyncLogger& operator=(const AsyncLogger&) = delete;
    
    /**
     * 添加输出器
     */
    void add_appender(std::unique_ptr<LogAppender> appender) {
        appenders_.push_back(std::move(appender));
    }
    
    /**
     * 设置最小日志级别
     */
    void set_min_level(LogLevel level) {
        min_level_ = level;
    }
    
    /**
     * 记录日志
     */
    void log(LogLevel level, const std::string& message, 
             const std::string& file = "", int line = 0, const std::string& func = "") {
        if (level < min_level_) return;
        
        LogRecord record(level, logger_name_, message, file, line, func);
        
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            if (log_queue_.size() >= MAX_QUEUE_SIZE) {
                dropped_logs_.fetch_add(1);
                return; // 队列已满，丢弃日志
            }
            log_queue_.push(std::move(record));
        }
        
        queue_condition_.notify_one();
        total_logs_.fetch_add(1);
    }
    
    /**
     * 便捷方法
     */
    void trace(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::TRACE, message, file, line, func);
    }
    
    void debug(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::DEBUG, message, file, line, func);
    }
    
    void info(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::INFO, message, file, line, func);
    }
    
    void warn(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::WARN, message, file, line, func);
    }
    
    void error(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::ERROR, message, file, line, func);
    }
    
    void fatal(const std::string& message, const std::string& file = "", int line = 0, const std::string& func = "") {
        log(LogLevel::FATAL, message, file, line, func);
    }
    
    /**
     * 刷新所有输出器
     */
    void flush() {
        for (auto& appender : appenders_) {
            appender->flush();
        }
    }
    
    /**
     * 获取统计信息
     */
    struct LoggerStats {
        uint64_t total_logs;
        uint64_t dropped_logs;
        size_t queue_size;
        double drop_rate;
    };
    
    LoggerStats get_statistics() const {
        LoggerStats stats;
        stats.total_logs = total_logs_.load();
        stats.dropped_logs = dropped_logs_.load();
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            stats.queue_size = log_queue_.size();
        }
        stats.drop_rate = stats.total_logs > 0 ? 
            static_cast<double>(stats.dropped_logs) / stats.total_logs * 100.0 : 0.0;
        return stats;
    }
    
    /**
     * 停止日志器
     */
    void stop() {
        should_stop_.store(true);
        queue_condition_.notify_all();
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        flush();
    }

private:
    void worker_function() {
        while (!should_stop_.load()) {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_condition_.wait(lock, [this] { 
                return !log_queue_.empty() || should_stop_.load(); 
            });
            
            while (!log_queue_.empty()) {
                LogRecord record = std::move(log_queue_.front());
                log_queue_.pop();
                lock.unlock();
                
                // 输出到所有appender
                for (auto& appender : appenders_) {
                    appender->append(record);
                }
                
                lock.lock();
            }
        }
        
        // 处理剩余的日志
        std::lock_guard<std::mutex> lock(queue_mutex_);
        while (!log_queue_.empty()) {
            LogRecord record = std::move(log_queue_.front());
            log_queue_.pop();
            for (auto& appender : appenders_) {
                appender->append(record);
            }
        }
    }
};

// ==================== 日志管理器 ====================

/**
 * 全局日志管理器
 */
class LogManager {
private:
    static std::unordered_map<std::string, std::shared_ptr<AsyncLogger>> loggers_;
    static std::mutex manager_mutex_;

public:
    static std::shared_ptr<AsyncLogger> get_logger(const std::string& name) {
        std::lock_guard<std::mutex> lock(manager_mutex_);
        auto it = loggers_.find(name);
        if (it != loggers_.end()) {
            return it->second;
        }
        
        auto logger = std::make_shared<AsyncLogger>(name);
        loggers_[name] = logger;
        return logger;
    }
    
    static void shutdown() {
        std::lock_guard<std::mutex> lock(manager_mutex_);
        for (auto& pair : loggers_) {
            pair.second->stop();
        }
        loggers_.clear();
    }
};

} // namespace logging
} // namespace arkreplay

// ==================== 日志宏定义 ====================

#define LOG_TRACE(logger, message) \
    (logger)->trace((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_DEBUG(logger, message) \
    (logger)->debug((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_INFO(logger, message) \
    (logger)->info((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_WARN(logger, message) \
    (logger)->warn((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_ERROR(logger, message) \
    (logger)->error((message), __FILE__, __LINE__, __FUNCTION__)

#define LOG_FATAL(logger, message) \
    (logger)->fatal((message), __FILE__, __LINE__, __FUNCTION__)

// 全局日志器宏
#define GLOBAL_LOGGER() \
    arkreplay::logging::LogManager::get_logger("global")

#define GLOG_TRACE(message) LOG_TRACE(GLOBAL_LOGGER(), message)
#define GLOG_DEBUG(message) LOG_DEBUG(GLOBAL_LOGGER(), message)
#define GLOG_INFO(message) LOG_INFO(GLOBAL_LOGGER(), message)
#define GLOG_WARN(message) LOG_WARN(GLOBAL_LOGGER(), message)
#define GLOG_ERROR(message) LOG_ERROR(GLOBAL_LOGGER(), message)
#define GLOG_FATAL(message) LOG_FATAL(GLOBAL_LOGGER(), message)
