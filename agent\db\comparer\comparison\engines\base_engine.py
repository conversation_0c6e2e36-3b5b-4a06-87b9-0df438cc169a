"""
比对引擎基础类
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ComparisonType(Enum):
    """比对类型枚举"""
    STRUCTURE = "structure"  # 表结构比对
    CONTENT = "content"      # 表内容比对


class DifferenceType(Enum):
    """差异类型枚举"""
    MISSING_IN_SOURCE = "missing_in_source"      # 源端缺失
    MISSING_IN_TARGET = "missing_in_target"      # 目标端缺失
    DATA_MISMATCH = "data_mismatch"              # 数据不匹配
    STRUCTURE_DIFFERENCE = "structure_difference" # 结构差异


@dataclass
class ComparisonResult:
    """比对结果数据类"""
    comparison_id: str
    comparison_type: ComparisonType
    source_info: Dict[str, Any]
    target_info: Dict[str, Any]
    total_records: int
    matched_records: int
    different_records: int
    source_only_records: int
    target_only_records: int
    differences: List[Dict[str, Any]]
    execution_time: float
    status: str
    error_message: Optional[str] = None


@dataclass
class DatabaseConnection:
    """数据库连接信息"""
    db_type: str
    host: str
    port: int
    database: str
    username: str
    password: str
    connection_string: Optional[str] = None


@dataclass
class ComparisonTask:
    """比对任务信息"""
    task_id: str
    comparison_type: ComparisonType
    source_connection: DatabaseConnection
    target_connection: DatabaseConnection
    table_name: str
    sql_rules: Optional[Dict[str, str]] = None
    batch_size: int = 1000
    timeout: int = 3600


class BaseComparisonEngine(ABC):
    """比对引擎基础类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def compare(self, task: ComparisonTask) -> ComparisonResult:
        """执行比对任务"""
        pass
    
    @abstractmethod
    def validate_task(self, task: ComparisonTask) -> Tuple[bool, str]:
        """验证比对任务"""
        pass
    
    def _create_comparison_result(self, task: ComparisonTask) -> ComparisonResult:
        """创建比对结果对象"""
        return ComparisonResult(
            comparison_id=task.task_id,
            comparison_type=task.comparison_type,
            source_info={
                "db_type": task.source_connection.db_type,
                "host": task.source_connection.host,
                "database": task.source_connection.database,
                "table": task.table_name
            },
            target_info={
                "db_type": task.target_connection.db_type,
                "host": task.target_connection.host,
                "database": task.target_connection.database,
                "table": task.table_name
            },
            total_records=0,
            matched_records=0,
            different_records=0,
            source_only_records=0,
            target_only_records=0,
            differences=[],
            execution_time=0.0,
            status="pending"
        )
    
    def _log_progress(self, message: str, task_id: str):
        """记录进度日志"""
        self.logger.info(f"[{task_id}] {message}")
    
    def _handle_error(self, error: Exception, task_id: str) -> str:
        """处理错误"""
        error_msg = f"比对任务 {task_id} 执行失败: {str(error)}"
        self.logger.error(error_msg, exc_info=True)
        return error_msg


class ComparisonEngineFactory:
    """比对引擎工厂类"""
    
    _engines = {}
    
    @classmethod
    def register_engine(cls, comparison_type: ComparisonType, engine_class):
        """注册比对引擎"""
        cls._engines[comparison_type] = engine_class
    
    @classmethod
    def create_engine(cls, comparison_type: ComparisonType, config: Optional[Dict[str, Any]] = None) -> BaseComparisonEngine:
        """创建比对引擎实例"""
        if comparison_type not in cls._engines:
            raise ValueError(f"不支持的比对类型: {comparison_type}")

        engine_class = cls._engines[comparison_type]

        # 尝试使用配置参数创建引擎，如果不支持则使用默认构造函数
        try:
            return engine_class(config=config)
        except TypeError:
            # 向后兼容：如果引擎不支持config参数，使用默认构造函数
            logger.warning(f"引擎 {engine_class.__name__} 不支持配置参数，使用默认构造函数")
            return engine_class()
    
    @classmethod
    def get_supported_types(cls) -> List[ComparisonType]:
        """获取支持的比对类型"""
        return list(cls._engines.keys())
