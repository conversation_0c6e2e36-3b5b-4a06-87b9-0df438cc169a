"""
比对引擎工厂

提供创建不同类型比对引擎的工厂方法。
"""
from typing import Dict, Any, Optional, Callable, Type

from app.models.db import DatabaseConnection
from app.models.task import CompareConfig, TaskStats
from app.core.comparison_engine import AbstractComparisonEngine
from app.core.comparison_base import DataSourceType
from app.models.logger import logger


class ComparisonEngineFactory:
    """比对引擎工厂"""

    _engine_registry: Dict[str, Type[AbstractComparisonEngine]] = {}

    @classmethod
    def register_engine(cls, source_type: str, target_type: str, engine_class: Type[AbstractComparisonEngine]) -> None:
        """
        注册比对引擎

        Args:
            source_type: 源数据类型
            target_type: 目标数据类型
            engine_class: 比对引擎类

        Returns:
            None
        """
        key = f"{source_type}:{target_type}"
        cls._engine_registry[key] = engine_class
        logger.debug(f"注册比对引擎: {key} -> {engine_class.__name__}")

    @classmethod
    def create_engine(
        cls,
        source_type: str,
        target_type: str,
        source_conn: Any,
        target_conn: Any,
        config: CompareConfig,
        progress_callback: Optional[Callable[[int, TaskStats], None]] = None
    ) -> AbstractComparisonEngine:
        """
        创建比对引擎

        Args:
            source_type: 源数据类型
            target_type: 目标数据类型
            source_conn: 源连接信息
            target_conn: 目标连接信息
            config: 比对配置
            progress_callback: 进度回调函数

        Returns:
            AbstractComparisonEngine: 比对引擎实例
        """
        # 尝试精确匹配
        key = f"{source_type}:{target_type}"
        engine_class = cls._engine_registry.get(key)

        if engine_class:
            logger.info(f"✅ 找到精确匹配的比对引擎: {key} -> {engine_class.__name__}")
            return engine_class(source_conn, target_conn, config, progress_callback)

        # 尝试通配符匹配
        wildcard_key = f"{source_type}:*"
        engine_class = cls._engine_registry.get(wildcard_key)

        if engine_class:
            logger.info(f"✅ 找到通配符匹配的比对引擎: {wildcard_key} -> {engine_class.__name__}")
            return engine_class(source_conn, target_conn, config, progress_callback)

        # 尝试默认引擎
        default_key = "*:*"
        engine_class = cls._engine_registry.get(default_key)

        if engine_class:
            logger.info(f"✅ 使用默认比对引擎: {default_key} -> {engine_class.__name__}")
            return engine_class(source_conn, target_conn, config, progress_callback)

        # 没有找到合适的引擎，记录详细错误信息
        logger.error(f"❌ 未找到合适的比对引擎")
        logger.error(f"请求的组合: {source_type} -> {target_type}")
        logger.error(f"尝试的键: [{key}, {wildcard_key}, {default_key}]")
        logger.error(f"可用的引擎组合: {list(cls._engine_registry.keys())}")

        raise ValueError(f"不支持的数据源类型组合: {source_type} -> {target_type}")

    @classmethod
    def get_supported_combinations(cls) -> Dict[str, str]:
        """
        获取支持的数据源类型组合

        Returns:
            Dict[str, str]: 数据源类型组合及对应的引擎名称
        """
        return {key: engine_class.__name__ for key, engine_class in cls._engine_registry.items()}


# 辅助函数，用于推断数据源类型
def infer_data_source_type(conn: Any) -> str:
    """
    推断数据源类型

    Args:
        conn: 连接信息

    Returns:
        str: 数据源类型
    """
    if isinstance(conn, DatabaseConnection):
        # 根据数据库类型返回具体的关系型数据库类型
        # 处理枚举类型和字符串类型
        if hasattr(conn, 'type') and conn.type:
            if hasattr(conn.type, 'value'):
                # 如果是枚举类型，获取其值
                db_type = conn.type.value.lower()
            elif hasattr(conn.type, 'lower'):
                # 如果是字符串类型，直接转换
                db_type = conn.type.lower()
            else:
                # 其他情况，转换为字符串再转小写
                db_type = str(conn.type).lower()
        else:
            db_type = ""

        logger.debug(f"推断数据源类型: 原始类型={conn.type}, 处理后类型={db_type}")

        # 映射数据库类型到DataSourceType中的常量
        if db_type == "db2":
            return DataSourceType.DB2
        elif db_type == "oracle":
            return DataSourceType.ORACLE
        elif db_type in ["gaussdb", "gauss"]:
            return DataSourceType.GAUSSDB
        elif db_type in ["sqlserver", "mssql"]:
            return DataSourceType.SQLSERVER
        elif db_type in ["postgres", "postgresql"]:
            return DataSourceType.POSTGRESQL
        elif db_type in ["mysql", "mariadb"]:
            return DataSourceType.MYSQL
        else:
            # 如果无法识别具体类型，返回默认数据库类型
            logger.warning(f"未识别的关系型数据库类型: {db_type}，使用默认类型: {DataSourceType.DEFAULT_DB}")
            return DataSourceType.DEFAULT_DB
    elif isinstance(conn, dict) and conn.get("type") == "api":
        return DataSourceType.API
    elif isinstance(conn, dict) and conn.get("type") == "file":
        return DataSourceType.FILE_SYSTEM
    elif isinstance(conn, dict) and conn.get("type") == "nosql":
        return DataSourceType.NOSQL_DB
    elif isinstance(conn, (list, dict)):
        return DataSourceType.MEMORY
    else:
        logger.warning(f"未知的数据源类型，使用默认类型: {DataSourceType.DEFAULT_DB}")
        return DataSourceType.DEFAULT_DB
