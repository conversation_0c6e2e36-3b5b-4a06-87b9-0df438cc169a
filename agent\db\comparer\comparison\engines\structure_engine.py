"""
表结构比对引擎
"""
import time
from typing import Dict, List, Any, Tuple
from sqlalchemy import create_engine, text
import asyncio

from .base_engine import (
    BaseComparisonEngine, ComparisonTask, ComparisonResult, 
    ComparisonType, DifferenceType
)
from ..connectors.database_connector import DatabaseConnectorFactory


class StructureComparisonEngine(BaseComparisonEngine):
    """表结构比对引擎"""
    
    def __init__(self):
        super().__init__()
        self.connector_factory = DatabaseConnectorFactory()
    
    async def compare(self, task: ComparisonTask) -> ComparisonResult:
        """执行表结构比对"""
        start_time = time.time()
        result = self._create_comparison_result(task)
        
        try:
            self._log_progress("开始表结构比对", task.task_id)
            
            # 验证任务
            is_valid, error_msg = self.validate_task(task)
            if not is_valid:
                result.status = "failed"
                result.error_message = error_msg
                return result
            
            # 获取源表结构
            source_structure = await self._get_table_structure(
                task.source_connection, task.table_name
            )
            
            # 获取目标表结构
            target_structure = await self._get_table_structure(
                task.target_connection, task.table_name
            )
            
            # 执行结构比对
            differences = self._compare_structures(
                source_structure, target_structure, task.task_id
            )
            
            # 统计结果
            result.total_records = len(source_structure) + len(target_structure)
            result.differences = differences
            result.different_records = len(differences)
            result.matched_records = result.total_records - result.different_records
            result.execution_time = time.time() - start_time
            result.status = "completed"
            
            self._log_progress(
                f"表结构比对完成，发现 {len(differences)} 个差异", 
                task.task_id
            )
            
        except Exception as e:
            result.status = "failed"
            result.error_message = self._handle_error(e, task.task_id)
            result.execution_time = time.time() - start_time
        
        return result
    
    def validate_task(self, task: ComparisonTask) -> Tuple[bool, str]:
        """验证比对任务"""
        if task.comparison_type != ComparisonType.STRUCTURE:
            return False, "任务类型不匹配，期望表结构比对"
        
        if not task.table_name:
            return False, "表名不能为空"
        
        # 验证数据库连接信息
        for conn_name, conn in [("源", task.source_connection), ("目标", task.target_connection)]:
            if not all([conn.host, conn.database, conn.username]):
                return False, f"{conn_name}数据库连接信息不完整"
        
        return True, ""
    
    async def _get_table_structure(self, connection, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        connector = self.connector_factory.create_connector(connection.db_type)
        
        try:
            # 建立连接
            await connector.connect(connection)
            
            # 获取表结构
            structure = await connector.get_table_structure(table_name)
            
            return structure
            
        finally:
            await connector.disconnect()
    
    def _compare_structures(self, source_structure: List[Dict], 
                          target_structure: List[Dict], task_id: str) -> List[Dict[str, Any]]:
        """比对表结构"""
        differences = []
        
        # 创建字段映射
        source_columns = {col['column_name']: col for col in source_structure}
        target_columns = {col['column_name']: col for col in target_structure}
        
        # 检查源端存在但目标端不存在的字段
        for col_name, col_info in source_columns.items():
            if col_name not in target_columns:
                differences.append({
                    'type': DifferenceType.MISSING_IN_TARGET.value,
                    'column_name': col_name,
                    'source_info': col_info,
                    'target_info': None,
                    'description': f"字段 '{col_name}' 在目标端缺失"
                })
        
        # 检查目标端存在但源端不存在的字段
        for col_name, col_info in target_columns.items():
            if col_name not in source_columns:
                differences.append({
                    'type': DifferenceType.MISSING_IN_SOURCE.value,
                    'column_name': col_name,
                    'source_info': None,
                    'target_info': col_info,
                    'description': f"字段 '{col_name}' 在源端缺失"
                })
        
        # 检查两端都存在但属性不同的字段
        for col_name in source_columns.keys() & target_columns.keys():
            source_col = source_columns[col_name]
            target_col = target_columns[col_name]
            
            field_differences = self._compare_column_attributes(source_col, target_col)
            
            if field_differences:
                differences.append({
                    'type': DifferenceType.STRUCTURE_DIFFERENCE.value,
                    'column_name': col_name,
                    'source_info': source_col,
                    'target_info': target_col,
                    'field_differences': field_differences,
                    'description': f"字段 '{col_name}' 属性不匹配: {', '.join(field_differences)}"
                })
        
        self._log_progress(f"结构比对完成，发现 {len(differences)} 个差异", task_id)
        return differences
    
    def _compare_column_attributes(self, source_col: Dict, target_col: Dict) -> List[str]:
        """比对字段属性"""
        differences = []
        
        # 比对的属性列表
        attributes_to_compare = [
            'data_type', 'is_nullable', 'column_default', 
            'character_maximum_length', 'numeric_precision', 'numeric_scale'
        ]
        
        for attr in attributes_to_compare:
            source_value = source_col.get(attr)
            target_value = target_col.get(attr)
            
            if source_value != target_value:
                differences.append(f"{attr}: {source_value} != {target_value}")
        
        return differences


# 注册结构比对引擎
from .base_engine import ComparisonEngineFactory
ComparisonEngineFactory.register_engine(ComparisonType.STRUCTURE, StructureComparisonEngine)
