#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地函数比对适配器
基于现有的本地比对服务逻辑，提供统一接口
"""

import time
from typing import Dict, Any, List

from .base_adapter import ComparisonAdapter, ErrorMapper
from .unified_models import (
    UnifiedResult, UnifiedDifference, UnifiedProgress, UnifiedError, ComparisonMode,
    ProgressCallback, create_unified_difference
)


class LocalComparisonAdapter(ComparisonAdapter):
    """本地函数比对适配器"""

    def __init__(self, app_instance, config=None):
        super().__init__(app_instance, ComparisonMode.LOCAL, config)
        self.error_mapper = LocalModeErrorMapper()

    async def execute(self, config: Dict[str, Any],
                     progress_callback: ProgressCallback) -> UnifiedResult:
        """执行本地函数比对"""
        try:
            # 检查本地服务可用性
            if not self._is_local_service_available():
                raise Exception("本地比对服务不可用")

            tables = config.get('tables', [])
            total_tables = len(tables)
            differences = []
            completed_tables = 0

            self.logger.info(f"开始本地函数比对，共 {total_tables} 个表")

            for i, table_config in enumerate(tables):
                # 检查取消请求
                if self._check_cancellation():
                    return self._handle_cancellation()

                table_id = table_config['table_id']

                # 更新进度
                progress = self._create_progress(
                    current=i + 1,
                    total=total_tables,
                    message=f"正在比对表: {table_id}",
                    stage="local_comparison"
                )
                progress_callback(progress)
                self._log_progress(progress)

                # 执行单表比对
                try:
                    table_diffs = await self._execute_local_table_comparison(table_config)
                    differences.extend(table_diffs)
                    completed_tables += 1

                    self.logger.debug(f"表 {table_id} 比对完成，发现 {len(table_diffs)} 条差异")

                except Exception as table_error:
                    self.logger.error(f"表 {table_id} 比对失败: {str(table_error)}")
                    # 继续处理其他表，不中断整个流程
                    continue

            # 创建成功结果
            return self._create_success_result(differences, total_tables, completed_tables)

        except Exception as e:
            return self._create_error_result(e)

    def _is_local_service_available(self) -> bool:
        """检查本地服务是否可用"""
        return (hasattr(self.app, 'comparison_service') and
                self.app.comparison_service is not None and
                hasattr(self.app, 'local_mode') and
                getattr(self.app, 'local_mode', False))

    async def _execute_local_table_comparison(self, table_config: Dict[str, Any]) -> List[UnifiedDifference]:
        """执行本地单表比对"""
        table_id = table_config['table_id']

        try:
            # 构建新版本比对引擎的任务
            source_config = self.app.config_manager.get_database_config('DB1')
            target_config = self.app.config_manager.get_database_config('DB2')

            # 创建比对任务
            comparison_task = self._create_comparison_task(source_config, target_config, table_config)

            # 执行比对
            result = await self._execute_comparison_engine(comparison_task)

            # 转换结果格式
            return self._convert_local_result(result, table_id)

        except Exception as e:
            self.logger.error(f"本地表比对失败 {table_id}: {str(e)}")
            raise

    def _create_comparison_task(self, source_config, target_config, table_config):
        """创建比对任务对象"""
        try:
            # 导入比对引擎模块（确保引擎注册）
            from comparison.engines import (
                ComparisonTask, ComparisonType, DatabaseConnection as EngineDBConnection
            )

            # 创建数据库连接对象
            source_conn = EngineDBConnection(
                db_type=source_config.db_type,
                host=source_config.host,
                port=source_config.port,
                username=source_config.username,
                password=source_config.password,
                database=source_config.database
            )

            target_conn = EngineDBConnection(
                db_type=target_config.db_type,
                host=target_config.host,
                port=target_config.port,
                username=target_config.username,
                password=target_config.password,
                database=target_config.database
            )

            # 创建比对任务
            task = ComparisonTask(
                task_id=f"local_{table_config['table_id']}_{int(time.time())}",
                comparison_type=ComparisonType.CONTENT,
                source_connection=source_conn,
                target_connection=target_conn,
                table_name=table_config['table_id'],
                sql_rules={
                    'sql_1': table_config['sql_1'],
                    'sql_2': table_config['sql_2']
                },
                batch_size=self.config.batch_size
            )

            return task

        except ImportError as e:
            self.logger.error(f"导入比对引擎模块失败: {str(e)}")
            raise Exception("本地比对引擎不可用")

    async def _execute_comparison_engine(self, comparison_task):
        """执行比对引擎"""
        try:
            # 导入并确保引擎注册
            from comparison.engines import ComparisonEngineFactory, verify_engine_registration

            # 验证引擎注册状态（调试模式下）
            if self.config.debug_mode:
                verify_engine_registration()

            # 创建比对引擎
            engine = ComparisonEngineFactory.create_engine(comparison_task.comparison_type)

            # 执行比对
            result = await engine.compare(comparison_task)

            return result

        except Exception as e:
            self.logger.error(f"比对引擎执行失败: {str(e)}")
            raise

    def _convert_local_result(self, result, table_id: str) -> List[UnifiedDifference]:
        """转换本地服务结果格式"""
        differences = []

        try:
            # 检查结果状态
            if not hasattr(result, 'status') or result.status != 'completed':
                error_msg = getattr(result, 'error_message', '比对未完成')
                raise Exception(f"比对失败: {error_msg}")

            # 提取差异数据
            if hasattr(result, 'differences') and result.differences:
                for diff_item in result.differences:
                    # 转换差异项格式
                    unified_diff = self._convert_difference_item(diff_item, table_id)
                    differences.append(unified_diff)

            return differences

        except Exception as e:
            self.logger.error(f"转换本地结果失败: {str(e)}")
            return []

    def _convert_difference_item(self, diff_item, table_id: str) -> UnifiedDifference:
        """转换单个差异项"""
        try:
            # 处理字典格式的差异项（从content_engine返回）
            if isinstance(diff_item, dict):
                diff_id = diff_item.get('key', '')
                source_data = diff_item.get('source_record') or {}
                target_data = diff_item.get('target_record') or {}
                diff_type_raw = diff_item.get('type', 'unknown')

                # 映射差异类型到统一格式
                diff_type_mapping = {
                    'MISSING_IN_TARGET': 'target_missing',
                    'MISSING_IN_SOURCE': 'source_missing',
                    'DATA_MISMATCH': 'difference'
                }
                diff_type = diff_type_mapping.get(diff_type_raw, 'unknown')

                # 提取差异字段
                diff_fields = []
                if 'field_differences' in diff_item:
                    diff_fields = [fd.get('field', '') for fd in diff_item['field_differences']]

            else:
                # 处理对象格式的差异项
                diff_id = getattr(diff_item, 'key', str(getattr(diff_item, 'id', '')))
                source_data = getattr(diff_item, 'source_record', {})
                target_data = getattr(diff_item, 'target_record', {})
                diff_type = getattr(diff_item, 'diff_type', 'unknown')

                # 提取差异字段
                diff_fields = []
                if hasattr(diff_item, 'field_diffs') and diff_item.field_diffs:
                    diff_fields = [fd.field for fd in diff_item.field_diffs]

                # 转换为字典格式（如果是对象）
                if hasattr(source_data, '__dict__'):
                    source_data = source_data.__dict__
                if hasattr(target_data, '__dict__'):
                    target_data = target_data.__dict__

            return create_unified_difference(
                id=str(diff_id),
                source_data=source_data or {},
                target_data=target_data or {},
                diff_fields=diff_fields,
                diff_type=diff_type,
                table_id=table_id,
                source='local_comparison'
            )

        except Exception as e:
            self.logger.error(f"转换差异项失败: {str(e)}")
            # 返回一个基本的差异记录
            return create_unified_difference(
                id=str(diff_item),
                source_data={},
                target_data={},
                diff_fields=[],
                diff_type='unknown',
                table_id=table_id,
                source='local_comparison'
            )

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        # 检查本地服务可用性
        if not self._is_local_service_available():
            self.logger.error("本地比对服务不可用")
            return False

        # 检查表配置
        if 'tables' not in config:
            self.logger.error("配置中缺少 'tables' 字段")
            return False

        tables = config['tables']
        if not isinstance(tables, list) or len(tables) == 0:
            self.logger.error("tables 必须是非空列表")
            return False

        # 验证每个表配置
        for i, table in enumerate(tables):
            required_fields = ['table_id', 'sql_1', 'sql_2']
            for field in required_fields:
                if field not in table:
                    self.logger.error(f"表配置 {i} 缺少必要字段: {field}")
                    return False
                if not table[field] or not isinstance(table[field], str):
                    self.logger.error(f"表配置 {i} 字段 {field} 不能为空")
                    return False

        # 验证数据库连接配置
        db1_config = self.app.config_manager.get_database_config('DB1')
        db2_config = self.app.config_manager.get_database_config('DB2')

        if not db1_config or not db2_config:
            self.logger.error("数据库连接配置不完整")
            return False

        return True


class LocalModeErrorMapper(ErrorMapper):
    """本地模式错误映射器"""

    @staticmethod
    def map_error(error: Exception, mode: str = "local") -> 'UnifiedError':
        """映射本地模式特定错误"""
        from .unified_models import create_unified_error

        error_msg = str(error)

        # 模块导入错误
        if "import" in error_msg.lower() or "module" in error_msg.lower():
            return create_unified_error(
                error_code="LOCAL_MODULE_ERROR",
                error_type="ImportError",
                message="本地比对模块导入失败",
                details={
                    'original_error': error_msg,
                    'mode': mode,
                    'suggestion': '请检查比对引擎模块是否正确安装'
                },
                recoverable=False
            )

        # 比对引擎错误
        elif "engine" in error_msg.lower():
            return create_unified_error(
                error_code="LOCAL_ENGINE_ERROR",
                error_type="EngineError",
                message="本地比对引擎执行失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )

        # 异步执行错误
        elif "asyncio" in error_msg.lower() or "event loop" in error_msg.lower():
            return create_unified_error(
                error_code="LOCAL_ASYNC_ERROR",
                error_type="AsyncError",
                message="本地异步执行失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )

        # 数据转换错误
        elif "convert" in error_msg.lower() or "format" in error_msg.lower():
            return create_unified_error(
                error_code="LOCAL_CONVERSION_ERROR",
                error_type="ConversionError",
                message="本地数据格式转换失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=False
            )

        else:
            # 使用基类的通用映射
            return ErrorMapper.map_error(error, mode)


# 本地服务兼容性检查器
class LocalServiceCompatibilityChecker:
    """本地服务兼容性检查器"""

    @staticmethod
    def check_compatibility() -> Dict[str, Any]:
        """检查本地服务兼容性"""
        result = {
            'compatible': False,
            'modules_available': {},
            'errors': [],
            'suggestions': []
        }

        # 检查必要模块
        required_modules = [
            'comparison.engines.base_engine',
            'backend.services.comparison_service',
            'backend.models.comparison_models'
        ]

        for i, module_name in enumerate(required_modules, 1):
            try:
                __import__(module_name)
                result['modules_available'][module_name] = True
            except ImportError as e:
                result['modules_available'][module_name] = False
                result['errors'].append(f"模块 {module_name} 导入失败: {str(e)}")

        # 检查整体兼容性
        if all(result['modules_available'].values()):
            result['compatible'] = True
        else:
            result['suggestions'].append("请确保所有必要的比对引擎模块已正确安装")
            result['suggestions'].append("可以尝试重新安装或更新比对引擎依赖")

        return result
