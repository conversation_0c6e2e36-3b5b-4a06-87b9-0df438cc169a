# DB-Agent 数据库比对微服务

## 📚 完整技术文档

本项目已完成全面的技术架构分析和文档化。详细文档请参考：

### 核心文档
- **[项目总览](docs/README.md)** - 项目概述、快速开始和使用指南
- **[系统架构](docs/architecture.md)** - 整体架构设计和技术选型分析
- **[API集成](docs/api-integration.md)** - API接口设计和与ArkReplay-Server集成
- **[任务管理](docs/task-management.md)** - 任务生命周期和异步处理机制
- **[比对算法](docs/comparison-algorithm.md)** - 多种比对算法实现和优化策略
- **[技术总结](docs/technical-summary.md)** - 完整的技术架构分析总结

### 快速导航
```
📁 docs/
├── 📄 README.md                    # 项目总览和快速开始
├── 🏗️ architecture.md              # 系统架构设计
├── 🔌 api-integration.md           # API接口和集成方式
├── ⚙️ task-management.md           # 任务管理系统
├── 🧮 comparison-algorithm.md      # 比对算法实现
└── 📊 technical-summary.md         # 技术架构总结
```

---

## 原始架构设计文档

msa/db_agent/
├── app/                     # 应用核心代码目录
│   ├── __init__.py
│   ├── main.py              # FastAPI 应用入口和全局配置
│   ├── api/                 # API 路由和端点
│   │   ├── __init__.py
│   │   ├── endpoints/       # 具体API端点的实现
│   │   │   ├── __init__.py
│   │   │   ├── connection.py # 处理 /test_connection
│   │   │   ├── comparison.py # 处理 /compare_data
│   │   │   └── tasks.py     # 处理任务相关API
│   │   └── routes.py        # 组合 API 路由
│   ├── core/                # 核心逻辑与工具
│   │   ├── __init__.py
│   │   ├── config.py        # 配置管理
│   │   ├── db_utils.py      # 数据库连接的辅助函数
│   │   ├── task_manager.py  # 任务管理系统
│   │   └── pool_manager.py  # 连接池管理
│   ├── models/              # Pydantic 模型定义
│   │   ├── __init__.py
│   │   ├── db.py            # 数据库连接相关模型
│   │   ├── comparison.py    # 数据比对相关模型
│   │   └── task.py          # 任务管理相关模型
│   └── workers/             # 后台工作处理
│       ├── __init__.py
│       └── tasks.py         # Celery/RQ任务定义
├── tests/                   # 单元测试和集成测试
├── Dockerfile               # Docker 配置文件
├── requirements.txt         # Python 依赖项
└── README.md                # 服务说明文档

## 1. 服务定位与职责 (Scope and Responsibilities)

*   **服务名称**: DB-Agent (或 DatabaseService)
*   **代码位置**: `msa/db_agent/` (假设结构)
*   **核心职责**:
    *   提供统一的API接口，用于**测试多种数据库的连接**。
    *   提供统一的API接口，用于执行**跨异构数据源的数据比对**。
    *   封装所有支持的数据库驱动程序及其复杂的本地依赖（如Oracle Instant Client, DB2 CLIDRIVER, ODBC驱动等）。
    *   与ArkReplay-Server解耦，独立部署和伸缩。
    *   支持**多任务并发**执行和资源动态分配。
*   **非职责**:
    *   不负责流量回放（由C++ Agent负责）。
    *   不直接与前端交互（由Server作为代理）。
    *   不负责长期存储比对结果（应由Server调用后存入主数据库）。

## 2. 技术选型 (Technology Stack)

*   **语言**: Python 3.x
*   **框架**: FastAPI (推荐，因其高性能、异步支持、类型提示支持、自动API文档生成)
*   **任务队列**: Celery + Redis/RabbitMQ (用于管理长时间运行的比对任务)
*   **数据库驱动**:
    *   `aiomysql` / `pymysql` (MySQL，前者支持异步)
    *   `asyncpg` / `psycopg2-binary` (PostgreSQL, GaussDB，前者支持异步)
    *   `cx_Oracle` / `oracledb` (Oracle) - 需要Oracle Instant Client
    *   `pyodbc` (SQL Server, 可选用于DB2/GaussDB等) - 需要ODBC驱动管理器和相应数据库的ODBC驱动
    *   `ibm_db` (DB2) - 需要DB2 CLIDRIVER
*   **缓存**: Redis (用于存储任务状态和临时结果)
*   **部署**: Docker + Kubernetes (推荐，用于封装所有依赖和支持弹性伸缩)

## 3. API 接口设计 (API Design)

### 3.1 数据库连接测试

*   **Endpoint**: `POST /test_connection`
*   **描述**: 测试与指定数据库的连接。
*   **请求体 (Request Body)**: JSON格式，包含连接所需的所有参数。

    ```json
    {
      "type": "mysql", // 或 "postgresql", "oracle", "sqlserver", "db2", "gaussdb"
      "host": "*************",
      "port": 3306,
      "username": "user",
      "password": "password", // 应考虑安全传输或配置管理
      "database": "mydatabase",
      "parameters": { // 可选，用于特定驱动的高级参数
        "charset": "utf8mb4", // 例如MySQL的字符集
        "service_name": "orclpdb" // 例如Oracle的服务名 (如果不用SID)
        // ... 其他特定数据库参数
      }
    }
    ```

*   **成功响应 (Success Response)**: HTTP 200 OK

    ```json
    {
      "success": true,
      "message": "连接测试成功"
    }
    ```

*   **失败响应 (Error Response)**: HTTP 400 Bad Request (参数错误或连接失败) 或 HTTP 500 Internal Server Error (服务内部错误)

    ```json
    // 连接失败示例 (400)
    {
      "success": false,
      "message": "连接测试失败: ORA-12541: TNS:no listener"
    }

    // 参数错误示例 (400)
    {
        "success": false,
        "message": "连接测试失败: 不支持的数据库类型 'unknown_db'"
    }

    // 驱动缺失示例 (400 or 500)
    {
        "success": false,
        "message": "连接测试失败: 缺少Oracle驱动程序或配置不正确"
    }
    ```

### 3.2 数据比对任务创建

*   **Endpoint**: `POST /api/v1/tasks`
*   **描述**: 创建新的数据比对任务，立即返回任务ID，异步执行比对。
*   **请求体 (Request Body)**: JSON格式，包含源和目标连接信息，以及比对配置。

    ```json
    {
      "source": { // 源数据源连接信息 (格式同 /test_connection 请求体)
        "type": "oracle",
        "host": "...",
        "port": 1521,
        "username": "...",
        "password": "...",
        "database": "...", // 或 service_name in parameters
        "parameters": {}
      },
      "target": { // 目标数据源连接信息
        "type": "postgresql",
        "host": "...",
        "port": 5432,
        "username": "...",
        "password": "...",
        "database": "...",
        "parameters": {}
      },
      "config": {
        "mode": "table", // "table" 或 "query"
        // mode="table" 时需要
        "source_table": "SCHEMA.TABLE_A",
        "target_table": "public.table_a_replica",
        "primary_keys": ["ID", "CODE"], // 联合主键用于匹配记录
        "compare_columns": ["NAME", "VALUE", "UPDATE_TIME"], // 需要比对的列，空数组或不传代表比对除主键外的所有公共列
        "ignore_columns": ["INTERNAL_ID"], // 明确忽略比对的列
        "where_clause": "STATUS = 'ACTIVE' AND CREATED_DATE > '2024-01-01'", // 可选，用于筛选比对的数据范围 (应用到源和目标)
        "limit": 10000, // 可选，限制比对的记录数
        "batch_size": 1000, // 可选，内部处理批次大小
        "priority": "normal", // 可选，任务优先级: "low", "normal", "high"
        "callback_url": "http://server-api/notify/task_completed" // 可选，任务完成后的回调URL
        // mode="query" 时需要
        // "source_query": "SELECT id, name, value FROM source_table WHERE ...",
        // "target_query": "SELECT id, name, value FROM target_table WHERE ...",
        // "primary_keys": ["id"] // 查询结果中用于匹配记录的键
      }
    }
    ```

*   **成功响应 (Success Response)**: HTTP 202 Accepted

    ```json
    {
      "task_id": "d290f1ee-6c54-4b01-90e6-d701748f0851",
      "status": "pending",
      "message": "比对任务已创建并加入队列",
      "links": {
        "status": "/api/v1/tasks/d290f1ee-6c54-4b01-90e6-d701748f0851",
        "results": "/api/v1/tasks/d290f1ee-6c54-4b01-90e6-d701748f0851/results"
      }
    }
    ```

### 3.3 任务状态查询

*   **Endpoint**: `GET /api/v1/tasks/{task_id}`
*   **描述**: 获取指定任务的状态和进度。
*   **成功响应 (Success Response)**: HTTP 200 OK

    ```json
    {
      "task_id": "d290f1ee-6c54-4b01-90e6-d701748f0851",
      "status": "running", // pending, running, completed, failed, canceled
      "progress": 45, // 百分比进度，0-100
      "stats": {
        "source_count": 5000, // 已处理的源数据记录数
        "target_count": 4980, // 已处理的目标数据记录数
        "processed_count": 2250, // 已比对的记录数
        "diff_count": 12, // 已发现的差异数
        "elapsed_time": 45, // 已执行时间(秒)
        "estimated_remaining_time": 55 // 预估剩余时间(秒)
      },
      "created_at": "2023-11-15T14:30:24Z",
      "started_at": "2023-11-15T14:30:25Z",
      "message": "正在比对数据",
      "links": {
        "results": "/api/v1/tasks/d290f1ee-6c54-4b01-90e6-d701748f0851/results"
      }
    }
    ```

### 3.4 任务结果查询

*   **Endpoint**: `GET /api/v1/tasks/{task_id}/results`
*   **描述**: 获取任务的比对结果。支持分页查询以获取详细差异。
*   **查询参数**: 
    * `page`: 页码，默认1
    * `page_size`: 每页记录数，默认50
    * `details`: 是否包含详细差异，默认false
*   **成功响应 (Success Response)**: HTTP 200 OK

    ```json
    {
      "task_id": "d290f1ee-6c54-4b01-90e6-d701748f0851",
      "status": "completed",
      "summary": {
        "source_count": 1005, // 源记录数
        "target_count": 1003, // 目标记录数
        "matched_count": 1000, // 匹配上的记录数
        "diff_count": 5,      // 值不同的记录数
        "source_only_count": 5, // 仅源存在的记录数
        "target_only_count": 3  // 仅目标存在的记录数
      },
      "completed_at": "2023-11-15T14:32:15Z",
      "execution_time": 110, // 执行时间(秒)
      "details": { // 仅当请求参数 details=true 时
        "page": 1,
        "page_size": 50,
        "total_pages": 1,
        "total_items": 8,
        "items": [
          {
            "key": {"ID": 101, "CODE": "A"}, // 记录的键值
            "diff_type": "value_diff", // "value_diff", "source_missing", "target_missing"
            "field_diffs": [ // 仅当 diff_type 为 "value_diff" 时存在
              {"field": "NAME", "source_value": "Old Name", "target_value": "New Name"},
              {"field": "VALUE", "source_value": 100, "target_value": 120}
            ]
          },
          // ... 其他差异 ...
        ]
      }
    }
    ```

### 3.5 任务控制

*   **暂停任务**: `PUT /api/v1/tasks/{task_id}/pause`
*   **恢复任务**: `PUT /api/v1/tasks/{task_id}/resume`
*   **取消任务**: `DELETE /api/v1/tasks/{task_id}`

## 4. 核心逻辑 (Core Logic)

*   **请求处理**: 使用FastAPI接收HTTP请求，使用Pydantic进行请求体验证。
*   **数据库连接管理**:
    *   根据请求中的`type`选择合适的Python驱动库。
    *   动态构建连接字符串或连接参数。
    *   使用连接池优化性能，支持异步驱动。
    *   细致的错误处理，捕获连接异常、SQL执行异常等，返回友好的错误信息。
*   **任务管理**:
    *   使用唯一ID标识每个任务。
    *   任务状态使用Redis或数据库持久化，支持服务重启后恢复。
    *   任务队列使用Celery，支持优先级排序。
    *   资源监控，确保任务不会耗尽系统资源。
*   **数据比对逻辑**:
    *   **数据获取**:
        *   根据`mode`确定是读取表还是执行查询。
        *   使用游标实现流式处理，避免一次性加载大量数据到内存。
        *   处理不同数据库在数据类型、大小写敏感性等方面的差异。
    *   **数据匹配**:
        *   基于`primary_keys`将源和目标数据进行匹配。使用字典或哈希表提高匹配效率。
        *   支持分块处理大型数据集，每次加载有限批次进行比对。
    *   **内容比较**:
        *   对匹配上的记录，逐一比较`compare_columns`中的字段值。
        *   处理不同数据库可能的数据类型差异（如数字精度、日期格式）。
    *   **差异统计与记录**: 统计各种差异类型（值不同、源缺失、目标缺失）的数量，并记录详细差异。
    *   **性能优化**: 实现批量处理、并行比对和增量结果返回。

## 5. 依赖管理与部署 (Dependencies & Deployment)

*   **Python 依赖**: 在`msa/db_agent/requirements.txt`中列出所有Python库。
*   **系统依赖**:
    *   **关键**: 需要一个包含所有数据库客户端库（Oracle Instant Client, DB2 CLIDRIVER, MySQL Client Libs, PostgreSQL Client Libs, ODBC Drivers for SQL Server等）的基础环境。
    *   **Docker是最佳实践**: 创建一个Dockerfile，基于合适的Linux基础镜像（如Debian/Ubuntu），在其中安装所有必要的系统库、ODBC驱动管理器、数据库客户端SDK，然后再安装Python和`requirements.txt`中的库。这样可以确保环境一致性，简化部署。
*   **容器资源配置**:
    *   为工作进程和API服务分别指定资源限制。
    *   支持水平扩展以处理高并发需求。
*   **支持服务**:
    *   Redis: 用于任务状态管理和Celery队列。
    *   RabbitMQ(可选): 如果需要更高可靠性的消息队列，可替代Redis作为Celery的队列后端。
*   **配置**: 服务地址、端口、资源限制等通过环境变量或配置文件管理。

## 6. 与 ArkReplay-Server 的交互 (Interaction with Server)

*   **服务发现**: Server需要知道DB-Agent的地址和端口（例如，通过环境变量`DB_AGENT_URL=http://db-agent-service:8080`）。
*   **调用模式**:
    *   **同步API**: 如连接测试，Server直接等待响应。
    *   **异步API**: 如数据比对，Server创建任务后立即返回，通过轮询或回调获取结果。
*   **任务状态更新**:
    *   **轮询模式**: Server定期查询任务状态。
    *   **回调模式**: DB-Agent在任务状态变更时主动通知Server。
*   **错误处理**: Server需要处理调用DB-Agent时可能出现的网络错误（连接超时、服务不可达）、DB-Agent返回的业务错误（4xx）和内部错误（5xx），并向前端或日志系统报告。
*   **安全性**: Server与DB-Agent之间的通信如果跨网络，应考虑使用HTTPS。对于敏感的数据库密码，应考虑更安全的传递方式（如配置中心、Secrets Management）。
*   **结果处理**: Server接收DB-Agent的响应，根据需要将结果（如比对摘要、差异详情）存储到ArkReplay的主数据库中，与测试计划或比对任务关联起来。

## 7. 高级并发设计 (Advanced Concurrency Design)

### 7.1 并发模型

*   **异步处理框架**:
    *   利用FastAPI+ASGI的高性能异步处理模式。
    *   对于IO密集型操作如数据库连接测试，使用异步IO避免阻塞。
    *   对于CPU密集型操作如大型数据比对，使用后台工作进程池。
*   **工作队列系统**:
    *   使用Celery任务队列实现任务的异步执行和状态跟踪。
    *   任务优先级管理，支持紧急任务优先处理。
    *   根据任务规模动态分配处理资源。
*   **并发限制机制**:
    *   服务级别限制：最大并发任务数控制。
    *   数据库级别限制：每个数据库源的最大并发连接数控制。
    *   资源自适应限制：基于系统负载动态调整并发度。

### 7.2 资源管理

*   **连接池设计**:
    *   按数据库类型的隔离连接池，避免不同类型数据库间的资源竞争。
    *   连接池大小动态调整：根据并发任务需求和系统资源自动伸缩。
    *   连接健康检查：定期验证池中连接的有效性，自动替换失效连接。
    *   连接超时策略：长时间空闲的连接自动释放，减少资源浪费。
*   **内存管理**:
    *   数据批处理策略：将大型数据集分割为可管理的批次，减少内存压力。
    *   流式处理：使用数据库游标(cursor)实现数据流式读取和处理。
    *   增量结果构建：逐步生成比对结果，而非一次性加载全部数据再比对。
    *   内存使用监控：实时监控任务内存使用，超过阈值时自动调整处理策略。
*   **CPU资源分配**:
    *   多进程处理：使用多个工作进程并行处理比对任务。
    *   任务分片：将大型比对任务分解为多个子任务并行执行。
    *   资源隔离：确保关键API服务不被大型比对任务影响。

### 7.3 任务生命周期管理

*   **任务状态模型**:
    *   状态定义：`pending` -> `running` -> `completed`/`failed`/`canceled`
    *   状态持久化：使用Redis存储任务状态和进度，支持服务重启后恢复。
    *   状态变更通知：通过回调或WebSocket推送状态变更。
*   **任务调度机制**:
    *   基于优先级的调度：支持任务优先级设置和队列插队。
    *   资源感知调度：根据系统负载和可用资源决定任务启动时机。
    *   公平调度：防止高优先级任务长期占用资源，导致低优先级任务饥饿。
    *   定时和周期性任务支持：支持预定时间执行和周期重复执行比对任务。
*   **任务控制能力**:
    *   暂停/恢复：支持长时间运行任务的暂停和恢复。
    *   取消：安全地取消正在执行的任务，释放相关资源。
    *   超时处理：自动检测和处理执行时间过长的任务。

### 7.4 数据并行处理

*   **数据分区比对**:
    *   水平分区：基于主键范围或哈希分区，将大表分割为多个子集并行比对。
    *   特征分区：按照数据特征(如时间段、业务类型)进行分区比对。
    *   自适应分区：根据数据分布特征动态调整分区策略。
*   **增量比对支持**:
    *   变更检测：使用时间戳、版本号等标识变更记录。
    *   差异重点比对：优先处理高变更可能性的数据区域。
    *   历史结果复用：利用先前比对结果减少重复工作。
*   **并行执行引擎**:
    *   多阶段并行：读取、匹配、比对多阶段并行流水线。
    *   动态任务分解：根据数据特征自动决定并行度。
    *   结果合并策略：高效合并多个并行比对子任务的结果。

### 7.5 高可用设计

*   **任务恢复机制**:
    *   故障检测：识别因系统崩溃、连接断开等中断的任务。
    *   自动恢复：服务重启后自动恢复中断的任务。
    *   检查点机制：定期保存比对进度，支持从中断点恢复。
*   **负载均衡**:
    *   多实例部署：支持多个DB-Agent实例共同提供服务。
    *   任务分发：根据各实例负载情况智能分发任务。
    *   会话亲和性：相关任务优先分配给同一实例，提高缓存命中率。
*   **监控与告警**:
    *   资源监控：实时监控CPU、内存、连接池使用情况。
    *   性能指标：追踪任务执行时间、等待时间等关键指标。
    *   异常报警：系统资源不足、任务异常率升高等情况自动告警。

### 7.6 扩展性设计

*   **插件化架构**:
    *   数据库适配器接口：标准化不同数据库访问方式。
    *   比对算法扩展：支持插入自定义比对算法实现特殊需求。
    *   结果处理器插件：自定义结果格式化和输出方式。
*   **服务组合**:
    *   微服务部署：API服务和工作进程可独立扩缩容。
    *   功能分解：可选择性部署连接测试、数据比对等功能模块。
    *   共享资源池：多服务实例共享连接池和缓存资源。
*   **水平扩展支持**:
    *   无状态API设计：支持API服务无缝扩展。
    *   任务分片：支持单个大型任务在多个工作节点上并行执行。
    *   动态资源分配：根据工作负载自动扩缩容。

通过以上设计，DB-Agent能够高效处理多任务并发执行，确保在有限资源条件下的最佳性能，同时保持系统的稳定性和可靠性。这种架构为处理大规模数据比对提供了强大支持，满足高并发、高吞吐量的业务需求。