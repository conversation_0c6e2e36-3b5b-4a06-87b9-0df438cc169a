#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APIClient模块
从原始app.py中提取的APIClient类，用于模块化GUI
"""

import os
import sys
import time
import threading
import logging
from typing import Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

# 添加项目根目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(SCRIPT_DIR)))
COMPARER_ROOT = os.path.join(PROJECT_ROOT, 'agent', 'db', 'comparer')
sys.path.insert(0, COMPARER_ROOT)

logger = logging.getLogger(__name__)


class APIStatus(Enum):
    """API状态枚举"""
    UNKNOWN = "unknown"
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"


@dataclass
class APIEndpoint:
    """API端点信息"""
    name: str
    url: str
    status: APIStatus = APIStatus.UNKNOWN
    response_time: Optional[float] = None
    last_check: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class ConnectionMetrics:
    """连接性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_error: Optional[str] = None
    last_success: Optional[float] = None


class APIClient:
    """HTTP API客户端
    
    从原始app.py中提取的APIClient类，支持：
    - 智能API版本检测
    - Legacy API和新版本API兼容
    - 连接池和重试机制
    - 性能监控和缓存
    """
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        try:
            import requests
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            self.base_url = base_url.rstrip('/')
            self._lock = threading.RLock()

            # 创建会话
            self.session = requests.Session()
            self.session.headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'DB-Agent-Client/1.0'
            })

            # 智能重试策略
            retry_strategy = Retry(
                total=2,  # 最多重试2次
                backoff_factor=0.5,  # 退避因子
                status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
                allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]  # 允许重试的方法
            )

            # 高性能连接池配置
            adapter = HTTPAdapter(
                pool_connections=5,   # 连接池大小
                pool_maxsize=10,      # 最大连接数
                max_retries=retry_strategy,
                pool_block=False      # 非阻塞模式
            )

            self.session.mount('http://', adapter)
            self.session.mount('https://', adapter)

            # API端点管理
            self._endpoints = {
                'v1_health': APIEndpoint('v1_health', f"{self.base_url}/api/v1/health"),
                'legacy_health': APIEndpoint('legacy_health', f"{self.base_url}/api/health"),
                'general_health': APIEndpoint('general_health', f"{self.base_url}/health")
            }

            # 缓存和性能配置
            self._cache_ttl = 300  # 5分钟缓存
            self._fast_timeout = 0.3  # 快速检测超时
            self._normal_timeout = 5  # 正常操作超时
            self._slow_timeout = 30   # 慢操作超时

            # 性能监控
            self._metrics = ConnectionMetrics()
            self._detection_in_progress = False

            # 状态管理
            self._api_version = None
            self._legacy_api_available = None
            self._backend_api_available = None
            self._detection_attempted = False
            self._detection_cache_time = None
            self._primary_endpoint = None
            self._fallback_endpoints = []

        except ImportError as e:
            logger.error(f"❌ 无法导入requests库: {e}")
            raise RuntimeError("APIClient需要requests库支持")
        except Exception as e:
            logger.error(f"❌ APIClient初始化失败: {e}")
            raise

    @property
    def api_version(self) -> str:
        """延迟获取API版本"""
        if not self._detection_attempted or self._is_cache_expired():
            self._detect_api_capabilities()
        return self._api_version or "unknown"

    @property
    def legacy_api_available(self) -> bool:
        """延迟检测Legacy API可用性"""
        if not self._detection_attempted or self._is_cache_expired():
            self._detect_api_capabilities()
        return self._legacy_api_available or False

    @property
    def backend_api_available(self) -> bool:
        """延迟检测新版本API可用性"""
        if not self._detection_attempted or self._is_cache_expired():
            self._detect_api_capabilities()
        return self._backend_api_available or False

    def _is_cache_expired(self) -> bool:
        """检查缓存是否过期"""
        if self._detection_cache_time is None:
            return True
        return time.time() - self._detection_cache_time > self._cache_ttl

    def _detect_api_capabilities(self):
        """API能力检测"""
        with self._lock:
            if self._detection_in_progress:
                return  # 避免重复检测

            if self._detection_attempted and not self._is_cache_expired():
                return

            self._detection_in_progress = True

        try:
            start_time = time.time()

            # 重置状态
            self._api_version = "unknown"
            self._legacy_api_available = False
            self._backend_api_available = False

            # 更新端点状态
            for endpoint in self._endpoints.values():
                endpoint.status = APIStatus.UNKNOWN
                endpoint.last_check = start_time

            # 先用极短超时测试基本连通性
            if not self._quick_connectivity_test():
                self._mark_all_endpoints_unavailable()
                return

            # 测试各个端点
            results = {}
            for name, endpoint in self._endpoints.items():
                try:
                    response = self.session.get(
                        endpoint.url,
                        timeout=self._normal_timeout,
                        allow_redirects=True
                    )
                    
                    response_time = time.time() - start_time
                    endpoint.response_time = response_time
                    endpoint.last_check = start_time
                    
                    if response.status_code == 200:
                        endpoint.status = APIStatus.AVAILABLE
                        results[name] = (True, response.status_code, response_time)
                    else:
                        endpoint.status = APIStatus.UNAVAILABLE
                        endpoint.error_message = f"HTTP {response.status_code}"
                        results[name] = (False, response.status_code, response_time)
                        
                except Exception as e:
                    endpoint.status = APIStatus.ERROR
                    endpoint.error_message = str(e)
                    results[name] = (False, str(e), 0)

            # 分析结果并设置API状态
            self._analyze_detection_results(results)

            detection_time = time.time() - start_time
            logger.info(f"🔍 API检测完成，耗时: {detection_time:.3f}秒")

        except Exception as e:
            logger.error(f"❌ API检测异常: {e}")
            self._mark_all_endpoints_unavailable()
        finally:
            with self._lock:
                self._detection_attempted = True
                self._detection_cache_time = time.time()
                self._detection_in_progress = False

    def _quick_connectivity_test(self) -> bool:
        """快速连通性测试"""
        try:
            response = self.session.get(
                f"{self.base_url}/",
                timeout=self._fast_timeout,
                allow_redirects=False
            )
            return True
        except Exception:
            return False

    def _mark_all_endpoints_unavailable(self):
        """标记所有端点为不可用"""
        current_time = time.time()
        for endpoint in self._endpoints.values():
            endpoint.status = APIStatus.UNAVAILABLE
            endpoint.last_check = current_time

    def _analyze_detection_results(self, results: Dict[str, Tuple]):
        """分析检测结果并设置API状态"""
        v1_result = results.get("v1_health", (False, None, 0))
        legacy_result = results.get("legacy_health", (False, None, 0))
        general_result = results.get("general_health", (False, None, 0))

        v1_available, _, v1_response_time = v1_result
        legacy_available, _, legacy_response_time = legacy_result
        general_available, _, general_response_time = general_result

        # 设置API状态
        if v1_available:
            self._api_version = "v1"
            self._backend_api_available = True
            self._primary_endpoint = self._endpoints['v1_health']
        elif legacy_available:
            self._api_version = "legacy"
            self._legacy_api_available = True
            self._primary_endpoint = self._endpoints['legacy_health']
        elif general_available:
            self._api_version = "general"
            self._primary_endpoint = self._endpoints['general_health']

        # 设置Legacy API状态（兼容性）
        if legacy_available or general_available:
            self._legacy_api_available = True

    def test_connection(self) -> bool:
        """测试API连接"""
        if self._detection_attempted and not self._is_cache_expired():
            return self.legacy_api_available or self.backend_api_available

        self._detect_api_capabilities()
        return self.legacy_api_available or self.backend_api_available

    def create_comparison_task(self, request_data: dict) -> dict:
        """创建比对任务"""
        try:
            if self.legacy_api_available:
                response = self.session.post(
                    f"{self.base_url}/api/tasks/compare",
                    json=request_data,
                    timeout=30
                )
            elif self.backend_api_available:
                response = self.session.post(
                    f"{self.base_url}/api/v1/comparison/start",
                    json=request_data,
                    timeout=30
                )
            else:
                raise Exception("没有可用的API端点")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"创建比对任务失败: {str(e)}")

    def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""
        try:
            if self.legacy_api_available:
                response = self.session.get(
                    f"{self.base_url}/api/tasks/{task_id}/status",
                    timeout=10
                )
            elif self.backend_api_available:
                response = self.session.get(
                    f"{self.base_url}/api/v1/comparison/status/{task_id}",
                    timeout=10
                )
            else:
                raise Exception("没有可用的API端点")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"获取任务状态失败: {str(e)}")

    def get_task_result(self, task_id: str, page: int = 1, page_size: int = 20) -> dict:
        """获取任务结果"""
        try:
            if self.legacy_api_available:
                response = self.session.get(
                    f"{self.base_url}/api/tasks/{task_id}/result",
                    params={'page': page, 'page_size': page_size, 'include_details': True},
                    timeout=30
                )
            elif self.backend_api_available:
                response = self.session.get(
                    f"{self.base_url}/api/v1/comparison/result/{task_id}",
                    params={'page': page, 'page_size': page_size, 'include_details': True},
                    timeout=30
                )
            else:
                raise Exception("没有可用的API端点")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"获取任务结果失败: {str(e)}")

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            if self.legacy_api_available:
                response = self.session.delete(
                    f"{self.base_url}/api/tasks/{task_id}",
                    timeout=10
                )
            elif self.backend_api_available:
                response = self.session.delete(
                    f"{self.base_url}/api/v1/comparison/task/{task_id}",
                    timeout=10
                )
            else:
                return False

            return response.status_code in [200, 204]
        except Exception:
            return False
