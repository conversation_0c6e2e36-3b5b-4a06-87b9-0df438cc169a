#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础数据模型
"""

from datetime import datetime
from typing import Optional, Any, Dict
from pydantic import BaseModel as PydanticBaseModel, Field, ConfigDict


class BaseModel(PydanticBaseModel):
    """基础Pydantic模型"""
    
    model_config = ConfigDict(
        # 允许从ORM对象创建
        from_attributes=True,
        # 使用枚举值而不是枚举名称
        use_enum_values=True,
        # 验证赋值
        validate_assignment=True,
        # 允许额外字段
        extra="forbid"
    )


class TimestampMixin(BaseModel):
    """时间戳混入类"""
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class PaginationResponse(BaseModel):
    """分页响应"""
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
    
    @classmethod
    def create(cls, total: int, page: int, size: int) -> "PaginationResponse":
        """创建分页响应"""
        pages = (total + size - 1) // size  # 向上取整
        return cls(
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )


class APIResponse(BaseModel):
    """API统一响应格式"""
    success: bool = Field(True, description="是否成功")
    message: str = Field("操作成功", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息")
    
    @classmethod
    def success_response(cls, data: Any = None, message: str = "操作成功") -> "APIResponse":
        """创建成功响应"""
        return cls(success=True, message=message, data=data)
    
    @classmethod
    def error_response(cls, message: str, error: Optional[Dict[str, Any]] = None) -> "APIResponse":
        """创建错误响应"""
        return cls(success=False, message=message, error=error)


class ListResponse(BaseModel):
    """列表响应基类"""
    items: list = Field([], description="数据列表")
    pagination: Optional[PaginationResponse] = Field(None, description="分页信息")
    
    @classmethod
    def create(cls, items: list, total: int, page: int, size: int) -> "ListResponse":
        """创建列表响应"""
        pagination = PaginationResponse.create(total, page, size)
        return cls(items=items, pagination=pagination)


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field("healthy", description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field("1.0.0", description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")
    
    
class ErrorDetail(BaseModel):
    """错误详情"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    field: Optional[str] = Field(None, description="错误字段")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


# ==================== API专用模型（主项目中不存在的） ====================

class ConnectionTestRequest(BaseModel):
    """连接测试请求模型"""
    db_type: str = Field(..., description="数据库类型")
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口号")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    database: str = Field(..., description="数据库名")
    schema: Optional[str] = Field(None, description="模式名")


class ConnectionTestResponse(BaseModel):
    """连接测试响应模型"""
    success: bool = Field(..., description="测试是否成功")
    message: str = Field(..., description="测试结果消息")
    response_time: float = Field(..., description="响应时间(毫秒)")
    database_version: Optional[str] = Field(None, description="数据库版本")
    error_details: Optional[str] = Field(None, description="错误详情")


class ConnectionUpdate(BaseModel):
    """连接更新模型"""
    name: Optional[str] = Field(None, description="连接名称")
    host: Optional[str] = Field(None, description="主机地址")
    port: Optional[int] = Field(None, description="端口号")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    database: Optional[str] = Field(None, description="数据库名")
    schema: Optional[str] = Field(None, description="模式名")
    description: Optional[str] = Field(None, description="连接描述")


class ModelUpdate(BaseModel):
    """模型更新模型"""
    name: Optional[str] = Field(None, description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    source_connid: Optional[int] = Field(None, description="源连接ID")
    target_connid: Optional[int] = Field(None, description="目标连接ID")
    cmp_type: Optional[str] = Field(None, description="比对类型")
    global_config: Optional[Dict[str, Any]] = Field(None, description="全局配置")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态消息")
    version: str = Field(..., description="版本号")
