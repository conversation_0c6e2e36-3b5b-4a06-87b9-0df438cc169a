"""
服务化SQLite适配器
将现有SQLite报告器与服务化数据模型集成
"""
import uuid
import time
from datetime import datetime
from typing import Dict, Any
from decimal import Decimal

from sqlalchemy.orm import Session
from models.sqlalchemy_models import (
    ComparisonTask, ComparisonResult,
    TaskStatus, DifferenceStatus
)
from reporters.sqlite_reporter import SqliteReporter
from core.models import DiffResult


class ServiceSqliteAdapter:
    """服务化SQLite适配器（重构版）"""

    def __init__(self, db_session: Session, task_id: str, exec_id: str = None):
        self.db_session = db_session
        self.task_id = task_id
        self.exec_id = exec_id  # 执行ID
        self.task = None
        self.execution = None  # 执行记录
        self.start_time = None
        self.processed_count = 0
        self.batch_count = 0
        
    def initialize_task(self, task_config: Dict[str, Any]) -> ComparisonTask:
        """初始化任务（重构版）"""
        # 获取任务对象
        self.task = self.db_session.query(ComparisonTask).filter(
            ComparisonTask.task_id == self.task_id
        ).first()

        if not self.task:
            raise ValueError(f"Task {self.task_id} not found")

        # 获取特定表规则的执行记录
        if self.exec_id:
            # 通过model_id关联查找执行记录
            self.execution = self.db_session.query(ComparisonTask).filter(
                ComparisonTask.task_id == self.task_id
            ).first()

            if not self.execution:
                raise ValueError(f"Task execution {self.task_id} not found")

        # 更新任务状态为运行中
        self.task.status = TaskStatus.RUNNING.value
        self.task.start_time = datetime.now()
        self.task.current_step = "初始化比对任务"
        self.start_time = time.time()

        # 如果有执行记录，也更新执行状态
        if self.execution:
            self.execution.status = TaskStatus.RUNNING.value
            self.execution.start_time = datetime.now()
            self.execution.current_step = "初始化表比对"

        # 记录开始日志
        self._log_execution("INFO", "任务开始执行", {
            "task_config": task_config,
            "exec_id": self.exec_id,
            "start_time": self.task.start_time.isoformat()
        })

        self.db_session.commit()
        return self.task
    
    def create_sqlite_reporter(self, config: Dict[str, Any]) -> SqliteReporter:
        """创建增强的SQLite报告器"""
        # 扩展配置以支持任务ID
        batch_size = 20000  # 默认值
        if self.task and hasattr(self.task, 'batch_size') and self.task.batch_size:
            batch_size = self.task.batch_size
        else:
            batch_size = config.get('batch_size', 20000)

        enhanced_config = {
            **config,
            'task_id': self.task_id,
            'silent_mode': True,  # 服务模式下静默运行
            'batch_size': batch_size
        }
        
        # 创建自定义报告器
        reporter = EnhancedSqliteReporter(enhanced_config, self)
        return reporter
    
    def update_progress(self, step_name: str, progress: float,
                       processed: int = 0, total: int = 0, message: str = ""):
        """更新任务进度（重构版）"""
        if not self.task:
            return

        # 更新任务进度
        self.task.progress_pct = Decimal(str(progress))
        self.task.current_step = step_name
        self.task.processed_records = processed
        if total > 0:
            self.task.total_records = total

        # 如果有执行记录，也更新执行进度
        if self.execution:
            self.execution.progress_pct = Decimal(str(progress))
            self.execution.current_step = step_name
            self.execution.processed_records = processed
            if total > 0:
                self.execution.total_records = total

        # 使用Python标准日志记录进度（替代数据库日志）
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"任务 {self.task_id} 进度更新: {step_name} - {progress:.1f}% ({processed}/{total}) - {message}")
    
    def complete_task(self, summary_data: Dict[str, Any]):
        """完成任务"""
        if not self.task:
            return
            
        end_time = time.time()
        execution_time = end_time - self.start_time if self.start_time else 0
        
        # 更新任务状态
        self.task.status = TaskStatus.COMPLETED.value
        self.task.complete_time = datetime.now()
        self.task.progress_pct = Decimal('100.0')
        self.task.current_step = "任务完成"
        self.task.exec_time = Decimal(str(execution_time))

        # 更新统计信息
        self.task.diff_records = summary_data.get('different_records', 0)
        self.task.source_only = summary_data.get('source_only_records', 0)
        self.task.target_only = summary_data.get('target_only_records', 0)
        
        # 更新任务统计信息（无需单独的汇总表）
        task = self.db_session.query(ComparisonTask).filter_by(task_id=self.task_id).first()
        if task:
            task.total_records = summary_data.get('total_records', 0)
            task.diff_records = summary_data.get('different_records', 0)
            task.source_only = summary_data.get('source_only_records', 0)
            task.target_only = summary_data.get('target_only_records', 0)
            task.exec_time = Decimal(str(execution_time))
            task.status = TaskStatus.COMPLETED.value
            task.complete_time = datetime.now()
        
        # 记录完成日志
        self._log_execution("INFO", "任务执行完成", {
            "execution_time": execution_time,
            "total_records": summary_data.get('total_records', 0),
            "different_records": summary_data.get('different_records', 0)
        })
        
        self.db_session.commit()
    
    def fail_task(self, error_message: str, error_details: Dict[str, Any] = None):
        """任务失败"""
        if not self.task:
            return
            
        self.task.status = TaskStatus.FAILED.value
        self.task.complete_time = datetime.now()
        self.task.error_msg = error_message
        self.task.error_details = error_details or {}
        
        # 使用Python标准日志记录错误（替代数据库日志）
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"任务 {self.task_id} 执行失败: {error_message}", extra=error_details or {})
        
        self.db_session.commit()
    
    def _log_execution(self, level: str, message: str, details: Dict[str, Any] = None):
        """记录执行日志（使用Python标准日志）"""
        import logging
        logger = logging.getLogger(__name__)

        # 根据级别选择日志方法
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(f"任务 {self.task_id}: {message}", extra=details or {})
    



class EnhancedSqliteReporter(SqliteReporter):
    """增强的SQLite报告器，支持服务化功能"""
    
    def __init__(self, config: Dict[str, Any], adapter: ServiceSqliteAdapter):
        super().__init__(config)
        self.adapter = adapter
        self.task_id = config.get('task_id')
        self.batch_count = 0
        
    def open(self):
        """打开报告器"""
        super().open()
        # 更新进度
        self.adapter.update_progress("初始化SQLite报告器", 5.0, message="数据库连接已建立")
    
    def report_diff(self, diff_result: DiffResult):
        """报告差异（扩展版本）"""
        # 调用原始方法
        super().report_diff(diff_result)
        
        # 同时写入服务化表结构
        self._write_to_service_table(diff_result)
        
        # 更新进度（每1000条记录更新一次）
        if self.total_records % 1000 == 0:
            progress = min(90.0, (self.total_records / 100000) * 80)  # 最多到90%
            self.adapter.update_progress(
                "处理比对结果", 
                progress, 
                self.total_records, 
                message=f"已处理 {self.total_records} 条差异记录"
            )
    
    def _write_to_service_table(self, diff_result: DiffResult):
        """写入服务化表结构（重构版）"""
        try:
            # 解析差异数据
            if hasattr(diff_result, 'field_diffs') and diff_result.field_diffs:
                # 字段级别差异
                for field_name, (source_value, target_value) in diff_result.field_diffs.items():
                    result = ComparisonResult(
                        task_id=self.task_id,
                        table_name=self.comparison_table,
                        record_key=diff_result.key,
                        status=DifferenceStatus.FIELD_DIFF.value,
                        field_name=field_name,
                        source_value=str(source_value) if source_value is not None else None,
                        target_value=str(target_value) if target_value is not None else None
                    )
                    self.adapter.db_session.add(result)
            else:
                # 记录级别差异
                result = ComparisonResult(
                    task_id=self.task_id,
                    table_name=self.comparison_table,
                    record_key=diff_result.key,
                    status=diff_result.status,
                    field_name=None,
                    source_value=str(diff_result.source_value) if diff_result.source_value is not None else None,
                    target_value=str(diff_result.target_value) if diff_result.target_value is not None else None
                )
                self.adapter.db_session.add(result)

            # 每批次提交一次
            if self.total_records % self.batch_size == 0:
                self.adapter.db_session.commit()
                self.batch_count += 1

        except Exception as e:
            # 记录错误但不中断主流程
            self.adapter._log_execution("WARN", f"写入服务化表失败: {str(e)}")
    
    def close(self):
        """关闭报告器"""
        # 最终提交
        try:
            self.adapter.db_session.commit()
        except Exception as e:
            self.adapter._log_execution("WARN", f"最终提交失败: {str(e)}")
        
        # 调用原始关闭方法
        super().close()
        
        # 更新进度到95%
        self.adapter.update_progress("完成数据写入", 95.0, self.total_records, 
                                   message=f"共处理 {self.total_records} 条差异记录")


def create_service_task_from_model(db_session: Session, user_id: str, model_id: int, task_data: Dict[str, Any]) -> str:
    """基于模型创建服务化任务（重构版）"""
    task_id = str(uuid.uuid4())

    # 创建主任务
    task = ComparisonTask(
        task_id=task_id,
        user_id=user_id,
        model_id=model_id,
        task_name=task_data.get('task_name')
    )

    db_session.add(task)
    db_session.flush()  # 获取task.id

    # 获取模型的表规则并创建执行记录
    from models.sqlalchemy_models import ComparisonTableRule

    rules_query = db_session.query(ComparisonTableRule).filter(
        ComparisonTableRule.model_id == model_id,
        ComparisonTableRule.is_active == True
    )

    # 如果指定了选择的表ID，进行过滤
    selected_table_ids = task_data.get('selected_table_ids')
    if selected_table_ids:
        rules_query = rules_query.filter(ComparisonTableRule.table_id.in_(selected_table_ids))

    rules = rules_query.all()

    # 为每个表规则创建ComparisonTask执行记录
    for rule in rules:
        execution = ComparisonTask(
            task_id=task_id,
            user_id=task.user_id,
            model_id=task.model_id,  # 从原任务获取模型ID
            task_name=task.task_name,
            status=TaskStatus.PENDING.value
        )
        db_session.add(execution)

    db_session.commit()
    return task_id

def create_service_task_direct(db_session: Session, user_id: str, task_data: Dict[str, Any]) -> str:
    """直接创建服务化任务（兼容原有API）"""
    from models.sqlalchemy_models import ComparisonConnection, ComparisonModel, ComparisonTableRule

    # 1. 创建临时连接配置
    source_conn = ComparisonConnection(
        name=f"临时源连接_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        type=task_data['source_conn']['db_type'],
        host=task_data['source_conn']['host'],
        port=task_data['source_conn']['port'],
        username=task_data['source_conn']['username'],
        password=task_data['source_conn']['password'],
        database=task_data['source_conn']['database'],
        parameters=task_data['source_conn'].get('parameters', {}),
        status='temp'
    )

    target_conn = ComparisonConnection(
        name=f"临时目标连接_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        type=task_data['target_conn']['db_type'],
        host=task_data['target_conn']['host'],
        port=task_data['target_conn']['port'],
        username=task_data['target_conn']['username'],
        password=task_data['target_conn']['password'],
        database=task_data['target_conn']['database'],
        parameters=task_data['target_conn'].get('parameters', {}),
        status='temp'
    )

    db_session.add(source_conn)
    db_session.add(target_conn)
    db_session.flush()

    # 2. 创建临时模型
    model = ComparisonModel(
            name=f"临时模型_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            description="通过直接API创建的临时模型",
            source_connid=source_conn.id,
            target_connid=target_conn.id,
            cmp_type=task_data.get('comparison_type', 'content'),
            status=False  # 临时模型设为不启用
        )

    db_session.add(model)
    db_session.flush()

    # 3. 创建表规则
    for rule_data in task_data['sql_rules']:
        rule = ComparisonTableRule(
            model_id=model.id,
            table_id=rule_data['table_id'],
            table_name=rule_data.get('table_name', rule_data['table_id']),
            sql_1=rule_data['sql_1'],
            sql_2=rule_data['sql_2'],
            remark=rule_data.get('remark'),
            primary_keys=rule_data.get('primary_keys'),
            ignore_fields=rule_data.get('ignore_fields'),
            field_mappings=rule_data.get('field_mappings')
        )
        db_session.add(rule)

    db_session.flush()

    # 4. 基于模型创建任务
    task_id = create_service_task_from_model(db_session, user_id, model.id, {'task_name': task_data.get('task_name')})

    return task_id
