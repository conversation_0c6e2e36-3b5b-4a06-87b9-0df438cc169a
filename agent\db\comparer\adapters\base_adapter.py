#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一适配器基类
定义所有比对适配器的通用接口和行为
"""

import time
import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

from .unified_models import (
    UnifiedResult, UnifiedDifference, UnifiedProgress, UnifiedSummary,
    UnifiedError, UnifiedConfig, ComparisonMode, ProgressCallback,
    create_unified_summary, create_unified_error
)


class ComparisonAdapter(ABC):
    """比对适配器抽象基类"""
    
    def __init__(self, app_instance, mode: ComparisonMode, config: Optional[UnifiedConfig] = None):
        self.app = app_instance
        self.mode = mode
        self.config = config or UnifiedConfig()
        self.logger = logging.getLogger(f"{__name__}.{mode.value}")
        
        # 性能监控
        self.start_time = None
        self.performance_metrics = {
            'total_queries': 0,
            'total_records_processed': 0,
            'total_differences_found': 0,
            'average_query_time': 0.0,
            'peak_memory_usage': 0
        }
    
    @abstractmethod
    async def execute(self, config: Dict[str, Any], 
                     progress_callback: ProgressCallback) -> UnifiedResult:
        """执行比对操作"""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        pass
    
    def _start_performance_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self.performance_metrics = {
            'total_queries': 0,
            'total_records_processed': 0,
            'total_differences_found': 0,
            'average_query_time': 0.0,
            'peak_memory_usage': 0
        }
    
    def _update_performance_metrics(self, **kwargs):
        """更新性能指标"""
        for key, value in kwargs.items():
            if key in self.performance_metrics:
                if key.startswith('total_'):
                    self.performance_metrics[key] += value
                elif key == 'average_query_time':
                    # 计算平均值
                    current_avg = self.performance_metrics[key]
                    total_queries = self.performance_metrics['total_queries']
                    if total_queries > 0:
                        self.performance_metrics[key] = (current_avg * (total_queries - 1) + value) / total_queries
                else:
                    self.performance_metrics[key] = max(self.performance_metrics[key], value)
    
    def _get_execution_time(self) -> float:
        """获取执行时间"""
        if self.start_time:
            return time.time() - self.start_time
        return 0.0
    
    def _create_error_result(self, error: Exception, execution_time: float = None) -> UnifiedResult:
        """创建错误结果"""
        if execution_time is None:
            execution_time = self._get_execution_time()
        
        error_msg = str(error)
        self.logger.error(f"{self.mode.value}模式执行失败: {error_msg}")
        
        return UnifiedResult(
            success=False,
            mode=self.mode.value,
            differences=[],
            summary=UnifiedSummary(
                total_tables=0,
                completed_tables=0,
                total_differences=0,
                execution_time=execution_time
            ),
            execution_time=execution_time,
            error=error_msg,
            metadata={
                'error_type': type(error).__name__,
                'performance_metrics': self.performance_metrics.copy()
            }
        )
    
    def _create_success_result(self, differences: List[UnifiedDifference], 
                             total_tables: int, completed_tables: int) -> UnifiedResult:
        """创建成功结果"""
        execution_time = self._get_execution_time()
        
        # 更新性能指标
        self._update_performance_metrics(
            total_differences_found=len(differences)
        )
        
        summary = create_unified_summary(
            total_tables=total_tables,
            completed_tables=completed_tables,
            differences=differences,
            execution_time=execution_time
        )
        
        return UnifiedResult(
            success=True,
            mode=self.mode.value,
            differences=differences,
            summary=summary,
            execution_time=execution_time,
            metadata={
                'performance_metrics': self.performance_metrics.copy(),
                'config': self.config.to_dict()
            }
        )
    
    def _calculate_eta(self, current: int, total: int, start_time: float) -> Optional[float]:
        """计算预估剩余时间"""
        if current <= 0 or total <= 0 or not self.config.enable_progress_estimation:
            return None
        
        elapsed = time.time() - start_time
        if elapsed <= 0:
            return None
        
        rate = current / elapsed
        remaining = total - current
        return remaining / rate if rate > 0 else None
    
    def _create_progress(self, current: int, total: int, message: str, 
                        stage: str, start_time: float = None) -> UnifiedProgress:
        """创建进度对象"""
        if start_time is None:
            start_time = self.start_time or time.time()
        
        percentage = round((current / total) * 100, 2) if total > 0 else 0
        eta = self._calculate_eta(current, total, start_time)
        
        return UnifiedProgress(
            current=current,
            total=total,
            percentage=percentage,
            message=message,
            stage=stage,
            estimated_remaining=eta,
            start_time=start_time
        )
    
    def _log_progress(self, progress: UnifiedProgress):
        """记录进度日志"""
        if self.config.debug_mode:
            eta_str = f", ETA: {progress.estimated_remaining:.1f}s" if progress.estimated_remaining else ""
            self.logger.debug(
                f"{self.mode.value}模式进度: {progress.percentage:.1f}% "
                f"({progress.current}/{progress.total}) - {progress.message}{eta_str}"
            )
    
    async def _safe_execute(self, config: Dict[str, Any], 
                           progress_callback: ProgressCallback) -> UnifiedResult:
        """安全执行包装器"""
        try:
            self._start_performance_monitoring()
            
            # 验证配置
            if not self.validate_config(config):
                raise ValueError(f"{self.mode.value}模式配置验证失败")
            
            # 执行比对
            result = await self.execute(config, progress_callback)
            
            # 记录成功日志
            self.logger.info(
                f"{self.mode.value}模式执行成功: "
                f"耗时 {result.execution_time:.2f}秒, "
                f"差异 {len(result.differences)}条"
            )
            
            return result
            
        except Exception as e:
            return self._create_error_result(e)
    
    def _check_cancellation(self) -> bool:
        """检查是否请求取消"""
        return getattr(self.app, 'cancel_requested', False)
    
    def _handle_cancellation(self) -> UnifiedResult:
        """处理取消请求"""
        self.logger.info(f"{self.mode.value}模式被用户取消")
        return UnifiedResult(
            success=False,
            mode=self.mode.value,
            differences=[],
            summary=UnifiedSummary(
                total_tables=0,
                completed_tables=0,
                total_differences=0,
                execution_time=self._get_execution_time()
            ),
            execution_time=self._get_execution_time(),
            error="用户取消操作",
            metadata={'cancelled': True}
        )


class ErrorMapper:
    """错误映射器基类"""
    
    @staticmethod
    def map_error(error: Exception, mode: str) -> UnifiedError:
        """映射错误到统一格式"""
        error_msg = str(error)
        error_type = type(error).__name__
        
        # 通用错误映射
        if "timeout" in error_msg.lower():
            return create_unified_error(
                error_code="TIMEOUT_ERROR",
                error_type="TimeoutError",
                message="操作超时",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )
        elif "connection" in error_msg.lower():
            return create_unified_error(
                error_code="CONNECTION_ERROR",
                error_type="ConnectionError",
                message="连接失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=True
            )
        elif "permission" in error_msg.lower() or "auth" in error_msg.lower():
            return create_unified_error(
                error_code="AUTH_ERROR",
                error_type="AuthenticationError",
                message="认证失败",
                details={'original_error': error_msg, 'mode': mode},
                recoverable=False
            )
        else:
            return create_unified_error(
                error_code="UNKNOWN_ERROR",
                error_type=error_type,
                message=error_msg,
                details={'mode': mode},
                recoverable=False
            )


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, name: str):
        """开始计时"""
        self.start_times[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if name in self.start_times:
            elapsed = time.time() - self.start_times[name]
            self.metrics[name] = elapsed
            del self.start_times[name]
            return elapsed
        return 0.0
    
    def record_metric(self, name: str, value: Any):
        """记录指标"""
        self.metrics[name] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return self.metrics.copy()
    
    def reset(self):
        """重置所有指标"""
        self.metrics.clear()
        self.start_times.clear()
