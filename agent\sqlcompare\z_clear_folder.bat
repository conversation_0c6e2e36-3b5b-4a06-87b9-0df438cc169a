@echo off
echo 正在删除所有的.pytest_cache、__pycache__ 目录和 .log 文件...
for /d /r . %%d in (.pytest_cache) do (
    echo 正在删除目录: %%d
    rd /s /q "%%d"
)
for /d /r . %%d in (__pycache__) do (
    echo 正在删除目录: %%d
    rd /s /q "%%d"
)
for /d /r . %%d in (build) do (
    echo 正在删除目录: %%d
    rd /s /q "%%d"
)
for /d /r . %%d in (dist) do (
    echo 正在删除目录: %%d
    rd /s /q "%%d"
)
for /r . %%f in (*.log) do (
    echo 正在删除文件: %%f
    del /q "%%f"
)
for /r . %%f in (*.spec) do (
    echo 正在删除文件: %%f
    del /q "%%f"
)
for /r . %%f in (*.csv) do (
    echo 正在删除文件: %%f
    del /q "%%f"
)
echo 删除完成。