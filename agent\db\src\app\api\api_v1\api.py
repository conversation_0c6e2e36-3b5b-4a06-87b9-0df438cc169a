import os
import sys
from fastapi import APIRouter, Request
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, os.path.dirname(PROJECT_DIR))
from app.api.endpoints.tasks import router as tasks_router
from app.api.endpoints.comparison import router as comparison_router
from app.api.endpoints.connection import router as connection_router

# 创建主API路由器
api_router = APIRouter()

# 新的统一路径 /api/database/connection
api_router.include_router(
    connection_router,
    prefix="/database/connection",
    tags=["连接测试"]
)

api_router.include_router(
    tasks_router,
    prefix="/tasks",
    tags=["任务管理"]
)

api_router.include_router(
    comparison_router,
    tags=["数据比对"]
)
