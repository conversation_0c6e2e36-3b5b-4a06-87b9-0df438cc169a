"""
结果管理API端点
"""
from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import FileResponse
from typing import Optional
import logging
import os
import json
import csv
import io
from datetime import datetime

from backend.services.comparison_service import ComparisonService
from backend.models.comparison_models import ComparisonResultResponse

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/{task_id}", response_model=ComparisonResultResponse)
async def get_comparison_result(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """获取比对结果"""
    try:
        result = await comparison_service.get_comparison_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="比对结果不存在")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取比对结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取比对结果失败: {str(e)}")


@router.get("/{task_id}/summary")
async def get_result_summary(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """获取比对结果摘要"""
    try:
        result = await comparison_service.get_comparison_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="比对结果不存在")
        
        # 返回摘要信息
        summary = {
            "task_id": result.task_id,
            "comparison_type": result.comparison_type,
            "total_records": result.total_records,
            "matched_records": result.matched_records,
            "different_records": result.different_records,
            "source_only_records": result.source_only_records,
            "target_only_records": result.target_only_records,
            "execution_time": result.execution_time,
            "match_rate": round(result.matched_records / result.total_records * 100, 2) if result.total_records > 0 else 0,
            "created_at": result.created_at
        }
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取结果摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取结果摘要失败: {str(e)}")


@router.get("/{task_id}/differences")
async def get_result_differences(
    task_id: str,
    limit: int = 100,
    offset: int = 0,
    difference_type: Optional[str] = None,
    comparison_service: ComparisonService = Depends()
):
    """获取比对差异详情"""
    try:
        result = await comparison_service.get_comparison_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="比对结果不存在")
        
        differences = result.differences or []
        
        # 按类型过滤
        if difference_type:
            differences = [d for d in differences if d.get('type') == difference_type]
        
        # 分页
        total = len(differences)
        paginated_differences = differences[offset:offset + limit]
        
        return {
            "differences": paginated_differences,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取差异详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取差异详情失败: {str(e)}")


@router.get("/{task_id}/export")
async def export_comparison_result(
    task_id: str,
    format: str = "csv",
    comparison_service: ComparisonService = Depends()
):
    """导出比对结果"""
    try:
        result = await comparison_service.get_comparison_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="比对结果不存在")
        
        # 根据格式导出
        if format.lower() == "json":
            return _export_as_json(result)
        elif format.lower() == "csv":
            return _export_as_csv(result)
        elif format.lower() == "html":
            return _export_as_html(result)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式，支持: json, csv, html")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出比对结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出比对结果失败: {str(e)}")


def _export_as_json(result: ComparisonResultResponse):
    """导出为JSON格式"""
    # 转换为字典
    result_dict = {
        "task_id": result.task_id,
        "comparison_type": result.comparison_type,
        "source_info": result.source_info,
        "target_info": result.target_info,
        "summary": {
            "total_records": result.total_records,
            "matched_records": result.matched_records,
            "different_records": result.different_records,
            "source_only_records": result.source_only_records,
            "target_only_records": result.target_only_records,
            "execution_time": result.execution_time
        },
        "differences": result.differences,
        "created_at": result.created_at.isoformat()
    }
    
    # 生成文件名
    filename = f"comparison_result_{result.task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # 返回JSON响应
    json_content = json.dumps(result_dict, ensure_ascii=False, indent=2)
    
    return Response(
        content=json_content,
        media_type="application/json",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


def _export_as_csv(result: ComparisonResultResponse):
    """导出为CSV格式"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入摘要信息
    writer.writerow(["比对结果摘要"])
    writer.writerow(["任务ID", result.task_id])
    writer.writerow(["比对类型", result.comparison_type])
    writer.writerow(["总记录数", result.total_records])
    writer.writerow(["匹配记录数", result.matched_records])
    writer.writerow(["差异记录数", result.different_records])
    writer.writerow(["源端独有记录数", result.source_only_records])
    writer.writerow(["目标端独有记录数", result.target_only_records])
    writer.writerow(["执行时间(秒)", result.execution_time])
    writer.writerow([])
    
    # 写入差异详情
    if result.differences:
        writer.writerow(["差异详情"])
        writer.writerow(["类型", "键值", "描述", "源端值", "目标端值"])
        
        for diff in result.differences:
            writer.writerow([
                diff.get('type', ''),
                diff.get('key', ''),
                diff.get('description', ''),
                str(diff.get('source_record', '')),
                str(diff.get('target_record', ''))
            ])
    
    # 生成文件名
    filename = f"comparison_result_{result.task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # 返回CSV响应
    csv_content = output.getvalue()
    output.close()
    
    return Response(
        content=csv_content.encode('utf-8-sig'),  # 添加BOM以支持Excel中文显示
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


def _export_as_html(result: ComparisonResultResponse):
    """导出为HTML格式"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>比对结果报告 - {result.task_id}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; }}
            .summary {{ margin: 20px 0; }}
            .summary table {{ border-collapse: collapse; width: 100%; }}
            .summary th, .summary td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .summary th {{ background-color: #f2f2f2; }}
            .differences {{ margin: 20px 0; }}
            .differences table {{ border-collapse: collapse; width: 100%; }}
            .differences th, .differences td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .differences th {{ background-color: #f2f2f2; }}
            .footer {{ margin-top: 30px; text-align: center; color: #666; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>数据比对结果报告</h1>
            <p>任务ID: {result.task_id}</p>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="summary">
            <h2>比对摘要</h2>
            <table>
                <tr><th>项目</th><th>值</th></tr>
                <tr><td>比对类型</td><td>{result.comparison_type}</td></tr>
                <tr><td>总记录数</td><td>{result.total_records}</td></tr>
                <tr><td>匹配记录数</td><td>{result.matched_records}</td></tr>
                <tr><td>差异记录数</td><td>{result.different_records}</td></tr>
                <tr><td>源端独有记录数</td><td>{result.source_only_records}</td></tr>
                <tr><td>目标端独有记录数</td><td>{result.target_only_records}</td></tr>
                <tr><td>执行时间(秒)</td><td>{result.execution_time}</td></tr>
            </table>
        </div>
        
        <div class="differences">
            <h2>差异详情</h2>
            <table>
                <tr><th>类型</th><th>键值</th><th>描述</th></tr>
    """
    
    # 添加差异详情
    if result.differences:
        for diff in result.differences[:100]:  # 限制显示前100条
            html_content += f"""
                <tr>
                    <td>{diff.get('type', '')}</td>
                    <td>{diff.get('key', '')}</td>
                    <td>{diff.get('description', '')}</td>
                </tr>
            """
    
    html_content += """
            </table>
        </div>
        
        <div class="footer">
            <p>数据比对器代理 v2.0 生成</p>
        </div>
    </body>
    </html>
    """
    
    # 生成文件名
    filename = f"comparison_result_{result.task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    return Response(
        content=html_content,
        media_type="text/html",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
