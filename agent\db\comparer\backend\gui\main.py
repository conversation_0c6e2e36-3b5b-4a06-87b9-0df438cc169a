#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的主GUI界面
支持模块化架构和动态比对模式切换
"""
import os
import sys
import time
import logging
import asyncio
import threading
import tkinter as tk
import tkinter.scrolledtext as scrolledtext
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, List, Optional
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
COMPARER_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, COMPARER_ROOT)
from config_manager import SmartConfigManager
from comparison_interface import ComparisonInterface, ComparisonConfig, ComparisonModeFactory

# 导入所有比对模式类以触发注册
from direct_comparison import DirectComparisonMode
from local_comparison import LocalComparisonMode
from api_comparison import APIComparisonMode

from async_task_runner import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorLevel, async_comparison_task, create_error_handler
from difference_storage import DifferenceStorageManager
from pagination_widget import PaginationWidget

logger = logging.getLogger(__name__)


class MainGUI:
    """重构后的主GUI界面
    
    特点：
    - 模块化架构，职责分离
    - 支持动态比对模式切换
    - 统一的进度反馈和错误处理
    - 保持与原有界面的兼容性
    """
    
    def __init__(self):
        # 启动性能优化，记录启动时间
        self._startup_start_time = time.time()
        
        # 初始化基本属性
        self._init_basic_attributes()
        
        # 创建主窗口
        self._create_main_window()
        
        # 初始化比对模式管理器
        self._init_comparison_modes()
        
        # 创建GUI组件
        self._create_widgets()
        
        # 初始化配置管理器
        self._init_config_manager()

        # 初始化差异数据存储管理器
        self._init_storage_manager()

        # 启动后台初始化
        self._start_background_initialization()
        
        # 记录启动完成时间
        startup_time = time.time() - self._startup_start_time
        self.log_message(f"🚀 GUI启动完成，耗时: {startup_time:.2f}秒")
    
    def _init_basic_attributes(self):
        """初始化基本属性"""
        # 状态管理
        self.cancel_requested = False
        self.current_task_id = None
        self.comparison_running = False

        # 比对模式管理
        self.available_modes = {}
        self.current_mode = None
        self.selected_mode_name = None

        # 配置管理
        self.config_manager = None
        self.config_file_var = None

        # 进度和状态
        self.progress_var = None
        self.progress_value = None

        # 消息缓冲区
        self._message_buffer = []
        # 异步任务管理
        self.error_handler = None

        # 结果存储
        self.comparison_results = []
        self.total_differences_count = 0

        # 差异数据存储和分页
        self.storage_manager = None
        self.current_session_id = None
        self.pagination_widget = None
    
    def _create_main_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("DB-Agent - 数据库比对工具")

        # 创建tkinter变量
        self.selected_mode_name = tk.StringVar(value="direct")
        self.config_file_var = tk.StringVar()
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_value = tk.IntVar(value=0)

        # 设置窗口大小和位置
        self._setup_window_geometry()

        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_closing)
        # 创建错误处理器
        self.error_handler = create_error_handler(self)

    def _setup_window_style(self):
        """设置窗口样式"""
        try:
            # 设置主题
            style = ttk.Style()
            style.theme_use('clam')
            
            # 自定义样式
            style.configure("Accent.TButton", foreground="white", background="#0078d4")
            style.configure("Success.TLabel", foreground="green")
            style.configure("Error.TLabel", foreground="red")
            
        except Exception as e:
            logger.warning(f"设置窗口样式失败: {e}")
    
    def _setup_window_geometry(self):
        """设置窗口几何属性"""
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 计算窗口尺寸（屏幕的80%）
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        
        # 计算窗口位置（居中）
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口几何属性
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(800, 600)
    
    def _init_comparison_modes(self):
        """初始化比对模式管理器"""
        try:
            # 注册所有比对模式（已在各模块中自动注册）
            self.available_modes = {}

            # 获取所有可用模式
            mode_names = ComparisonModeFactory.get_available_modes()
            
            for mode_name in mode_names:
                try:
                    # 创建模式实例
                    mode_instance = ComparisonModeFactory.create_mode(mode_name, self)
                    self.available_modes[mode_name] = mode_instance

                    # 设置回调函数
                    mode_instance.set_progress_callback(self._on_progress_update)
                    mode_instance.set_error_callback(self._on_error_occurred)
                    mode_instance.set_completion_callback(self._on_comparison_completed)

                except Exception as e:
                    logger.error(f"初始化比对模式 {mode_name} 失败: {e}")

            # 设置默认模式
            if "direct" in self.available_modes:
                self.current_mode = self.available_modes["direct"]
            elif self.available_modes:
                self.current_mode = list(self.available_modes.values())[0]

            logger.info(f"已初始化 {len(self.available_modes)} 个比对模式: {list(self.available_modes.keys())}")

        except Exception as e:
            logger.error(f"初始化比对模式失败: {e}")
            self.available_modes = {}
    
    def _create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏（包含比对模式选择）
        self._create_toolbar(main_frame)
        
        # 创建主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 创建左侧表列表和右侧结果区域
        self._create_main_content(content_frame)
        
        # 创建底部状态栏
        self._create_status_bar(main_frame)
    
    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))

        # 配置文件选择和比对模式选择
        config_mode_frame = ttk.Frame(toolbar)
        config_mode_frame.pack(fill=tk.X, pady=(0, 5))

        # 配置文件选择部分
        ttk.Label(config_mode_frame, text="配置文件:").pack(side=tk.LEFT, padx=(0, 5))

        config_entry = ttk.Entry(config_mode_frame, textvariable=self.config_file_var, width=55, state='readonly')
        config_entry.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(config_mode_frame, text="选择配置", command=self.select_config_file).pack(side=tk.LEFT, padx=(0, 15))

        # 比对模式选择部分
        ttk.Label(config_mode_frame, text="比对模式:").pack(side=tk.LEFT, padx=(0, 5))

        self.mode_combo = ttk.Combobox(config_mode_frame, textvariable=self.selected_mode_name,
                                 values=list(self.available_modes.keys()),
                                 state="readonly", width=12)
        self.mode_combo.pack(side=tk.LEFT, padx=(0, 5))
        self.mode_combo.bind("<<ComboboxSelected>>", self._on_mode_changed)

        # 模式状态指示器
        self.mode_status_label = ttk.Label(config_mode_frame, text="", foreground="gray")
        self.mode_status_label.pack(side=tk.LEFT, padx=(0, 5))

        # 执行控制按钮
        ttk.Button(config_mode_frame, text="开始比对", command=self.start_comparison).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_mode_frame, text="停止比对", command=self.stop_comparison).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_mode_frame, text="导出结果", command=self.export_results).pack(side=tk.LEFT, padx=(0, 5))

        # 界面控制按钮
        ttk.Button(config_mode_frame, text="重置布局", command=self.reset_layout).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(config_mode_frame, text="刷新模式", command=self.refresh_modes).pack(side=tk.LEFT, padx=(0, 5))
    
    def _create_main_content(self, parent):
        """创建主要内容区域"""
        # 创建水平分割面板
        self.main_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：表列表和控制（占40%宽度）
        left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(left_frame, weight=2)
        
        # 右侧：结果显示（占60%宽度）
        right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(right_frame, weight=3)
        
        self._create_table_list(left_frame)
        self._create_result_area(right_frame)
    
    def _create_table_list(self, parent):
        """创建表列表区域"""
        # 表列表标题
        ttk.Label(parent, text="比对表列表").pack(anchor=tk.W, pady=(0, 5))
        
        # 创建表格容器
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建表格
        columns = ("表ID", "说明", "状态")
        self.table_tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)
        
        # 设置列
        for col in columns:
            self.table_tree.heading(col, text=col)
            if col == "表ID":
                self.table_tree.column(col, width=120, minwidth=80)
            elif col == "说明":
                self.table_tree.column(col, width=200, minwidth=150)
            else:
                self.table_tree.column(col, width=80, minwidth=60)
        
        # 添加滚动条
        table_scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.table_tree.yview)
        self.table_tree.configure(yscrollcommand=table_scrollbar.set)
        
        # 布局
        self.table_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        table_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_result_area(self, parent):
        """创建结果显示区域"""
        # 创建选项卡
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 日志选项卡
        self._create_log_tab()
        
        # 比对结果选项卡
        self._create_result_tab()
    
    def _create_log_tab(self):
        """创建日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="执行日志")
        
        # 创建日志文本框
        self.log_text = tk.Text(log_frame, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 布局
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 处理缓冲的消息
        self._flush_message_buffer()
    
    def _create_result_tab(self):
        """创建比对结果选项卡"""
        result_frame = ttk.Frame(self.notebook)
        self.notebook.add(result_frame, text="比对结果")

        # 创建筛选控件区域
        self._create_filter_controls(result_frame)

        # 创建表格容器（用于放置表格和滚动条）
        table_container = ttk.Frame(result_frame)
        table_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

        # 创建结果表格（添加隐藏的完整记录列）
        result_columns = ("所属表", "记录ID", "差异类型", "差异字段", "源数据", "目标数据", "完整记录")
        self.diff_tree = ttk.Treeview(table_container, columns=result_columns, show="headings", height=20)

        # 设置列
        for i, col in enumerate(result_columns):
            self.diff_tree.heading(col, text=col)
            if col == "完整记录":
                # 隐藏完整记录列
                self.diff_tree.column(col, width=0, minwidth=0, stretch=False)
            else:
                self.diff_tree.column(col, width=120, minwidth=80)

        # 添加滚动条
        result_scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.diff_tree.yview)
        self.diff_tree.configure(yscrollcommand=result_scrollbar.set)

        # 表格和滚动条布局（水平布局）
        self.diff_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.diff_tree.bind("<Double-1>", self._on_result_double_click)

        # 创建分页控件（放置在表格容器下方）
        self.pagination_widget = PaginationWidget(result_frame, self._on_page_changed)

    def _create_filter_controls(self, parent):
        """创建筛选控件区域"""
        filter_frame = ttk.LabelFrame(parent, text="筛选条件", padding=5)
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建控件容器
        controls_frame = ttk.Frame(filter_frame)
        controls_frame.pack(fill=tk.X)

        # 表名筛选
        ttk.Label(controls_frame, text="筛选表:").pack(side=tk.LEFT, padx=(0, 5))
        self.table_filter_var = tk.StringVar(value="全部表")
        self.table_filter_combo = ttk.Combobox(controls_frame, textvariable=self.table_filter_var,
                                               width=15, state="readonly")
        self.table_filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.table_filter_combo.bind('<<ComboboxSelected>>', self._on_table_filter_changed)

        # 差异类型筛选
        ttk.Label(controls_frame, text="差异类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.diff_type_filter_var = tk.StringVar(value="全部类型")
        self.diff_type_filter_combo = ttk.Combobox(controls_frame, textvariable=self.diff_type_filter_var,
                                                   width=15, state="readonly")
        self.diff_type_filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.diff_type_filter_combo.bind('<<ComboboxSelected>>', self._on_diff_type_filter_changed)

        # 搜索框
        ttk.Label(controls_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(controls_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self._on_search_changed)

        # 清除筛选按钮
        ttk.Button(controls_frame, text="清除筛选", command=self._clear_filters).pack(side=tk.LEFT, padx=(10, 0))

        # 初始化筛选相关变量
        self.all_diff_records = []  # 存储所有差异记录
        self.filtered_records = []  # 存储筛选后的记录

    def _create_status_bar(self, parent):
        """创建状态栏"""
        # 进度条区域
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(5, 0))

        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_value, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # 进度状态标签
        self.status_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.status_label.pack(side=tk.RIGHT)

        # 分隔线
        separator = ttk.Separator(parent, orient='horizontal')
        separator.pack(fill=tk.X, pady=(2, 2))

        # 详细状态栏
        status_bar_frame = ttk.Frame(parent)
        status_bar_frame.pack(fill=tk.X, pady=(0, 2))

        # 创建状态栏变量
        self.app_status_var = tk.StringVar(value="就绪")
        self.mode_status_var = tk.StringVar(value="未选择")
        self.db_status_var = tk.StringVar(value="未连接")
        self.last_operation_var = tk.StringVar(value="无")

        # 应用状态
        ttk.Label(status_bar_frame, text="状态:").pack(side=tk.LEFT, padx=(0, 2))
        self.app_status_label = ttk.Label(status_bar_frame, textvariable=self.app_status_var,
                                         foreground="blue")
        self.app_status_label.pack(side=tk.LEFT, padx=(0, 10))

        # 比对模式状态
        ttk.Label(status_bar_frame, text="模式:").pack(side=tk.LEFT, padx=(0, 2))
        self.mode_status_display = ttk.Label(status_bar_frame, textvariable=self.mode_status_var,
                                           foreground="green")
        self.mode_status_display.pack(side=tk.LEFT, padx=(0, 10))

        # 数据库连接状态
        ttk.Label(status_bar_frame, text="数据库:").pack(side=tk.LEFT, padx=(0, 2))
        self.db_status_display = ttk.Label(status_bar_frame, textvariable=self.db_status_var,
                                         foreground="orange")
        self.db_status_display.pack(side=tk.LEFT, padx=(0, 10))

        # 最后操作时间
        ttk.Label(status_bar_frame, text="最后操作:").pack(side=tk.LEFT, padx=(0, 2))
        self.last_operation_display = ttk.Label(status_bar_frame, textvariable=self.last_operation_var,
                                              foreground="gray")
        self.last_operation_display.pack(side=tk.LEFT, padx=(0, 5))

        # 右侧信息
        # 总差异数量
        self.total_diff_var = tk.StringVar(value="差异: 0")
        ttk.Label(status_bar_frame, textvariable=self.total_diff_var,
                 foreground="red").pack(side=tk.RIGHT, padx=(5, 0))

    def _update_status_bar(self, app_status=None, mode_status=None, db_status=None, update_time=True):
        """更新状态栏信息"""
        try:
            if app_status:
                self.app_status_var.set(app_status)

            if mode_status:
                self.mode_status_var.set(mode_status)

            if db_status:
                self.db_status_var.set(db_status)

            if update_time:
                import datetime
                current_time = datetime.datetime.now().strftime("%H:%M:%S")
                self.last_operation_var.set(current_time)

            # 更新差异计数
            self.total_diff_var.set(f"差异: {self.total_differences_count}")

        except Exception as e:
            logger.error(f"更新状态栏失败: {e}")

    def _init_config_manager(self):
        """初始化配置管理器"""
        try:
            # 初始化SmartConfigManager
            self.config_manager = SmartConfigManager()
            self.log_message("✅ 配置管理器初始化成功")

            # 自动发现配置文件
            config_files = self.config_manager.auto_discover_configs()
            if config_files:
                # 自动加载第一个找到的配置文件
                if self.config_manager.load_config(config_files[0]):
                    self.config_file_var.set(config_files[0])
                    self.log_message(f"✅ 自动加载配置: {os.path.basename(config_files[0])}")
                    self._load_table_data()
                    self._update_status_bar(app_status="配置已加载", db_status="配置就绪")
                else:
                    self._update_status_bar(app_status="配置加载失败")
            else:
                self._update_status_bar(app_status="未找到配置")

        except Exception as e:
            self.log_message(f"❌ 配置管理器初始化失败: {e}")
            self.config_manager = None
            self._update_status_bar(app_status="初始化失败")

    def _init_storage_manager(self):
        """初始化差异数据存储管理器"""
        try:
            self.storage_manager = DifferenceStorageManager()
            self.log_message("✅ 差异数据存储管理器初始化完成")

            # 检查性能配置
            if hasattr(self.storage_manager, 'memory_threshold_mb'):
                self.log_message(f"📊 存储配置: 内存阈值 {self.storage_manager.memory_threshold_mb}MB, "
                               f"分块大小 {self.storage_manager.chunk_size}")

        except Exception as e:
            self.log_message(f"❌ 初始化存储管理器失败: {e}")
            # 存储管理器失败不应该阻止GUI启动
            self.storage_manager = None

    def _start_background_initialization(self):
        """启动后台初始化"""
        def background_init():
            try:
                # 检查各比对模式的可用性
                self._check_modes_availability()
                
                # 更新界面状态
                self.root.after(0, self._update_mode_status)
                
            except Exception as e:
                logger.error(f"后台初始化失败: {e}")
        
        thread = threading.Thread(target=background_init, daemon=True)
        thread.start()
    
    def _check_modes_availability(self):
        """检查比对模式可用性（完整版）"""
        try:
            # 创建事件循环来运行异步初始化
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 异步初始化所有比对模式
                for mode_name, mode_instance in self.available_modes.items():
                    try:
                        # 调用异步初始化方法
                        is_initialized = loop.run_until_complete(mode_instance.initialize())
                        mode_instance._is_available = is_initialized

                    except Exception as e:
                        logger.error(f"❌ 比对模式 {mode_name} 初始化异常: {e}")
                        mode_instance._is_available = False

                available_count = sum(1 for mode in self.available_modes.values() if getattr(mode, '_is_available', False))
                self.log_message(f"✅ 已初始化 {available_count}/{len(self.available_modes)} 个比对模式")

                # 更新下拉框的值
                self.root.after(0, self._update_mode_dropdown)

            finally:
                loop.close()

        except Exception as e:
            logger.error(f"检查模式可用性失败: {e}")
            self.log_message(f"❌ 检查模式可用性失败: {e}")

    def _update_mode_dropdown(self):
        """更新比对模式下拉框"""
        try:
            if hasattr(self, 'mode_combo') and self.mode_combo:
                # 更新下拉框的值
                self.mode_combo['values'] = list(self.available_modes.keys())
                logger.info(f"下拉框已更新，可用模式: {list(self.available_modes.keys())}")
        except Exception as e:
            logger.error(f"更新下拉框失败: {e}")
    
    def _update_mode_status(self):
        """更新模式状态显示（简化版）"""
        if self.current_mode:
            is_available = getattr(self.current_mode, '_is_available', True)
            # 使用简化的状态指示器，适应单行布局
            status_text = "✓" if is_available else "✗"
            status_color = "green" if is_available else "red"
            self.mode_status_label.config(text=status_text, foreground=status_color)
    
    # 事件处理方法
    def _on_mode_changed(self, event=None):
        """比对模式改变事件"""
        try:
            selected_mode = self.selected_mode_name.get()
            if selected_mode in self.available_modes:
                self.current_mode = self.available_modes[selected_mode]
                self._update_mode_status()
                self.log_message(f"🔄 切换到比对模式: {self.current_mode.display_name}")
                # 更新状态栏
                self._update_status_bar(mode_status=f"{selected_mode}模式")
            else:
                self.current_mode = None
                self._update_status_bar(mode_status="未选择")
        except Exception as e:
            self.log_message(f"❌ 切换比对模式失败: {e}")
            self._update_status_bar(mode_status="错误")
    
    def _on_progress_update(self, progress: int, message: str):
        """进度更新回调"""
        self.update_progress(progress, message)
    
    def _on_error_occurred(self, error_message: str):
        """错误发生回调"""
        self.log_message(f"❌ {error_message}")
    
    def _on_comparison_completed(self, result: Dict[str, Any]):
        """比对完成回调"""
        self.log_message(f"✅ 比对完成: {result.get('mode', '未知模式')}")
        self.comparison_running = False

    def _on_table_filter_changed(self, event=None):
        """表名筛选改变事件"""
        self._apply_filters()

    def _on_diff_type_filter_changed(self, event=None):
        """差异类型筛选改变事件"""
        self._apply_filters()

    def _on_search_changed(self, event=None):
        """搜索内容改变事件"""
        self._apply_filters()

    def _clear_filters(self):
        """清除所有筛选条件"""
        self.table_filter_var.set("全部表")
        self.diff_type_filter_var.set("全部类型")
        self.search_var.set("")
        self._apply_filters()

    def _apply_filters(self):
        """应用筛选条件"""
        try:
            if not hasattr(self, 'all_diff_records') or not self.all_diff_records:
                self.filtered_records = []
                self._update_pagination_after_filter()
                return

            # 获取筛选条件
            table_filter = self.table_filter_var.get()
            diff_type_filter = self.diff_type_filter_var.get()
            search_text = self.search_var.get().strip().lower()

            # 应用筛选
            self.filtered_records = []
            for record in self.all_diff_records:
                # 表筛选
                if table_filter != "全部表" and record.get('table_id', '') != table_filter:
                    continue

                # 差异类型筛选
                if diff_type_filter != "全部类型" and record.get('diff_type', '') != diff_type_filter:
                    continue

                # 搜索筛选
                if search_text:
                    searchable_text = f"{record.get('id', '')} {record.get('diff_fields', [])} {record.get('source_data', {})} {record.get('target_data', {})}".lower()
                    if search_text not in searchable_text:
                        continue

                self.filtered_records.append(record)

            # 更新分页显示
            self._update_pagination_after_filter()

        except Exception as e:
            logger.error(f"应用筛选失败: {e}")

    def _update_pagination_after_filter(self):
        """筛选后更新分页显示"""
        if hasattr(self, 'pagination_widget') and self.pagination_widget:
            # 重置到第一页并更新数据
            self.pagination_widget.reset_to_first_page()
            self.pagination_widget.update_data(self.filtered_records)

    def _update_filter_options(self):
        """更新筛选选项"""
        try:
            if not hasattr(self, 'all_diff_records'):
                return

            # 收集所有表名和差异类型
            table_ids = set()
            diff_types = set()

            for record in self.all_diff_records:
                table_ids.add(record.get('table_id', ''))
                diff_types.add(record.get('diff_type', ''))

            # 更新表筛选选项
            current_table_filter = self.table_filter_var.get()
            table_options = ["全部表"] + sorted(list(table_ids))
            self.table_filter_combo['values'] = table_options
            if current_table_filter not in table_options:
                self.table_filter_var.set("全部表")

            # 更新差异类型筛选选项
            current_diff_type_filter = self.diff_type_filter_var.get()
            diff_type_options = ["全部类型"] + sorted(list(diff_types))
            self.diff_type_filter_combo['values'] = diff_type_options
            if current_diff_type_filter not in diff_type_options:
                self.diff_type_filter_var.set("全部类型")

        except Exception as e:
            logger.error(f"更新筛选选项失败: {e}")

    def _on_result_double_click(self, event):
        """结果双击事件 - 增强版本"""
        try:
            selection = self.diff_tree.selection()
            if not selection:
                self.log_message("⚠️ 没有选中任何记录")
                return

            # 获取选中的记录
            item = selection[0]
            item_values = self.diff_tree.item(item)['values']

            # 检查是否有足够的列数据（现在有7列：所属表、记录ID、差异类型、差异字段、源数据、目标数据、完整记录）
            if len(item_values) < 7:
                self.log_message(f"❌ 记录数据不完整，列数: {len(item_values)}")
                messagebox.showerror("错误", "记录数据不完整，无法查看详细信息")
                return

            # 获取完整记录数据（在第7列，索引为6）
            full_record_json = item_values[6]

            if not full_record_json or full_record_json.strip() == "":
                self.log_message("❌ 完整记录数据为空")
                messagebox.showerror("错误", "无法获取记录详细信息")
                return

            # 解析JSON数据
            try:
                import json
                record = json.loads(full_record_json)
                self.log_message(f"✅ 成功解析记录数据: {record.get('id', 'unknown')}")
            except json.JSONDecodeError as json_error:
                self.log_message(f"❌ JSON解析失败: {str(json_error)}")
                self.log_message(f"原始数据: {full_record_json[:200]}...")
                messagebox.showerror("错误", f"记录数据格式错误: {str(json_error)}")
                return

            # 创建详细信息窗口
            self._show_detail_window(record)

        except Exception as e:
            error_msg = f"查看详细信息失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", error_msg)

    def _on_page_changed(self, page: int, page_size: int):
        """分页变化回调 - 支持筛选数据"""
        try:
            # 使用筛选后的数据进行分页
            if hasattr(self, 'filtered_records') and self.filtered_records:
                self._display_filtered_page(page, page_size)
            elif self.current_session_id and self.storage_manager:
                self._display_storage_page(page, page_size)
            else:
                self.log_message("⚠️ 没有可用的数据")
                return

        except Exception as e:
            self.log_message(f"❌ 加载分页数据失败: {e}")
            if hasattr(self, 'pagination_widget'):
                self.pagination_widget.set_loading_state(False)
            messagebox.showerror("错误", f"加载数据失败: {e}")

    def _display_filtered_page(self, page: int, page_size: int):
        """显示筛选后数据的指定页面"""
        try:
            self.log_message(f"📄 显示筛选数据第{page}页，每页{page_size}条")

            # 设置加载状态
            self.pagination_widget.set_loading_state(True)

            # 计算分页范围
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            page_data = self.filtered_records[start_index:end_index]

            # 清空现有结果
            for item in self.diff_tree.get_children():
                self.diff_tree.delete(item)

            # 显示分页数据
            self._display_paginated_differences(page_data)

            # 更新分页信息
            total_count = len(self.filtered_records)
            self.pagination_widget.update_pagination_info(page, total_count, page_size)

            # 取消加载状态
            self.pagination_widget.set_loading_state(False)

            self.log_message(f"✅ 筛选数据第{page}页加载完成，共{len(page_data)}条记录")

        except Exception as e:
            self.log_message(f"❌ 显示筛选页面失败: {e}")
            self.pagination_widget.set_loading_state(False)
            raise

    def _display_storage_page(self, page: int, page_size: int):
        """显示存储数据的指定页面（原有逻辑）"""
        try:
            self.log_message(f"📄 加载存储数据第{page}页，每页{page_size}条")

            # 设置加载状态
            self.pagination_widget.set_loading_state(True)

            # 查询分页数据
            differences, total_count = self.storage_manager.get_differences_paginated(
                self.current_session_id, page, page_size
            )

            # 清空现有结果
            for item in self.diff_tree.get_children():
                self.diff_tree.delete(item)

            # 显示分页数据
            self._display_paginated_differences(differences)

            # 更新分页信息
            self.pagination_widget.update_pagination_info(page, total_count, page_size)

            # 取消加载状态
            self.pagination_widget.set_loading_state(False)

            self.log_message(f"✅ 存储数据第{page}页加载完成，共{len(differences)}条记录")

        except Exception as e:
            self.log_message(f"❌ 加载存储页面失败: {e}")
            self.pagination_widget.set_loading_state(False)
            raise

    def _display_paginated_differences(self, differences: List[Dict[str, Any]]):
        """显示分页差异数据"""
        try:
            for diff in differences:
                table_id = diff.get('table_id', '')
                record_id = diff.get('id', '')
                diff_type = diff.get('diff_type', '')
                diff_fields = ', '.join(diff.get('diff_fields', []))
                source_data = str(diff.get('source_data', ''))[:100]  # 限制显示长度
                target_data = str(diff.get('target_data', ''))[:100]

                # 将完整记录数据序列化为JSON字符串
                import json
                full_record_json = json.dumps(diff, ensure_ascii=False)

                self.diff_tree.insert('', tk.END, values=(
                    table_id, record_id, diff_type, diff_fields, source_data, target_data, full_record_json
                ))

        except Exception as e:
            self.log_message(f"❌ 显示分页数据失败: {e}")
            raise

    def _show_detail_window(self, record: Dict[str, Any]):
        """显示详细信息窗口 - 增强版本"""
        try:
            # 创建新窗口
            detail_window = tk.Toplevel(self.root)
            record_id = record.get('id', 'unknown')
            diff_type = record.get('diff_type', 'unknown')
            detail_window.title(f"记录详情 - {record_id} ({diff_type})")
            detail_window.geometry("1400x800")  # 增大窗口尺寸
            detail_window.transient(self.root)
            detail_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(detail_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 获取差异字段信息
            diff_fields = record.get('diff_fields', [])
            source_data = record.get('source_data', {})
            target_data = record.get('target_data', {})

            # 创建增强的基本信息区域
            self._create_enhanced_info_section(main_frame, record_id, diff_type, diff_fields, source_data, target_data)

            # 创建选项卡界面
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

            # 并排对比视图选项卡
            self._create_side_by_side_tab(notebook, source_data, target_data, diff_fields)

            # JSON详细视图选项卡
            self._create_json_detail_tab(notebook, source_data, target_data, diff_fields)

            # 差异摘要选项卡
            self._create_diff_summary_tab(notebook, source_data, target_data, diff_fields, diff_type)

            # 创建增强的按钮区域
            self._create_enhanced_button_section(main_frame, detail_window, source_data, target_data, record)

            self.log_message(f"✅ 增强详细信息窗口已打开: {record_id}")

        except Exception as e:
            self.log_message(f"❌ 显示详细信息窗口失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", f"无法显示详细信息: {str(e)}")

    def _create_enhanced_info_section(self, parent, record_id, diff_type, diff_fields, source_data, target_data):
        """创建增强的基本信息区域"""
        info_frame = ttk.LabelFrame(parent, text="📋 基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建信息网格
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill=tk.X)

        # 第一行：基本信息
        ttk.Label(info_grid, text="记录ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_grid, text=record_id).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(info_grid, text="差异类型:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        type_label = ttk.Label(info_grid, text=self._get_diff_type_display(diff_type))
        type_label.grid(row=0, column=3, sticky=tk.W)

        # 第二行：统计信息
        ttk.Label(info_grid, text="差异字段数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        diff_count_label = ttk.Label(info_grid, text=str(len(diff_fields)), foreground="#d32f2f", font=('Arial', 10, 'bold'))
        diff_count_label.grid(row=1, column=1, sticky=tk.W, padx=(0, 20), pady=(5, 0))

        ttk.Label(info_grid, text="源数据字段数:").grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        ttk.Label(info_grid, text=str(len(source_data)) if source_data else "0").grid(row=1, column=3, sticky=tk.W, pady=(5, 0))

        # 第三行：差异字段列表
        if diff_fields:
            ttk.Label(info_grid, text="差异字段:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
            fields_text = ', '.join(diff_fields[:5])  # 显示前5个字段
            if len(diff_fields) > 5:
                fields_text += f" ... (共{len(diff_fields)}个)"
            diff_fields_label = ttk.Label(info_grid, text=fields_text, foreground="#d32f2f")
            diff_fields_label.grid(row=2, column=1, columnspan=3, sticky=tk.W, pady=(5, 0))

    def _get_diff_type_display(self, diff_type):
        """获取差异类型的显示文本"""
        type_mapping = {
            'missing_in_source': '源端缺失',
            'missing_in_target': '目标端缺失',
            'data_mismatch': '数据不匹配',
            'difference': '数据差异'
        }
        return type_mapping.get(diff_type, diff_type)

    def _create_side_by_side_tab(self, notebook, source_data, target_data, diff_fields):
        """创建并排对比视图选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="🔍 并排对比")

        # 创建对比框架
        compare_frame = ttk.Frame(tab_frame)
        compare_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 源数据框架
        source_frame = ttk.LabelFrame(compare_frame, text="📊 源数据", padding=10)
        source_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建源数据表格
        source_tree = ttk.Treeview(source_frame, columns=("字段", "值"), show="headings", height=20)
        source_tree.heading("字段", text="字段")
        source_tree.heading("值", text="值")
        source_tree.column("字段", width=150)
        source_tree.column("值", width=300)

        # 添加源数据
        if source_data:
            for key, value in source_data.items():
                # 高亮差异字段
                tag = "diff" if key in diff_fields else ""
                source_tree.insert("", tk.END, values=(key, str(value)), tags=(tag,))

        # 配置差异字段的样式（使用原版app.py的颜色方案）
        source_tree.tag_configure("diff", background="#ffebee", foreground="#d32f2f")

        source_tree.pack(fill=tk.BOTH, expand=True)

        # 目标数据框架
        target_frame = ttk.LabelFrame(compare_frame, text="🎯 目标数据", padding=10)
        target_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 创建目标数据表格
        target_tree = ttk.Treeview(target_frame, columns=("字段", "值"), show="headings", height=20)
        target_tree.heading("字段", text="字段")
        target_tree.heading("值", text="值")
        target_tree.column("字段", width=150)
        target_tree.column("值", width=300)

        # 添加目标数据
        if target_data:
            for key, value in target_data.items():
                # 高亮差异字段
                tag = "diff" if key in diff_fields else ""
                target_tree.insert("", tk.END, values=(key, str(value)), tags=(tag,))

        # 配置差异字段的样式（使用原版app.py的颜色方案）
        target_tree.tag_configure("diff", background="#ffebee", foreground="#d32f2f")

        target_tree.pack(fill=tk.BOTH, expand=True)

    def _create_json_detail_tab(self, notebook, source_data, target_data, diff_fields):
        """创建JSON详细视图选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="📄 JSON详细")

        # 创建数据对比框架
        compare_frame = ttk.Frame(tab_frame)
        compare_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 源数据
        source_frame = ttk.LabelFrame(compare_frame, text="📊 源数据", padding=10)
        source_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        import tkinter.scrolledtext as scrolledtext
        source_text = scrolledtext.ScrolledText(source_frame, height=25, width=45, wrap=tk.WORD)
        source_text.pack(fill=tk.BOTH, expand=True)

        # 目标数据
        target_frame = ttk.LabelFrame(compare_frame, text="🎯 目标数据", padding=10)
        target_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        target_text = scrolledtext.ScrolledText(target_frame, height=25, width=45, wrap=tk.WORD)
        target_text.pack(fill=tk.BOTH, expand=True)

        # 格式化并插入JSON数据，支持差异字段高亮
        import json
        try:
            source_json = json.dumps(source_data, indent=2, ensure_ascii=False) if source_data else "无数据"
            target_json = json.dumps(target_data, indent=2, ensure_ascii=False) if target_data else "无数据"

            # 配置高亮标签（与并排对比保持一致：整行高亮）
            source_text.tag_configure("diff_line", background="#ffebee", foreground="#d32f2f")
            target_text.tag_configure("diff_line", background="#ffebee", foreground="#d32f2f")

            # 插入JSON文本
            source_text.insert(tk.END, source_json)
            target_text.insert(tk.END, target_json)

            # 高亮差异字段所在的整行（在设置只读之前）
            self._highlight_json_diff_lines(source_text, source_json, diff_fields)
            self._highlight_json_diff_lines(target_text, target_json, diff_fields)

            # 设置为只读
            source_text.config(state=tk.DISABLED)
            target_text.config(state=tk.DISABLED)
        except Exception as e:
            source_text.insert(tk.END, f"JSON格式化失败: {e}")
            target_text.insert(tk.END, f"JSON格式化失败: {e}")

    def _create_diff_summary_tab(self, notebook, source_data, target_data, diff_fields, diff_type):
        """创建差异摘要选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="📈 差异摘要")

        # 创建摘要文本区域
        import tkinter.scrolledtext as scrolledtext
        summary_text = scrolledtext.ScrolledText(tab_frame, height=25, wrap=tk.WORD)
        summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 配置高亮标签（使用原版app.py的颜色方案）
        summary_text.tag_configure("diff_field", foreground="#d32f2f", font=('Arial', 10, 'bold'))
        summary_text.tag_configure("diff_value", foreground="#d32f2f")
        summary_text.tag_configure("header", font=('Arial', 12, 'bold'))
        summary_text.tag_configure("section", font=('Arial', 10, 'bold'))

        # 生成并插入差异摘要（带高亮）
        self._insert_diff_summary_with_highlight(summary_text, source_data, target_data, diff_fields, diff_type)
        summary_text.config(state=tk.DISABLED)

    def _create_enhanced_button_section(self, parent, detail_window, source_data, target_data, record):
        """创建增强的按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧按钮组
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)

        # 复制按钮
        def copy_source_data():
            detail_window.clipboard_clear()
            import json
            detail_window.clipboard_append(json.dumps(source_data, indent=2, ensure_ascii=False))
            messagebox.showinfo("提示", "源数据已复制到剪贴板")

        def copy_target_data():
            detail_window.clipboard_clear()
            import json
            detail_window.clipboard_append(json.dumps(target_data, indent=2, ensure_ascii=False))
            messagebox.showinfo("提示", "目标数据已复制到剪贴板")

        def copy_full_record():
            detail_window.clipboard_clear()
            import json
            detail_window.clipboard_append(json.dumps(record, indent=2, ensure_ascii=False))
            messagebox.showinfo("提示", "完整记录已复制到剪贴板")

        ttk.Button(left_buttons, text="复制源数据", command=copy_source_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="复制目标数据", command=copy_target_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="复制完整记录", command=copy_full_record).pack(side=tk.LEFT, padx=(0, 5))

        # 右侧按钮组
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(right_buttons, text="关闭", command=detail_window.destroy).pack(side=tk.RIGHT)

    def _generate_diff_summary(self, source_data, target_data, diff_fields, diff_type):
        """生成差异摘要"""
        summary_lines = []
        summary_lines.append("=" * 60)
        summary_lines.append("差异记录摘要")
        summary_lines.append("=" * 60)
        summary_lines.append("")

        # 基本信息
        summary_lines.append(f"差异类型: {self._get_diff_type_display(diff_type)}")
        summary_lines.append(f"差异字段数量: {len(diff_fields)}")
        summary_lines.append(f"源数据字段数量: {len(source_data) if source_data else 0}")
        summary_lines.append(f"目标数据字段数量: {len(target_data) if target_data else 0}")
        summary_lines.append("")

        # 差异字段详情
        if diff_fields:
            summary_lines.append("差异字段详情:")
            summary_lines.append("-" * 40)
            for field in diff_fields:
                source_val = source_data.get(field, "N/A") if source_data else "N/A"
                target_val = target_data.get(field, "N/A") if target_data else "N/A"
                summary_lines.append(f"字段: {field}")
                summary_lines.append(f"  源值: {source_val}")
                summary_lines.append(f"  目标值: {target_val}")
                summary_lines.append("")

        return "\n".join(summary_lines)

    def _insert_diff_summary_with_highlight(self, text_widget, source_data, target_data, diff_fields, diff_type):
        """插入带高亮的差异摘要"""
        try:
            # 标题
            text_widget.insert(tk.END, "=" * 60 + "\n", "header")
            text_widget.insert(tk.END, "差异记录摘要\n", "header")
            text_widget.insert(tk.END, "=" * 60 + "\n\n", "header")

            # 基本信息
            text_widget.insert(tk.END, f"差异类型: {self._get_diff_type_display(diff_type)}\n")
            text_widget.insert(tk.END, "差异字段数量: ", "section")
            text_widget.insert(tk.END, f"{len(diff_fields)}\n", "diff_field")
            text_widget.insert(tk.END, f"源数据字段数量: {len(source_data) if source_data else 0}\n")
            text_widget.insert(tk.END, f"目标数据字段数量: {len(target_data) if target_data else 0}\n\n")

            # 差异字段详情
            if diff_fields:
                text_widget.insert(tk.END, "差异字段详情:\n", "section")
                text_widget.insert(tk.END, "-" * 40 + "\n")

                for field in diff_fields:
                    source_val = source_data.get(field, "N/A") if source_data else "N/A"
                    target_val = target_data.get(field, "N/A") if target_data else "N/A"

                    # 字段名高亮
                    text_widget.insert(tk.END, "字段: ")
                    text_widget.insert(tk.END, f"{field}\n", "diff_field")

                    # 值高亮
                    text_widget.insert(tk.END, "  源值: ")
                    text_widget.insert(tk.END, f"{source_val}\n", "diff_value")
                    text_widget.insert(tk.END, "  目标值: ")
                    text_widget.insert(tk.END, f"{target_val}\n", "diff_value")
                    text_widget.insert(tk.END, "\n")

        except Exception as e:
            logger.error(f"插入差异摘要失败: {e}")
            text_widget.insert(tk.END, f"生成摘要失败: {e}")

    def _highlight_json_diff_lines(self, text_widget, json_text: str, diff_fields: list):
        """高亮JSON中包含差异字段的整行（与并排对比保持一致）

        Args:
            text_widget: Text控件（应该处于可编辑状态）
            json_text: JSON文本内容
            diff_fields: 差异字段列表
        """
        try:
            if not diff_fields or not json_text:
                return

            # 按行检查JSON内容
            lines = json_text.split('\n')
            highlighted_count = 0

            for line_num, line_content in enumerate(lines, 1):
                # 检查当前行是否包含差异字段
                is_diff_line = False
                matched_field = None

                for diff_field in diff_fields:
                    # 检查字段名是否在当前行中（带引号）
                    field_pattern = f'"{diff_field}"'
                    if field_pattern in line_content:
                        is_diff_line = True
                        matched_field = diff_field
                        break

                # 如果是差异行，高亮整行
                if is_diff_line:
                    line_start = f"{line_num}.0"
                    line_end = f"{line_num}.end"
                    text_widget.tag_add("diff_line", line_start, line_end)
                    highlighted_count += 1
                    logger.debug(f"高亮第{line_num}行 (字段: {matched_field}): {line_content.strip()}")

            logger.debug(f"JSON高亮完成，共高亮{highlighted_count}行")

        except Exception as e:
            logger.error(f"高亮JSON差异行失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def _on_window_closing(self):
        """窗口关闭事件（增强版 - 包含AsyncTaskRunner清理）"""
        try:
            # 检查是否有正在运行的比对任务
            if self.comparison_running:
                if messagebox.askyesno("确认", "比对正在进行中，确定要退出吗？"):
                    self.stop_comparison()
                    # 等待一小段时间让任务取消完成
                    self.root.after(500, self._cleanup_and_destroy)
                return

            # 直接清理并关闭
            self._cleanup_and_destroy()

        except Exception as e:
            logger.error(f"窗口关闭处理失败: {e}")
            self.root.destroy()

    def _cleanup_and_destroy(self):
        """清理资源并销毁窗口"""
        try:
            # 显示最终的性能统计
            metrics = AsyncTaskRunner.get_metrics_summary()
            if metrics.get('total_tasks', 0) > 0:
                logger.info(f"应用关闭 - 总共执行了 {metrics['total_tasks']} 个任务，"
                          f"成功率: {metrics.get('success_rate', 0):.1f}%")

            # 取消所有活跃任务
            active_tasks = AsyncTaskRunner.get_active_tasks()
            if active_tasks:
                logger.info(f"正在取消 {len(active_tasks)} 个活跃任务...")
                for task_id in active_tasks.keys():
                    AsyncTaskRunner.cancel_task(task_id)

            # 清理错误处理器
            if self.error_handler:
                error_stats = self.error_handler.get_error_stats()
                if error_stats.get('total_count', 0) > 0:
                    logger.info(f"应用关闭 - 错误统计: {error_stats}")

            logger.info("🔄 应用资源清理完成")

        except Exception as e:
            logger.error(f"资源清理失败: {e}")

        finally:
            # 确保窗口被销毁
            self.root.destroy()
    
    # 公共方法
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, formatted_message + "\n")
            self.log_text.see(tk.END)
        else:
            self._message_buffer.append(formatted_message)
    
    def _flush_message_buffer(self):
        """刷新消息缓冲区"""
        if hasattr(self, 'log_text') and self._message_buffer:
            for message in self._message_buffer:
                self.log_text.insert(tk.END, message + "\n")
            self.log_text.see(tk.END)
            self._message_buffer.clear()
    
    def update_progress(self, progress: int, message: str):
        """更新进度"""
        self.progress_value.set(progress)
        self.progress_var.set(message)
        self.root.update_idletasks()
    
    def select_config_file(self):
        """选择配置文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择配置文件",
                filetypes=[("INI文件", "*.ini"), ("所有文件", "*.*")]
            )

            if file_path:
                self.config_file_var.set(file_path)
                self.log_message(f"📁 已选择配置文件: {file_path}")
                # 这里应该加载配置文件
                self._load_config_file(file_path)
                self._update_status_bar(app_status="配置已选择")
        except Exception as e:
            self.log_message(f"❌ 选择配置文件失败: {e}")
            self._update_status_bar(app_status="配置选择失败")
    
    def _load_config_file(self, file_path: str):
        """加载配置文件"""
        try:
            if not self.config_manager:
                raise Exception("配置管理器未初始化")

            # 加载配置文件
            if self.config_manager.load_config(file_path):
                self.log_message(f"✅ 配置文件加载成功: {os.path.basename(file_path)}")

                # 加载表数据
                self._load_table_data()

                # 更新数据库连接配置
                self._update_database_configs()

                # 验证配置完整性
                self._validate_config()

                # 更新状态栏
                self._update_status_bar(app_status="配置已加载", db_status="配置就绪")

            else:
                raise Exception("配置文件格式错误或内容无效")

        except Exception as e:
            self.log_message(f"❌ 加载配置文件失败: {e}")
            messagebox.showerror("配置错误", f"加载配置文件失败:\n{str(e)}")
            self._update_status_bar(app_status="配置加载失败", db_status="配置错误")

    def _load_table_data(self):
        """加载表数据到界面"""
        try:
            if not self.config_manager or not hasattr(self.config_manager, 'get_comparison_tables'):
                self.log_message("⚠️ 配置管理器不支持表数据获取")
                return

            # 清空现有数据
            for item in self.table_tree.get_children():
                self.table_tree.delete(item)

            # 获取表配置
            tables = self.config_manager.get_comparison_tables()

            # 添加表数据到界面
            for table in tables:
                table_id = table.get('table_id', '')
                remark = table.get('remark', '')
                status = "就绪"

                self.table_tree.insert('', tk.END, values=(table_id, remark, status))

            self.log_message(f"✅ 已加载 {len(tables)} 个比对表")

        except Exception as e:
            self.log_message(f"❌ 加载表数据失败: {e}")

    def _update_database_configs(self):
        """更新数据库连接配置"""
        try:
            if not self.config_manager:
                return

            # 获取数据库配置
            if hasattr(self.config_manager, 'get_database_config'):
                source_config = self.config_manager.get_database_config('DB1')
                target_config = self.config_manager.get_database_config('DB2')

                if source_config and target_config:
                    self.log_message(f"✅ 数据库配置已更新")
                    self.log_message(f"   源数据库: {source_config.db_type}@{source_config.host}:{source_config.port}")
                    self.log_message(f"   目标数据库: {target_config.db_type}@{target_config.host}:{target_config.port}")
                else:
                    self.log_message("⚠️ 数据库配置不完整")

        except Exception as e:
            self.log_message(f"❌ 更新数据库配置失败: {e}")

    def _validate_config(self):
        """验证配置完整性"""
        try:
            if not self.config_manager:
                raise Exception("配置管理器未初始化")

            # 检查基本配置
            if not hasattr(self.config_manager, 'config') or not self.config_manager.config:
                raise Exception("配置文件内容为空")

            # 检查表配置
            if hasattr(self.config_manager, 'get_comparison_tables'):
                tables = self.config_manager.get_comparison_tables()
                if not tables:
                    raise Exception("没有找到比对表配置")

            # 检查数据库配置
            if hasattr(self.config_manager, 'get_database_config'):
                source_config = self.config_manager.get_database_config('DB1')
                target_config = self.config_manager.get_database_config('DB2')

                if not source_config or not target_config:
                    raise Exception("数据库连接配置不完整")

            self.log_message("✅ 配置验证通过")

        except Exception as e:
            self.log_message(f"❌ 配置验证失败: {e}")
            raise
    
    def start_comparison(self):
        """开始比对"""
        try:
            if self.comparison_running:
                messagebox.showwarning("警告", "比对正在进行中")
                return

            if not self.current_mode:
                messagebox.showerror("错误", "没有可用的比对模式")
                self._update_status_bar(app_status="错误", db_status="未连接")
                return

            # 检查配置
            if not self.config_file_var.get():
                messagebox.showerror("错误", "请先选择配置文件")
                self._update_status_bar(app_status="配置错误")
                return

            self.comparison_running = True
            self.cancel_requested = False

            # 更新状态栏
            self._update_status_bar(app_status="比对中", db_status="连接中")

            # 启动比对线程
            self._start_comparison_thread()

        except Exception as e:
            self.log_message(f"❌ 启动比对失败: {e}")
            self.comparison_running = False
            self._update_status_bar(app_status="启动失败", db_status="连接失败")
    
    def _start_comparison_thread(self):
        """启动比对线程（完整版AsyncTaskRunner）"""
        try:
            self.log_message(f"🚀 使用 {self.current_mode.display_name} 开始比对")

            # 创建比对配置
            config = self._create_comparison_config()
            if not config:
                raise Exception("创建比对配置失败")

            # 生成任务ID用于跟踪
            task_id = f"comparison_{self.current_mode.mode_name}_{int(time.time())}"
            self.current_task_id = task_id

            # 定义回调函数
            def on_success(result):
                """比对成功回调"""
                self._process_comparison_result(result)

                # 显示性能指标
                metrics = AsyncTaskRunner.get_metrics_summary()
                if metrics.get('total_tasks', 0) > 0:
                    self.log_message(f"✅ 比对任务完成")
                    self.log_message(f"📊 性能统计: 成功率 {metrics.get('success_rate', 0):.1f}%, "
                                   f"平均耗时 {metrics.get('average_execution_time', 0):.3f}秒")
                else:
                    self.log_message(f"✅ 比对任务完成")

                # 更新状态栏
                self._update_status_bar(app_status="比对完成", db_status="已断开")

            def on_error(error):
                """比对错误回调"""
                error_msg = str(error)

                # 使用完整版的错误处理器
                if self.error_handler:
                    if isinstance(error, asyncio.TimeoutError):
                        self.error_handler.handle_error(
                            error, ErrorLevel.WARNING,
                            "比对任务超时", show_dialog=True
                        )
                        self._update_status_bar(app_status="超时", db_status="已断开")
                    else:
                        self.error_handler.handle_error(
                            error, ErrorLevel.ERROR,
                            "比对执行失败", show_dialog=True
                        )
                        self._update_status_bar(app_status="执行失败", db_status="连接失败")
                else:
                    # 后备错误处理
                    self.log_message(f"❌ 比对执行失败: {error_msg}")
                    messagebox.showerror("比对错误", f"比对执行失败:\n{error_msg}")
                    self._update_status_bar(app_status="执行失败", db_status="连接失败")

            def on_finally():
                """比对完成回调（无论成功失败）"""
                self.comparison_running = False
                self.current_task_id = None
                self.update_progress(0, "就绪")
                # 如果状态还是"比对中"，更新为"就绪"
                if self.app_status_var.get() == "比对中":
                    self._update_status_bar(app_status="就绪")

                # 显示错误统计（如果有错误处理器）
                if self.error_handler:
                    error_stats = self.error_handler.get_error_stats()
                    if error_stats.get('total_count', 0) > 0:
                        self.log_message(f"📊 错误统计: {error_stats}")

            # 使用完整版的异步任务运行器
            async_comparison_task(
                self,
                self.current_mode,
                config,
                timeout=600.0,  # 10分钟超时
                on_success=on_success,
                on_error=on_error,
                on_finally=on_finally
            )

        except Exception as e:
            self.log_message(f"❌ 启动比对失败: {e}")
            self.comparison_running = False
            self.current_task_id = None

    def _create_comparison_config(self):
        """创建比对配置"""
        try:
            if not self.config_manager or not self.config_manager.config:
                raise Exception("配置文件未加载")

            # 获取数据库配置
            source_config = self.config_manager.get_database_config('DB1')
            target_config = self.config_manager.get_database_config('DB2')

            if not source_config or not target_config:
                raise Exception("数据库配置不完整")

            # 获取表配置
            tables = self.config_manager.get_comparison_tables()
            if not tables:
                raise Exception("没有找到比对表配置")

            config = ComparisonConfig(
                source_config={
                    'db_type': source_config.db_type,
                    'host': source_config.host,
                    'port': source_config.port,
                    'database': source_config.database,
                    'username': source_config.username,
                    'password': source_config.password
                },
                target_config={
                    'db_type': target_config.db_type,
                    'host': target_config.host,
                    'port': target_config.port,
                    'database': target_config.database,
                    'username': target_config.username,
                    'password': target_config.password
                },
                tables=tables,
                batch_size=100000000,
                timeout=300
            )

            return config

        except Exception as e:
            self.log_message(f"❌ 创建比对配置失败: {e}")
            return None

    def _process_comparison_result(self, result):
        """处理比对结果"""
        try:
            if not result:
                self.log_message("❌ 比对结果为空")
                return

            self.comparison_results = result

            if result.success:
                self.log_message(f"✅ 比对完成，模式: {result.mode}")
                self.log_message(f"   执行时间: {result.execution_time:.2f}秒")
                self.log_message(f"   处理表数: {result.completed_tables}/{result.total_tables}")

                # 显示差异结果
                if hasattr(result, 'differences') and result.differences:
                    self._display_differences(result.differences)
                    self.log_message(f"   发现差异: {len(result.differences)}条")
                else:
                    self.log_message("   未发现差异")

                self.update_progress(100, "比对完成")

            else:
                error_msg = getattr(result, 'error_message', '未知错误')
                self.log_message(f"❌ 比对失败: {error_msg}")
                self.update_progress(0, "比对失败")

        except Exception as e:
            self.log_message(f"❌ 处理比对结果失败: {e}")

    def _display_differences(self, differences):
        """显示差异结果 - 支持海量数据存储和分页"""
        try:
            if not differences:
                self.log_message("📄 没有发现差异记录")
                self.pagination_widget.reset()
                # 清空筛选数据
                self.all_diff_records = []
                self.filtered_records = []
                self._update_filter_options()
                return

            # 存储所有差异记录用于筛选
            self.all_diff_records = differences
            self.filtered_records = differences.copy()  # 初始时显示所有记录

            # 更新筛选选项
            self._update_filter_options()

            # 创建新的比对会话
            if self.storage_manager:
                try:
                    # 生成任务ID
                    import time
                    task_id = f"comparison_{int(time.time())}"

                    # 获取比对模式名称
                    if self.current_mode and hasattr(self.current_mode, '__class__'):
                        mode_name = self.current_mode.__class__.__name__
                        self.log_message(f"🔍 检测到比对模式: {mode_name}")
                    else:
                        mode_name = 'unknown'
                        self.log_message(f"⚠️ 无法获取比对模式名称，使用默认值: {mode_name}")

                    # 创建会话
                    self.current_session_id = self.storage_manager.create_session(
                        task_id=task_id,
                        mode=mode_name,
                        metadata={
                            'total_differences': len(differences),
                            'config_file': self.config_file_var.get() if self.config_file_var else None
                        }
                    )

                    self.log_message(f"📊 创建存储会话: {self.current_session_id}")

                    # 异步批量存储差异记录
                    self.log_message(f"💾 开始异步存储 {len(differences)} 条差异记录...")
                    self._async_store_differences(differences)

                except Exception as storage_error:
                    self.log_message(f"❌ 存储差异记录失败: {storage_error}")
                    # 存储失败时回退到传统显示方式
                    self._display_differences_fallback(differences)
            else:
                # 没有存储管理器时使用传统方式
                self._display_differences_fallback(differences)

            # 切换到结果选项卡
            self.notebook.select(1)  # 选择比对结果选项卡

        except Exception as e:
            self.log_message(f"❌ 显示差异结果失败: {e}")

    def _async_store_differences(self, differences):
        """异步存储差异记录，避免界面卡顿 - 内存优化版"""
        import threading

        def store_worker():
            error_info = None
            stored_count = 0

            try:
                # 更新进度显示
                self.root.after(0, lambda: self.update_progress(10, "正在存储差异记录..."))
                self.root.after(0, lambda: self.log_message("📊 开始数据库写入操作..."))

                # 简单的进度回调函数
                def progress_callback(current, total, message):
                    progress_val = int((current / total) * 80) + 10  # 10-90%
                    # 添加速度信息
                    if hasattr(self.storage_manager, 'operation_times') and self.storage_manager.operation_times:
                        avg_time = sum(self.storage_manager.operation_times) / len(self.storage_manager.operation_times)
                        rate = current / avg_time if avg_time > 0 else 0
                        enhanced_message = f"{message} ({rate:.0f} 条/秒)"
                    else:
                        enhanced_message = message

                    self.root.after(0, lambda p=progress_val, m=enhanced_message: self.update_progress(p, m))

                # 检查是否取消
                if self.cancel_requested:
                    self.root.after(0, lambda: self.log_message("⚠️ 存储操作已取消"))
                    return

                # 使用智能存储（已集成到storage_manager中）
                self.root.after(0, lambda: self.log_message("💾 开始智能存储差异记录..."))

                # 执行存储操作
                stored_count = self.storage_manager.batch_insert_differences(
                    self.current_session_id, differences, progress_callback=progress_callback
                )

                # 完成会话
                self.storage_manager.complete_session(self.current_session_id)

                # 更新UI（必须在主线程中执行）
                self.root.after(0, lambda count=stored_count: self._on_storage_complete(count))

            except Exception as e:
                # 捕获错误信息
                error_info = str(e)
                import traceback
                error_detail = traceback.format_exc()

                # 错误处理（必须在主线程中执行）
                self.root.after(0, lambda err=error_info, detail=error_detail: self._on_storage_error(err, detail))

        # 启动后台线程
        storage_thread = threading.Thread(target=store_worker, daemon=True)
        storage_thread.start()

    def _on_storage_complete(self, stored_count):
        """存储完成回调（在主线程中执行）"""
        try:
            self.update_progress(90, "数据存储完成，加载显示...")
            self.log_message(f"✅ 差异记录存储完成，共 {stored_count} 条")

            # 加载第一页数据
            self._load_first_page()

            # 完成进度
            self.update_progress(100, "比对和存储完成")

        except Exception as e:
            self.log_message(f"❌ 存储完成处理失败: {e}")

    def _on_storage_error(self, error_msg, error_detail=None):
        """存储错误回调（在主线程中执行）"""
        try:
            self.log_message(f"❌ 存储差异记录失败: {error_msg}")
            if error_detail:
                logger.error(f"详细错误信息: {error_detail}")

            # 更新进度显示
            self.update_progress(0, "存储失败，回退到内存显示")

            # 存储失败时回退到传统显示方式
            if hasattr(self, 'all_diff_records') and self.all_diff_records:
                self._display_differences_fallback(self.all_diff_records)
            else:
                self.log_message("⚠️ 无法回退显示，没有可用的差异记录")

        except Exception as e:
            self.log_message(f"❌ 存储错误处理失败: {e}")
            logger.error(f"存储错误处理异常: {e}")

    def _display_differences_fallback(self, differences):
        """回退的差异显示方法"""
        try:
            self.log_message(f"🔄 回退到内存显示模式，共 {len(differences)} 条记录")

            # 直接显示差异记录（不使用数据库存储）
            self.all_diff_records = differences
            self.filtered_records = differences.copy()

            # 更新筛选选项
            self._update_filter_options()

            # 显示第一页数据
            self._display_filtered_page(1, 100)

            self.log_message("✅ 内存显示模式加载完成")

        except Exception as e:
            self.log_message(f"❌ 回退显示失败: {e}")
            logger.error(f"回退显示异常: {e}")

    def _load_first_page(self):
        """加载第一页数据"""
        try:
            if not self.current_session_id or not self.storage_manager:
                return

            # 获取第一页数据
            page_size = 100  # 默认每页100条
            differences, total_count = self.storage_manager.get_differences_paginated(
                self.current_session_id, 1, page_size
            )

            # 清空现有结果
            for item in self.diff_tree.get_children():
                self.diff_tree.delete(item)

            # 显示数据
            self._display_paginated_differences(differences)

            # 更新分页信息
            self.pagination_widget.update_pagination_info(1, total_count, page_size)

            self.log_message(f"📄 第一页数据加载完成，共 {total_count} 条记录")

        except Exception as e:
            self.log_message(f"❌ 加载第一页数据失败: {e}")

    def _display_differences_fallback(self, differences):
        """传统差异显示方式（回退方案）"""
        try:
            self.log_message("⚠️ 使用传统显示方式")

            # 清空现有结果
            for item in self.diff_tree.get_children():
                self.diff_tree.delete(item)

            # 限制显示数量以避免界面卡顿
            max_display = 1000
            display_differences = differences[:max_display]

            if len(differences) > max_display:
                self.log_message(f"⚠️ 差异记录过多，仅显示前 {max_display} 条")

            # 添加差异记录
            for diff in display_differences:
                table_id = diff.get('table_id', '')
                record_id = diff.get('id', '')
                diff_type = diff.get('diff_type', '')
                diff_fields = ', '.join(diff.get('diff_fields', []))
                source_data = str(diff.get('source_data', ''))[:100]  # 限制显示长度
                target_data = str(diff.get('target_data', ''))[:100]

                # 将完整记录数据序列化为JSON字符串
                import json
                full_record_json = json.dumps(diff, ensure_ascii=False)

                self.diff_tree.insert('', tk.END, values=(
                    table_id, record_id, diff_type, diff_fields, source_data, target_data, full_record_json
                ))

            # 重置分页控件
            self.pagination_widget.reset()

        except Exception as e:
            self.log_message(f"❌ 传统显示方式失败: {e}")
            raise
    
    def stop_comparison(self):
        """停止比对（增强版 - 使用AsyncTaskRunner任务管理）"""
        try:
            if not self.comparison_running:
                messagebox.showinfo("信息", "没有正在进行的比对")
                return

            self.cancel_requested = True

            # 使用完整版AsyncTaskRunner的任务取消功能
            if self.current_task_id:
                success = AsyncTaskRunner.cancel_task(self.current_task_id)
                if success:
                    self.log_message(f"⏹️ 任务 {self.current_task_id} 取消请求已发送")
                else:
                    self.log_message(f"⚠️ 任务 {self.current_task_id} 取消失败或已完成")

            # 同时尝试取消当前模式的比对（向后兼容）
            if self.current_mode:
                try:
                    asyncio.run(self.current_mode.cancel_comparison())
                except Exception as e:
                    logger.warning(f"模式级别取消失败: {e}")

            self.log_message("⏹️ 比对已取消")
            self.update_progress(0, "比对已取消")

            # 显示当前活跃任务状态
            active_tasks = AsyncTaskRunner.get_active_tasks()
            if active_tasks:
                self.log_message(f"📊 当前活跃任务: {len(active_tasks)}个")

        except Exception as e:
            self.log_message(f"❌ 停止比对失败: {e}")

    def export_results(self):
        """导出结果"""
        try:
            if not self.comparison_results:
                messagebox.showinfo("信息", "没有可导出的结果")
                return
            
            file_path = filedialog.asksaveasfilename(
                title="导出比对结果",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
            
            if file_path:
                # 这里应该实现结果导出逻辑
                self.log_message(f"📤 结果导出功能待实现: {file_path}")
                
        except Exception as e:
            self.log_message(f"❌ 导出结果失败: {e}")
    
    def reset_layout(self):
        """重置布局"""
        try:
            # 重置分割面板位置
            self.main_paned.sashpos(0, 400)
            self.log_message("🔄 布局已重置")
        except Exception as e:
            self.log_message(f"❌ 重置布局失败: {e}")
    
    def refresh_modes(self):
        """刷新比对模式"""
        try:
            self.log_message("🔄 正在刷新比对模式...")
            # 重新检查模式可用性
            self._start_background_initialization()
            self.log_message("✅ 比对模式刷新完成")
        except Exception as e:
            self.log_message(f"❌ 刷新比对模式失败: {e}")
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"GUI运行失败: {e}")


# 主程序入口
if __name__ == "__main__":
    try:
        print("启动 DB-Agent 模块化GUI...")
        print("=" * 50)

        # 检查当前工作目录
        current_dir = os.getcwd()
        print(f"当前目录: {current_dir}")

        # 确保项目根目录在Python路径中
        # if PROJECT_ROOT not in sys.path:
        #     sys.path.insert(0, PROJECT_ROOT)
        #     print(f"已添加项目根目录到路径: {PROJECT_ROOT}")

        # 创建并运行应用
        app = MainGUI()
        print("✅ GUI应用创建成功")
        app.run()

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("\n💡 解决方案:")
        print("1. 确保在正确的目录中运行")
        print("2. 使用启动脚本: python ../../app_modular.py")
        print("3. 或者设置PYTHONPATH环境变量")
        import traceback
        traceback.print_exc()
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
