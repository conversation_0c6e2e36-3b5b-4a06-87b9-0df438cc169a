"""
统一的数据比对引擎
整合所有比对相关的功能，提供统一的接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime
import time
import asyncio

from app.models.task import CompareConfig, ComparisonSummary, TaskStats
from app.models.db import DatabaseConnection
from app.models.logger import logger
from .result_sync import result_sync_service


class ComparisonContext:
    """比对上下文，用于传递比对过程中的状态信息"""

    def __init__(self, task_id: str, config: CompareConfig):
        self.task_id = task_id
        self.config = config
        self.start_time = datetime.now()
        self.current_progress = 0
        self.total_records = 0
        self.processed_records = 0
        self.errors = []
        self.warnings = []

    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append({
            'timestamp': datetime.now().isoformat(),
            'message': error
        })

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append({
            'timestamp': datetime.now().isoformat(),
            'message': warning
        })

    def update_progress(self, processed: int, total: int = None):
        """更新进度"""
        self.processed_records = processed
        if total is not None:
            self.total_records = total

        if self.total_records > 0:
            self.current_progress = int((self.processed_records / self.total_records) * 100)


class AbstractComparisonEngine(ABC):
    """抽象比对引擎基类"""

    def __init__(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig,
        progress_callback: Optional[Callable[[int, TaskStats], None]] = None
    ):
        self.source_conn = source_conn
        self.target_conn = target_conn
        self.config = config
        self.progress_callback = progress_callback
        self.context: Optional[ComparisonContext] = None

        # 性能指标
        self._performance_metrics = {
            'start_time': None,
            'end_time': None,
            'source_query_time': 0,
            'target_query_time': 0,
            'comparison_time': 0,
            'total_records_processed': 0,
            'memory_usage': 0
        }

    async def initialize(self, task_id: str) -> bool:
        """初始化比对引擎"""
        try:
            self.context = ComparisonContext(task_id, self.config)
            self._performance_metrics['start_time'] = time.time()
            logger.info(f"比对引擎初始化完成: {task_id}")
            return True
        except Exception as e:
            logger.error(f"比对引擎初始化失败: {str(e)}")
            return False

    @abstractmethod
    async def run(self) -> ComparisonSummary:
        """执行比对操作"""
        pass

    @abstractmethod
    async def get_source_data(self, partition=None) -> List[Dict[str, Any]]:
        """获取源数据"""
        pass

    @abstractmethod
    async def get_target_data(self, partition=None) -> List[Dict[str, Any]]:
        """获取目标数据"""
        pass

    @abstractmethod
    async def compare_data(self, source_data: List[Dict[str, Any]], target_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """比对数据"""
        pass

    def _update_progress(self, progress: int, stats: TaskStats):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, stats)

        if self.context:
            self.context.current_progress = progress

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if self._performance_metrics['start_time'] and not self._performance_metrics['end_time']:
            self._performance_metrics['end_time'] = time.time()

        return self._performance_metrics.copy()

    async def sync_results_to_server(self, results_dir) -> bool:
        """同步结果到Server"""
        if not self.context:
            logger.error("比对上下文未初始化，无法同步结果")
            return False

        try:
            # 更新任务状态为完成
            await result_sync_service.update_task_status(
                self.context.task_id,
                'completed',
                progress=100
            )

            # 同步结果文件
            success = await result_sync_service.sync_results_to_server(
                self.context.task_id,
                results_dir
            )

            if success:
                logger.info(f"任务 {self.context.task_id} 结果同步成功")
            else:
                logger.error(f"任务 {self.context.task_id} 结果同步失败")

            return success

        except Exception as e:
            logger.error(f"同步结果到Server失败: {str(e)}")
            return False

    async def update_task_status(self, status: str, progress: int = None, error_message: str = None):
        """更新任务状态"""
        if not self.context:
            return

        try:
            await result_sync_service.update_task_status(
                self.context.task_id,
                status,
                progress,
                error_message
            )
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")

    async def cleanup(self):
        """清理资源"""
        try:
            # 子类可以重写此方法来清理特定资源
            logger.info(f"比对引擎清理完成: {self.context.task_id if self.context else 'unknown'}")
        except Exception as e:
            logger.error(f"比对引擎清理失败: {str(e)}")


class ComparisonEngineManager:
    """比对引擎管理器，负责引擎的生命周期管理"""

    def __init__(self):
        self.active_engines: Dict[str, AbstractComparisonEngine] = {}
        self.engine_stats: Dict[str, Dict[str, Any]] = {}

    def register_engine(self, task_id: str, engine: AbstractComparisonEngine):
        """注册比对引擎"""
        self.active_engines[task_id] = engine
        self.engine_stats[task_id] = {
            'created_at': datetime.now().isoformat(),
            'status': 'registered'
        }
        logger.info(f"注册比对引擎: {task_id}")

    def get_engine(self, task_id: str) -> Optional[AbstractComparisonEngine]:
        """获取比对引擎"""
        return self.active_engines.get(task_id)

    async def cleanup_engine(self, task_id: str):
        """清理比对引擎"""
        engine = self.active_engines.pop(task_id, None)
        if engine:
            await engine.cleanup()
            self.engine_stats.pop(task_id, None)
            logger.info(f"清理比对引擎: {task_id}")

    def get_engine_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取引擎统计信息"""
        return self.engine_stats.copy()


# 全局引擎管理器实例
engine_manager = ComparisonEngineManager()
