"""
统一的配置管理器
集中管理所有配置信息，提供配置验证和动态更新功能
"""
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum
import os
import json
from pathlib import Path

from app.models.logger import logger


class DatabaseType(Enum):
    """支持的数据库类型"""
    DB2 = "db2"
    ORACLE = "oracle"
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    SQLSERVER = "sqlserver"
    GAUSSDB = "gaussdb"


class ComparisonMode(Enum):
    """比对模式"""
    TABLE = "table"
    QUERY = "query"
    SCHEMA = "schema"


class AlgorithmType(Enum):
    """比对算法类型"""
    PRIMARY_KEY = "primary_key"
    HASH = "hash"
    TWO_PHASE = "two_phase"


class DatabaseConfig(BaseModel):
    """数据库配置"""
    type: DatabaseType
    host: str
    port: int
    username: str
    password: str = Field(..., exclude=True)  # 密码不会被序列化
    database: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v


class ComparisonConfig(BaseModel):
    """比对配置"""
    mode: ComparisonMode
    algorithm: AlgorithmType = AlgorithmType.PRIMARY_KEY
    batch_size: int = Field(default=1000, ge=1, le=10000)
    timeout: int = Field(default=300, ge=1)  # 秒
    max_diff_records: int = Field(default=1000, ge=1)
    enable_parallel: bool = Field(default=True)
    parallel_workers: int = Field(default=4, ge=1, le=16)
    
    # 表模式配置
    source_table: Optional[str] = None
    target_table: Optional[str] = None
    key_columns: List[str] = Field(default_factory=list)
    ignore_columns: List[str] = Field(default_factory=list)
    
    # 查询模式配置
    source_query: Optional[str] = None
    target_query: Optional[str] = None
    
    @validator('parallel_workers')
    def validate_workers(cls, v, values):
        if values.get('enable_parallel', True) and v < 1:
            raise ValueError('并行工作线程数必须大于0')
        return v


class SystemConfig(BaseModel):
    """系统配置"""
    # 任务管理
    max_concurrent_tasks: int = Field(default=10, ge=1, le=100)
    task_timeout: int = Field(default=3600, ge=60)  # 秒
    task_cleanup_interval: int = Field(default=300, ge=60)  # 秒
    
    # 内存管理
    max_memory_usage: int = Field(default=2048, ge=512)  # MB
    memory_check_interval: int = Field(default=30, ge=10)  # 秒
    
    # 日志配置
    log_level: str = Field(default="INFO")
    log_max_size: int = Field(default=100, ge=1)  # MB
    log_backup_count: int = Field(default=5, ge=1)
    
    # WebSocket配置
    websocket_timeout: int = Field(default=60, ge=10)  # 秒
    websocket_ping_interval: int = Field(default=30, ge=5)  # 秒
    
    # 性能监控
    enable_performance_monitoring: bool = Field(default=True)
    performance_sample_rate: float = Field(default=0.1, ge=0.0, le=1.0)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.config_dir = Path(config_dir or os.getenv('CONFIG_DIR', './config'))
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置缓存
        self._database_configs: Dict[str, DatabaseConfig] = {}
        self._comparison_configs: Dict[str, ComparisonConfig] = {}
        self._system_config: Optional[SystemConfig] = None
        
        # 加载配置
        self._load_configs()
    
    def _load_configs(self):
        """加载所有配置"""
        try:
            # 加载系统配置
            self._load_system_config()
            
            # 加载数据库配置
            self._load_database_configs()
            
            # 加载比对配置
            self._load_comparison_configs()
            
            logger.info("配置加载完成")
        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}")
            # 使用默认配置
            self._system_config = SystemConfig()
    
    def _load_system_config(self):
        """加载系统配置"""
        config_file = self.config_dir / "system.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                self._system_config = SystemConfig(**config_data)
        else:
            self._system_config = SystemConfig()
            self._save_system_config()
    
    def _load_database_configs(self):
        """加载数据库配置"""
        db_config_dir = self.config_dir / "databases"
        if not db_config_dir.exists():
            db_config_dir.mkdir()
            return
        
        for config_file in db_config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    config_name = config_file.stem
                    self._database_configs[config_name] = DatabaseConfig(**config_data)
            except Exception as e:
                logger.error(f"加载数据库配置失败: {config_file}, 错误: {str(e)}")
    
    def _load_comparison_configs(self):
        """加载比对配置"""
        comp_config_dir = self.config_dir / "comparisons"
        if not comp_config_dir.exists():
            comp_config_dir.mkdir()
            return
        
        for config_file in comp_config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    config_name = config_file.stem
                    self._comparison_configs[config_name] = ComparisonConfig(**config_data)
            except Exception as e:
                logger.error(f"加载比对配置失败: {config_file}, 错误: {str(e)}")
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self._system_config or SystemConfig()
    
    def get_database_config(self, name: str) -> Optional[DatabaseConfig]:
        """获取数据库配置"""
        return self._database_configs.get(name)
    
    def get_comparison_config(self, name: str) -> Optional[ComparisonConfig]:
        """获取比对配置"""
        return self._comparison_configs.get(name)
    
    def save_database_config(self, name: str, config: DatabaseConfig) -> bool:
        """保存数据库配置"""
        try:
            db_config_dir = self.config_dir / "databases"
            db_config_dir.mkdir(exist_ok=True)
            
            config_file = db_config_dir / f"{name}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                # 排除密码字段
                config_dict = config.dict(exclude={'password'})
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            self._database_configs[name] = config
            logger.info(f"数据库配置已保存: {name}")
            return True
        except Exception as e:
            logger.error(f"保存数据库配置失败: {name}, 错误: {str(e)}")
            return False
    
    def save_comparison_config(self, name: str, config: ComparisonConfig) -> bool:
        """保存比对配置"""
        try:
            comp_config_dir = self.config_dir / "comparisons"
            comp_config_dir.mkdir(exist_ok=True)
            
            config_file = comp_config_dir / f"{name}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config.dict(), f, indent=2, ensure_ascii=False)
            
            self._comparison_configs[name] = config
            logger.info(f"比对配置已保存: {name}")
            return True
        except Exception as e:
            logger.error(f"保存比对配置失败: {name}, 错误: {str(e)}")
            return False
    
    def _save_system_config(self):
        """保存系统配置"""
        try:
            config_file = self.config_dir / "system.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self._system_config.dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存系统配置失败: {str(e)}")
    
    def list_database_configs(self) -> List[str]:
        """列出所有数据库配置名称"""
        return list(self._database_configs.keys())
    
    def list_comparison_configs(self) -> List[str]:
        """列出所有比对配置名称"""
        return list(self._comparison_configs.keys())
    
    def delete_database_config(self, name: str) -> bool:
        """删除数据库配置"""
        try:
            config_file = self.config_dir / "databases" / f"{name}.json"
            if config_file.exists():
                config_file.unlink()
            
            self._database_configs.pop(name, None)
            logger.info(f"数据库配置已删除: {name}")
            return True
        except Exception as e:
            logger.error(f"删除数据库配置失败: {name}, 错误: {str(e)}")
            return False
    
    def delete_comparison_config(self, name: str) -> bool:
        """删除比对配置"""
        try:
            config_file = self.config_dir / "comparisons" / f"{name}.json"
            if config_file.exists():
                config_file.unlink()
            
            self._comparison_configs.pop(name, None)
            logger.info(f"比对配置已删除: {name}")
            return True
        except Exception as e:
            logger.error(f"删除比对配置失败: {name}, 错误: {str(e)}")
            return False
    
    def validate_config(self, config_type: str, config_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """验证配置"""
        errors = []
        try:
            if config_type == "database":
                DatabaseConfig(**config_data)
            elif config_type == "comparison":
                ComparisonConfig(**config_data)
            elif config_type == "system":
                SystemConfig(**config_data)
            else:
                errors.append(f"未知的配置类型: {config_type}")
                return False, errors
            
            return True, []
        except Exception as e:
            errors.append(str(e))
            return False, errors


# 全局配置管理器实例
config_manager = ConfigManager()
