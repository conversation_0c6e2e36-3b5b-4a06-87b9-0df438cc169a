import os
import logging
from utils.config_manager import SmartConfigManager
from connectors.db2_connector import DB2Connector
from reporters.csv_reporter import CsvReporter
from core.hybrid_engine import compare_sources_hybrid

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """主函数，使用混合策略进行数据比对"""
    logger.info("开始混合智能数据比对...")
    
    # 1. 加载配置
    config_manager = SmartConfigManager()
    config_files = config_manager.auto_discover_configs()
    if not config_files:
        logger.error("未找到配置文件，程序退出。")
        return
    if not config_manager.load_config(config_files[0]):
        logger.error("无法加载配置，程序退出。")
        return
    
    # 2. 从配置中提取数据库和报告器信息
    try:
        db1_config = dict(config_manager.config['DB1'])
        db2_config = dict(config_manager.config['DB2'])
        
        # 假设我们只比对第一个表格规则
        table_rule = config_manager.rules.find('table')
        if table_rule is None:
            logger.error("在规则文件中找不到任何 'table' 规则。")
            return
        
        sql1 = table_rule.find('sql_1').text.strip()
        sql2 = table_rule.find('sql_2').text.strip()
        
        # 报告器配置
        reporter_config = {'filepath': 'hybrid_comparison_report.csv'}
        
    except (KeyError, AttributeError) as e:
        logger.error(f"配置文件格式错误: {e}")
        return
    
    # 3. 实例化连接器和报告器
    source_a = DB2Connector(db1_config, query=sql1)
    source_b = DB2Connector(db2_config, query=sql2)
    reporter = CsvReporter(config=reporter_config)
    
    # 4. 配置混合引擎
    hybrid_config = {
        'strategy_selector': {
            'small_data_threshold': 100000,  # 10万条记录作为阈值
            'memory_limit_mb': 2048,         # 2GB内存限制
            'force_strategy': None           # 自动选择策略，可设置为 'hash' 或 'merge' 强制使用
        }
    }
    
    # 5. 执行混合比对
    try:       
        def progress_callback(message):
            """进度回调函数"""
            logger.info(f"进度更新: {message}")
        
        result = compare_sources_hybrid(
            source_a, source_b, reporter,
            config=hybrid_config,
            progress_callback=progress_callback
        )
        
        # 输出详细结果
        logger.info("=" * 60)
        logger.info("混合比对完成")
        logger.info("=" * 60)
        logger.info(f"使用策略: {result['strategy_info']['strategy_name']}")
        logger.info(f"选择原因: {result['strategy_info']['reason']}")
        logger.info(f"需要排序: {'是' if result['strategy_info']['requires_sorting'] else '否'}")
        logger.info(f"估算内存: {result['strategy_info']['estimated_memory_mb']:.1f} MB")
        logger.info(f"A源记录数: {result['total_records_a']:,}")
        logger.info(f"B源记录数: {result['total_records_b']:,}")
        logger.info(f"总差异数: {result['differences']:,}")
        logger.info(f"总耗时: {result['total_time']:.2f} 秒")
        logger.info(f"处理速率: {result['processing_rate']:.2f} 条/秒")
        logger.info(f"报告文件: {reporter_config['filepath']}")
        
        # 策略特定的统计信息
        if 'load_time_a' in result:
            # 哈希策略的统计信息
            logger.info("哈希策略详细统计:")
            logger.info(f"  A源加载时间: {result['load_time_a']:.2f} 秒")
            logger.info(f"  B源处理时间: {result['load_time_b']:.2f} 秒")
            logger.info(f"  数据比较时间: {result['compare_time']:.2f} 秒")
            logger.info(f"  差异报告时间: {result['report_time']:.2f} 秒")
        elif 'total_loop_time' in result:
            # 归并策略的统计信息
            logger.info("归并策略详细统计:")
            logger.info(f"  循环总时间: {result['total_loop_time']:.2f} 秒")
            logger.info(f"  A源获取时间: {result['fetch_time_a']:.2f} 秒")
            logger.info(f"  B源获取时间: {result['fetch_time_b']:.2f} 秒")
            logger.info(f"  数据比较时间: {result['compare_time']:.2f} 秒")
            logger.info(f"  差异报告时间: {result['report_time']:.2f} 秒")
        
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"混合比对过程中发生严重错误: {e}", exc_info=True)


if __name__ == '__main__':
    main()
