#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DB-Agent GUI主程序

数据库比对工具的图形用户界面主入口程序，支持真实数据库连接和模拟模式
具备智能模式切换、完整的错误处理和用户友好的界面

功能特性：
- 智能检测DB2驱动程序状态，自动选择真实或模拟模式
- 支持真实数据库连接和数据比对
- 配置文件驱动的数据库连接和比对规则
- 完整的进度监控和结果展示
- 优雅的错误处理和用户指导
"""
import sys
import os
import json
import uuid
import time
import threading
import configparser
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
# 导入tkinter库
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
# 导入HTTP客户端
import requests
from urllib.parse import urljoin
# 尝试导入pandas
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ pandas未安装，导出功能将受限")

# 导入Agent核心模块
try:
    # 添加当前目录到Python路径，以便正确导入模块
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # 在导入其他模块之前，先配置DB2驱动程序
    try:
        from core.db_utils import configure_db2_driver
        db2_configured = configure_db2_driver()
        if db2_configured:
            print("✅ DB2驱动程序配置成功")
        else:
            print("⚠️ DB2驱动程序配置失败，将使用模拟模式")
    except Exception as e:
        print(f"⚠️ 配置DB2驱动程序时出错: {e}")
        db2_configured = False

    # 导入核心模块
    from app.models.db import DatabaseType, DatabaseConnection
    from app.models.logger import logger
    from app.models.task import CompareTaskRequest, CompareConfig, TaskStatus

    # 导入API服务类
    from app.core.task_manager import TaskManager
    from app.core.db_utils import test_connection
    from app.core.comparison import unified_comparison_service
    from app.core.dependencies import get_task_manager
    # 手动初始化比对引擎
    from app.core.comparison_init import initialize_comparison_engines

    AGENT_MODULES_AVAILABLE = True

except ImportError as e:
    print(f"⚠️ 部分Agent模块导入失败: {e}")
    print("GUI将以简化模式运行，某些功能可能不可用")
    AGENT_MODULES_AVAILABLE = False

    # 创建简化的数据类型
    class DatabaseType:
        DB2 = "db2"
        ORACLE = "oracle"
        GAUSSDB = "gaussdb"
        SQLSERVER = "sqlserver"
        POSTGRESQL = "postgresql"
        MYSQL = "mysql"

    class DatabaseConnection:
        def __init__(self, type, host, port, username, password, database, schema=None):
            self.type = type
            self.host = host
            self.port = port
            self.username = username
            self.password = password
            self.database = database
            self.schema = schema

    class logger:
        @staticmethod
        def info(msg): print(f"INFO: {msg}")
        @staticmethod
        def error(msg): print(f"ERROR: {msg}")
        @staticmethod
        def warning(msg): print(f"WARNING: {msg}")
        @staticmethod
        def debug(msg): print(f"DEBUG: {msg}")


class APIClient:
    """HTTP API客户端 - 用于与REST API服务通信"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except Exception:
            return False

    def create_comparison_task(self, request_data: dict) -> dict:
        """创建比对任务"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/tasks/compare",
                json=request_data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"创建比对任务失败: {str(e)}")

    def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/tasks/{task_id}/status",
                timeout=10
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"获取任务状态失败: {str(e)}")

    def get_task_result(self, task_id: str, page: int = 1, page_size: int = 20) -> dict:
        """获取任务结果"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/tasks/{task_id}/result",
                params={'page': page, 'page_size': page_size, 'include_details': True},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise Exception(f"获取任务结果失败: {str(e)}")

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            response = self.session.delete(
                f"{self.base_url}/api/tasks/{task_id}",
                timeout=10
            )
            return response.status_code in [200, 204]
        except Exception:
            return False


class SmartConfigManager:
    """智能配置管理器 - 支持自动发现和加载配置文件"""

    def __init__(self):
        self.config = None
        self.rules = None
        self.config_file = None
        self.rules_file = None

    def auto_discover_configs(self) -> List[str]:
        """自动发现配置文件"""
        config_files = []

        # 搜索路径
        search_paths = [
            os.path.join(os.path.dirname(__file__), "config"),
            os.path.dirname(__file__),
            os.getcwd()
        ]

        for search_path in search_paths:
            if os.path.exists(search_path):
                for file in os.listdir(search_path):
                    if file.endswith('.ini'):
                        full_path = os.path.join(search_path, file)
                        config_files.append(full_path)

        return config_files

    def load_config(self, config_file: str) -> bool:
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            self.config.read(config_file, encoding='utf-8')
            self.config_file = config_file

            # 验证必要的配置节
            required_sections = ['COMMON', 'DB1', 'DB2']
            for section in required_sections:
                if not self.config.has_section(section):
                    raise ValueError(f"配置文件缺少必要的节: {section}")

            # 加载规则文件
            rules_file = self.config.get('COMMON', 'TAB_RULE')
            if not os.path.isabs(rules_file):
                rules_file = os.path.join(os.path.dirname(config_file), rules_file)

            return self.load_rules(rules_file)

        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            return False

    def load_rules(self, rules_file: str) -> bool:
        """加载规则文件"""
        try:
            tree = ET.parse(rules_file)
            self.rules = tree.getroot()
            self.rules_file = rules_file
            return True
        except Exception as e:
            logger.error(f"加载规则文件失败: {str(e)}")
            return False

    def get_database_config(self, db_key: str) -> DatabaseConnection:
        """获取数据库配置"""
        if not self.config or not self.config.has_section(db_key):
            return None

        section = self.config[db_key]
        return DatabaseConnection(
            type=DatabaseType(section.get('TYPE', 'db2').lower()),
            host=section.get('IP'),
            port=int(section.get('PORT', 50000)),
            username=section.get('USER_NAME'),
            password=section.get('PASSWORD'),
            database=section.get('SCHEMA')
        )

    def get_comparison_tables(self) -> List[Dict[str, Any]]:
        """获取比对表配置"""
        tables = []
        if self.rules is not None:
            for table in self.rules.findall('table'):
                table_info = {
                    'table_id': table.get('table_id'),
                    'remark': table.get('remark', ''),
                    'sql_1': table.find('sql_1').text.strip() if table.find('sql_1') is not None else '',
                    'sql_2': table.find('sql_2').text.strip() if table.find('sql_2') is not None else ''
                }
                tables.append(table_info)
        return tables

    def get_title(self) -> str:
        """获取应用程序标题"""
        if self.config and self.config.has_option('COMMON', 'TITLE'):
            return self.config.get('COMMON', 'TITLE')
        return "数据库比对工具"

    def get_database_config_dict(self, db_key: str) -> Dict[str, Any]:
        """获取数据库配置字典（用于需要原始配置的场景）"""
        if not self.config or not self.config.has_section(db_key):
            return {}

        config_dict = {}
        for key, value in self.config.items(db_key):
            config_dict[key] = value
        return config_dict


class EnhancedComparisonToolGUI:
    """DB-Agent GUI主程序 - 支持真实数据库连接和智能模式切换"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DB-Agent - 数据库比对工具")

        # 初始化窗口管理器（在设置几何形状之前）
        self._init_window_manager()

        # 应用智能窗口尺寸和位置
        self._apply_smart_window_geometry()

        # 初始化基本组件（不依赖GUI的）
        self.config_manager = SmartConfigManager()
        self.current_task_id = None
        self.comparison_results = {}
        self.is_comparison_running = False
        self.cancel_requested = False

        # 性能优化配置
        self.max_display_records = 10000  # 最大显示记录数
        self.batch_size = 1000  # 批量处理大小
        self.current_displayed_records = 0  # 当前已显示记录数
        self.pending_records = []  # 待显示记录队列
        self.display_warning_shown = False  # 是否已显示数量警告
        self.record_processing_interval = 100  # 记录处理间隔(ms)

        # 分页和筛选相关变量（传统模式）
        self.all_diff_records = []  # 存储所有差异记录
        self.filtered_records = []  # 存储筛选后的记录
        self.current_page = 1
        self.total_pages = 0
        self.page_size = 50

        # 实时统计变量
        self.completed_tables_count = 0
        self.total_tables_count = 0
        self.total_differences_count = 0
        self.comparison_start_time = None

        # 线程安全和界面更新优化
        self._gui_update_queue = []  # GUI更新队列
        self._last_gui_update_time = 0  # 上次GUI更新时间
        self._gui_update_interval = 0.1  # GUI更新间隔（秒）
        self._stats_update_pending = False  # 统计更新是否待处理
        self._log_buffer = []  # 日志缓冲区
        self._log_buffer_max_size = 100  # 日志缓冲区最大大小

        # 线程锁
        import threading
        self._gui_update_lock = threading.Lock()
        self._stats_lock = threading.Lock()

        # 初始化消息缓冲区（用于在log_text创建前存储消息）
        self._message_buffer = []

        # 创建界面（必须在任何log_message调用之前）
        self.create_widgets()

        # 现在可以安全地使用log_message了
        self.log_message("🚀 DB-Agent GUI主程序启动")

        # 初始化API服务实例
        self._initialize_api_services()

        # 检查Agent模块可用性
        self._check_agent_modules()

        # 加载用户偏好设置
        self._load_user_preferences()

        # 处理缓冲的消息
        self._flush_message_buffer()

        # 启动GUI更新调度器
        self._start_gui_update_scheduler()

        # 延迟自动加载配置文件，确保界面完全创建后再加载
        self.root.after(50, self._auto_load_config)

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_closing)

    def _start_gui_update_scheduler(self):
        """启动GUI更新调度器"""
        try:
            # 启动定时GUI更新
            self._schedule_gui_updates()
            self.log_message("✅ GUI更新调度器启动成功")
        except Exception as e:
            self.log_message(f"❌ GUI更新调度器启动失败: {str(e)}")

    def _schedule_gui_updates(self):
        """定时处理GUI更新队列"""
        try:
            # 处理GUI更新队列
            self._process_gui_update_queue()

            # 处理日志缓冲区
            self._process_log_buffer()

            # 定时调度下次更新（100ms间隔）
            self.root.after(100, self._schedule_gui_updates)

        except Exception as e:
            # 如果出错，继续调度以保持系统稳定
            self.root.after(100, self._schedule_gui_updates)

    def _process_gui_update_queue(self):
        """处理GUI更新队列"""
        try:
            with self._gui_update_lock:
                if not self._gui_update_queue:
                    return

                # 批量处理更新
                updates_to_process = self._gui_update_queue.copy()
                self._gui_update_queue.clear()

            # 执行更新（在锁外执行以避免死锁）
            for update_func in updates_to_process:
                try:
                    update_func()
                except Exception as e:
                    # 单个更新失败不影响其他更新
                    pass

        except Exception as e:
            pass

    def _process_log_buffer(self):
        """处理日志缓冲区"""
        try:
            if not self._log_buffer or not hasattr(self, 'log_text') or not self.log_text:
                return

            # 批量处理日志
            logs_to_process = self._log_buffer.copy()
            self._log_buffer.clear()

            # 批量插入日志
            if logs_to_process:
                log_text = '\n'.join(logs_to_process) + '\n'
                self.log_text.insert(tk.END, log_text)
                self.log_text.see(tk.END)

        except Exception as e:
            pass

    def _queue_gui_update(self, update_func):
        """将GUI更新加入队列（线程安全）"""
        try:
            with self._gui_update_lock:
                self._gui_update_queue.append(update_func)
        except Exception as e:
            # 如果队列操作失败，直接执行更新
            try:
                self.root.after(0, update_func)
            except:
                pass

    def _queue_stats_update(self):
        """队列统计信息更新（防止重复更新）"""
        try:
            with self._stats_lock:
                if self._stats_update_pending:
                    return
                self._stats_update_pending = True

            def update_stats():
                try:
                    with self._stats_lock:
                        self._stats_update_pending = False
                    self.update_real_time_stats()
                except Exception as e:
                    with self._stats_lock:
                        self._stats_update_pending = False

            self._queue_gui_update(update_stats)

        except Exception as e:
            pass

    def _initialize_api_services(self):
        """初始化API服务实例"""
        try:
            # 初始化HTTP API客户端
            self.api_client = APIClient()
            self.log_message("✅ HTTP API客户端初始化成功")

            # 测试API连接
            if self.api_client.test_connection():
                self.log_message("✅ API服务连接测试成功")
                self.api_available = True
            else:
                self.log_message("⚠️ API服务连接测试失败，将使用本地模式")
                self.api_available = False

            # 如果API不可用，尝试本地服务
            if not self.api_available and AGENT_MODULES_AVAILABLE:
                try:
                    self.task_manager = TaskManager()
                    self.log_message("✅ 本地任务管理器初始化成功")
                    self.local_mode = True
                except Exception as e:
                    self.log_message(f"⚠️ 本地任务管理器初始化失败: {e}")
                    self.task_manager = None
                    self.local_mode = False
            else:
                self.task_manager = None
                self.local_mode = False

        except Exception as e:
            self.log_message(f"❌ API服务初始化异常: {e}")
            self.api_client = None
            self.task_manager = None
            self.api_available = False
            self.local_mode = False

    def _check_agent_modules(self):
        """检查Agent模块可用性"""
        try:
            if not AGENT_MODULES_AVAILABLE:
                self.log_message("⚠️ Agent模块不可用，将使用模拟模式")
                self.log_message("💡 要启用真实数据库连接，请确保Agent核心模块正确安装")
        except Exception as e:
            self.log_message(f"❌ 检查Agent模块时出错: {e}")

    def _flush_message_buffer(self):
        """处理缓冲的消息"""
        try:
            if hasattr(self, '_message_buffer') and self._message_buffer:
                for message in self._message_buffer:
                    self.log_message(message)
                self._message_buffer.clear()
        except Exception as e:
            print(f"处理消息缓冲区失败: {e}")

    def _auto_load_config(self):
        """自动加载配置文件"""
        try:
            config_files = self.config_manager.auto_discover_configs()
            if config_files:
                # 优先加载config目录下的Conifg.ini
                preferred_config = None
                for config_file in config_files:
                    if 'Conifg.ini' in config_file:
                        preferred_config = config_file
                        break

                if not preferred_config:
                    preferred_config = config_files[0]

                if self.config_manager.load_config(preferred_config):
                    # 更新配置文件路径显示
                    self.config_file_var.set(preferred_config)

                    # 立即加载表列表，避免延迟显示
                    self._load_table_list_immediately()

                    # 更新窗口标题
                    title = self.config_manager.get_title()
                    self.root.title(f"DB-Agent - {title}")

                    self.log_message(f"✅ 自动加载配置文件: {os.path.basename(preferred_config)}")

                    # 在表列表加载完成后，再设置分割位置
                    self.root.after(50, self._set_initial_paned_position)

                    return True
        except Exception as e:
            self.log_message(f"⚠️ 自动加载配置失败: {e}")

        return False

    def _load_table_list_immediately(self):
        """立即加载表列表，避免延迟显示"""
        try:
            # 清空现有数据
            for item in self.table_tree.get_children():
                self.table_tree.delete(item)

            # 获取比对表列表
            tables = self.config_manager.get_comparison_tables()

            if tables:
                for table in tables:
                    table_id = table.get('table_id', '')  # 修复：使用正确的键名
                    remark = table.get('remark', '')
                    status = "就绪"

                    self.table_tree.insert('', 'end', values=(table_id, remark, status))

                self.log_message(f"✅ 立即加载表列表完成，共 {len(tables)} 个表")

                # 数据加载完成后，延迟调整列宽以确保界面已完全渲染
                self.root.after(100, self._adjust_table_column_widths)
            else:
                self.log_message("⚠️ 未找到比对表配置")

        except Exception as e:
            self.log_message(f"❌ 立即加载表列表失败: {e}")

    def _load_user_preferences(self):
        """加载用户偏好设置"""
        try:
            # 加载窗口状态偏好设置
            self._load_window_preferences()
        except Exception as e:
            self.log_message(f"⚠️ 加载用户偏好设置失败: {e}")

    def _init_window_manager(self):
        """初始化窗口管理器"""
        try:
            # 窗口状态配置文件路径
            self.window_config_file = os.path.join(
                os.path.dirname(__file__),
                "config", "window_state.json"
            )

            # 确保配置目录存在
            config_dir = os.path.dirname(self.window_config_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)

            # 默认窗口配置
            self.default_window_config = {
                "width": 1400,
                "height": 900,
                "x": None,  # 将在_calculate_smart_geometry中计算
                "y": None,  # 将在_calculate_smart_geometry中计算
                "maximized": False,
                "screen_width": None,
                "screen_height": None
            }

            # 最小窗口尺寸
            self.min_window_size = (800, 600)

            # 设置最小尺寸
            self.root.minsize(*self.min_window_size)

        except Exception as e:
            print(f"⚠️ 初始化窗口管理器失败: {e}")

    def _apply_smart_window_geometry(self):
        """应用智能窗口几何形状"""
        try:
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 尝试加载保存的窗口状态
            saved_config = self._load_window_state()

            if saved_config and self._is_valid_window_config(saved_config, screen_width, screen_height):
                # 使用保存的配置
                width = saved_config.get("width", self.default_window_config["width"])
                height = saved_config.get("height", self.default_window_config["height"])
                x = saved_config.get("x")
                y = saved_config.get("y")
                maximized = saved_config.get("maximized", False)

                # 确保窗口在屏幕范围内
                if x is not None and y is not None:
                    x = max(0, min(x, screen_width - width))
                    y = max(0, min(y, screen_height - height))
                else:
                    # 居中显示
                    x = (screen_width - width) // 2
                    y = (screen_height - height) // 2

                # 应用几何形状
                self.root.geometry(f"{width}x{height}+{x}+{y}")

                # 如果之前是最大化状态，则最大化窗口
                if maximized:
                    self.root.after(100, lambda: self.root.state('zoomed'))

            else:
                # 使用智能计算的初始配置
                width, height, x, y = self._calculate_smart_geometry(screen_width, screen_height)
                self.root.geometry(f"{width}x{height}+{x}+{y}")
                self.log_message(f"✅ 应用智能窗口配置: {width}x{height}+{x}+{y}")

        except Exception as e:
            # 如果出错，使用默认配置
            self.root.geometry("1400x900")
            self.log_message(f"⚠️ 应用窗口几何形状失败，使用默认配置: {e}")

    def _calculate_smart_geometry(self, screen_width: int, screen_height: int) -> tuple:
        """计算智能窗口几何形状（75%屏幕尺寸，居中显示）"""
        try:
            # 计算75%屏幕尺寸
            width = int(screen_width * 0.75)
            height = int(screen_height * 0.75)

            # 确保不小于最小尺寸
            width = max(width, self.min_window_size[0])
            height = max(height, self.min_window_size[1])

            # 确保不超过屏幕尺寸
            width = min(width, screen_width - 100)  # 留50px边距
            height = min(height, screen_height - 100)  # 留50px边距

            # 居中计算
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2

            return width, height, x, y

        except Exception as e:
            print(f"⚠️ 计算智能几何形状失败: {e}")
            return 1400, 900, 100, 100

    def _load_window_state(self) -> dict:
        """加载保存的窗口状态"""
        try:
            if os.path.exists(self.window_config_file):
                with open(self.window_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载窗口状态失败: {e}")
        return None

    def _save_window_state(self):
        """保存当前窗口状态"""
        try:
            # 获取当前窗口状态
            current_state = self._get_current_window_state()

            # 保存到文件
            with open(self.window_config_file, 'w', encoding='utf-8') as f:
                json.dump(current_state, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ 保存窗口状态失败: {e}")

    def _get_current_window_state(self) -> dict:
        """获取当前窗口状态"""
        try:
            # 检查是否最大化
            is_maximized = self.root.state() == 'zoomed'

            if is_maximized:
                # 如果是最大化状态，使用之前保存的正常尺寸
                # 或者使用默认尺寸
                width = getattr(self, '_normal_width', self.default_window_config["width"])
                height = getattr(self, '_normal_height', self.default_window_config["height"])
                x = getattr(self, '_normal_x', None)
                y = getattr(self, '_normal_y', None)
            else:
                # 获取当前窗口几何形状
                geometry = self.root.geometry()
                # 解析几何字符串 "widthxheight+x+y"
                size_part, pos_part = geometry.split('+', 1)
                width, height = map(int, size_part.split('x'))

                if '+' in pos_part:
                    x_str, y_str = pos_part.split('+', 1)
                    x, y = int(x_str), int(y_str)
                else:
                    # 处理负坐标的情况
                    parts = pos_part.split('-')
                    if len(parts) >= 2:
                        x = -int(parts[1]) if parts[0] == '' else int(parts[0])
                        y = -int(parts[2]) if len(parts) > 2 else 0
                    else:
                        x, y = 0, 0

                # 保存正常状态的尺寸和位置
                self._normal_width = width
                self._normal_height = height
                self._normal_x = x
                self._normal_y = y

            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            return {
                "width": width,
                "height": height,
                "x": x,
                "y": y,
                "maximized": is_maximized,
                "screen_width": screen_width,
                "screen_height": screen_height,
                "timestamp": time.time()
            }

        except Exception as e:
            print(f"⚠️ 获取窗口状态失败: {e}")
            return self.default_window_config.copy()

    def _is_valid_window_config(self, config: dict, screen_width: int, screen_height: int) -> bool:
        """验证窗口配置是否有效"""
        try:
            if not config:
                return False

            # 检查必要字段
            required_fields = ["width", "height", "x", "y"]
            if not all(field in config for field in required_fields):
                return False

            width = config["width"]
            height = config["height"]
            x = config["x"]
            y = config["y"]

            # 检查尺寸是否合理
            if width < self.min_window_size[0] or height < self.min_window_size[1]:
                return False

            if width > screen_width or height > screen_height:
                return False

            # 检查位置是否在屏幕范围内（允许部分超出）
            if x is not None and y is not None:
                if x < -width // 2 or y < -height // 2:
                    return False

                if x > screen_width or y > screen_height:
                    return False

            # 检查屏幕分辨率是否发生重大变化
            saved_screen_width = config.get("screen_width")
            saved_screen_height = config.get("screen_height")

            if saved_screen_width and saved_screen_height:
                # 如果屏幕尺寸变化超过20%，认为配置可能不适用
                width_change = abs(screen_width - saved_screen_width) / saved_screen_width
                height_change = abs(screen_height - saved_screen_height) / saved_screen_height

                if width_change > 0.2 or height_change > 0.2:
                    return False

            return True

        except Exception as e:
            print(f"⚠️ 验证窗口配置失败: {e}")
            return False

    def _load_window_preferences(self):
        """加载窗口偏好设置"""
        try:
            # 绑定窗口状态变化事件
            self.root.bind('<Configure>', self._on_window_configure_for_state)

        except Exception as e:
            print(f"⚠️ 加载窗口偏好设置失败: {e}")

    def _on_window_configure_for_state(self, event):
        """窗口配置变化事件处理（用于状态保存）"""
        try:
            # 只处理主窗口的事件
            if event.widget != self.root:
                return

            # 延迟保存状态，避免频繁保存
            if hasattr(self, '_save_state_timer'):
                self.root.after_cancel(self._save_state_timer)

            # 2秒后保存状态
            self._save_state_timer = self.root.after(2000, self._save_window_state)

        except Exception as e:
            pass  # 忽略配置事件中的错误

    def _on_window_closing(self):
        """窗口关闭事件处理"""
        try:
            # 保存窗口状态
            self._save_window_state()

            # 停止比对任务（如果正在运行）
            if hasattr(self, 'is_comparison_running') and self.is_comparison_running:
                self.stop_comparison()

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            print(f"⚠️ 窗口关闭处理失败: {e}")
            # 确保窗口能够关闭
            try:
                self.root.destroy()
            except:
                pass

    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 创建左侧表列表和右侧结果区域
        self.create_main_content(content_frame)
        
        # 创建底部状态栏
        self.create_status_bar(main_frame)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 配置文件选择
        ttk.Label(toolbar, text="配置文件:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.config_file_var = tk.StringVar()
        config_entry = ttk.Entry(toolbar, textvariable=self.config_file_var, width=50, state='readonly')
        config_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(toolbar, text="选择配置", command=self.select_config_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 执行控制按钮
        ttk.Button(toolbar, text="开始比对", command=self.start_comparison, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="停止比对", command=self.stop_comparison).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="导出结果", command=self.export_results).pack(side=tk.LEFT, padx=(0, 5))
    
    def create_main_content(self, parent):
        """创建主要内容区域"""
        # 创建水平分割面板
        self.main_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧：表列表和控制（占40%宽度）
        left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(left_frame, weight=2)

        # 右侧：结果显示（占60%宽度）
        right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(right_frame, weight=3)

        self.create_table_list(left_frame)
        self.create_result_area(right_frame)

        # 初始化分割位置设置标志
        self._paned_position_set = False

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self._on_window_configure)

    def _set_initial_paned_position(self):
        """设置PanedWindow的初始分割位置"""
        try:
            # 避免重复设置
            if self._paned_position_set:
                return

            # 获取窗口宽度
            window_width = self.root.winfo_width()
            if window_width > 100:  # 确保窗口已经完全创建
                # 设置左侧占40%宽度
                left_width = int(window_width * 0.4)
                self.main_paned.sashpos(0, left_width)
                self._paned_position_set = True
            else:
                # 如果窗口还没完全创建，再次延迟
                self.root.after(100, self._set_initial_paned_position)
        except Exception as e:
            self.log_message(f"⚠️ 设置分割位置失败: {e}")

    def _on_window_configure(self, event):
        """窗口大小变化事件处理"""
        try:
            # 只处理主窗口的配置变化，且分割位置已经初始设置过
            if (event.widget == self.root and
                hasattr(self, 'main_paned') and
                hasattr(self, '_paned_position_set') and
                self._paned_position_set):

                # 延迟调整分割位置，避免频繁调整
                if hasattr(self, '_resize_timer'):
                    self.root.after_cancel(self._resize_timer)
                self._resize_timer = self.root.after(300, self._adjust_paned_position)
        except Exception as e:
            pass  # 忽略配置事件中的错误

    def _adjust_paned_position(self):
        """调整分割位置以维持40%比例"""
        try:
            window_width = self.root.winfo_width()
            if window_width > 100:
                left_width = int(window_width * 0.4)
                self.main_paned.sashpos(0, left_width)
        except Exception as e:
            pass  # 忽略调整过程中的错误

    def create_table_list(self, parent):
        """创建表列表区域"""
        # 表列表标题
        ttk.Label(parent, text="比对表列表", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 5))

        # 创建表格容器
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("表ID", "说明", "状态")
        self.table_tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)

        # 初始化列宽设置（将在数据加载后动态调整）
        for col in columns:
            self.table_tree.heading(col, text=col)
            if col == "表ID":
                self.table_tree.column(col, width=120, minwidth=80)  # 初始表ID列宽度
            elif col == "说明":
                self.table_tree.column(col, width=200, minwidth=150)  # 初始说明列宽度
            else:
                self.table_tree.column(col, width=80, minwidth=60)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.table_tree.yview)
        self.table_tree.configure(yscrollcommand=scrollbar.set)

        # 使用grid布局确保更好的控制
        self.table_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        # 配置grid权重
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # 绑定双击事件
        self.table_tree.bind("<Double-1>", self.on_table_double_click)

    def _adjust_table_column_widths(self):
        """动态调整表列表的列宽，确保表ID完整显示"""
        try:
            if not hasattr(self, 'table_tree') or not self.table_tree:
                return

            # 获取所有表ID，计算最长的表ID长度
            max_table_id_length = 0
            max_remark_length = 0

            for item in self.table_tree.get_children():
                values = self.table_tree.item(item, 'values')
                if values and len(values) >= 2:
                    table_id = str(values[0])
                    remark = str(values[1])
                    max_table_id_length = max(max_table_id_length, len(table_id))
                    max_remark_length = max(max_remark_length, len(remark))

            if max_table_id_length > 0:
                # 计算表ID列的理想宽度（每个字符约8像素，加上一些边距）
                ideal_table_id_width = max(120, min(300, max_table_id_length * 8 + 20))

                # 计算说明列的理想宽度
                ideal_remark_width = max(150, min(400, max_remark_length * 8 + 20))

                # 获取容器宽度来合理分配空间
                try:
                    container_width = self.table_tree.winfo_width()
                    if container_width > 100:  # 确保容器已经渲染
                        # 为状态列预留80px，滚动条预留20px
                        available_width = container_width - 80 - 20

                        # 如果表ID和说明列的理想宽度超过可用宽度，按比例调整
                        total_ideal = ideal_table_id_width + ideal_remark_width
                        if total_ideal > available_width:
                            # 优先保证表ID列，说明列可以适当压缩
                            if ideal_table_id_width > available_width * 0.6:
                                table_id_width = int(available_width * 0.6)
                                remark_width = available_width - table_id_width
                            else:
                                table_id_width = ideal_table_id_width
                                remark_width = available_width - table_id_width
                        else:
                            table_id_width = ideal_table_id_width
                            remark_width = ideal_remark_width
                    else:
                        # 容器还未完全渲染，使用理想宽度
                        table_id_width = ideal_table_id_width
                        remark_width = ideal_remark_width

                except:
                    # 如果获取容器宽度失败，使用理想宽度
                    table_id_width = ideal_table_id_width
                    remark_width = ideal_remark_width

                # 应用新的列宽
                self.table_tree.column("表ID", width=table_id_width, minwidth=80)
                self.table_tree.column("说明", width=remark_width, minwidth=100)
                self.table_tree.column("状态", width=80, minwidth=60)

        except Exception as e:
            self.log_message(f"⚠️ 调整表列表列宽失败: {e}")
    
    def create_result_area(self, parent):
        """创建结果显示区域"""
        # 创建选项卡
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 执行监控选项卡（合并执行状态和日志）
        self.create_monitor_tab(notebook)

        # 比对结果选项卡（增强版）
        self.create_enhanced_results_tab(notebook)
    
    def create_monitor_tab(self, parent):
        """创建执行监控选项卡（合并执行状态和日志）"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="执行监控")

        # 创建主容器，消除外边距
        main_container = ttk.Frame(frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 上半部分：进度和统计（紧凑布局）
        top_container = ttk.Frame(main_container)
        top_container.pack(fill=tk.X, padx=5, pady=(5, 0))

        # 进度显示
        progress_frame = ttk.LabelFrame(top_container, text="执行进度", padding=6)
        progress_frame.pack(fill=tk.X, pady=(0, 0))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=2)

        # 状态信息框架
        status_info_frame = ttk.Frame(progress_frame)
        status_info_frame.pack(fill=tk.X, pady=2)

        self.status_label = ttk.Label(status_info_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)

        self.current_table_label = ttk.Label(status_info_frame, text="", foreground="blue")
        self.current_table_label.pack(side=tk.RIGHT)

        # 实时统计信息，紧贴进度框架（消除间距）
        stats_frame = ttk.LabelFrame(top_container, text="实时统计", padding=6)
        stats_frame.pack(fill=tk.X, pady=(2, 0))

        # 统计信息网格
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        # 第一行统计
        row1 = ttk.Frame(stats_grid)
        row1.pack(fill=tk.X, pady=1)

        ttk.Label(row1, text="已完成表数:").pack(side=tk.LEFT)
        self.completed_tables_label = ttk.Label(row1, text="0", foreground="green", font=('Arial', 10, 'bold'))
        self.completed_tables_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(row1, text="总表数:").pack(side=tk.LEFT)
        self.total_tables_label = ttk.Label(row1, text="0", font=('Arial', 10, 'bold'))
        self.total_tables_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(row1, text="发现差异:").pack(side=tk.LEFT)
        self.total_diffs_label = ttk.Label(row1, text="0", foreground="red", font=('Arial', 10, 'bold'))
        self.total_diffs_label.pack(side=tk.LEFT, padx=(5, 0))

        # 第二行统计
        row2 = ttk.Frame(stats_grid)
        row2.pack(fill=tk.X, pady=1)

        ttk.Label(row2, text="预计剩余时间:").pack(side=tk.LEFT)
        self.eta_label = ttk.Label(row2, text="--", foreground="orange")
        self.eta_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(row2, text="已用时间:").pack(side=tk.LEFT)
        self.elapsed_time_label = ttk.Label(row2, text="00:00:00")
        self.elapsed_time_label.pack(side=tk.LEFT, padx=(5, 0))

        # 下半部分：执行日志，紧贴统计信息（消除间距）
        log_frame = ttk.LabelFrame(main_container, text="执行日志", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(2, 5))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 初始化统计变量
        self.completed_tables_count = 0
        self.total_tables_count = 0
        self.total_differences_count = 0
        self.comparison_start_time = None

        # 初始化取消标志
        self.cancel_requested = False
    
    def create_enhanced_results_tab(self, parent):
        """创建增强版比对结果选项卡"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="比对结果")

        # 顶部控制栏
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 左侧筛选控件
        filter_frame = ttk.Frame(control_frame)
        filter_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 表筛选
        ttk.Label(filter_frame, text="筛选表:").pack(side=tk.LEFT, padx=(0, 5))
        self.table_filter_var = tk.StringVar(value="全部表")
        self.table_filter_combo = ttk.Combobox(filter_frame, textvariable=self.table_filter_var,
                                               width=15, state="readonly")
        self.table_filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.table_filter_combo.bind('<<ComboboxSelected>>', self.on_table_filter_changed)

        # 差异类型筛选
        ttk.Label(filter_frame, text="差异类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.diff_type_filter_var = tk.StringVar(value="全部类型")
        self.diff_type_filter_combo = ttk.Combobox(filter_frame, textvariable=self.diff_type_filter_var,
                                                   width=15, state="readonly")
        self.diff_type_filter_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.diff_type_filter_combo.bind('<<ComboboxSelected>>', self.on_diff_type_filter_changed)

        # 搜索框
        ttk.Label(filter_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(filter_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.search_var.trace_add('write', self.on_search_changed)

        ttk.Button(filter_frame, text="清空", command=self.clear_search).pack(side=tk.LEFT, padx=(5, 0))

        # 右侧分页控件
        page_frame = ttk.Frame(control_frame)
        page_frame.pack(side=tk.RIGHT)

        # 每页显示数量
        ttk.Label(page_frame, text="每页:").pack(side=tk.LEFT, padx=(0, 5))
        self.page_size_var = tk.StringVar(value="50")
        page_size_combo = ttk.Combobox(page_frame, textvariable=self.page_size_var,
                                       values=["25", "50", "100", "200"], width=8, state="readonly")
        page_size_combo.pack(side=tk.LEFT, padx=(0, 10))
        page_size_combo.bind('<<ComboboxSelected>>', self.on_page_size_changed)

        # 分页导航
        nav_frame = ttk.Frame(page_frame)
        nav_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Button(nav_frame, text="首页", command=self.goto_first_page, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(nav_frame, text="上页", command=self.goto_prev_page, width=6).pack(side=tk.LEFT, padx=1)

        # 页码信息
        self.page_info_var = tk.StringVar(value="第0页，共0页")
        ttk.Label(nav_frame, textvariable=self.page_info_var).pack(side=tk.LEFT, padx=10)

        ttk.Button(nav_frame, text="下页", command=self.goto_next_page, width=6).pack(side=tk.LEFT, padx=1)
        ttk.Button(nav_frame, text="末页", command=self.goto_last_page, width=6).pack(side=tk.LEFT, padx=1)

        # 跳转页码
        ttk.Label(nav_frame, text="跳转:").pack(side=tk.LEFT, padx=(10, 5))
        self.goto_page_var = tk.StringVar()
        goto_entry = ttk.Entry(nav_frame, textvariable=self.goto_page_var, width=6)
        goto_entry.pack(side=tk.LEFT, padx=(0, 5))
        goto_entry.bind('<Return>', self.on_goto_page)
        ttk.Button(nav_frame, text="GO", command=self.goto_specific_page, width=4).pack(side=tk.LEFT)

        # 差异记录表格
        table_frame = ttk.Frame(frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加所属表列
        columns = ("所属表", "记录ID", "差异类型", "差异字段", "源数据", "目标数据")
        all_columns = columns + ("full_record",)
        self.diff_tree = ttk.Treeview(table_frame, columns=all_columns, show="headings", height=20)

        # 设置列标题和宽度
        column_widths = {"所属表": 100, "记录ID": 120, "差异类型": 100, "差异字段": 150, "源数据": 200, "目标数据": 200}
        for col in columns:
            self.diff_tree.heading(col, text=col)
            self.diff_tree.column(col, width=column_widths.get(col, 120))

        # 隐藏full_record列
        self.diff_tree.heading("full_record", text="")
        self.diff_tree.column("full_record", width=0, minwidth=0)

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.diff_tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.diff_tree.xview)
        self.diff_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        self.diff_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_v.grid(row=0, column=1, sticky="ns")
        scrollbar_h.grid(row=1, column=0, sticky="ew")

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 底部状态栏
        status_frame = ttk.Frame(frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.results_status_var = tk.StringVar(value="总计0条记录")
        ttk.Label(status_frame, textvariable=self.results_status_var).pack(side=tk.LEFT)

        # 快速操作按钮
        quick_frame = ttk.Frame(status_frame)
        quick_frame.pack(side=tk.RIGHT)

        ttk.Button(quick_frame, text="导出当前页", command=self.export_current_page).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_frame, text="导出全部", command=self.export_all_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_frame, text="清空结果", command=self.clear_results).pack(side=tk.LEFT, padx=2)

        # 绑定双击事件
        self.diff_tree.bind("<Double-1>", self.view_record_details)

        # 初始化分页相关变量
        self.all_diff_records = []  # 存储所有差异记录
        self.filtered_records = []  # 存储筛选后的记录
        self.current_page = 1
        self.total_pages = 0
        self.page_size = 50

        # 初始化筛选选项
        self.table_filter_combo['values'] = ["全部表"]
        self.diff_type_filter_combo['values'] = ["全部类型"]
    
    # 分页和筛选功能方法
    def on_table_filter_changed(self, event=None):
        """表筛选改变事件"""
        self.apply_filters()

    def on_diff_type_filter_changed(self, event=None):
        """差异类型筛选改变事件"""
        self.apply_filters()

    def on_search_changed(self, *args):
        """搜索内容改变事件"""
        self.apply_filters()

    def clear_search(self):
        """清空搜索"""
        self.search_var.set("")

    def on_page_size_changed(self, event=None):
        """每页显示数量改变事件"""
        try:
            self.page_size = int(self.page_size_var.get())
            self.current_page = 1
            self.update_pagination()
        except ValueError:
            pass

    def apply_filters(self):
        """应用筛选条件 - 传统模式"""
        try:
            if not hasattr(self, 'all_diff_records'):
                self.filtered_records = []
                self.update_pagination()
                return

            # 获取筛选条件
            table_filter = self.table_filter_var.get()
            diff_type_filter = self.diff_type_filter_var.get()
            search_text = self.search_var.get().strip().lower()

            # 应用筛选
            self.filtered_records = []
            for record in self.all_diff_records:
                # 表筛选
                if table_filter != "全部表" and record.get('table_id', '') != table_filter:
                    continue

                # 差异类型筛选
                if diff_type_filter != "全部类型" and record.get('diff_type', '') != diff_type_filter:
                    continue

                # 搜索筛选
                if search_text:
                    searchable_text = f"{record.get('record_id', '')} {record.get('source_preview', '')} {record.get('target_preview', '')}".lower()
                    if search_text not in searchable_text:
                        continue
                self.filtered_records.append(record)

            # 重置到第一页
            self.current_page = 1
            self.update_pagination()

        except Exception as e:
            self.log_message(f"❌ 应用筛选失败: {str(e)}")

    def update_pagination(self):
        """传统分页显示（备用）"""
        try:
            # 计算总页数
            total_records = len(self.filtered_records) if hasattr(self, 'filtered_records') else 0
            self.total_pages = max(1, (total_records + self.page_size - 1) // self.page_size)

            # 确保当前页在有效范围内
            self.current_page = max(1, min(self.current_page, self.total_pages))

            # 更新页码信息
            self.page_info_var.set(f"第{self.current_page}页，共{self.total_pages}页")
            self.results_status_var.set(f"总计{total_records}条记录，显示第{(self.current_page-1)*self.page_size+1}-{min(self.current_page*self.page_size, total_records)}条")

            # 显示当前页数据
            self.display_current_page()

        except Exception as e:
            self.log_message(f"❌ 更新分页失败: {str(e)}")

    def display_current_page(self):
        """传统显示当前页数据（备用）"""
        try:
            # 清空现有显示
            for item in self.diff_tree.get_children():
                self.diff_tree.delete(item)

            if not hasattr(self, 'filtered_records'):
                return

            # 计算当前页数据范围
            start_idx = (self.current_page - 1) * self.page_size
            end_idx = min(start_idx + self.page_size, len(self.filtered_records))

            # 显示当前页数据
            for i in range(start_idx, end_idx):
                record = self.filtered_records[i]
                self.diff_tree.insert('', 'end', values=(
                    record.get('table_id', ''),
                    record.get('record_id', ''),
                    record.get('diff_type', ''),
                    record.get('diff_fields', ''),
                    record.get('source_preview', ''),
                    record.get('target_preview', ''),
                    record.get('full_record_json', '')
                ))

        except Exception as e:
            self.log_message(f"❌ 显示当前页失败: {str(e)}")

    def goto_first_page(self):
        """跳转到首页"""
        self.current_page = 1
        self.update_pagination()

    def goto_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_pagination()

    def goto_next_page(self):
        """跳转到下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_pagination()

    def goto_last_page(self):
        """跳转到末页"""
        self.current_page = self.total_pages
        self.update_pagination()

    def on_goto_page(self, event=None):
        """回车跳转到指定页"""
        self.goto_specific_page()

    def goto_specific_page(self):
        """跳转到指定页"""
        try:
            page_num = int(self.goto_page_var.get())
            if 1 <= page_num <= self.total_pages:
                self.current_page = page_num
                self.update_pagination()
                self.goto_page_var.set("")
            else:
                messagebox.showwarning("警告", f"页码必须在1-{self.total_pages}之间")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的页码")

    def export_current_page(self):
        """导出当前页数据"""
        try:
            if not self.filtered_records:
                messagebox.showinfo("提示", "没有数据可导出")
                return

            # 获取当前页数据
            start_idx = (self.current_page - 1) * self.page_size
            end_idx = min(start_idx + self.page_size, len(self.filtered_records))
            current_page_data = self.filtered_records[start_idx:end_idx]

            # 调用导出功能
            self._export_records(current_page_data, f"当前页_{self.current_page}")

        except Exception as e:
            self.log_message(f"❌ 导出当前页失败: {str(e)}")

    def export_all_results(self):
        """导出全部结果"""
        try:
            if not self.filtered_records:
                messagebox.showinfo("提示", "没有数据可导出")
                return

            # 调用导出功能
            self._export_records(self.filtered_records, "全部结果")

        except Exception as e:
            self.log_message(f"❌ 导出全部结果失败: {str(e)}")

    def clear_results(self):
        """清空结果 - 传统模式"""
        try:
            if messagebox.askyesno("确认", "确定要清空所有比对结果吗？"):
                # 清空传统存储
                if hasattr(self, 'all_diff_records'):
                    self.all_diff_records = []

                self.filtered_records = []
                self.current_page = 1
                self.total_pages = 0
                self.total_differences_count = 0

                # 清空显示
                for item in self.diff_tree.get_children():
                    self.diff_tree.delete(item)

                # 重置筛选选项
                self.table_filter_combo['values'] = ["全部表"]
                self.table_filter_var.set("全部表")
                self.diff_type_filter_combo['values'] = ["全部类型"]
                self.diff_type_filter_var.set("全部类型")

                # 更新状态
                self.page_info_var.set("第0页，共0页")
                self.results_status_var.set("总计0条记录")

                self.log_message("✅ 比对结果已清空")

        except Exception as e:
            self.log_message(f"❌ 清空结果失败: {str(e)}")

    def update_real_time_stats(self):
        """更新实时统计信息 - 线程安全版本"""
        try:
            # 检查GUI组件是否存在
            if not hasattr(self, 'completed_tables_label'):
                return

            # 更新已完成表数
            self.completed_tables_label.config(text=str(self.completed_tables_count))

            # 更新总表数
            self.total_tables_label.config(text=str(self.total_tables_count))

            # 更新总差异数
            self.total_diffs_label.config(text=str(self.total_differences_count))

            # 更新已用时间
            if self.comparison_start_time:
                elapsed = datetime.now() - self.comparison_start_time
                elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
                self.elapsed_time_label.config(text=elapsed_str)

                # 计算预计剩余时间
                if self.completed_tables_count > 0 and self.total_tables_count > 0:
                    remaining_tables = self.total_tables_count - self.completed_tables_count
                    if remaining_tables > 0:
                        avg_time_per_table = elapsed.total_seconds() / self.completed_tables_count
                        eta_seconds = avg_time_per_table * remaining_tables
                        eta_str = str(timedelta(seconds=int(eta_seconds)))
                        self.eta_label.config(text=eta_str)
                    else:
                        self.eta_label.config(text="已完成")
                else:
                    self.eta_label.config(text="计算中...")

        except Exception as e:
            # 不在这里记录日志，避免递归调用
            pass

    def start_comparison_timer(self):
        """开始比对计时"""
        self.comparison_start_time = datetime.now()
        self.completed_tables_count = 0
        self.total_differences_count = 0

        # 获取总表数
        self.total_tables_count = len(self.table_tree.get_children())

        # 重置统计显示
        self.update_real_time_stats()

        # 启动定时更新
        self._schedule_stats_update()

    def _schedule_stats_update(self):
        """定时更新统计信息"""
        if self.is_comparison_running:
            self.update_real_time_stats()
            # 每秒更新一次
            self.root.after(1000, self._schedule_stats_update)

    def complete_table_comparison(self, table_id: str):
        """完成一个表的比对 - 线程安全版本"""
        self.completed_tables_count += 1

        # 使用队列更新GUI
        def update_gui():
            if hasattr(self, 'current_table_label'):
                self.current_table_label.config(text=f"已完成: {table_id}")

        self._queue_gui_update(update_gui)
        self._queue_stats_update()

    def set_current_table(self, table_id: str):
        """设置当前正在处理的表 - 线程安全版本"""
        def update_gui():
            if hasattr(self, 'current_table_label'):
                self.current_table_label.config(text=f"正在处理: {table_id}")

        self._queue_gui_update(update_gui)
        self._queue_stats_update()
    
    def create_status_bar(self, parent):
        """创建简化的状态栏（移除内存监控）"""
        try:
            # 创建传统状态栏
            status_frame = ttk.Frame(parent)
            status_frame.pack(fill=tk.X, pady=(5, 0))

            self.status_bar = ttk.Label(status_frame, text="就绪", relief=tk.SUNKEN)
            self.status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # 时间显示
            self.time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
            self.time_label.pack(side=tk.RIGHT)

            self.update_time()

        except Exception as e:
            self.log_message(f"❌ 状态栏创建失败: {e}")

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    def set_status(self, text: str):
        """设置状态栏文本"""
        try:
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.config(text=text)
        except Exception as e:
            pass
    

    
    def log_message(self, message: str):
        """添加日志消息 - 线程安全版本"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"

        # 检查log_text是否存在
        if hasattr(self, 'log_text') and self.log_text:
            try:
                # 使用日志缓冲区，避免频繁更新GUI
                self._log_buffer.append(log_entry)

                # 如果缓冲区满了，立即处理
                if len(self._log_buffer) >= self._log_buffer_max_size:
                    self._process_log_buffer()

            except Exception as e:
                # 如果缓冲区操作失败，回退到控制台输出
                print(f"日志缓冲失败: {e}")
                print(log_entry)
        else:
            # 如果log_text还不存在，存储到消息缓冲区或输出到控制台
            if hasattr(self, '_message_buffer'):
                self._message_buffer.append(message)
            else:
                # 如果连缓冲区都没有，直接输出到控制台
                print(log_entry)
    
    def select_config_file(self):
        """选择配置文件"""
        try:
            # 默认配置文件路径（config目录下的Conifg.ini）
            default_config = os.path.join(os.path.dirname(__file__), "config", "Conifg.ini")
            initial_dir = os.path.join(os.path.dirname(__file__), "config")
            
            filename = filedialog.askopenfilename(
                title="选择配置文件",
                initialdir=initial_dir,
                filetypes=[("配置文件", "*.ini"), ("所有文件", "*.*")]
            )
            
            if filename:
                self.log_message(f"📁 选择配置文件: {filename}")

                # 验证文件存在
                if not os.path.exists(filename):
                    self.log_message(f"❌ 配置文件不存在: {filename}")
                    messagebox.showerror("错误", "选择的配置文件不存在")
                    return

                if self.config_manager.load_config(filename):
                    # 更新配置文件路径显示
                    self.config_file_var.set(filename)
                    self.log_message(f"✅ 配置文件路径已设置: {filename}")

                    # 加载表列表
                    self.load_table_list()

                    # 更新窗口标题
                    title = self.config_manager.get_title()
                    self.root.title(f"DB-Agent - {title}")

                    self.log_message(f"✅ 配置文件加载成功: {filename}")
                    self.set_status("配置文件已加载")

                    # 强制刷新界面
                    self.root.update_idletasks()
                else:
                    self.log_message("❌ 配置管理器加载失败")
                    messagebox.showerror("错误", "配置文件加载失败，请检查文件格式")
            else:
                self.log_message("⚠️ 用户取消了配置文件选择")
                    
        except Exception as e:
            error_msg = f"选择配置文件失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def load_table_list(self):
        """加载表列表 - 修复显示问题"""
        try:
            # 清空现有列表
            for item in self.table_tree.get_children():
                self.table_tree.delete(item)

            self.log_message("🔍 正在加载表列表...")

            # 检查配置管理器状态
            if not self.config_manager or not self.config_manager.config:
                self.log_message("⚠️ 配置管理器或配置为空")
                return

            # 加载比对表配置
            tables = self.config_manager.get_comparison_tables()

            if not tables:
                self.log_message("⚠️ 没有找到表配置")
                return

            self.log_message(f"🔍 发现 {len(tables)} 个表配置")

            # 添加表到树形控件
            for i, table in enumerate(tables):
                try:
                    table_id = table.get('table_id', f'table_{i}')
                    remark = table.get('remark', '无备注')
                    status = "就绪"

                    self.table_tree.insert('', 'end', values=(
                        table_id,
                        remark,
                        status
                    ))

                    self.log_message(f"  • 表 {table_id}: {remark}")

                except Exception as table_e:
                    self.log_message(f"⚠️ 添加表配置失败: {table_e}")
                    continue

            self.log_message(f"✅ 成功加载了 {len(tables)} 个比对表配置")

            # 强制刷新表列表显示
            self.table_tree.update()

            # 数据加载完成后，延迟调整列宽以确保界面已完全渲染
            self.root.after(100, self._adjust_table_column_widths)

        except Exception as e:
            error_msg = f"加载表列表失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", error_msg)

    def start_comparison(self):
        """开始比对 - 智能模式切换"""
        try:
            if not self.config_manager.config:
                messagebox.showwarning("提示", "请先选择配置文件")
                return

            if self.is_comparison_running:
                messagebox.showwarning("提示", "比对任务正在运行中")
                return

            # 重置状态
            self._reset_comparison_state()
            self.is_comparison_running = True
            self.cancel_requested = False

            # 启动比对计时
            self.start_comparison_timer()

            self.log_message("🚀 开始执行数据库比对...")
            self.set_status("正在执行比对...")

            # 在后台线程中执行比对
            def run_comparison():
                try:
                    # 强制使用真实数据库连接（禁用模拟模式）
                    if not AGENT_MODULES_AVAILABLE:
                        self.log_message("❌ Agent模块不可用，无法执行真实数据库比对")
                        self.log_message("💡 请确保Agent核心模块正确安装")
                        raise ValueError("Agent模块不可用，无法执行真实数据库比对")
                    else:
                        self._execute_real_comparison()

                    if not self.cancel_requested:
                        self.log_message("✅ 比对任务完成")
                        self.root.after(0, lambda: self.set_status("比对完成"))
                    else:
                        self.log_message("⏹️ 比对任务已取消")
                        self.root.after(0, lambda: self.set_status("已取消"))

                except Exception as e:
                    error_msg = str(e)  # 捕获错误信息到局部变量
                    self.log_message(f"❌ 比对任务失败: {error_msg}")
                    self.root.after(0, lambda: self.set_status("比对失败"))
                    self.root.after(0, lambda msg=error_msg: messagebox.showerror("比对失败", f"比对执行失败:\n{msg}"))
                finally:
                    self.is_comparison_running = False

            # 启动后台线程
            self.comparison_thread = threading.Thread(target=run_comparison, daemon=True)
            self.comparison_thread.start()

        except Exception as e:
            error_msg = f"启动比对任务失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("启动失败", error_msg)
            self.is_comparison_running = False

    def stop_comparison(self):
        """停止比对 - 增强版中断机制"""
        try:
            if self.is_comparison_running:
                self.log_message("⏹️ 正在停止比对任务...")
                self.cancel_requested = True
                self.set_status("正在停止...")

                # 强制中断数据库连接（如果存在）
                self._force_close_db_connections()

                # 等待线程结束，增加超时时间
                if hasattr(self, 'comparison_thread') and self.comparison_thread.is_alive():
                    self.log_message("⏳ 等待比对线程结束...")
                    self.comparison_thread.join(timeout=10)  # 增加到10秒

                    # 如果线程仍然活跃，记录警告
                    if self.comparison_thread.is_alive():
                        self.log_message("⚠️ 比对线程未能及时结束，可能仍在后台运行")

                # 清理待处理记录队列
                self.pending_records.clear()
                self.current_displayed_records = 0
                self.display_warning_shown = False

                self.is_comparison_running = False
                self.set_status("已停止")
                self.log_message("✅ 比对任务已停止")
            else:
                self.log_message("⚠️ 没有正在运行的比对任务")

        except Exception as e:
            error_msg = f"停止比对任务失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("停止失败", error_msg)

    def _force_close_db_connections(self):
        """强制关闭数据库连接"""
        try:
            self.log_message("🔌 强制关闭数据库连接...")

            # 尝试关闭可能存在的数据库连接
            try:
                import ibm_db
                # 注意：ibm_db没有全局连接管理，这里主要是设置标志
                # 实际的连接关闭会在各个查询方法的finally块中处理
                self.log_message("🔌 DB2连接关闭信号已发送")
            except ImportError:
                self.log_message("⚠️ DB2驱动程序未导入，跳过连接关闭")

            # 设置强制中断标志，确保所有正在进行的数据库操作能够检测到
            self.cancel_requested = True

            # 给数据库操作一些时间来响应中断信号
            import time
            time.sleep(1)

            self.log_message("✅ 数据库连接关闭操作完成")

        except Exception as e:
            self.log_message(f"⚠️ 强制关闭数据库连接失败: {e}")

    def _execute_real_comparison(self):
        """执行真实数据库比对 - 使用API服务或本地服务"""
        try:
            self.log_message("🚀 启动真实数据库比对...")
            self.current_task_id = f"real_task_{uuid.uuid4().hex[:8]}"
            self.log_message(f"📋 任务ID: {self.current_task_id}")

            # 获取数据库连接配置
            source_config = self.config_manager.get_database_config('DB1')
            target_config = self.config_manager.get_database_config('DB2')

            self.log_message(f"🔗 源数据库: {source_config.type}@{source_config.host}:{source_config.port}")
            self.log_message(f"🔗 目标数据库: {target_config.type}@{target_config.host}:{target_config.port}")

            # 获取比对表列表
            tables = self.config_manager.get_comparison_tables()
            total_tables = len(tables)

            if total_tables == 0:
                raise ValueError("没有找到比对表配置")

            self.log_message(f"📊 共需比对 {total_tables} 个表")

            # 优先使用HTTP API服务
            if hasattr(self, 'api_available') and self.api_available and self.api_client:
                self.log_message("🌐 使用HTTP API服务执行比对")
                self._execute_with_api_service(source_config, target_config, tables)
            elif AGENT_MODULES_AVAILABLE and hasattr(self, 'task_manager') and self.task_manager:
                self.log_message("🔧 使用本地任务管理器执行比对")
                self._execute_with_local_service(source_config, target_config, tables)
            else:
                self.log_message("⚠️ API和本地服务都不可用，使用直接数据库比对")
                self._execute_direct_database_comparison(tables)

        except Exception as e:
            self.log_message(f"❌ 真实比对执行异常: {str(e)}")
            self.update_progress(0, f"执行失败: {str(e)}")
            raise

    def _execute_with_api_service(self, source_config, target_config, tables):
        """使用HTTP API服务执行比对"""
        try:
            # 构建API请求数据
            request_data = {
                "source_db": {
                    "type": source_config.type,
                    "host": source_config.host,
                    "port": source_config.port,
                    "username": source_config.username,
                    "password": source_config.password,
                    "database": source_config.database
                },
                "target_db": {
                    "type": target_config.type,
                    "host": target_config.host,
                    "port": target_config.port,
                    "username": target_config.username,
                    "password": target_config.password,
                    "database": target_config.database
                },
                "tables": [
                    {
                        "table_id": table["table_id"],
                        "source_sql": table["sql_1"],
                        "target_sql": table["sql_2"],
                        "remark": table.get("remark", "")
                    }
                    for table in tables
                ],
                "algorithm": "primary_key",
                "options": {
                    "batch_size": 1000,
                    "timeout": 300
                }
            }

            # 创建比对任务
            self.log_message("📤 正在创建API比对任务...")
            task_response = self.api_client.create_comparison_task(request_data)

            if task_response.get("success"):
                api_task_id = task_response["data"]["task_id"]
                self.log_message(f"✅ API任务创建成功，任务ID: {api_task_id}")

                # 监控任务进度
                self._monitor_api_task_progress(api_task_id)
            else:
                raise Exception(f"API任务创建失败: {task_response.get('message', '未知错误')}")

        except Exception as e:
            self.log_message(f"❌ API服务执行失败: {str(e)}")
            # 回退到本地服务
            if AGENT_MODULES_AVAILABLE and hasattr(self, 'task_manager') and self.task_manager:
                self.log_message("🔄 回退到本地服务执行比对")
                self._execute_with_local_service(source_config, target_config, tables)
            else:
                self.log_message("🔄 回退到直接数据库比对")
                self._execute_direct_database_comparison(tables)

    def _execute_with_local_service(self, source_config, target_config, tables):
        """使用本地任务管理器执行比对"""
        try:
            self.log_message("🔧 正在构建本地任务请求...")

            # 检查必要的模块是否可用
            if not AGENT_MODULES_AVAILABLE:
                raise Exception("Agent模块不可用，无法使用本地服务")

            # 导入必要的模块 - 使用正确的app前缀
            from app.models.task import CompareTaskRequest, CompareConfig, CompareMode, AlgorithmType
            from app.models.db import DatabaseConnection as TaskDatabaseConnection, DatabaseType as TaskDatabaseType

            # 动态确定数据库类型
            def get_db_type(config):
                """根据配置确定数据库类型"""
                if hasattr(config, 'type'):
                    db_type_str = config.type.lower() if hasattr(config.type, 'lower') else str(config.type).lower()
                else:
                    db_type_str = 'db2'  # 默认值

                # 映射到TaskDatabaseType
                type_mapping = {
                    'db2': TaskDatabaseType.DB2,
                    'oracle': TaskDatabaseType.ORACLE,
                    'mysql': TaskDatabaseType.MYSQL,
                    'postgresql': TaskDatabaseType.POSTGRESQL,
                    'sqlserver': TaskDatabaseType.SQLSERVER,
                    'gaussdb': TaskDatabaseType.GAUSSDB,
                    'mongodb': TaskDatabaseType.MONGODB
                }
                return type_mapping.get(db_type_str, TaskDatabaseType.DB2)

            # 转换数据库配置为TaskDatabaseConnection格式
            try:
                self.log_message(f"🔧 转换源数据库配置: {source_config.host}:{source_config.port}")
                source_db = TaskDatabaseConnection(
                    type=get_db_type(source_config),
                    host=source_config.host,
                    port=source_config.port,
                    username=source_config.username,
                    password=source_config.password,
                    database=source_config.database
                )
                self.log_message(f"✅ 源数据库配置转换成功")

                self.log_message(f"🔧 转换目标数据库配置: {target_config.host}:{target_config.port}")
                target_db = TaskDatabaseConnection(
                    type=get_db_type(target_config),
                    host=target_config.host,
                    port=target_config.port,
                    username=target_config.username,
                    password=target_config.password,
                    database=target_config.database
                )
                self.log_message(f"✅ 目标数据库配置转换成功")

            except Exception as db_config_error:
                self.log_message(f"❌ 数据库配置转换失败: {str(db_config_error)}")
                import traceback
                self.log_message(f"详细错误: {traceback.format_exc()}")
                raise

            # 为每个表创建单独的比对任务
            total_tables = len(tables)
            completed_tables = 0

            for i, table_config in enumerate(tables):
                # 检查取消请求
                if self.cancel_requested:
                    self.log_message("⏹️ 用户取消比对")
                    break

                table_id = table_config['table_id']
                self.log_message(f"🔄 开始本地比对表: {table_id} ({table_config['remark']})")

                # 设置当前处理的表
                self.set_current_table(table_id)
                self._update_table_status(table_id, "执行中")

                # 更新进度
                progress = int((i / total_tables) * 100)
                self.update_progress(progress, f"正在比对表: {table_id}")

                # 构建比对配置
                try:
                    compare_config = CompareConfig(
                        mode=CompareMode.QUERY,
                        source_query=table_config['sql_1'],
                        target_query=table_config['sql_2'],
                        primary_keys=["id"],  # 默认主键，实际应该从SQL解析
                        batch_size=1000,
                        algorithm_type=AlgorithmType.PRIMARY_KEY
                    )

                    self.log_message(f"✅ 表 {table_id} 比对配置创建成功")

                except Exception as config_error:
                    self.log_message(f"❌ 表 {table_id} 比对配置创建失败: {str(config_error)}")
                    continue

                # 构建任务请求
                try:
                    task_request = CompareTaskRequest(
                        source=source_db,
                        target=target_db,
                        config=compare_config
                    )

                    self.log_message(f"✅ 表 {table_id} 任务请求创建成功")

                except Exception as request_error:
                    self.log_message(f"❌ 表 {table_id} 任务请求创建失败: {str(request_error)}")
                    self.log_message(f"详细错误: {request_error}")
                    continue

                # 执行单表比对任务
                if self._execute_single_local_task(task_request, table_id):
                    self._update_table_status(table_id, "完成")
                    completed_tables += 1
                    self.complete_table_comparison(table_id)
                    self.log_message(f"✅ 表 {table_id} 本地比对完成")
                else:
                    if self.cancel_requested:
                        self._update_table_status(table_id, "已取消")
                        self.log_message(f"⏹️ 表 {table_id} 比对已取消")
                    else:
                        self._update_table_status(table_id, "失败")
                        self.log_message(f"❌ 表 {table_id} 本地比对失败")

            # 更新最终进度
            if not self.cancel_requested:
                self.update_progress(100, "本地比对完成")
                self._display_local_summary(tables, completed_tables)
                self.log_message(f"🎉 本地任务管理器比对完成，成功: {completed_tables}/{total_tables}")

        except Exception as e:
            self.log_message(f"❌ 本地服务执行失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            # 回退到直接数据库比对
            self.log_message("🔄 回退到直接数据库比对")
            self._execute_direct_database_comparison(tables)

    def _execute_single_local_task(self, task_request, table_id):
        """执行单个本地任务"""
        try:
            # 创建结果容器
            task_result = {"success": False, "task_id": None, "error": None}

            def run_local_task():
                """在后台线程中运行本地任务"""
                try:
                    import asyncio
                    # 创建事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 创建任务
                    task_response = loop.run_until_complete(
                        self.task_manager.create_task(task_request)
                    )

                    if task_response and task_response.task_id:
                        local_task_id = task_response.task_id
                        task_result["task_id"] = local_task_id
                        self.log_message(f"✅ 本地任务创建成功，任务ID: {local_task_id}")

                        # 监控任务进度
                        while not self.cancel_requested:
                            # 获取任务状态
                            status_response = loop.run_until_complete(
                                self.task_manager.get_task_status(local_task_id)
                            )

                            if status_response:
                                status = status_response.status
                                progress = status_response.progress
                                message = status_response.message

                                # 更新进度（在主线程中）
                                self.root.after(0, lambda: self.log_message(f"📊 表 {table_id} 进度: {progress}% - {message}"))

                                # 检查任务是否完成
                                if status.value in ["completed", "failed", "canceled"]:
                                    if status.value == "completed":
                                        # 获取任务结果
                                        result_response = loop.run_until_complete(
                                            self.task_manager.get_task_result(local_task_id)
                                        )
                                        if result_response:
                                            self._process_local_task_result(result_response, table_id)
                                        task_result["success"] = True
                                    else:
                                        error_msg = f"任务状态: {status.value}"
                                        task_result["error"] = error_msg
                                        self.root.after(0, lambda: self.log_message(f"❌ 表 {table_id} 任务失败: {error_msg}"))
                                    break
                            else:
                                task_result["error"] = "无法获取任务状态"
                                break

                            # 等待一段时间再检查
                            time.sleep(1)

                    else:
                        task_result["error"] = "任务创建失败"

                except Exception as e:
                    task_result["error"] = str(e)
                    self.root.after(0, lambda: self.log_message(f"❌ 本地任务执行异常: {str(e)}"))

            # 启动后台线程
            task_thread = threading.Thread(target=run_local_task, daemon=True)
            task_thread.start()

            # 等待任务完成（带超时）
            task_thread.join(timeout=300)  # 5分钟超时

            if task_thread.is_alive():
                self.log_message(f"⚠️ 表 {table_id} 本地任务超时")
                return False

            return task_result["success"]

        except Exception as e:
            self.log_message(f"❌ 执行单个本地任务失败: {str(e)}")
            return False

    def _process_local_task_result(self, result_response, table_id):
        """处理本地任务结果"""
        try:
            if result_response and result_response.summary:
                summary = result_response.summary
                self.log_message(f"📊 表 {table_id} 比对结果:")
                self.log_message(f"  • 源记录数: {getattr(summary, 'source_count', 0)}")
                self.log_message(f"  • 目标记录数: {getattr(summary, 'target_count', 0)}")
                self.log_message(f"  • 匹配记录数: {getattr(summary, 'matched_count', 0)}")
                self.log_message(f"  • 差异记录数: {getattr(summary, 'diff_count', 0)}")
                self.log_message(f"  • 源独有记录: {getattr(summary, 'source_only_count', 0)}")
                self.log_message(f"  • 目标独有记录: {getattr(summary, 'target_only_count', 0)}")

                # 尝试获取差异详情
                diff_count = getattr(summary, 'diff_count', 0)
                if diff_count > 0:
                    self.log_message(f"🔍 尝试获取表 {table_id} 的差异详情...")

                    # 检查是否有详细差异数据
                    if hasattr(result_response, 'details') and result_response.details:
                        if hasattr(result_response.details, 'items') and result_response.details.items:
                            self._display_local_task_differences(result_response.details.items, table_id)
                        else:
                            self.log_message(f"⚠️ 表 {table_id} 差异详情为空，但摘要显示有 {diff_count} 条差异")
                            # 尝试从任务管理器直接获取差异数据
                            self._try_get_task_differences_directly(table_id, result_response)
                    else:
                        self.log_message(f"⚠️ 表 {table_id} 没有差异详情数据，但摘要显示有 {diff_count} 条差异")
                        # 尝试从任务管理器直接获取差异数据
                        self._try_get_task_differences_directly(table_id, result_response)
                else:
                    self.log_message(f"✅ 表 {table_id} 没有发现差异")

            self.log_message(f"✅ 表 {table_id} 本地比对完成")

        except Exception as e:
            self.log_message(f"❌ 处理本地任务结果失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")

    def _try_get_task_differences_directly(self, table_id, result_response):
        """尝试直接从任务管理器获取差异数据"""
        try:
            if hasattr(result_response, 'task_id') and AGENT_MODULES_AVAILABLE and self.task_manager:
                task_id = result_response.task_id
                self.log_message(f"🔍 尝试直接获取任务 {task_id} 的差异数据...")

                import asyncio

                # 创建事件循环
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                # 获取差异详情
                diff_details = loop.run_until_complete(
                    self.task_manager.get_task_diff_details(task_id, page=1, page_size=1000)
                )

                if diff_details and diff_details.items:
                    self.log_message(f"✅ 成功获取任务 {task_id} 的 {len(diff_details.items)} 条差异记录")
                    self._display_local_task_differences(diff_details.items, table_id)
                else:
                    self.log_message(f"⚠️ 任务 {task_id} 没有差异详情数据")

            else:
                if not hasattr(result_response, 'task_id'):
                    self.log_message(f"⚠️ 无法获取任务ID，无法直接获取差异数据")
                elif not AGENT_MODULES_AVAILABLE:
                    self.log_message(f"⚠️ Agent模块不可用，无法直接获取差异数据")
                else:
                    self.log_message(f"⚠️ 任务管理器不可用，无法直接获取差异数据")

        except Exception as e:
            self.log_message(f"❌ 直接获取差异数据失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")

    def _display_local_task_differences(self, diff_items, table_id):
        """显示本地任务差异记录"""
        try:
            # 转换本地任务格式到界面格式
            differences = []
            for item in diff_items:
                record = {
                    'id': str(item.key),
                    'source_data': getattr(item, 'source_record', {}),
                    'target_data': getattr(item, 'target_record', {}),
                    'diff_fields': [fd.field for fd in item.field_diffs] if item.field_diffs else [],
                    'diff_type': item.diff_type,
                    'table_id': table_id
                }
                differences.append(record)

            # 使用批量添加优化性能
            self._batch_add_differences_to_tree(differences, table_id)
            self.log_message(f"✅ 已显示表 {table_id} 的 {len(diff_items)} 条差异记录")

        except Exception as e:
            self.log_message(f"❌ 显示本地任务差异记录失败: {str(e)}")

    def _display_local_summary(self, tables, completed_tables):
        """显示本地比对结果摘要"""
        try:
            self.log_message("📊 本地比对任务结果摘要")
            self.log_message("=" * 50)
            self.log_message("✅ 这是通过本地任务管理器执行的真实比对结果")
            self.log_message(f"📈 比对统计:")
            self.log_message(f"  • 总表数: {len(tables)}")
            self.log_message(f"  • 成功表数: {completed_tables}")
            self.log_message(f"  • 失败表数: {len(tables) - completed_tables}")
            self.log_message(f"  • 成功率: {(completed_tables/len(tables)*100):.1f}%")
            self.log_message(f"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message("💡 提示: 查看'比对结果'选项卡查看详细差异记录")
            self.log_message("🎉 本地任务管理器比对已完成")

        except Exception as e:
            self.log_message(f"❌ 显示本地摘要失败: {str(e)}")

    def _execute_direct_database_comparison(self, tables):
        """直接执行数据库比对（原有逻辑）"""
        try:
            total_tables = len(tables)
            completed_tables = 0

            for i, table_config in enumerate(tables):
                # 增强的中断检查
                if self.cancel_requested:
                    self.log_message("⏹️ 用户取消比对")
                    break

                table_id = table_config['table_id']

                # 设置当前处理的表
                self.set_current_table(table_id)

                # 更新表状态
                self._update_table_status(table_id, "执行中")

                # 更新进度
                progress = int((i / total_tables) * 100)
                self.update_progress(progress, f"正在比对表: {table_id}")

                self.log_message(f"🔄 开始直接比对表: {table_id} ({table_config['remark']})")

                # 执行真实表比对（带中断检查）
                if self._execute_real_table_comparison_with_interruption(table_config):
                    self._update_table_status(table_id, "完成")
                    completed_tables += 1
                    # 完成表比对统计
                    self.complete_table_comparison(table_id)
                    self.log_message(f"✅ 表 {table_id} 比对完成")
                else:
                    if self.cancel_requested:
                        self._update_table_status(table_id, "已取消")
                        self.log_message(f"⏹️ 表 {table_id} 比对已取消")
                    else:
                        self._update_table_status(table_id, "失败")
                        self.log_message(f"❌ 表 {table_id} 比对失败")

            # 更新最终进度
            if not self.cancel_requested:
                self.update_progress(100, "比对完成")
                # 显示真实结果摘要
                self._display_real_summary(tables, completed_tables)
                self.log_message(f"🎉 直接数据库比对完成，成功: {completed_tables}/{total_tables}")

        except Exception as e:
            self.log_message(f"❌ 直接数据库比对异常: {str(e)}")
            raise

    def _monitor_api_task_progress(self, task_id: str):
        """监控API任务进度"""
        try:
            def monitor_task():
                """在后台线程中监控API任务"""
                try:
                    while not self.cancel_requested:
                        # 获取任务状态
                        status_response = self.api_client.get_task_status(task_id)

                        if status_response.get("success"):
                            task_status = status_response["data"]
                            status = task_status.get("status")
                            progress = task_status.get("progress", 0)
                            message = task_status.get("message", "")

                            # 更新进度
                            self.root.after(0, lambda: self.update_progress(progress, message))
                            self.root.after(0, lambda: self.log_message(f"📊 API任务进度: {progress}% - {message}"))

                            # 检查任务是否完成
                            if status in ["completed", "failed"]:
                                if status == "completed":
                                    self.root.after(0, lambda: self._handle_api_task_completion(task_id))
                                else:
                                    error_msg = task_status.get("error", "未知错误")
                                    self.root.after(0, lambda: self.log_message(f"❌ API任务失败: {error_msg}"))
                                break
                        else:
                            self.root.after(0, lambda: self.log_message("⚠️ 无法获取API任务状态"))
                            break

                        # 等待一段时间再检查
                        time.sleep(2)

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"❌ 监控API任务进度异常: {str(e)}"))

            # 启动监控线程
            monitor_thread = threading.Thread(target=monitor_task, daemon=True)
            monitor_thread.start()

        except Exception as e:
            self.log_message(f"❌ 启动API任务监控失败: {str(e)}")

    def _handle_api_task_completion(self, task_id: str):
        """处理API任务完成"""
        try:
            # 获取任务结果
            result_response = self.api_client.get_task_result(task_id)

            if result_response.get("success"):
                task_result = result_response["data"]

                # 显示API结果摘要
                self._display_api_result_summary(task_result)

                # 如果有详细差异数据，显示到界面
                if task_result.get("differences"):
                    self._display_api_result_differences(task_result["differences"])

                self.log_message("✅ API任务结果已加载到界面")
            else:
                self.log_message("⚠️ 无法获取API任务结果详情")

        except Exception as e:
            self.log_message(f"❌ 处理API任务完成异常: {str(e)}")

    def _display_api_result_summary(self, task_result):
        """显示API任务结果摘要"""
        try:
            # 在日志中显示摘要信息
            self.log_message("📊 API任务结果摘要")
            self.log_message("=" * 50)
            self.log_message("✅ 这是通过HTTP API执行的真实比对结果")

            summary = task_result.get("summary", {})
            self.log_message("📈 比对统计:")
            self.log_message(f"  • 源记录数: {summary.get('source_count', 0)}")
            self.log_message(f"  • 目标记录数: {summary.get('target_count', 0)}")
            self.log_message(f"  • 匹配记录数: {summary.get('matched_count', 0)}")
            self.log_message(f"  • 差异记录数: {summary.get('diff_count', 0)}")
            self.log_message(f"  • 源独有记录: {summary.get('source_only_count', 0)}")
            self.log_message(f"  • 目标独有记录: {summary.get('target_only_count', 0)}")

            execution_time = task_result.get("execution_time")
            if execution_time:
                self.log_message(f"⏱️ 执行时间: {execution_time:.2f}秒")

            self.log_message(f"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message("💡 提示: 查看'比对结果'选项卡查看详细差异记录")
            self.log_message("🎉 HTTP API比对任务已完成")

        except Exception as e:
            self.log_message(f"❌ 显示API结果摘要失败: {str(e)}")

    def _display_api_result_differences(self, differences):
        """显示API结果差异记录"""
        try:
            # 转换API格式到界面格式
            converted_differences = []
            for diff in differences:
                record = {
                    'id': str(diff.get('key', '')),
                    'source_data': diff.get('source_record', {}),
                    'target_data': diff.get('target_record', {}),
                    'diff_fields': diff.get('diff_fields', []),
                    'diff_type': diff.get('diff_type', 'unknown'),
                    'table_id': diff.get('table_id', 'API')
                }
                converted_differences.append(record)

            # 使用批量添加优化性能
            self._batch_add_differences_to_tree(converted_differences, "API")

            self.log_message(f"✅ 已批量显示 {len(differences)} 条API差异记录")

        except Exception as e:
            self.log_message(f"❌ 显示API差异记录失败: {str(e)}")

    def _monitor_task_progress(self, task_id: str):
        """监控任务进度 - 使用API服务"""
        try:
            if not AGENT_MODULES_AVAILABLE or not self.task_manager:
                self.log_message("⚠️ API服务不可用，无法监控任务进度")
                return

            import asyncio
            import threading

            def monitor_task():
                """在后台线程中监控任务"""
                try:
                    # 创建事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    while not self.cancel_requested:
                        # 获取任务状态
                        task_status = loop.run_until_complete(
                            self.task_manager.get_task_status(task_id)
                        )

                        if task_status:
                            # 更新进度
                            progress = task_status.progress
                            status_msg = task_status.message

                            self.root.after(0, lambda: self.update_progress(progress, status_msg))
                            self.root.after(0, lambda: self.log_message(f"📊 任务进度: {progress}% - {status_msg}"))

                            # 检查任务是否完成
                            if task_status.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                                if task_status.status == TaskStatus.COMPLETED:
                                    self.root.after(0, lambda: self._handle_task_completion(task_id))
                                else:
                                    error_msg = task_status.error.get('message', '未知错误') if task_status.error else '任务失败'
                                    self.root.after(0, lambda: self.log_message(f"❌ 任务失败: {error_msg}"))
                                break
                        else:
                            self.root.after(0, lambda: self.log_message("⚠️ 无法获取任务状态"))
                            break

                        # 等待一段时间再检查
                        time.sleep(2)

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"❌ 监控任务进度异常: {str(e)}"))

            # 启动监控线程
            monitor_thread = threading.Thread(target=monitor_task, daemon=True)
            monitor_thread.start()

        except Exception as e:
            self.log_message(f"❌ 启动任务监控失败: {str(e)}")

    def _handle_task_completion(self, task_id: str):
        """处理任务完成 - 使用API服务获取结果"""
        try:
            if not AGENT_MODULES_AVAILABLE or not self.task_manager:
                self.log_message("⚠️ API服务不可用，无法获取任务结果")
                return

            import asyncio

            # 创建事件循环
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # 获取任务结果
            task_result = loop.run_until_complete(
                self.task_manager.get_task_result(task_id)
            )

            if task_result and task_result.summary:
                # 显示API结果摘要
                self._display_api_summary(task_result)

                # 如果有详细差异数据，显示到界面
                if task_result.details and task_result.details.items:
                    self._display_api_differences(task_result.details.items)

                self.log_message("✅ 任务结果已加载到界面")
            else:
                self.log_message("⚠️ 无法获取任务结果详情")

        except Exception as e:
            self.log_message(f"❌ 处理任务完成异常: {str(e)}")

    def _display_api_summary(self, task_result):
        """显示API任务结果摘要 - 直接在日志中显示"""
        try:
            # 在日志中显示摘要信息
            self.log_message("📊 API任务结果摘要")
            self.log_message("=" * 50)
            self.log_message("✅ 这是通过REST API执行的真实比对结果")

            if task_result.summary:
                summary = task_result.summary
                self.log_message("📈 比对统计:")
                self.log_message(f"  • 源记录数: {getattr(summary, 'source_count', 0)}")
                self.log_message(f"  • 目标记录数: {getattr(summary, 'target_count', 0)}")
                self.log_message(f"  • 匹配记录数: {getattr(summary, 'matched_count', 0)}")
                self.log_message(f"  • 差异记录数: {getattr(summary, 'diff_count', 0)}")
                self.log_message(f"  • 源独有记录: {getattr(summary, 'source_only_count', 0)}")
                self.log_message(f"  • 目标独有记录: {getattr(summary, 'target_only_count', 0)}")

            if task_result.execution_time:
                self.log_message(f"⏱️ 执行时间: {task_result.execution_time:.2f}秒")

            self.log_message(f"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_message("💡 提示: 查看'比对结果'选项卡查看详细差异记录")
            self.log_message("🎉 API比对任务已完成")

        except Exception as e:
            self.log_message(f"❌ 显示API摘要失败: {str(e)}")

    def _display_api_differences(self, diff_items):
        """显示API差异记录 - 使用批量优化"""
        try:
            # 转换API格式到界面格式
            differences = []
            for item in diff_items:
                record = {
                    'id': str(item.key),
                    'source_data': getattr(item, 'source_record', {}),
                    'target_data': getattr(item, 'target_record', {}),
                    'diff_fields': [fd.field for fd in item.field_diffs] if item.field_diffs else [],
                    'diff_type': item.diff_type
                }
                differences.append(record)

            # 使用批量添加优化性能
            self._batch_add_differences_to_tree(differences, "API")

            self.log_message(f"✅ 已批量显示 {len(diff_items)} 条API差异记录")

        except Exception as e:
            self.log_message(f"❌ 显示API差异记录失败: {str(e)}")

    def _check_database_connections(self) -> bool:
        """检查数据库连接"""
        try:
            # 获取数据库配置
            db1_config = self.config_manager.get_database_config('DB1')
            db2_config = self.config_manager.get_database_config('DB2')

            if not db1_config or not db2_config:
                raise ValueError("数据库配置不完整")

            # 测试数据库连接
            db1_ok = self._test_single_db_connection(db1_config, 'DB1')
            db2_ok = self._test_single_db_connection(db2_config, 'DB2')

            if db1_ok and db2_ok:
                self.log_message("✅ 所有数据库连接检查通过")
                return True
            else:
                raise ValueError("数据库连接测试失败")

        except Exception as e:
            self.log_message(f"❌ 数据库连接检查失败: {str(e)}")
            return False

    def _test_single_db_connection(self, db_config: DatabaseConnection, db_name: str) -> bool:
        """测试单个数据库连接 - 使用与查询相同的连接策略"""
        try:
            import ibm_db
            import time

            # 构建连接字符串 - 与查询方法保持一致
            conn_str = (
                f"DATABASE={db_config.database};"
                f"HOSTNAME={db_config.host};"
                f"PORT={db_config.port};"
                f"PROTOCOL=TCPIP;"
                f"UID={db_config.username};"
                f"PWD={db_config.password};"
                f"AUTHENTICATION=SERVER;"
                f"CONNECTTIMEOUT=30;"
                f"QUERYTIMEOUT=60;"
            )

            # 实现连接重试机制
            conn = None
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # 连接数据库
                    conn = ibm_db.connect(conn_str, "", "")

                    if conn:
                        # 测试基本查询
                        try:
                            stmt = ibm_db.exec_immediate(conn, "SELECT 1 FROM SYSIBM.SYSDUMMY1")
                            if stmt:
                                result = ibm_db.fetch_tuple(stmt)
                                if result:
                                    # 获取数据库版本信息
                                    try:
                                        server_info = ibm_db.server_info(conn)
                                        version = f"DB2 v{server_info.DBMS_VER}"
                                        self.log_message(f"✅ {db_name} 连接成功 ({db_config.host}:{db_config.port}/{db_config.database})")
                                        self.log_message(f"  • 数据库版本: {version}")
                                    except Exception:
                                        self.log_message(f"✅ {db_name} 连接成功 ({db_config.host}:{db_config.port}/{db_config.database})")

                                    ibm_db.close(conn)
                                    return True
                                else:
                                    self.log_message(f"⚠️ {db_name} 查询测试无结果")
                            else:
                                self.log_message(f"⚠️ {db_name} 查询测试失败")
                        except Exception as query_error:
                            self.log_message(f"⚠️ {db_name} 查询测试异常: {query_error}")

                        ibm_db.close(conn)
                        return True  # 连接成功，即使查询测试有问题
                    else:
                        self.log_message(f"❌ {db_name} 连接失败 - 连接对象为空")
                        if attempt < max_retries - 1:
                            time.sleep(2)

                except Exception as conn_error:
                    error_msg = str(conn_error)
                    self.log_message(f"❌ {db_name} 连接异常 (尝试 {attempt + 1}): {error_msg}")

                    if attempt < max_retries - 1:
                        # 分析错误类型并决定是否重试
                        if "SQL30082N" in error_msg:
                            self.log_message(f"⚠️ 认证错误，跳过重试")
                            break
                        elif "SQL1042C" in error_msg:
                            self.log_message(f"⚠️ 系统错误，等待后重试...")
                            time.sleep(3)
                        else:
                            time.sleep(2)
                    else:
                        self.log_message(f"❌ {db_name} 所有连接尝试失败")

            return False

        except Exception as e:
            self.log_message(f"❌ {db_name} 连接异常: {str(e)}")
            return False

    def _execute_real_table_comparison_with_interruption(self, table_config: Dict[str, Any]) -> bool:
        """执行真实表比对 - 带中断检查版本"""
        try:
            table_id = table_config['table_id']
            sql_1 = table_config['sql_1']
            sql_2 = table_config['sql_2']

            # 中断检查点1：开始查询前
            if self.cancel_requested:
                self.log_message(f"⏹️ 表 {table_id}: 用户取消，跳过查询")
                return False

            self.log_message(f"📊 表 {table_id}: 正在查询源数据...")

            # 查询源数据（带中断检查）
            source_data = self._execute_sql_query_with_interruption('DB1', sql_1, table_id, '源')
            if source_data is None:
                return False

            # 中断检查点2：源数据查询完成后
            if self.cancel_requested:
                self.log_message(f"⏹️ 表 {table_id}: 用户取消，跳过目标查询")
                return False

            self.log_message(f"📊 表 {table_id}: 正在查询目标数据...")

            # 查询目标数据（带中断检查）
            target_data = self._execute_sql_query_with_interruption('DB2', sql_2, table_id, '目标')
            if target_data is None:
                return False

            # 中断检查点3：数据查询完成后
            if self.cancel_requested:
                self.log_message(f"⏹️ 表 {table_id}: 用户取消，跳过数据比对")
                return False

            self.log_message(f"🔍 表 {table_id}: 正在比对数据...")

            # 比对数据（带中断检查）
            differences = self._compare_table_data_with_interruption(source_data, target_data, table_id)

            # 中断检查点4：数据比对完成后
            if self.cancel_requested:
                self.log_message(f"⏹️ 表 {table_id}: 用户取消，跳过结果显示")
                return False

            # 显示比对结果
            self.log_message(f"📊 表 {table_id}: 源记录 {len(source_data)} 条，目标记录 {len(target_data)} 条，差异 {len(differences)} 条")

            # 批量添加差异记录到界面（优化性能）
            self._batch_add_differences_to_tree(differences, table_id)

            return True

        except Exception as e:
            self.log_message(f"❌ 表 {table_id} 比对异常: {str(e)}")
            return False

    def _execute_real_table_comparison(self, table_config: Dict[str, Any]) -> bool:
        """执行真实表比对"""
        try:
            table_id = table_config['table_id']
            sql_1 = table_config['sql_1']
            sql_2 = table_config['sql_2']

            self.log_message(f"📊 表 {table_id}: 正在查询源数据...")

            # 查询源数据
            source_data = self._execute_sql_query('DB1', sql_1, table_id, '源')
            if source_data is None:
                return False

            self.log_message(f"📊 表 {table_id}: 正在查询目标数据...")

            # 查询目标数据
            target_data = self._execute_sql_query('DB2', sql_2, table_id, '目标')
            if target_data is None:
                return False

            self.log_message(f"🔍 表 {table_id}: 正在比对数据...")

            # 比对数据
            differences = self._compare_table_data(source_data, target_data, table_id)

            # 显示比对结果
            self.log_message(f"📊 表 {table_id}: 源记录 {len(source_data)} 条，目标记录 {len(target_data)} 条，差异 {len(differences)} 条")

            # 添加差异记录到界面
            for diff in differences:
                category = f"{table_id}-{diff['diff_type']}"
                self._add_difference_to_tree(diff, category, table_id)

            return True

        except Exception as e:
            self.log_message(f"❌ 表 {table_id} 比对异常: {str(e)}")
            return False

    def _execute_sql_query(self, db_key: str, sql: str, table_id: str, db_type: str):
        """执行SQL查询 - 使用改进的连接策略"""
        try:
            import ibm_db
            import time

            # 获取数据库配置
            db_config = self.config_manager.get_database_config(db_key)
            if not db_config:
                self.log_message(f"❌ {db_key} 配置不存在")
                return None

            # 构建连接字符串 - 添加更多连接参数
            conn_str = (
                f"DATABASE={db_config.database};"
                f"HOSTNAME={db_config.host};"
                f"PORT={db_config.port};"
                f"PROTOCOL=TCPIP;"
                f"UID={db_config.username};"
                f"PWD={db_config.password};"
                f"AUTHENTICATION=SERVER;"
                f"CONNECTTIMEOUT=30;"
                f"QUERYTIMEOUT=60;"
            )

            # 实现连接重试机制
            conn = None
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # 连接数据库
                    conn = ibm_db.connect(conn_str, "", "")

                    if conn:
                        self.log_message(f"✅ {db_type} 连接成功")
                        break
                    else:
                        self.log_message(f"❌ {db_type} 连接失败 {attempt + 1}/{max_retries} - 连接对象为空")
                        if attempt < max_retries - 1:
                            time.sleep(2)  # 等待2秒后重试

                except Exception as conn_error:
                    error_msg = str(conn_error)
                    self.log_message(f"❌ {db_type} 连接异常 (尝试 {attempt + 1}): {error_msg}")

                    if attempt < max_retries - 1:
                        # 分析错误类型并决定是否重试
                        if "SQL30082N" in error_msg:
                            self.log_message(f"⚠️ 认证错误，跳过重试")
                            break
                        elif "SQL1042C" in error_msg:
                            self.log_message(f"⚠️ 系统错误，等待后重试...")
                            time.sleep(3)  # 系统错误等待更长时间
                        else:
                            time.sleep(2)
                    else:
                        self.log_message(f"❌ {db_type} 所有连接尝试失败")
                        return None

            if not conn:
                self.log_message(f"❌ {db_type} 数据库连接失败")
                return None

            try:
                # 执行查询
                self.log_message(f"📊 {db_type} 执行SQL查询...")
                stmt = ibm_db.exec_immediate(conn, sql)

                if not stmt:
                    self.log_message(f"❌ {db_type} SQL执行失败")
                    return None

                # 获取列信息
                columns = []
                num_columns = ibm_db.num_fields(stmt)
                for i in range(num_columns):
                    field_name = ibm_db.field_name(stmt, i)
                    columns.append(field_name)

                # 获取数据
                data = []
                row_count = 0

                while ibm_db.fetch_row(stmt):
                    row = {}
                    for i, col_name in enumerate(columns):
                        value = ibm_db.result(stmt, i)
                        row[col_name] = value
                    data.append(row)
                    row_count += 1

                    # 每1000行显示一次进度
                    if row_count % 1000 == 0:
                        self.log_message(f"📊 {db_type} 已获取 {row_count} 条记录...")

                self.log_message(f"✅ {db_type} 查询成功: {len(data)} 条记录")
                return data

            except Exception as query_error:
                self.log_message(f"❌ {db_type} 查询执行异常: {str(query_error)}")
                return None

            finally:
                # 确保连接被关闭
                try:
                    if conn:
                        ibm_db.close(conn)
                        self.log_message(f"🔒 {db_type} 连接已关闭")
                except Exception as close_error:
                    self.log_message(f"⚠️ {db_type} 连接关闭异常: {str(close_error)}")

        except Exception as e:
            self.log_message(f"❌ {db_type} 查询异常: {str(e)}")
            return None

    def _compare_table_data(self, source_data: list, target_data: list, table_id: str) -> list:
        """比对表数据"""
        try:
            differences = []

            # 创建目标数据索引（假设第一个字段是主键）
            if not source_data and not target_data:
                return differences

            # 获取主键字段名（使用第一个字段作为主键）
            if source_data:
                key_field = list(source_data[0].keys())[0]
            elif target_data:
                key_field = list(target_data[0].keys())[0]
            else:
                return differences

            # 创建索引
            target_index = {str(row[key_field]): row for row in target_data}
            source_index = {str(row[key_field]): row for row in source_data}

            # 检测差异和目标缺失
            for source_row in source_data:
                key_value = str(source_row[key_field])

                if key_value not in target_index:
                    # 目标缺失
                    differences.append({
                        'id': key_value,
                        'source_data': source_row,
                        'target_data': {},
                        'diff_fields': [],
                        'diff_type': 'target_missing'
                    })
                else:
                    # 检测字段差异
                    target_row = target_index[key_value]
                    diff_fields = []

                    for field, source_value in source_row.items():
                        if field in target_row:
                            target_value = target_row[field]
                            if str(source_value) != str(target_value):
                                diff_fields.append(field)

                    if diff_fields:
                        differences.append({
                            'id': key_value,
                            'source_data': source_row,
                            'target_data': target_row,
                            'diff_fields': diff_fields,
                            'diff_type': 'difference'
                        })

            # 检测源缺失
            for target_row in target_data:
                key_value = str(target_row[key_field])
                if key_value not in source_index:
                    differences.append({
                        'id': key_value,
                        'source_data': {},
                        'target_data': target_row,
                        'diff_fields': [],
                        'diff_type': 'source_missing'
                    })

            return differences

        except Exception as e:
            self.log_message(f"❌ 表 {table_id} 数据比对异常: {str(e)}")
            return []

    def _execute_sql_query_with_interruption(self, db_key: str, sql: str, table_id: str, db_type: str):
        """执行SQL查询 - 带中断检查版本"""
        try:
            import ibm_db
            import time

            # 获取数据库配置
            db_config = self.config_manager.get_database_config(db_key)
            if not db_config:
                self.log_message(f"❌ {db_key} 配置不存在")
                return None

            # 构建连接字符串
            conn_str = (
                f"DATABASE={db_config.database};"
                f"HOSTNAME={db_config.host};"
                f"PORT={db_config.port};"
                f"PROTOCOL=TCPIP;"
                f"UID={db_config.username};"
                f"PWD={db_config.password};"
                f"AUTHENTICATION=SERVER;"
                f"CONNECTTIMEOUT=30;"
                f"QUERYTIMEOUT=60;"
            )

            # 连接数据库（带中断检查）
            conn = None
            max_retries = 3

            for attempt in range(max_retries):
                # 中断检查
                if self.cancel_requested:
                    self.log_message(f"⏹️ {db_type} 连接被用户取消")
                    return None

                try:
                    # 在连接前再次检查中断
                    if self.cancel_requested:
                        self.log_message(f"⏹️ {db_type} 连接在执行前被取消")
                        return None

                    conn = ibm_db.connect(conn_str, "", "")

                    if conn:
                        self.log_message(f"✅ {db_type} 连接成功")
                        break
                    else:
                        self.log_message(f"❌ {db_type} 连接失败 - 连接对象为空")
                        if attempt < max_retries - 1:
                            # 在等待期间也检查中断
                            for i in range(20):  # 分成20个100ms的检查
                                if self.cancel_requested:
                                    self.log_message(f"⏹️ {db_type} 连接重试等待被取消")
                                    return None
                                time.sleep(0.1)

                except Exception as conn_error:
                    error_msg = str(conn_error)
                    self.log_message(f"❌ {db_type} 连接异常 (尝试 {attempt + 1}): {error_msg}")

                    if attempt < max_retries - 1:
                        if "SQL30082N" in error_msg:
                            self.log_message(f"⚠️ 认证错误，跳过重试")
                            break
                        elif "SQL1042C" in error_msg:
                            self.log_message(f"⚠️ 系统错误，等待后重试...")
                            time.sleep(3)
                        else:
                            time.sleep(2)
                    else:
                        self.log_message(f"❌ {db_type} 所有连接尝试失败")
                        return None

            if not conn:
                self.log_message(f"❌ {db_type} 数据库连接失败")
                return None

            try:
                # 中断检查
                if self.cancel_requested:
                    self.log_message(f"⏹️ {db_type} 查询被用户取消")
                    return None

                # 执行查询
                self.log_message(f"📊 {db_type} 执行SQL查询...")
                stmt = ibm_db.exec_immediate(conn, sql)

                if not stmt:
                    self.log_message(f"❌ {db_type} SQL执行失败")
                    return None

                # 获取列信息
                columns = []
                num_columns = ibm_db.num_fields(stmt)
                for i in range(num_columns):
                    field_name = ibm_db.field_name(stmt, i)
                    columns.append(field_name)

                # 获取数据（带中断检查）
                data = []
                row_count = 0

                while ibm_db.fetch_row(stmt):
                    # 中断检查（每100行检查一次）
                    if row_count % 100 == 0 and self.cancel_requested:
                        self.log_message(f"⏹️ {db_type} 数据读取被用户取消（已读取 {row_count} 行）")
                        return None

                    row = {}
                    for i, col_name in enumerate(columns):
                        value = ibm_db.result(stmt, i)
                        row[col_name] = value
                    data.append(row)
                    row_count += 1

                    # 每100000行显示一次进度
                    if row_count % 100000 == 0:
                        self.log_message(f"📊 {db_type} 已读取 {row_count} 行数据...")

                self.log_message(f"✅ {db_type} 查询完成，共 {row_count} 行数据")
                return data

            except Exception as query_error:
                self.log_message(f"❌ {db_type} 查询执行异常: {str(query_error)}")
                return None
            finally:
                # 确保连接被关闭
                if conn:
                    try:
                        ibm_db.close(conn)
                        self.log_message(f"🔌 {db_type} 数据库连接已关闭")
                    except Exception as close_error:
                        self.log_message(f"⚠️ {db_type} 关闭连接时出错: {close_error}")

        except Exception as e:
            self.log_message(f"❌ {db_type} 查询异常: {str(e)}")
            return None

    def _compare_table_data_with_interruption(self, source_data: list, target_data: list, table_id: str) -> list:
        """比对表数据 - 带中断检查版本"""
        try:
            differences = []

            # 创建目标数据索引（假设第一个字段是主键）
            if not source_data and not target_data:
                return differences

            # 获取主键字段名（使用第一个字段作为主键）
            if source_data:
                key_field = list(source_data[0].keys())[0]
            elif target_data:
                key_field = list(target_data[0].keys())[0]
            else:
                return differences

            # 创建索引
            target_index = {str(row[key_field]): row for row in target_data}
            source_index = {str(row[key_field]): row for row in source_data}

            # 检测差异和目标缺失（带中断检查）
            processed_count = 0
            for source_row in source_data:
                # 每100条记录检查一次中断
                if processed_count % 100 == 0 and self.cancel_requested:
                    self.log_message(f"⏹️ 表 {table_id} 数据比对被用户取消（已处理 {processed_count} 条源记录）")
                    return differences

                key_value = str(source_row[key_field])

                if key_value not in target_index:
                    # 目标缺失
                    differences.append({
                        'id': key_value,
                        'source_data': source_row,
                        'target_data': {},
                        'diff_fields': [],
                        'diff_type': 'target_missing'
                    })
                else:
                    # 检测字段差异
                    target_row = target_index[key_value]
                    diff_fields = []

                    for field, source_value in source_row.items():
                        if field in target_row:
                            target_value = target_row[field]
                            if str(source_value) != str(target_value):
                                diff_fields.append(field)

                    if diff_fields:
                        differences.append({
                            'id': key_value,
                            'source_data': source_row,
                            'target_data': target_row,
                            'diff_fields': diff_fields,
                            'diff_type': 'difference'
                        })

                processed_count += 1

            # 检测源缺失（带中断检查）
            processed_count = 0
            for target_row in target_data:
                # 每100条记录检查一次中断
                if processed_count % 100 == 0 and self.cancel_requested:
                    self.log_message(f"⏹️ 表 {table_id} 数据比对被用户取消（已处理 {processed_count} 条目标记录）")
                    return differences

                key_value = str(target_row[key_field])
                if key_value not in source_index:
                    differences.append({
                        'id': key_value,
                        'source_data': {},
                        'target_data': target_row,
                        'diff_fields': [],
                        'diff_type': 'source_missing'
                    })

                processed_count += 1

            return differences

        except Exception as e:
            self.log_message(f"❌ 表 {table_id} 数据比对异常: {str(e)}")
            return []

    def _display_real_summary(self, tables: list, completed_tables: int):
        """显示真实比对结果摘要"""
        try:
            # 在日志中显示摘要信息
            self.log_message("📊 真实比对结果摘要")
            self.log_message("=" * 50)
            self.log_message("✅ 这是真实数据库比对结果")

            total_tables = len(tables)
            self.log_message("📈 总体统计:")
            self.log_message(f"  • 比对表数量: {total_tables}")
            self.log_message(f"  • 成功完成: {completed_tables}")
            self.log_message(f"  • 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示每个表的统计
            self.log_message("📋 各表比对结果:")
            for table in tables:
                table_id = table['table_id']
                self.log_message(f"  • {table_id}: 已完成（真实数据）")

            self.log_message("💡 提示: 查看'比对结果'选项卡查看详细差异记录")
            self.log_message("🎉 真实数据库比对已完成")

        except Exception as e:
            self.log_message(f"❌ 显示真实摘要失败: {str(e)}")

    def _simulate_comparison(self):
        """模拟比对过程"""
        try:
            # 生成任务ID
            self.current_task_id = f"gui_task_{uuid.uuid4().hex[:8]}"
            self.log_message(f"📋 任务ID: {self.current_task_id}")

            # 获取数据库连接配置
            source_config = self.config_manager.get_database_config('DB1')
            target_config = self.config_manager.get_database_config('DB2')

            self.log_message(f"🔗 源数据库: {source_config.type}@{source_config.host}:{source_config.port}")
            self.log_message(f"🔗 目标数据库: {target_config.type}@{target_config.host}:{target_config.port}")

            # 获取比对表列表
            tables = self.config_manager.get_comparison_tables()
            total_tables = len(tables)

            if total_tables == 0:
                raise ValueError("没有找到比对表配置")

            self.log_message(f"📊 开始比对 {total_tables} 个表")

            # 模拟逐个执行表比对
            completed_tables = 0

            for i, table_config in enumerate(tables):
                table_id = table_config['table_id']

                # 更新表状态
                self._update_table_status(table_id, "执行中")

                # 更新进度
                progress = int((i / total_tables) * 100)
                self.update_progress(progress, f"正在比对表: {table_id}")

                self.log_message(f"🔄 开始比对表: {table_id} ({table_config['remark']})")

                # 模拟比对时间
                time.sleep(1)

                # 模拟比对结果
                self._simulate_table_result(table_id)

                self._update_table_status(table_id, "完成")
                completed_tables += 1
                self.log_message(f"✅ 表 {table_id} 比对完成")

            # 更新最终进度
            self.update_progress(100, "比对完成")

            # 显示模拟结果摘要
            self._display_simulation_summary(tables)

            self.log_message(f"🎉 所有表比对完成，成功: {completed_tables}/{total_tables}")

        except Exception as e:
            self.log_message(f"❌ 比对执行异常: {str(e)}")
            self.update_progress(0, f"执行失败: {str(e)}")
            raise

    def _simulate_table_result(self, table_id: str):
        """模拟单个表的比对结果"""
        try:
            # 模拟一些差异记录
            import random

            # 随机生成一些模拟差异
            for i in range(random.randint(1, 5)):
                record = {
                    'id': f"{table_id}_record_{i+1}",
                    'source_data': {'field1': f'source_value_{i+1}', 'field2': f'source_data_{i+1}'},
                    'target_data': {'field1': f'target_value_{i+1}', 'field2': f'target_data_{i+1}'},
                    'diff_fields': ['field1', 'field2'],
                    'diff_type': 'difference'
                }
                self._add_difference_to_tree(record, f"{table_id}-差异", table_id)

            # 模拟一些源独有记录
            for i in range(random.randint(0, 2)):
                record = {
                    'id': f"{table_id}_source_only_{i+1}",
                    'source_data': {'field1': f'source_only_value_{i+1}'},
                    'target_data': {},
                    'diff_fields': [],
                    'diff_type': 'source_missing'
                }
                self._add_difference_to_tree(record, f"{table_id}-源独有", table_id)

        except Exception as e:
            self.log_message(f"❌ 模拟表 {table_id} 结果失败: {str(e)}")

    def _display_simulation_summary(self, tables):
        """显示模拟结果摘要 - 直接在日志中显示"""
        try:
            # 在日志中显示摘要信息
            self.log_message("📊 模拟比对结果摘要")
            self.log_message("=" * 50)
            self.log_message("⚠️ 注意：这是模拟结果，非真实比对数据")

            total_tables = len(tables)
            self.log_message("📈 总体统计:")
            self.log_message(f"  • 比对表数量: {total_tables}")
            self.log_message(f"  • 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示每个表的统计
            self.log_message("📋 各表比对结果:")
            for table in tables:
                table_id = table['table_id']
                self.log_message(f"  • {table_id}: 已完成（模拟）")

            self.log_message("💡 提示: 查看'比对结果'选项卡查看模拟差异记录")
            self.log_message("🔧 要执行真实比对，请确保Agent核心模块正确安装")

        except Exception as e:
            self.log_message(f"❌ 显示摘要失败: {str(e)}")

    def _reset_comparison_state(self):
        """重置比对状态"""
        self.current_task_id = None
        self.comparison_results = {}
        self.progress_var.set(0)
        self.status_label.config(text="准备中...")

        # 重置性能优化变量
        self.current_displayed_records = 0
        self.pending_records.clear()
        self.display_warning_shown = False

        # 重置分页和筛选相关变量
        self.all_diff_records = []
        self.filtered_records = []
        self.current_page = 1
        self.total_pages = 0

        # 重置实时统计变量
        self.completed_tables_count = 0
        self.total_differences_count = 0
        self.comparison_start_time = None

        # 清空结果显示
        for item in self.diff_tree.get_children():
            self.diff_tree.delete(item)

        # 重置筛选选项
        self.table_filter_combo['values'] = ["全部表"]
        self.table_filter_var.set("全部表")
        self.diff_type_filter_combo['values'] = ["全部类型"]
        self.diff_type_filter_var.set("全部类型")
        self.search_var.set("")

        # 重置分页显示
        self.page_info_var.set("第0页，共0页")
        self.results_status_var.set("总计0条记录")

        # 重置实时统计显示
        self.completed_tables_label.config(text="0")
        self.total_tables_label.config(text="0")
        self.total_diffs_label.config(text="0")
        self.eta_label.config(text="--")
        self.elapsed_time_label.config(text="00:00:00")
        self.current_table_label.config(text="")

        # 重置统计摘要显示（已移除独立的统计区域）
        # 统计信息现在直接显示在日志中

        # 重置表状态
        for item in self.table_tree.get_children():
            values = list(self.table_tree.item(item)['values'])
            values[2] = "就绪"  # 状态列
            self.table_tree.item(item, values=values)



    def _update_table_status(self, table_id: str, status: str):
        """更新表状态"""
        try:
            for item in self.table_tree.get_children():
                values = list(self.table_tree.item(item)['values'])
                if values[0] == table_id:  # 表ID匹配
                    values[2] = status  # 更新状态列
                    self.table_tree.item(item, values=values)
                    break
        except Exception as e:
            self.log_message(f"❌ 更新表状态失败: {str(e)}")

    def update_progress(self, progress: int, status: str = ""):
        """更新进度条和状态 - 线程安全版本"""
        try:
            def update_gui():
                try:
                    if hasattr(self, 'progress_var'):
                        self.progress_var.set(progress)
                    if status and hasattr(self, 'status_label'):
                        self.status_label.config(text=status)
                except Exception as e:
                    pass

            self._queue_gui_update(update_gui)

        except Exception as e:
            # 避免在这里记录日志，防止递归调用
            pass



    def _batch_add_differences_to_tree(self, differences: list, table_id: str):
        """批量添加差异记录 - 简化的传统模式"""
        try:
            if not differences:
                return

            total_differences = len(differences)
            self.log_message(f"📊 表 {table_id}: 准备添加 {total_differences} 条差异记录...")

            # 简化的记录处理
            records_to_add = []
            for i, record in enumerate(differences):
                if self.cancel_requested:
                    break

                # 创建简化的记录对象
                diff_record = {
                    'table_id': table_id,
                    'record_id': str(record.get('id', f'record_{i}')),
                    'diff_type': record.get('diff_type', 'UPDATE'),
                    'diff_fields': record.get('diff_fields', []),
                    'source_data': record.get('source_data', {}),
                    'target_data': record.get('target_data', {}),
                    'source_preview': self._format_data_preview(record.get('source_data', {})),
                    'target_preview': self._format_data_preview(record.get('target_data', {})),
                    'full_record_json': json.dumps(record, ensure_ascii=False)
                }
                records_to_add.append(diff_record)

                # 每50000条记录显示一次进度
                if (i + 1) % 50000 == 0:
                    progress_percent = ((i + 1) / total_differences) * 100
                    self.log_message(f"📈 表 {table_id}: 已处理 {i+1}/{total_differences} 条记录 ({progress_percent:.1f}%)")

            # 添加到传统存储
            if not hasattr(self, 'all_diff_records'):
                self.all_diff_records = []

            self.all_diff_records.extend(records_to_add)
            self.total_differences_count += len(records_to_add)

            # 队列更新统计信息
            self._queue_stats_update()

            self.log_message(f"✅ 表 {table_id}: 成功添加 {len(records_to_add)} 条记录")

            if self.cancel_requested:
                self.log_message(f"⏹️ 表 {table_id}: 处理被用户取消")

            # 更新筛选选项和显示
            self._update_filter_options()
            if (self.table_filter_var.get() == "全部表" or self.table_filter_var.get() == table_id):
                self.apply_filters()

        except Exception as e:
            self.log_message(f"❌ 批量添加记录失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")



    def _update_filter_options(self):
        """更新筛选选项 - 使用内存管理器"""
        try:
            table_ids = set()
            diff_types = set()

            # 传统模式 - 从所有记录中获取选项
            if hasattr(self, 'all_diff_records'):
                for record in self.all_diff_records:
                    table_ids.add(record.get('table_id', ''))
                    diff_types.add(record.get('diff_type', ''))

            # 更新表筛选选项
            current_table_filter = self.table_filter_var.get()
            table_options = ["全部表"] + sorted(list(table_ids))
            self.table_filter_combo['values'] = table_options
            if current_table_filter not in table_options:
                self.table_filter_var.set("全部表")

            # 更新差异类型筛选选项
            current_diff_type_filter = self.diff_type_filter_var.get()
            diff_type_options = ["全部类型"] + sorted(list(diff_types))
            self.diff_type_filter_combo['values'] = diff_type_options
            if current_diff_type_filter not in diff_type_options:
                self.diff_type_filter_var.set("全部类型")

        except Exception as e:
            self.log_message(f"❌ 更新筛选选项失败: {str(e)}")

    def _export_records(self, records, filename_suffix=""):
        """导出记录到文件"""
        try:
            if not records:
                messagebox.showinfo("提示", "没有数据可导出")
                return

            # 选择导出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"比对结果_{filename_suffix}_{timestamp}.xlsx"

            filename = filedialog.asksaveasfilename(
                title="导出比对结果",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel文件", "*.xlsx"),
                    ("CSV文件", "*.csv"),
                    ("JSON文件", "*.json")
                ],
                initialname=default_filename
            )

            if not filename:
                return

            # 根据文件扩展名选择导出格式
            file_ext = filename.lower().split('.')[-1]

            if file_ext == 'xlsx':
                self._export_to_excel(records, filename)
            elif file_ext == 'csv':
                self._export_to_csv(records, filename)
            elif file_ext == 'json':
                self._export_to_json(records, filename)
            else:
                messagebox.showerror("错误", "不支持的文件格式")
                return

            messagebox.showinfo("成功", f"已导出 {len(records)} 条记录到文件：\n{filename}")
            self.log_message(f"✅ 成功导出 {len(records)} 条记录到: {filename}")

        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.log_message(f"❌ {error_msg}")

    def _export_to_excel(self, records, filename):
        """导出到Excel文件"""
        try:
            import pandas as pd

            # 准备数据
            export_data = []
            for record in records:
                # 格式化差异字段
                diff_fields = record.get('diff_fields', [])
                if isinstance(diff_fields, list):
                    diff_fields_str = ', '.join(diff_fields) if diff_fields else '全部字段'
                else:
                    diff_fields_str = str(diff_fields)

                export_data.append({
                    '所属表': record.get('table_id', ''),
                    '记录ID': record.get('record_id', ''),
                    '差异类型': record.get('diff_type', ''),
                    '差异字段': diff_fields_str,
                    '源数据预览': record.get('source_preview', ''),
                    '目标数据预览': record.get('target_preview', ''),
                    '源数据完整': json.dumps(record.get('source_data', {}), ensure_ascii=False, indent=2),
                    '目标数据完整': json.dumps(record.get('target_data', {}), ensure_ascii=False, indent=2)
                })

            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)
            df.to_excel(filename, index=False, engine='openpyxl')

        except ImportError:
            # 如果没有pandas，使用简单的CSV格式
            self._export_to_csv(records, filename.replace('.xlsx', '.csv'))
            messagebox.showinfo("提示", "由于缺少pandas库，已导出为CSV格式")

    def _export_to_csv(self, records, filename):
        """导出到CSV文件"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['所属表', '记录ID', '差异类型', '差异字段', '源数据预览', '目标数据预览']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for record in records:
                # 格式化差异字段
                diff_fields = record.get('diff_fields', [])
                if isinstance(diff_fields, list):
                    diff_fields_str = ', '.join(diff_fields) if diff_fields else '全部字段'
                else:
                    diff_fields_str = str(diff_fields)

                writer.writerow({
                    '所属表': record.get('table_id', ''),
                    '记录ID': record.get('record_id', ''),
                    '差异类型': record.get('diff_type', ''),
                    '差异字段': diff_fields_str,
                    '源数据预览': record.get('source_preview', ''),
                    '目标数据预览': record.get('target_preview', '')
                })

    def _export_to_json(self, records, filename):
        """导出到JSON文件"""
        export_data = {
            'export_time': datetime.now().isoformat(),
            'total_records': len(records),
            'records': []
        }

        for record in records:
            export_record = {
                'table_id': record.get('table_id', ''),
                'record_id': record.get('record_id', ''),
                'diff_type': record.get('diff_type', ''),
                'diff_fields': record.get('diff_fields', []),
                'source_data': record.get('source_data', {}),
                'target_data': record.get('target_data', {})
            }
            export_data['records'].append(export_record)

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

    def _format_data_preview(self, data: Dict[str, Any], max_length: int = 100) -> str:
        """格式化数据预览"""
        if not data:
            return "(空)"

        try:
            # 选择几个关键字段进行预览
            preview_fields = []
            for key, value in list(data.items())[:3]:  # 只显示前3个字段
                if value is not None:
                    preview_fields.append(f"{key}={value}")

            preview = ", ".join(preview_fields)
            if len(preview) > max_length:
                preview = preview[:max_length-3] + "..."

            return preview if preview else str(data)[:max_length]

        except Exception:
            return str(data)[:max_length]

    def _display_overall_summary(self, all_results: Dict[str, Any]):
        """显示总体统计摘要 - 直接在日志中显示"""
        try:
            # 在日志中显示摘要信息
            self.log_message("📊 多表比对结果摘要")
            self.log_message("=" * 50)

            total_tables = len(all_results)
            self.log_message("📈 总体统计:")
            self.log_message(f"  • 比对表数量: {total_tables}")
            self.log_message(f"  • 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示每个表的统计
            self.log_message("📋 各表比对结果:")
            for table_id in all_results.keys():
                self.log_message(f"  • {table_id}: 已完成")

            self.log_message("💡 提示: 查看'比对结果'选项卡查看详细差异记录")

        except Exception as e:
            self.log_message(f"❌ 显示摘要失败: {str(e)}")

    def on_table_double_click(self, event):
        """表列表双击事件"""
        try:
            selection = self.table_tree.selection()
            if selection:
                item = selection[0]
                values = self.table_tree.item(item)['values']
                table_id = values[0]
                self.log_message(f"🔍 选中表: {table_id}")
        except Exception as e:
            self.log_message(f"❌ 处理表选择事件失败: {str(e)}")

    def view_record_details(self, event):
        """查看记录详情"""
        try:
            selection = self.diff_tree.selection()
            if not selection:
                self.log_message("⚠️ 没有选中任何记录")
                return

            # 获取选中的记录
            item = selection[0]
            item_values = self.diff_tree.item(item)['values']

            # 检查是否有足够的列数据（现在有7列：所属表、记录ID、差异类型、差异字段、源数据、目标数据、full_record）
            if len(item_values) < 7:
                self.log_message(f"❌ 记录数据不完整，列数: {len(item_values)}")
                messagebox.showerror("错误", "记录数据不完整，无法查看详细信息")
                return

            # 获取完整记录数据（在第7列，索引为6）
            full_record_json = item_values[6]

            if not full_record_json or full_record_json.strip() == "":
                self.log_message("❌ 完整记录数据为空")
                messagebox.showerror("错误", "无法获取记录详细信息")
                return

            # 解析JSON数据
            try:
                record = json.loads(full_record_json)
                self.log_message(f"✅ 成功解析记录数据: {record.get('id', 'unknown')}")
            except json.JSONDecodeError as json_error:
                self.log_message(f"❌ JSON解析失败: {str(json_error)}")
                self.log_message(f"原始数据: {full_record_json[:200]}...")
                messagebox.showerror("错误", f"记录数据格式错误: {str(json_error)}")
                return

            # 创建详细信息窗口
            self._show_detail_window(record)

        except Exception as e:
            error_msg = f"查看详细信息失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", error_msg)

    def _show_detail_window(self, record: Dict[str, Any]):
        """显示详细信息窗口 - 增强版本"""
        try:
            # 创建新窗口
            detail_window = tk.Toplevel(self.root)
            record_id = record.get('id', 'unknown')
            diff_type = record.get('diff_type', 'unknown')
            detail_window.title(f"记录详情 - {record_id} ({diff_type})")
            detail_window.geometry("1400x800")  # 增大窗口尺寸
            detail_window.transient(self.root)
            detail_window.grab_set()

            # 创建主框架
            main_frame = ttk.Frame(detail_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 获取差异字段信息
            diff_fields = record.get('diff_fields', [])
            source_data = record.get('source_data', {})
            target_data = record.get('target_data', {})

            # 创建增强的基本信息区域
            self._create_enhanced_info_section(main_frame, record_id, diff_type, diff_fields, source_data, target_data)

            # 创建选项卡界面
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

            # 并排对比视图选项卡
            self._create_side_by_side_tab(notebook, source_data, target_data, diff_fields)

            # JSON详细视图选项卡
            self._create_json_detail_tab(notebook, source_data, target_data, diff_fields)

            # 差异摘要选项卡
            self._create_diff_summary_tab(notebook, source_data, target_data, diff_fields, diff_type)

            # 创建增强的按钮区域
            self._create_enhanced_button_section(main_frame, detail_window, source_data, target_data, record)

            self.log_message(f"✅ 增强详细信息窗口已打开: {record_id}")

        except Exception as e:
            self.log_message(f"❌ 显示详细信息窗口失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            messagebox.showerror("错误", f"无法显示详细信息: {str(e)}")

    def _create_enhanced_info_section(self, parent, record_id, diff_type, diff_fields, source_data, target_data):
        """创建增强的基本信息区域"""
        info_frame = ttk.LabelFrame(parent, text="📋 基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建信息网格
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill=tk.X)

        # 第一行：基本信息
        ttk.Label(info_grid, text="记录ID:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_grid, text=record_id).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(info_grid, text="差异类型:", font=('Arial', 9, 'bold')).grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        type_label = ttk.Label(info_grid, text=self._get_diff_type_display(diff_type))
        type_label.grid(row=0, column=3, sticky=tk.W)

        # 第二行：统计信息
        source_count = len(source_data) if source_data else 0
        target_count = len(target_data) if target_data else 0
        diff_count = len(diff_fields) if diff_fields else 0

        ttk.Label(info_grid, text="源字段数:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        ttk.Label(info_grid, text=str(source_count)).grid(row=1, column=1, sticky=tk.W, padx=(0, 20), pady=(5, 0))

        ttk.Label(info_grid, text="目标字段数:", font=('Arial', 9, 'bold')).grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        ttk.Label(info_grid, text=str(target_count)).grid(row=1, column=3, sticky=tk.W, pady=(5, 0))

        # 第三行：差异字段信息
        if diff_fields:
            ttk.Label(info_grid, text="差异字段:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
            diff_text = f"共 {diff_count} 个字段存在差异"
            ttk.Label(info_grid, text=diff_text, foreground='red').grid(row=2, column=1, columnspan=3, sticky=tk.W, pady=(5, 0))

            # 显示差异字段列表
            diff_fields_text = ', '.join(diff_fields[:10])  # 最多显示10个字段
            if len(diff_fields) > 10:
                diff_fields_text += f" ... (还有{len(diff_fields)-10}个)"
            ttk.Label(info_grid, text=diff_fields_text, font=('Arial', 8)).grid(row=3, column=0, columnspan=4, sticky=tk.W, pady=(2, 0))
        else:
            if diff_type == 'target_missing':
                ttk.Label(info_grid, text="差异说明:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
                ttk.Label(info_grid, text="目标数据库中缺失此记录", foreground='orange').grid(row=2, column=1, columnspan=3, sticky=tk.W, pady=(5, 0))
            elif diff_type == 'source_missing':
                ttk.Label(info_grid, text="差异说明:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
                ttk.Label(info_grid, text="源数据库中缺失此记录", foreground='orange').grid(row=2, column=1, columnspan=3, sticky=tk.W, pady=(5, 0))



    def _get_diff_type_display(self, diff_type):
        """获取差异类型的显示文本"""
        type_map = {
            'difference': '🔄 字段差异',
            'target_missing': '❌ 目标缺失',
            'source_missing': '➕ 源缺失',
            'unknown': '❓ 未知类型'
        }
        return type_map.get(diff_type, f'❓ {diff_type}')

    def _create_side_by_side_tab(self, notebook, source_data, target_data, diff_fields):
        """创建并排对比视图选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="🔍 并排对比")

        # 配置网格权重
        tab_frame.grid_rowconfigure(1, weight=1)
        tab_frame.grid_columnconfigure(0, weight=1)

        # 创建搜索框 - 使用grid布局
        search_frame = ttk.Frame(tab_frame)
        search_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        ttk.Label(search_frame, text="搜索字段:").pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))

        # 差异导航按钮
        nav_frame = ttk.Frame(search_frame)
        nav_frame.pack(side=tk.RIGHT)

        self.current_diff_index = 0
        self.diff_positions = []

        prev_btn = ttk.Button(nav_frame, text="⬆ 上一个差异", command=lambda: self._navigate_diff(-1))
        prev_btn.pack(side=tk.LEFT, padx=(0, 5))

        next_btn = ttk.Button(nav_frame, text="⬇ 下一个差异", command=lambda: self._navigate_diff(1))
        next_btn.pack(side=tk.LEFT)

        # 创建对比表格
        columns = ("字段名", "源数据值", "目标数据值", "状态")
        tree = ttk.Treeview(tab_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        tree.heading("字段名", text="字段名")
        tree.heading("源数据值", text="源数据值")
        tree.heading("目标数据值", text="目标数据值")
        tree.heading("状态", text="状态")

        tree.column("字段名", width=150)
        tree.column("源数据值", width=300)
        tree.column("目标数据值", width=300)
        tree.column("状态", width=100)

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(tab_frame, orient=tk.VERTICAL, command=tree.yview)
        scrollbar_h = ttk.Scrollbar(tab_frame, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # 使用grid布局放置表格和滚动条
        tree.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        scrollbar_v.grid(row=1, column=1, sticky="ns")
        scrollbar_h.grid(row=2, column=0, sticky="ew")

        # 填充数据
        self._populate_comparison_tree(tree, source_data, target_data, diff_fields, search_var)

        # 绑定搜索事件
        search_var.trace_add('write', lambda *args: self._filter_comparison_tree(tree, source_data, target_data, diff_fields, search_var.get()))

        # 存储tree引用用于导航
        self.comparison_tree = tree

    def _create_json_detail_tab(self, notebook, source_data, target_data, diff_fields):
        """创建JSON详细视图选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="📄 JSON详细")

        # 创建数据对比框架
        compare_frame = ttk.Frame(tab_frame)
        compare_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 源数据
        source_frame = ttk.LabelFrame(compare_frame, text="📊 源数据", padding=10)
        source_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        source_text = scrolledtext.ScrolledText(source_frame, height=25, width=45, wrap=tk.WORD, font=('Consolas', 10))
        source_text.pack(fill=tk.BOTH, expand=True)

        # 目标数据
        target_frame = ttk.LabelFrame(compare_frame, text="🎯 目标数据", padding=10)
        target_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        target_text = scrolledtext.ScrolledText(target_frame, height=25, width=45, wrap=tk.WORD, font=('Consolas', 10))
        target_text.pack(fill=tk.BOTH, expand=True)

        # 填充数据并高亮差异
        self._populate_json_with_highlighting(source_text, source_data, diff_fields, 'source')
        self._populate_json_with_highlighting(target_text, target_data, diff_fields, 'target')

    def _create_diff_summary_tab(self, notebook, source_data, target_data, diff_fields, diff_type):
        """创建差异摘要选项卡"""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="📈 差异摘要")

        # 创建摘要文本区域
        summary_text = scrolledtext.ScrolledText(tab_frame, height=25, wrap=tk.WORD, font=('Arial', 10))
        summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 生成差异摘要
        summary_content = self._generate_diff_summary(source_data, target_data, diff_fields, diff_type)
        summary_text.insert(tk.END, summary_content)
        summary_text.config(state=tk.DISABLED)

    def _create_enhanced_button_section(self, parent, detail_window, source_data, target_data, record):
        """创建增强的按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧按钮组
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)

        # 复制按钮
        def copy_source_data():
            detail_window.clipboard_clear()
            detail_window.clipboard_append(json.dumps(source_data, indent=2, ensure_ascii=False))
            messagebox.showinfo("提示", "源数据已复制到剪贴板")

        def copy_target_data():
            detail_window.clipboard_clear()
            detail_window.clipboard_append(json.dumps(target_data, indent=2, ensure_ascii=False))
            messagebox.showinfo("提示", "目标数据已复制到剪贴板")

        def copy_diff_summary():
            diff_fields = record.get('diff_fields', [])
            diff_type = record.get('diff_type', 'unknown')
            summary = self._generate_diff_summary(source_data, target_data, diff_fields, diff_type)
            detail_window.clipboard_clear()
            detail_window.clipboard_append(summary)
            messagebox.showinfo("提示", "差异摘要已复制到剪贴板")

        ttk.Button(left_buttons, text="📋 复制源数据", command=copy_source_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="📋 复制目标数据", command=copy_target_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="📋 复制差异摘要", command=copy_diff_summary).pack(side=tk.LEFT, padx=(0, 5))

        # 右侧按钮组
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(right_buttons, text="❌ 关闭", command=detail_window.destroy).pack(side=tk.RIGHT)

    def _populate_comparison_tree(self, tree, source_data, target_data, diff_fields, search_var=None):
        """填充对比树数据"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)

        self.diff_positions = []  # 重置差异位置列表

        # 获取所有字段
        all_fields = set(source_data.keys()) | set(target_data.keys())

        for i, field in enumerate(sorted(all_fields)):
            source_value = source_data.get(field, "(缺失)")
            target_value = target_data.get(field, "(缺失)")

            # 确定状态
            if field in diff_fields:
                status = "🔴 差异"
                tags = ("diff",)
                self.diff_positions.append(i)
            elif field not in source_data:
                status = "➕ 目标独有"
                tags = ("target_only",)
            elif field not in target_data:
                status = "❌ 源独有"
                tags = ("source_only",)
            else:
                status = "✅ 匹配"
                tags = ("match",)

            # 插入数据
            item = tree.insert('', 'end', values=(field, str(source_value), str(target_value), status), tags=tags)

        # 配置标签样式
        tree.tag_configure("diff", background="#ffebee", foreground="#d32f2f")
        tree.tag_configure("target_only", background="#e8f5e8", foreground="#388e3c")
        tree.tag_configure("source_only", background="#fff3e0", foreground="#f57c00")
        tree.tag_configure("match", background="#f5f5f5", foreground="#666666")

    def _filter_comparison_tree(self, tree, source_data, target_data, diff_fields, search_text):
        """过滤对比树数据"""
        if not search_text:
            self._populate_comparison_tree(tree, source_data, target_data, diff_fields)
            return

        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)

        search_text = search_text.lower()
        all_fields = set(source_data.keys()) | set(target_data.keys())

        for field in sorted(all_fields):
            if search_text in field.lower():
                source_value = source_data.get(field, "(缺失)")
                target_value = target_data.get(field, "(缺失)")

                # 确定状态
                if field in diff_fields:
                    status = "🔴 差异"
                    tags = ("diff",)
                elif field not in source_data:
                    status = "➕ 目标独有"
                    tags = ("target_only",)
                elif field not in target_data:
                    status = "❌ 源独有"
                    tags = ("source_only",)
                else:
                    status = "✅ 匹配"
                    tags = ("match",)

                tree.insert('', 'end', values=(field, str(source_value), str(target_value), status), tags=tags)

    def _navigate_diff(self, direction):
        """导航到下一个/上一个差异"""
        if not hasattr(self, 'comparison_tree') or not self.diff_positions:
            return

        if direction > 0:  # 下一个
            self.current_diff_index = (self.current_diff_index + 1) % len(self.diff_positions)
        else:  # 上一个
            self.current_diff_index = (self.current_diff_index - 1) % len(self.diff_positions)

        # 选中并滚动到对应项
        items = self.comparison_tree.get_children()
        if self.current_diff_index < len(items):
            target_item = items[self.diff_positions[self.current_diff_index]]
            self.comparison_tree.selection_set(target_item)
            self.comparison_tree.see(target_item)

    def _populate_json_with_highlighting(self, text_widget, data, diff_fields, data_type):
        """填充JSON数据并高亮差异字段"""
        text_widget.config(state=tk.NORMAL)
        text_widget.delete(1.0, tk.END)

        if not data:
            text_widget.insert(tk.END, "(无数据)")
            text_widget.config(state=tk.DISABLED)
            return

        # 配置标签样式
        text_widget.tag_configure("diff_field", background="#ffcdd2", foreground="#d32f2f", font=('Consolas', 10, 'bold'))
        text_widget.tag_configure("diff_value", background="#ffebee", foreground="#d32f2f")
        text_widget.tag_configure("normal", foreground="#333333")

        # 格式化JSON
        formatted_json = json.dumps(data, indent=2, ensure_ascii=False)
        lines = formatted_json.split('\n')

        for line_num, line in enumerate(lines, 1):
            # 检查是否包含差异字段
            is_diff_line = False
            for diff_field in diff_fields:
                if f'"{diff_field}"' in line:
                    is_diff_line = True
                    break

            if is_diff_line:
                # 高亮整行
                text_widget.insert(tk.END, line + '\n', "diff_value")
            else:
                text_widget.insert(tk.END, line + '\n', "normal")

        text_widget.config(state=tk.DISABLED)

    def _generate_diff_summary(self, source_data, target_data, diff_fields, diff_type):
        """生成差异摘要"""
        summary = "📊 差异分析摘要\n"
        summary += "=" * 50 + "\n\n"

        # 基本统计
        summary += f"🔍 差异类型: {self._get_diff_type_display(diff_type)}\n"
        summary += f"📈 源字段数量: {len(source_data) if source_data else 0}\n"
        summary += f"📈 目标字段数量: {len(target_data) if target_data else 0}\n"
        summary += f"🔴 差异字段数量: {len(diff_fields) if diff_fields else 0}\n\n"

        if diff_fields:
            summary += "🔍 详细差异分析:\n"
            summary += "-" * 30 + "\n"

            for i, field in enumerate(diff_fields, 1):
                source_value = source_data.get(field, "(缺失)")
                target_value = target_data.get(field, "(缺失)")

                summary += f"{i}. 字段: {field}\n"
                summary += f"   源值: {source_value}\n"
                summary += f"   目标值: {target_value}\n"
                summary += f"   差异: {source_value} ➜ {target_value}\n\n"

        # 字段分析
        if source_data and target_data:
            source_only = set(source_data.keys()) - set(target_data.keys())
            target_only = set(target_data.keys()) - set(source_data.keys())

            if source_only:
                summary += f"❌ 仅源存在的字段 ({len(source_only)}个):\n"
                for field in sorted(source_only):
                    summary += f"   - {field}: {source_data[field]}\n"
                summary += "\n"

            if target_only:
                summary += f"➕ 仅目标存在的字段 ({len(target_only)}个):\n"
                for field in sorted(target_only):
                    summary += f"   - {field}: {target_data[field]}\n"
                summary += "\n"

        summary += f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

        return summary

    def export_results(self):
        """导出结果"""
        try:
            if len(self.diff_tree.get_children()) == 0:
                messagebox.showwarning("提示", "没有可导出的比对结果")
                return

            # 选择导出格式
            export_format = messagebox.askyesnocancel("导出格式", "选择导出格式:\n是 - Excel\n否 - CSV\n取消 - 取消导出")

            if export_format is None:  # 取消
                return
            elif export_format:  # Excel
                self._export_excel()
            else:  # CSV
                self._export_csv()

        except Exception as e:
            error_msg = f"导出结果失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def _export_csv(self):
        """导出为CSV格式"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出CSV文件",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )

            if not filename:
                return

            # 收集所有记录数据
            records = self._collect_all_records()

            if not records:
                messagebox.showwarning("提示", "没有记录可导出")
                return

            # 导出CSV
            if PANDAS_AVAILABLE:
                # 使用pandas导出CSV
                import pandas as pd
                df = pd.DataFrame(records)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
            else:
                # 简化版CSV导出
                import csv
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    if records:
                        fieldnames = records[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(records)

            self.log_message(f"✅ 成功导出 {len(records)} 条记录到: {filename}")
            messagebox.showinfo("导出成功", f"已成功导出 {len(records)} 条记录到:\n{filename}")

        except Exception as e:
            error_msg = f"导出CSV失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def _export_excel(self):
        """导出为Excel格式"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )

            if not filename:
                return

            # 收集所有记录数据
            records = self._collect_all_records()

            if not records:
                messagebox.showwarning("提示", "没有记录可导出")
                return

            # 导出Excel
            if PANDAS_AVAILABLE:
                # 使用pandas导出Excel
                import pandas as pd
                df = pd.DataFrame(records)

                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    # 导出所有记录到主工作表
                    df.to_excel(writer, sheet_name='比对结果', index=False)

                    # 按类型分别导出到不同工作表
                    for category in df['差异类型'].unique():
                        category_records = df[df['差异类型'] == category]
                        if len(category_records) > 0:
                            sheet_name = category.replace('-', '_')[:31]  # Excel工作表名限制
                            category_records.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                # 简化版Excel导出（实际上导出为CSV）
                csv_filename = filename.replace('.xlsx', '.csv')
                import csv
                with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    if records:
                        fieldnames = records[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(records)
                self.log_message("⚠️ pandas未安装，已导出为CSV格式")

            self.log_message(f"✅ 成功导出 {len(records)} 条记录到: {filename}")
            messagebox.showinfo("导出成功", f"已成功导出 {len(records)} 条记录到:\n{filename}")

        except Exception as e:
            error_msg = f"导出Excel失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def _collect_all_records(self) -> List[Dict[str, Any]]:
        """收集所有记录数据用于导出"""
        records = []

        try:
            for item in self.diff_tree.get_children():
                values = self.diff_tree.item(item)['values']

                # 检查是否有足够的列数据
                if len(values) < 6:
                    self.log_message(f"⚠️ 跳过不完整的记录，列数: {len(values)}")
                    continue

                # 获取完整记录数据（在第6列，索引为5）
                full_record_json = values[5]

                if full_record_json and full_record_json.strip():
                    try:
                        full_record = json.loads(full_record_json)

                        # 构建导出记录
                        export_record = {
                            '记录ID': values[0],
                            '差异类型': values[1],
                            '差异字段': values[2],
                            '源数据预览': values[3],
                            '目标数据预览': values[4],
                            '源数据完整': json.dumps(full_record.get('source_data', {}), ensure_ascii=False),
                            '目标数据完整': json.dumps(full_record.get('target_data', {}), ensure_ascii=False)
                        }
                        records.append(export_record)
                    except json.JSONDecodeError as json_error:
                        self.log_message(f"⚠️ 跳过JSON解析失败的记录: {str(json_error)}")
                        continue
                else:
                    self.log_message(f"⚠️ 跳过空的完整记录数据")

            self.log_message(f"✅ 成功收集 {len(records)} 条记录用于导出")
            return records

        except Exception as e:
            self.log_message(f"❌ 收集记录数据失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            return []

    def on_closing(self):
        """应用程序关闭事件"""
        try:
            # 如果有正在进行的比对，先停止
            if hasattr(self, 'comparison_thread') and self.comparison_thread and self.comparison_thread.is_alive():
                self.stop_comparison()
                # 等待线程结束
                self.comparison_thread.join(timeout=2)



            self.log_message("👋 应用程序正在关闭...")
            self.root.destroy()

        except Exception as e:
            print(f"关闭应用程序时出错: {e}")
            self.root.destroy()

    def run(self):
        """运行GUI应用程序"""
        self.log_message("🚀 DB-Agent 数据库比对工具启动")

        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = EnhancedComparisonToolGUI()
        app.run()
    except Exception as e:
        print(f"❌ 启动GUI应用程序失败: {e}")
        import traceback
        traceback.print_exc()
