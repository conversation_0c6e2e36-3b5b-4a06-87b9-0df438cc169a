"""
系统配置管理
"""
import os
from urllib.parse import quote_plus
from typing import List, Optional
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "数据比对平台"
    VERSION: str = "2.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./comparison.db"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_CACHE_TTL: int = 3600  # 1小时
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # 文件存储配置
    UPLOAD_DIR: str = "./uploads"
    RESULT_DIR: str = "./results"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    # 比对配置
    DEFAULT_BATCH_SIZE: int = 1000
    MAX_CONCURRENT_TASKS: int = 5
    COMPARISON_TIMEOUT: int = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # 支持的数据库类型
    SUPPORTED_DB_TYPES: List[str] = [
        "oracle", "mysql", "postgresql", 
        "sqlserver", "db2", "gaussdb"
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


class DatabaseConfig:
    """数据库连接配置"""
    
    @staticmethod
    def get_connection_string(db_type: str, host: str, port: int, 
                            database: str, username: str, password: str) -> str:
        """根据数据库类型生成连接字符串"""

        # 对用户名和密码进行URL编码，防止特殊字符导致URL解析错误
        # quote_plus会将空格转换为+，其他特殊字符转换为%XX格式
        encoded_username = quote_plus(username) if username else ""
        encoded_password = quote_plus(password) if password else ""
        
        connection_templates = {
            "oracle": "oracle+cx_oracle://{username}:{password}@{host}:{port}/{database}",
            "mysql": "mysql+pymysql://{username}:{password}@{host}:{port}/{database}",
            "postgresql": "postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}",
            "sqlserver": "mssql+pyodbc://{username}:{password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server",
            "db2": "db2+ibm_db://{username}:{password}@{host}:{port}/{database}",
            "gaussdb": "postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"
        }
        
        if db_type.lower() not in connection_templates:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        return connection_templates[db_type.lower()].format(
            username=encoded_username,
            password=encoded_password,
            host=host,
            port=port,
            database=database
        )


class ComparisonConfig:
    """比对配置"""
    
    # 表结构比对配置
    STRUCTURE_COMPARISON_FIELDS = [
        "column_name", "data_type", "is_nullable", 
        "column_default", "character_maximum_length"
    ]
    
    # 表内容比对配置
    CONTENT_COMPARISON_BATCH_SIZE = 1000
    CONTENT_COMPARISON_TIMEOUT = 1800  # 30分钟
    
    # 结果存储格式
    SUPPORTED_EXPORT_FORMATS = ["csv", "excel", "json", "html"]
    
    # 差异类型定义
    DIFFERENCE_TYPES = {
        "MISSING_IN_SOURCE": "源端缺失",
        "MISSING_IN_TARGET": "目标端缺失", 
        "DATA_MISMATCH": "数据不匹配",
        "STRUCTURE_DIFFERENCE": "结构差异"
    }
