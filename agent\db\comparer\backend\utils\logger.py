import os
import sys
import time
import threading
from loguru import logger

# 避免循环导入，直接从环境变量获取日志级别
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

class LoggerManager:
    _instances = {}  # 保存不同名称的记录器
    _initialized = False
    _lock = threading.RLock()  # 添加可重入锁以保证线程安全
    _handlers = {}  # 保存处理器ID，避免重复添加

    @classmethod
    def get_logger(cls, role_name, log_level="INFO"):
        """获取指定角色的logger

        Args:
            role_name: 角色/模块名称
            log_level: 日志级别

        Returns:
            logger实例
        """
        with cls._lock:  # 使用锁保护临界区
            # 检查是否需要初始化基础配置
            if not cls._initialized:
                cls._initialize_base_config(log_level)
                cls._initialized = True

            # 如果这个记录器名称已存在
            if role_name in cls._instances:
                existing_logger = cls._instances[role_name]
                # 检查是否需要更新日志级别
                current_level = cls._get_logger_level(existing_logger)
                if current_level != log_level:
                    cls._update_logger_level(existing_logger, log_level)
                return existing_logger

            # 为新模块配置日志文件
            module_file_handler = cls._configure_module_logger(role_name, log_level)

            # 创建新的记录器并保存
            new_logger = logger.bind(role=role_name)
            cls._instances[role_name] = new_logger
            return new_logger

    @classmethod
    def _get_logger_level(cls, logger_instance):
        """获取logger当前级别"""
        # 这是简化实现，实际上loguru没有简单方式获取已绑定logger的级别
        # 在实际项目中可能需要存储这个信息
        return "INFO"  # 默认返回

    @classmethod
    def _update_logger_level(cls, logger_instance, new_level):
        """更新logger级别"""
        # 注意：这里只作为示例，loguru实际并没有提供直接修改绑定logger级别的方法
        # 实际项目中可能需要更复杂的处理
        pass

    @classmethod
    def _initialize_base_config(cls, log_level):
        """初始化基础日志配置"""
        with cls._lock:
            # 移除默认处理器 - 修复：不直接移除ID为0的处理器，改为检查是否存在
            try:
                logger.remove(0)  # 尝试移除默认处理器
            except ValueError:
                # 如果默认处理器不存在，则跳过，不抛出异常
                pass

            # 标准输出配置
            stderr_handler_id = logger.add(sys.stderr, level=log_level)
            cls._handlers["stderr"] = stderr_handler_id

            # 可以添加一个所有模块共享的主日志文件
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(script_dir))
            logger_dir = os.path.join(project_root, 'logs')

            if not os.path.exists(logger_dir):
                os.makedirs(logger_dir, exist_ok=True)

            # 主日志文件，包含所有模块日志
            date_str = time.strftime("%Y%m%d")
            main_log_file = os.path.join(logger_dir, f"{date_str}_main.log")

            main_handler_id = logger.add(
                main_log_file,
                rotation="00:00",
                retention="30 days",
                enqueue=True,
                encoding="utf-8",
                level=log_level,
                format=(
                    "{time:YYYY-MM-DD HH:mm:ss} | "
                    "{level} | "
                    "{extra[role]}:{name}:{function}:{line} | "
                    "{message}"
                )
            )
            cls._handlers["main"] = main_handler_id

    @classmethod
    def _configure_module_logger(cls, role_name, log_level):
        """为特定模块配置日志文件

        Args:
            role_name: 模块/角色名称
            log_level: 日志级别

        Returns:
            int: 处理器ID
        """
        with cls._lock:
            # 检查是否已经为此模块配置过日志文件
            handler_key = f"module_{role_name}"
            if handler_key in cls._handlers:
                return cls._handlers[handler_key]

            # 创建日志目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(script_dir))
            logger_dir = os.path.join(project_root, 'logs', 'modules')

            if not os.path.exists(logger_dir):
                os.makedirs(logger_dir, exist_ok=True)

            # 按日期和角色生成日志文件名
            date_str = time.strftime("%Y%m%d")
            log_file = os.path.join(logger_dir, f"{date_str}_{role_name.replace('.', '_')}.log")

            # 为该模块添加文件处理器
            handler_id = logger.add(
                log_file,
                rotation="00:00",  # 每天午夜轮换
                retention="30 days",  # 保留30天
                enqueue=True,  # 使用队列处理日志记录，提高性能
                encoding="utf-8",
                level=log_level,
                format=(
                    "{time:YYYY-MM-DD HH:mm:ss} | "
                    "{level} | "
                    "{name}:{function}:{line} | "
                    "{message}"
                )
            )

            # 保存处理器ID以避免重复添加
            cls._handlers[handler_key] = handler_id
            return handler_id

    @classmethod
    def reset(cls):
        """重置日志管理器（主要用于测试）"""
        with cls._lock:
            # 移除所有处理器
            for handler_id in cls._handlers.values():
                try:
                    logger.remove(handler_id)
                except:
                    pass

            # 重置所有状态
            cls._instances.clear()
            cls._handlers.clear()
            cls._initialized = False

def get_logger(role_name, log_level="INFO"):
    """获取logger的便捷函数

    Args:
        role_name: 模块/角色名称
        log_level: 日志级别

    Returns:
        logger实例
    """
    return LoggerManager.get_logger(role_name, log_level)

def init_logging(role_name, log_level="INFO"):
    """初始化日志系统，兼容旧API

    Args:
        role_name: 模块/角色名称
        log_level: 日志级别

    Returns:
        logger实例
    """
    return LoggerManager.get_logger(role_name, log_level)

logger = get_logger("db.agent", LOG_LEVEL)