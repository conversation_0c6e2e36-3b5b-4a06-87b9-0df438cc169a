"""
差异智能分析模块

提供数据比对结果的智能分析功能，自动识别差异模式，评估差异严重程度，
并提供处理建议，帮助用户更高效地理解和处理比对结果。
"""

import logging
import re
from typing import Dict, Any, List, Callable, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class DifferenceAnalyzer:
    """差异智能分析器"""
    
    def __init__(self):
        """初始化差异分析器"""
        self.patterns = self._load_diff_patterns()
    
    def _load_diff_patterns(self) -> List[Dict[str, Any]]:
        """
        加载差异模式库
        
        Returns:
            差异模式列表
        """
        # 实际实现中可从配置文件或数据库加载
        return [
            {
                "name": "数值精度差异",
                "pattern": lambda src, tgt: self._is_numeric_precision_diff(src, tgt),
                "severity": "low",
                "suggestion": "可能是浮点数精度问题，建议检查数据类型定义或比较逻辑"
            },
            {
                "name": "日期格式差异",
                "pattern": lambda src, tgt: self._is_date_format_diff(src, tgt),
                "severity": "medium",
                "suggestion": "日期格式不一致，建议统一日期格式或增加格式转换"
            },
            {
                "name": "空值与默认值",
                "pattern": lambda src, tgt: self._is_null_default_diff(src, tgt),
                "severity": "low",
                "suggestion": "NULL与默认空值混用，建议统一空值处理逻辑"
            },
            {
                "name": "大小写差异",
                "pattern": lambda src, tgt: self._is_case_diff(src, tgt),
                "severity": "low",
                "suggestion": "仅大小写不同，检查是否大小写敏感"
            },
            {
                "name": "前后空格差异",
                "pattern": lambda src, tgt: self._is_whitespace_diff(src, tgt),
                "severity": "low",
                "suggestion": "存在前后空格差异，建议规范化字符串处理"
            },
            {
                "name": "四舍五入差异",
                "pattern": lambda src, tgt: self._is_rounding_diff(src, tgt),
                "severity": "low",
                "suggestion": "可能是四舍五入导致的差异，检查舍入规则是否一致"
            },
            {
                "name": "字符编码差异",
                "pattern": lambda src, tgt: self._is_encoding_diff(src, tgt),
                "severity": "medium",
                "suggestion": "可能存在字符编码问题，检查字符集设置"
            },
            {
                "name": "时区差异",
                "pattern": lambda src, tgt: self._is_timezone_diff(src, tgt),
                "severity": "medium",
                "suggestion": "可能存在时区问题，检查时区设置是否一致"
            }
        ]
    
    def _is_numeric_precision_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为数值精度差异"""
        try:
            if not (self._is_numeric(src) and self._is_numeric(tgt)):
                return False
            
            src_val = float(src)
            tgt_val = float(tgt)
            
            # 检查相对误差
            if abs(src_val) < 1e-10 and abs(tgt_val) < 1e-10:
                return True  # 两个值都接近于0，视为相等
            
            relative_diff = abs(src_val - tgt_val) / max(abs(src_val), abs(tgt_val))
            return relative_diff < 0.001  # 相对误差小于0.1%
        except:
            return False
    
    def _is_date_format_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为日期格式差异"""
        if not (isinstance(src, str) and isinstance(tgt, str)):
            return False
        
        # 常见日期格式正则
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
            r'\d{2}\.\d{2}\.\d{4}',  # DD.MM.YYYY
            r'\d{4}/\d{2}/\d{2}',  # YYYY/MM/DD
            r'\d{2}-\d{2}-\d{4}'   # MM-DD-YYYY
        ]
        
        # 检查源和目标是否都匹配日期格式
        src_is_date = any(re.match(pattern, src) for pattern in date_patterns)
        tgt_is_date = any(re.match(pattern, tgt) for pattern in date_patterns)
        
        return src_is_date and tgt_is_date
    
    def _is_null_default_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为空值与默认值差异"""
        null_values = [None, '', 0, '0', 'NULL', 'null', 'None', 'none']
        
        return (src in null_values and tgt in null_values) or \
               (src is None and tgt in null_values) or \
               (tgt is None and src in null_values)
    
    def _is_case_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为大小写差异"""
        return isinstance(src, str) and isinstance(tgt, str) and \
               src.lower() == tgt.lower() and src != tgt
    
    def _is_whitespace_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为空格差异"""
        return isinstance(src, str) and isinstance(tgt, str) and \
               src.strip() == tgt.strip() and src != tgt
    
    def _is_rounding_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为四舍五入差异"""
        try:
            if not (self._is_numeric(src) and self._is_numeric(tgt)):
                return False
            
            src_val = float(src)
            tgt_val = float(tgt)
            
            # 检查是否为整数差异
            if abs(round(src_val) - tgt_val) < 1e-10 or abs(src_val - round(tgt_val)) < 1e-10:
                return True
            
            # 检查是否为小数位数差异
            for digits in range(1, 6):
                if abs(round(src_val, digits) - tgt_val) < 1e-10 or \
                   abs(src_val - round(tgt_val, digits)) < 1e-10:
                    return True
            
            return False
        except:
            return False
    
    def _is_encoding_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为字符编码差异"""
        if not (isinstance(src, str) and isinstance(tgt, str)):
            return False
        
        # 检查是否包含特殊字符
        special_chars = set('äöüßÄÖÜéèêëàâîïôùûçÉÈÊËÀÂÎÏÔÙÛÇñÑ')
        
        src_has_special = any(c in special_chars for c in src)
        tgt_has_special = any(c in special_chars for c in tgt)
        
        # 如果一个有特殊字符而另一个没有，可能是编码问题
        if src_has_special != tgt_has_special:
            return True
        
        # 检查是否有替换字符
        replacement_chars = ['?', '�', '#', '*']
        
        for char in replacement_chars:
            if (char in src and char not in tgt) or (char in tgt and char not in src):
                return True
        
        return False
    
    def _is_timezone_diff(self, src: Any, tgt: Any) -> bool:
        """判断是否为时区差异"""
        if not (isinstance(src, str) and isinstance(tgt, str)):
            return False
        
        # 检查是否为时间戳格式
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}',  # YYYY-MM-DD HH:MM:SS
            r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}'      # YYYY/MM/DD HH:MM:SS
        ]
        
        src_is_timestamp = any(re.match(pattern, src) for pattern in timestamp_patterns)
        tgt_is_timestamp = any(re.match(pattern, tgt) for pattern in timestamp_patterns)
        
        if not (src_is_timestamp and tgt_is_timestamp):
            return False
        
        # 尝试解析时间戳
        try:
            # 提取日期部分
            src_date = re.search(r'\d{4}[-/]\d{2}[-/]\d{2}', src).group(0)
            tgt_date = re.search(r'\d{4}[-/]\d{2}[-/]\d{2}', tgt).group(0)
            
            # 如果日期相同但时间不同，可能是时区问题
            if src_date == tgt_date:
                # 提取时间部分
                src_time = re.search(r'\d{2}:\d{2}:\d{2}', src).group(0)
                tgt_time = re.search(r'\d{2}:\d{2}:\d{2}', tgt).group(0)
                
                # 计算时间差（小时）
                src_hour = int(src_time.split(':')[0])
                tgt_hour = int(tgt_time.split(':')[0])
                
                hour_diff = abs(src_hour - tgt_hour)
                
                # 常见时区差为整数小时
                return hour_diff in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
        except:
            pass
        
        return False
    
    def _is_numeric(self, value: Any) -> bool:
        """判断值是否为数值"""
        if isinstance(value, (int, float)):
            return True
        
        if isinstance(value, str):
            try:
                float(value)
                return True
            except:
                return False
        
        return False
    
    def analyze_difference(self, diff_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个差异项
        
        Args:
            diff_item: 差异项
            
        Returns:
            分析结果
        """
        result = {
            "original_diff": diff_item,
            "analysis": [],
            "severity": "high",  # 默认高严重度
            "suggestions": []
        }
        
        # 获取源值和目标值
        if diff_item["diff_type"] == "value":
            source_value = diff_item.get("source_value")
            target_value = diff_item.get("target_value")
            
            # 应用模式匹配
            for pattern in self.patterns:
                try:
                    if pattern["pattern"](source_value, target_value):
                        result["analysis"].append({
                            "pattern_name": pattern["name"],
                            "confidence": 0.9  # 可以实现更复杂的置信度计算
                        })
                        
                        # 更新严重度（取最低严重度）
                        severity_levels = {"low": 1, "medium": 2, "high": 3}
                        if severity_levels.get(pattern["severity"], 3) < severity_levels.get(result["severity"], 3):
                            result["severity"] = pattern["severity"]
                        
                        # 添加建议
                        if pattern["suggestion"] not in result["suggestions"]:
                            result["suggestions"].append(pattern["suggestion"])
                except Exception as e:
                    # 忽略模式匹配过程中的错误
                    logger.debug(f"模式匹配错误: {str(e)}")
                    pass
        
        # 如果没有匹配任何模式，添加默认分析
        if not result["analysis"]:
            if diff_item["diff_type"] == "source_only":
                result["analysis"].append({
                    "pattern_name": "目标缺失记录",
                    "confidence": 1.0
                })
                result["suggestions"].append("检查目标系统是否正确处理了该记录")
            elif diff_item["diff_type"] == "target_only":
                result["analysis"].append({
                    "pattern_name": "源缺失记录",
                    "confidence": 1.0
                })
                result["suggestions"].append("检查源系统是否应该包含该记录")
        
        return result
    
    def batch_analyze(self, diff_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量分析差异项
        
        Args:
            diff_items: 差异项列表
            
        Returns:
            批量分析结果
        """
        results = []
        for item in diff_items:
            results.append(self.analyze_difference(item))
        
        # 生成总体分析
        summary = self._generate_summary(results)
        
        return {
            "items": results,
            "summary": summary
        }
    
    def _generate_summary(self, analyzed_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成总体分析摘要
        
        Args:
            analyzed_items: 已分析的差异项列表
            
        Returns:
            分析摘要
        """
        # 统计各类模式出现次数
        pattern_counts = {}
        severity_counts = {"low": 0, "medium": 0, "high": 0}
        
        for item in analyzed_items:
            for analysis in item["analysis"]:
                pattern_name = analysis["pattern_name"]
                pattern_counts[pattern_name] = pattern_counts.get(pattern_name, 0) + 1
            
            severity_counts[item["severity"]] = severity_counts.get(item["severity"], 0) + 1
        
        # 找出最常见的模式
        common_patterns = sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)
        
        # 生成摘要
        summary = {
            "total_differences": len(analyzed_items),
            "severity_distribution": severity_counts,
            "common_patterns": common_patterns[:5],  # 前5个最常见模式
            "overall_severity": "high" if severity_counts["high"] > 0 else "medium" if severity_counts["medium"] > 0 else "low",
            "suggestions": self._generate_overall_suggestions(common_patterns, severity_counts)
        }
        
        return summary
    
    def _generate_overall_suggestions(self, common_patterns: List[tuple], severity_counts: Dict[str, int]) -> List[str]:
        """
        生成总体建议
        
        Args:
            common_patterns: 常见模式列表
            severity_counts: 严重度统计
            
        Returns:
            建议列表
        """
        suggestions = []
        
        # 根据常见模式生成建议
        if common_patterns:
            most_common = common_patterns[0]
            if most_common[0] == "数值精度差异" and most_common[1] > 5:
                suggestions.append("系统中存在大量数值精度差异，建议检查数据类型定义和处理逻辑")
            elif most_common[0] == "日期格式差异" and most_common[1] > 5:
                suggestions.append("系统中存在大量日期格式差异，建议统一日期格式标准")
            elif most_common[0] == "空值与默认值" and most_common[1] > 5:
                suggestions.append("系统中存在大量空值处理差异，建议统一空值处理策略")
            elif most_common[0] == "大小写差异" and most_common[1] > 5:
                suggestions.append("系统中存在大量大小写差异，建议统一大小写处理或使用大小写不敏感的比较")
            elif most_common[0] == "前后空格差异" and most_common[1] > 5:
                suggestions.append("系统中存在大量空格差异，建议在比较前统一处理字符串前后空格")
        
        # 根据严重度分布生成建议
        if severity_counts["high"] > 10:
            suggestions.append("存在大量高严重度差异，建议优先处理这些问题")
        
        # 添加一些通用建议
        if len(common_patterns) > 3:
            suggestions.append("差异类型多样，建议检查系统间的数据处理逻辑是否一致")
        
        return suggestions
