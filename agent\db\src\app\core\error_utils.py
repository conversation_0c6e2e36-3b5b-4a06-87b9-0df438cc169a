"""
错误处理工具模块

提供错误处理的通用工具函数，避免循环导入问题。
"""
from enum import Enum
from typing import Dict, Any, Optional

class DatabaseError(Exception):
    """数据库错误基类"""
    def __init__(self, message: str, error_code: Optional[str] = None, sql_state: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        self.sql_state = sql_state
        super().__init__(message)

class ErrorCategory(str, Enum):
    """错误类别枚举，用于分类错误"""
    # 数据库相关错误
    DATABASE = "database"          # 数据库通用错误
    QUERY = "query"                # 查询错误
    COMPARISON = "comparison"      # 数据比对错误

    # 网络相关错误
    NETWORK = "network"            # 网络通用错误
    CONNECTION = "connection"      # 连接错误
    TIMEOUT = "timeout"            # 超时错误

    # 认证授权相关错误
    AUTHENTICATION = "authentication"  # 认证错误
    AUTHORIZATION = "authorization"    # 授权错误
    PERMISSION = "permission"      # 权限错误

    # 数据相关错误
    VALIDATION = "validation"      # 数据验证错误

    # 资源相关错误
    RESOURCE = "resource"          # 资源错误

    # 配置相关错误
    CONFIGURATION = "configuration"  # 配置错误

    # 服务相关错误
    EXTERNAL = "external"          # 外部服务错误
    INTERNAL = "internal"          # 内部错误
    SYSTEM = "system"              # 系统错误

    # 其他
    UNKNOWN = "unknown"            # 未知错误

class ErrorSeverity(str, Enum):
    """错误严重程度枚举"""
    INFO = "info"          # 信息级别，不影响任务执行
    WARNING = "warning"    # 警告级别，可能影响部分功能
    ERROR = "error"        # 错误级别，影响任务正常执行
    CRITICAL = "critical"  # 严重错误，导致任务失败

def categorize_error(error: Exception) -> ErrorCategory:
    """根据异常类型分类错误"""
    error_type = type(error).__name__
    error_msg = str(error).lower()
    error_module = error.__class__.__module__

    # 数据库相关错误
    if any(name in error_type for name in ["Database", "DB", "SQL", "Postgres", "MySQL", "Oracle"]):
        # 查询错误
        if any(name in error_type for name in ["Query", "SQL", "Syntax"]) or \
           any(term in error_msg for term in ["query", "sql", "syntax", "statement"]):
            return ErrorCategory.QUERY
        # 数据比对错误
        elif "Comparison" in error_type or "compare" in error_msg:
            return ErrorCategory.COMPARISON
        # 一般数据库错误
        else:
            return ErrorCategory.DATABASE

    # 网络相关错误
    if any(name in error_type for name in ["HTTP", "Socket", "Network", "Connection"]):
        # 连接错误
        if any(name in error_type for name in ["Connection", "Socket"]) or \
           any(term in error_msg for term in ["connection", "connect", "socket", "refused"]):
            return ErrorCategory.CONNECTION
        # 超时错误
        elif "Timeout" in error_type or "timeout" in error_msg or "timed out" in error_msg:
            return ErrorCategory.TIMEOUT
        # 一般网络错误
        else:
            return ErrorCategory.NETWORK

    # 认证授权相关错误
    if any(name in error_type for name in ["Auth", "Login", "Credential", "Permission", "Access", "Forbidden"]):
        # 认证错误
        if any(name in error_type for name in ["Auth", "Login", "Credential"]) or \
           any(term in error_msg for term in ["auth", "login", "password", "credential"]):
            return ErrorCategory.AUTHENTICATION
        # 授权错误
        elif any(name in error_type for name in ["Authorization"]) or \
             any(term in error_msg for term in ["authorization", "authorize"]):
            return ErrorCategory.AUTHORIZATION
        # 权限错误
        elif any(name in error_type for name in ["Permission", "Access", "Forbidden"]) or \
             any(term in error_msg for term in ["permission", "access", "forbidden", "denied"]):
            return ErrorCategory.PERMISSION
        # 默认为认证错误
        else:
            return ErrorCategory.AUTHENTICATION

    # 数据验证错误
    if any(name in error_type for name in ["Validation", "Schema", "Type", "Format"]) or \
       any(term in error_msg for term in ["validation", "schema", "type", "format", "invalid"]):
        return ErrorCategory.VALIDATION

    # 资源错误
    if any(name in error_type for name in ["Resource", "Memory", "Disk", "IO", "CPU"]) or \
       any(term in error_msg for term in ["resource", "memory", "disk", "space", "io", "full", "capacity"]):
        return ErrorCategory.RESOURCE

    # 配置错误
    if any(name in error_type for name in ["Config", "Setting", "Environment"]) or \
       any(term in error_msg for term in ["config", "setting", "environment", "configuration"]):
        return ErrorCategory.CONFIGURATION

    # 外部服务错误
    if any(ext_module in error_module for ext_module in ["requests", "httpx", "aiohttp", "urllib"]):
        return ErrorCategory.EXTERNAL

    # 内部错误
    if error_module.startswith("app.") or error_module.startswith("fastapi"):
        return ErrorCategory.INTERNAL

    # 系统错误
    if any(name in error_type for name in ["System", "OS", "Environment", "Fatal"]) or \
       any(term in error_msg for term in ["system", "os", "environment", "fatal", "critical"]):
        return ErrorCategory.SYSTEM

    # 超时错误（如果前面没有捕获到）
    if "Timeout" in error_type or "timeout" in error_msg or "timed out" in error_msg:
        return ErrorCategory.TIMEOUT

    # 默认为未知错误
    return ErrorCategory.UNKNOWN

def determine_severity(error: Exception, category: Optional[ErrorCategory] = None) -> ErrorSeverity:
    """确定错误的严重程度"""
    if category is None:
        category = categorize_error(error)

    error_msg = str(error).lower()
    error_type = type(error).__name__

    # 检查错误消息中是否包含表明严重程度的关键词
    if any(term in error_msg for term in ["fatal", "critical", "严重", "致命"]):
        return ErrorSeverity.CRITICAL

    # 检查错误类型是否表明严重程度
    if any(term in error_type for term in ["Fatal", "Critical"]):
        return ErrorSeverity.CRITICAL

    # 根据错误类别确定默认严重程度
    default_severities = {
        ErrorCategory.CONNECTION: ErrorSeverity.ERROR,
        ErrorCategory.AUTHENTICATION: ErrorSeverity.ERROR,
        ErrorCategory.PERMISSION: ErrorSeverity.ERROR,
        ErrorCategory.VALIDATION: ErrorSeverity.WARNING,
        ErrorCategory.QUERY: ErrorSeverity.ERROR,
        ErrorCategory.COMPARISON: ErrorSeverity.WARNING,
        ErrorCategory.RESOURCE: ErrorSeverity.CRITICAL,  # 资源错误通常是致命的
        ErrorCategory.TIMEOUT: ErrorSeverity.ERROR,      # 提升超时错误的严重程度
        ErrorCategory.SYSTEM: ErrorSeverity.CRITICAL,
        ErrorCategory.UNKNOWN: ErrorSeverity.ERROR
    }

    return default_severities.get(category, ErrorSeverity.ERROR)

def is_fatal_error(error: Exception, severity: Optional[ErrorSeverity] = None) -> bool:
    """
    判断错误是否为致命错误，需要立即停止任务

    Args:
        error: 异常对象
        severity: 错误严重程度，如果为None则自动判断

    Returns:
        是否为致命错误
    """
    if severity is None:
        category = categorize_error(error)
        severity = determine_severity(error, category)

    # CRITICAL级别的错误都是致命错误
    if severity == ErrorSeverity.CRITICAL:
        return True

    error_msg = str(error).lower()
    error_type = type(error).__name__

    # 检查错误消息中是否包含表明致命错误的关键词
    fatal_keywords = [
        "fatal", "critical", "致命", "严重",
        "cannot continue", "无法继续",
        "database is down", "数据库已关闭",
        "connection lost", "连接丢失",
        "out of memory", "内存不足",
        "disk full", "磁盘已满",
        # SQL错误关键词
        "sql0206n", "sql0104n", "sql0901n", "sql0902n", "sql0911n",
        "sqlstate=42703", "sqlstate=42601", "sqlstate=08001", "sqlstate=08003", "sqlstate=40003",
        "not valid in the context", "syntax error", "table or view does not exist",
        "column does not exist", "invalid identifier", "invalid column name"
    ]

    if any(keyword in error_msg for keyword in fatal_keywords):
        return True

    # 特定类型的错误通常是致命的
    fatal_error_types = [
        "MemoryError", "SystemError", "SystemExit", "KeyboardInterrupt",
        "OutOfMemoryError", "DatabaseError", "ConnectionAbortedError",
        "ConnectionRefusedError", "ConnectionResetError",
        # SQL错误类型
        "ProgrammingError", "OperationalError", "IntegrityError", "DataError",
        "NotSupportedError", "InternalError"
    ]

    if any(fatal_type in error_type for fatal_type in fatal_error_types):
        return True

    # 检查SQL错误代码
    sql_error_codes = ["-206", "-104", "-901", "-902", "-911"]
    for code in sql_error_codes:
        if code in error_msg:
            return True

    # 默认不是致命错误
    return False

class TableNotFoundError(Exception):
    """表不存在错误"""
    def __init__(self, message: str, table_name: str = None):
        super().__init__(message)
        self.table_name = table_name
        self.is_fatal = True

class SQLSyntaxError(Exception):
    """SQL语法错误"""
    def __init__(self, message: str, sql: str = None):
        super().__init__(message)
        self.sql = sql
        self.is_fatal = True

class DatabaseValidationError(Exception):
    """数据库验证错误"""
    def __init__(self, message: str, validation_type: str = None):
        super().__init__(message)
        self.validation_type = validation_type
        self.is_fatal = True
