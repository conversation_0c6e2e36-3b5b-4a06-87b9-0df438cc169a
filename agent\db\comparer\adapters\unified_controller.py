#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一比对控制器
协调三种比对模式的执行，提供统一的入口点
"""

import time
import logging
from typing import Dict, Any, Optional, List

from .unified_models import (
    UnifiedResult, UnifiedProgress, UnifiedConfig, ComparisonMode,
    ProgressCallback, create_unified_error
)
from .direct_adapter import DirectComparisonAdapter
from .api_adapter import APIComparisonAdapter
from .local_adapter import LocalComparisonAdapter, LocalServiceCompatibilityChecker


class UnifiedComparisonController:
    """统一比对控制器"""

    def __init__(self, app_instance, config: Optional[UnifiedConfig] = None):
        self.app = app_instance
        self.config = config or UnifiedConfig.from_env()
        self.logger = logging.getLogger(__name__)

        # 初始化适配器
        self.adapters = {}
        self._initialize_adapters()

        # 性能监控
        self.performance_monitor = None
        if self.config.enable_performance_monitoring:
            from .base_adapter import PerformanceMonitor
            self.performance_monitor = PerformanceMonitor()

    def _initialize_adapters(self):
        """初始化适配器"""
        try:
            # 内置比对适配器（总是可用）
            self.adapters[ComparisonMode.DIRECT] = DirectComparisonAdapter(self.app, self.config)

            # API比对适配器
            try:
                self.adapters[ComparisonMode.API] = APIComparisonAdapter(self.app, self.config)
            except Exception as e:
                self.logger.warning(f"API适配器初始化失败: {str(e)}")

            # 本地函数比对适配器
            try:
                # 检查兼容性
                compatibility = LocalServiceCompatibilityChecker.check_compatibility()
                if compatibility['compatible']:
                    self.adapters[ComparisonMode.LOCAL] = LocalComparisonAdapter(self.app, self.config)
                else:
                    self.logger.warning(f"本地服务不兼容: {compatibility['errors']}")
            except Exception as e:
                self.logger.warning(f"本地适配器初始化失败: {str(e)}")

            self.logger.info(f"已初始化 {len(self.adapters)} 个适配器: {list(self.adapters.keys())}")

        except Exception as e:
            self.logger.error(f"适配器初始化失败: {str(e)}")
            # 确保至少有内置适配器可用
            if ComparisonMode.DIRECT not in self.adapters:
                self.adapters[ComparisonMode.DIRECT] = DirectComparisonAdapter(self.app, self.config)

    async def execute_comparison(self, config: Dict[str, Any],
                               progress_callback: Optional[ProgressCallback] = None,
                               preferred_mode: Optional[ComparisonMode] = None) -> UnifiedResult:
        """执行统一比对"""
        start_time = time.time()

        # 开始性能监控
        if self.performance_monitor:
            self.performance_monitor.start_timer('total_execution')

        try:
            # 确定比对模式
            mode = self._determine_comparison_mode(preferred_mode)
            self.logger.info(f"选择比对模式: {mode.value}")

            # 获取适配器
            adapter = self.adapters.get(mode)
            if not adapter:
                raise Exception(f"适配器 {mode.value} 不可用")

            # 验证配置
            if not adapter.validate_config(config):
                raise Exception(f"{mode.value}模式配置验证失败")

            # 创建进度回调包装器
            wrapped_callback = self._create_progress_callback_wrapper(progress_callback, mode)

            # 执行比对
            result = await adapter._safe_execute(config, wrapped_callback)

            # 记录性能指标
            if self.performance_monitor:
                execution_time = self.performance_monitor.end_timer('total_execution')
                self.performance_monitor.record_metric('selected_mode', mode.value)
                self.performance_monitor.record_metric('success', result.success)
                self.performance_monitor.record_metric('differences_count', len(result.differences))

            # 添加控制器元数据
            result.metadata.update({
                'controller_version': '1.0.0',
                'selected_mode': mode.value,
                'available_modes': [m.value for m in self.adapters.keys()],
                'performance_metrics': self.performance_monitor.get_metrics() if self.performance_monitor else {}
            })

            self.logger.info(
                f"比对完成: 模式={mode.value}, 成功={result.success}, "
                f"差异={len(result.differences)}条, 耗时={result.execution_time:.2f}秒"
            )

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"统一比对执行失败: {str(e)}")

            # 尝试回退到其他模式
            if self.config.fallback_to_legacy:
                return await self._try_fallback_execution(config, progress_callback, e)

            # 创建错误结果
            return self._create_error_result(e, execution_time)

    def _determine_comparison_mode(self, preferred_mode: Optional[ComparisonMode] = None) -> ComparisonMode:
        """确定比对模式"""
        # 如果指定了首选模式且可用，使用首选模式
        if preferred_mode and preferred_mode in self.adapters:
            adapter = self.adapters[preferred_mode]
            # 简单验证模式是否真正可用
            if self._is_mode_truly_available(preferred_mode, adapter):
                return preferred_mode

        # 按优先级选择可用模式
        for mode in self.config.mode_priority:
            if mode in self.adapters:
                adapter = self.adapters[mode]
                if self._is_mode_truly_available(mode, adapter):
                    return mode

        # 默认使用内置模式
        return ComparisonMode.DIRECT

    def _is_mode_truly_available(self, mode: ComparisonMode, adapter) -> bool:
        """检查模式是否真正可用"""
        try:
            if mode == ComparisonMode.API:
                return (hasattr(self.app, 'api_available') and self.app.api_available and
                       hasattr(self.app, 'api_client') and self.app.api_client)
            elif mode == ComparisonMode.LOCAL:
                return (hasattr(self.app, 'comparison_service') and
                       self.app.comparison_service is not None and
                       hasattr(self.app, 'local_mode') and
                       getattr(self.app, 'local_mode', False))
            elif mode == ComparisonMode.DIRECT:
                return True  # 内置模式总是可用
        except Exception:
            return False

        return False

    def _create_progress_callback_wrapper(self, original_callback: Optional[ProgressCallback],
                                        mode: ComparisonMode) -> ProgressCallback:
        """创建进度回调包装器"""
        def wrapped_callback(progress: UnifiedProgress):
            # 添加模式信息
            progress.metadata = getattr(progress, 'metadata', {})
            progress.metadata['mode'] = mode.value

            # 调用原始回调
            if original_callback:
                original_callback(progress)

            # 记录性能指标
            if self.performance_monitor:
                self.performance_monitor.record_metric('last_progress', progress.percentage)

        return wrapped_callback

    async def _try_fallback_execution(self, config: Dict[str, Any],
                                    progress_callback: Optional[ProgressCallback],
                                    original_error: Exception) -> UnifiedResult:
        """尝试回退执行"""
        self.logger.warning(f"尝试回退执行，原始错误: {str(original_error)}")

        # 按回退优先级尝试其他模式
        fallback_modes = [ComparisonMode.DIRECT, ComparisonMode.LOCAL, ComparisonMode.API]

        for mode in fallback_modes:
            if mode in self.adapters:
                try:
                    adapter = self.adapters[mode]
                    if adapter.validate_config(config):
                        self.logger.info(f"回退到 {mode.value} 模式")

                        wrapped_callback = self._create_progress_callback_wrapper(progress_callback, mode)
                        result = await adapter._safe_execute(config, wrapped_callback)

                        # 添加回退信息
                        result.metadata.update({
                            'fallback_mode': True,
                            'original_error': str(original_error),
                            'fallback_to': mode.value
                        })

                        return result

                except Exception as fallback_error:
                    self.logger.warning(f"回退到 {mode.value} 模式失败: {str(fallback_error)}")
                    continue

        # 所有回退都失败，返回原始错误
        return self._create_error_result(original_error, 0)

    def _create_error_result(self, error: Exception, execution_time: float) -> UnifiedResult:
        """创建错误结果"""
        from .unified_models import UnifiedSummary

        return UnifiedResult(
            success=False,
            mode="controller",
            differences=[],
            summary=UnifiedSummary(
                total_tables=0,
                completed_tables=0,
                total_differences=0,
                execution_time=execution_time
            ),
            execution_time=execution_time,
            error=str(error),
            metadata={
                'error_type': type(error).__name__,
                'controller_error': True,
                'available_adapters': list(self.adapters.keys())
            }
        )

    def get_available_modes(self) -> List[ComparisonMode]:
        """获取可用的比对模式"""
        available_modes = []
        for mode, adapter in self.adapters.items():
            if self._is_mode_truly_available(mode, adapter):
                available_modes.append(mode)
        return available_modes

    def get_mode_status(self) -> Dict[str, Dict[str, Any]]:
        """获取各模式状态"""
        status = {}
        for mode in ComparisonMode:
            mode_status = {
                'available': mode in self.adapters,
                'truly_available': False,
                'error': None
            }

            if mode in self.adapters:
                try:
                    adapter = self.adapters[mode]
                    mode_status['truly_available'] = self._is_mode_truly_available(mode, adapter)
                except Exception as e:
                    mode_status['error'] = str(e)

            status[mode.value] = mode_status

        return status

    def update_config(self, new_config: UnifiedConfig):
        """更新配置"""
        self.config = new_config

        # 重新初始化适配器
        for adapter in self.adapters.values():
            adapter.config = new_config

        self.logger.info("配置已更新")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if self.performance_monitor:
            return self.performance_monitor.get_metrics()
        return {}

    def reset_performance_metrics(self):
        """重置性能指标"""
        if self.performance_monitor:
            self.performance_monitor.reset()
