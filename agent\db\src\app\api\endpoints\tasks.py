"""
任务管理API
提供任务创建、查询、操作等功能。
"""
import os
import sys
import asyncio
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, os.path.dirname(PROJECT_DIR))
from typing import List, Optional, Dict
from fastapi import Query, Path, WebSocket, Response, status, Depends
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.models.task import TaskStatusResponse, TaskResultResponse
from app.models.task import CompareTaskRequest, TaskStatus, DiffDetails, AlgorithmType, TaskResultFormat, RecordDiff
from app.models.response import TaskResponse, ErrorResponse
from app.core.task_manager import TaskManager
from app.core.dependencies import get_task_manager
from app.models.logger import logger

# 创建路由器
router = APIRouter()

@router.post("/compare", response_model=TaskResponse, summary="创建数据比对任务")
async def create_comparison_task(
    request: CompareTaskRequest,
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    创建一个新的数据比对任务

    Returns:
        TaskResponse: 统一格式的任务响应，包含任务ID和状态信息
    """

    try:
        # 添加任务到任务管理器，获取返回的任务创建响应
        task_response = await task_manager.create_task(request)

        # 获取任务ID
        task_id = task_response.task_id

        # 任务管理器已经在create_task中启动了真正的比对引擎
        # 这里不需要额外的后台任务，避免重复执行

        # 返回统一格式的任务响应，保持向后兼容
        return TaskResponse(
            success=True,
            message="任务创建成功",
            code=200,
            data={
                "task_id": task_response.task_id,
                "status": task_response.status,
                "created_at": task_response.created_at.isoformat(),
                "message": task_response.message,
                # 保持向后兼容的字段
                "legacy_response": task_response.model_dump()
            },
            task_id=task_response.task_id,
            status=task_response.status,
            created_at=task_response.created_at.isoformat()
        )

    except Exception as e:
        logger.exception(f"创建比对任务失败: {str(e)}")
        return ErrorResponse(
            success=False,
            message=f"创建比对任务失败: {str(e)}",
            code=500,
            error_code="TASK_CREATION_FAILED",
            error_details={
                "exception_type": type(e).__name__,
                "exception_message": str(e)
            }
        )

@router.get("/", response_model=List[TaskStatusResponse], summary="获取所有任务")
async def list_tasks(
    status: Optional[TaskStatus] = None,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """获取任务列表，可选按状态筛选"""
    tasks = await task_manager.list_tasks(status, limit, offset)
    return tasks

@router.get("/{task_id}/status", response_model=TaskStatusResponse, summary="获取任务状态")
async def get_task_status(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """获取指定任务的状态信息"""
    try:
        task_status = await task_manager.get_task_status(task_id)
        if not task_status:
            return ErrorResponse(
                success=False,
                message=f"任务 {task_id} 不存在",
                code=404,
                error_code="TASK_NOT_FOUND",
                error_details={
                    "task_id": task_id
                }
            )
        return task_status
    except Exception as e:
        logger.exception(f"获取任务状态失败: task_id={task_id}, error={str(e)}")
        return ErrorResponse(
            success=False,
            message=f"获取任务状态失败: {str(e)}",
            code=500,
            error_code="GET_TASK_STATUS_FAILED",
            error_details={
                "task_id": task_id,
                "exception_type": type(e).__name__,
                "exception_message": str(e)
            }
        )

@router.get("/{task_id}/result", response_model=TaskResultResponse, summary="获取任务结果")
async def get_task_result(
    task_id: str = Path(..., description="任务ID"),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    include_details: bool = Query(True, description="是否包含差异详情"),
    format: Optional[TaskResultFormat] = Query(None, description="响应结果格式，不指定时使用默认JSON格式"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    获取指定任务的详细结果

    - **task_id**: 任务的唯一标识符
    - **page**: 差异数据的页码
    - **page_size**: 每页显示的记录数
    - **include_details**: 是否包含差异详情，设为false可以减少响应大小
    - **format**: 导出格式，支持json、csv、excel和html，用于数据导出
    """
    # 获取任务状态
    task_status = await task_manager.get_task_status(task_id)
    if not task_status:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 不存在",
                "error": {
                    "type": "NotFound",
                    "task_id": task_id
                }
            }
        )

    # 检查任务是否已完成
    if task_status.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"任务 {task_id} 尚未完成，当前状态: {task_status.status}",
                "error": {
                    "type": "InvalidTaskStatus",
                    "current_status": str(task_status.status)
                }
            }
        )

    # 获取任务结果
    result = await task_manager.get_task_result(task_id)
    if not result:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 的结果不存在",
                "error": {
                    "type": "ResultNotFound",
                    "task_id": task_id
                }
            }
        )

    # TODO: 导出功能需要重新实现，暂时跳过
    if format:
        logger.warning(f"导出功能暂未实现，返回基本结果格式")
        # 导出功能需要重新实现

    # 获取差异详情
    if include_details:
        try:
            # 从任务管理器获取差异详情
            diff_details = await task_manager.get_task_diff_details(task_id, page, page_size)
            if diff_details:
                result.details = diff_details
                logger.info(f"已获取任务 {task_id} 的差异详情: 第{page}页，共{diff_details.total_items}条记录")
            else:
                # 如果没有差异详情，返回空结构
                result.details = DiffDetails(
                    page=page,
                    page_size=page_size,
                    total_pages=1,
                    total_items=0,
                    items=[]
                )
                logger.info(f"任务 {task_id} 没有差异详情数据")
        except Exception as e:
            logger.exception(f"获取差异详情失败: {str(e)}")
            # 发生错误时返回空结构，避免整个请求失败
            result.details = DiffDetails(
                page=page,
                page_size=page_size,
                total_pages=1,
                total_items=0,
                items=[]
            )

    return result

@router.delete("/{task_id}", status_code=204, summary="取消或删除任务")
async def delete_task(
    task_id: str = Path(..., description="任务ID"),
    force: bool = Query(False, description="是否强制删除"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    取消正在运行的任务或删除已完成的任务

    - **task_id**: 任务的唯一标识符
    - **force**: 是否强制删除，即使任务正在运行
    """
    task_status = await task_manager.get_task_status(task_id)
    if not task_status:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 不存在",
                "error": {
                    "type": "NotFound",
                    "task_id": task_id
                }
            }
        )

    # 如果任务正在运行且不是强制删除，则取消任务
    if task_status.status == TaskStatus.RUNNING and not force:
        success = await task_manager.cancel_task(task_id)
        if not success:
            # 不使用raise，改为返回JSONResponse
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": "无法取消任务",
                    "error": {
                        "type": "CancelFailed",
                        "task_id": task_id
                    }
                }
            )
    else:
        # 否则直接删除任务
        success = await task_manager.delete_task(task_id)
        if not success:
            # 不使用raise，改为返回JSONResponse
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": "无法删除任务",
                    "error": {
                        "type": "DeleteFailed",
                        "task_id": task_id
                    }
                }
            )

    return Response(status_code=204)  # 204状态码不返回内容

@router.post("/{task_id}/pause", response_model=TaskStatusResponse, summary="暂停任务")
async def pause_task(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    暂停正在运行的任务

    - **task_id**: 任务的唯一标识符
    """
    success = await task_manager.pause_task(task_id)
    if not success:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": "无法暂停任务或任务不在运行状态",
                "error": {
                    "type": "PauseFailed",
                    "task_id": task_id
                }
            }
        )

    return await task_manager.get_task_status(task_id)

@router.post("/{task_id}/resume", response_model=TaskStatusResponse, summary="恢复任务")
async def resume_task(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    恢复已暂停的任务

    - **task_id**: 任务的唯一标识符
    """
    # 检查任务状态
    task_status = await task_manager.get_task_status(task_id)
    if not task_status:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 不存在",
                "error": {
                    "type": "NotFound",
                    "task_id": task_id
                }
            }
        )

    if task_status.status != TaskStatus.PAUSED:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"只有暂停状态的任务可以恢复，当前状态: {task_status.status}",
                "error": {
                    "type": "InvalidTaskStatus",
                    "current_status": str(task_status.status),
                    "required_status": str(TaskStatus.PAUSED)
                }
            }
        )

    # 获取任务请求信息
    request = await task_manager.get_task_request(task_id)
    if not request:
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": "无法获取任务请求信息",
                "error": {
                    "type": "RequestNotFound",
                    "task_id": task_id
                }
            }
        )

    try:
        # 更新任务状态为运行中
        await task_manager.update_task_status(task_id, TaskStatus.RUNNING)

        # 恢复任务：重新启动真正的比对引擎
        # 使用任务管理器重新处理任务，而不是模拟的比对服务
        asyncio.create_task(task_manager._process_task(task_id, request))

        logger.info(f"已提交任务恢复请求: {task_id}")
        return await task_manager.get_task_status(task_id)
    except Exception as e:
        # 如果恢复失败，恢复为暂停状态
        await task_manager.update_task_status(task_id, TaskStatus.PAUSED)
        logger.exception(f"恢复任务失败: {task_id}, 错误: {str(e)}")
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"恢复任务失败: {str(e)}",
                "error": {
                    "type": type(e).__name__,
                    "message": str(e),
                    "task_id": task_id
                }
            }
        )

@router.websocket("/ws/{task_id}/status")
async def task_status_websocket(
    websocket: WebSocket,
    task_id: str,
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    通过WebSocket获取任务实时状态更新

    建立WebSocket连接后，服务器会在任务状态变化时推送更新。
    客户端可以发送"ping"消息以保持连接活跃。
    """
    logger.info(f"WebSocket连接请求: 任务ID={task_id}")

    try:
        # 处理WebSocket连接
        await task_manager.handle_ws_connection(websocket, task_id)
    except Exception as e:
        logger.exception(f"WebSocket连接处理异常: {task_id}")
        if websocket.client_state.CONNECTED:
            await websocket.close(code=1011, reason=f"内部服务器错误: {str(e)}")

@router.get("/algorithms", response_model=Dict[str, str], summary="获取支持的比对算法")
async def get_algorithms():
    """
    获取系统支持的数据比对算法列表

    返回算法代码和描述的映射
    """
    try:
        return {
            AlgorithmType.PRIMARY_KEY: "默认主键比对（适用于一般场景）",
            AlgorithmType.HASH_BASED: "基于哈希的比对（适用于大规模数据）",
            AlgorithmType.TWO_PHASE: "两阶段比对（适用于差异较大的数据集）"
        }
    except Exception as e:
        logger.exception(f"获取算法列表时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取算法列表失败: {str(e)}",
                "error": {
                    "type": type(e).__name__,
                    "message": str(e)
                }
            }
        )

@router.get("/{task_id}/export", summary="导出任务结果")
async def export_task_result(
    task_id: str = Path(..., description="任务ID"),
    format: TaskResultFormat = Query(TaskResultFormat.JSON, description="导出格式"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    导出指定任务的结果为指定格式的文件

    - **task_id**: 任务的唯一标识符
    - **format**: 导出格式，支持:
      - json: JSON格式 (默认)
      - csv: CSV格式
      - excel: Excel格式
      - html: HTML格式

    返回文件下载响应
    """
    # 获取任务状态
    task_status = await task_manager.get_task_status(task_id)
    if not task_status:
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 不存在",
                "error": {
                    "type": "NotFound",
                    "task_id": task_id
                }
            }
        )

    # 检查任务是否已完成
    if task_status.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"任务 {task_id} 尚未完成，当前状态: {task_status.status}",
                "error": {
                    "type": "InvalidTaskStatus",
                    "current_status": str(task_status.status)
                }
            }
        )

    # TODO: 导出功能需要重新实现
    return JSONResponse(
        status_code=501,
        content={
            "success": False,
            "message": "导出功能暂未实现",
            "error": {
                "type": "NotImplemented",
                "task_id": task_id,
                "format": str(format)
            }
        }
    )

@router.get("/{task_id}/sample", response_model=List[RecordDiff], summary="获取差异样本")
async def get_diff_samples(
    task_id: str = Path(..., description="任务ID"),
    diff_type: Optional[str] = Query(None, description="差异类型筛选: value_diff, source_only, target_only"),
    limit: int = Query(10, ge=1, le=100, description="返回的最大样本数量"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    获取指定任务的差异记录样本，用于快速查看差异

    - **task_id**: 任务的唯一标识符
    - **diff_type**: 可选的差异类型过滤
    - **limit**: 返回的样本数量，默认10条

    返回列表中的每一项都是一条差异记录
    """
    # 获取任务状态
    task_status = await task_manager.get_task_status(task_id)
    if not task_status:
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"任务 {task_id} 不存在",
                "error": {
                    "type": "NotFound",
                    "task_id": task_id
                }
            }
        )

    # 检查任务是否已完成
    if task_status.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"任务 {task_id} 尚未完成，当前状态: {task_status.status}",
                "error": {
                    "type": "InvalidTaskStatus",
                    "current_status": str(task_status.status)
                }
            }
        )

    try:
        # 获取差异样本
        diff_details = await task_manager.get_task_diff_details(task_id, 1, limit)
        if diff_details and diff_details.items:
            # 根据差异类型过滤
            filtered_items = diff_details.items
            if diff_type:
                filtered_items = [item for item in diff_details.items if item.diff_type == diff_type]

            # 限制返回数量
            return filtered_items[:limit]
        else:
            logger.info(f"任务 {task_id} 没有差异样本数据")
            return []
    except Exception as e:
        logger.exception(f"获取差异样本时发生错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取差异样本失败: {str(e)}",
                "error": {
                    "type": type(e).__name__,
                    "message": str(e)
                }
            }
        )

@router.get("/{task_id}/tables", summary="获取任务表级进度")
async def get_task_tables_progress(
    task_id: str = Path(..., description="任务ID"),
    task_manager: TaskManager = Depends(get_task_manager)
):
    """
    获取指定任务的表级进度信息

    - **task_id**: 任务的唯一标识符

    返回各表的进度信息，包括每个表的记录总数、已处理数量、匹配数量、差异数量等
    """
    try:
        # 先检查任务是否存在
        task_status = await task_manager.get_task_status(task_id)
        if not task_status:
            # 不使用raise，改为返回JSONResponse
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "message": f"任务 {task_id} 不存在",
                    "error": {"type": "NotFound", "task_id": task_id}
                }
            )

        # 检查任务是否失败，如果失败应该返回错误状态而不是尝试获取表级进度
        if task_status.status == TaskStatus.FAILED:
            # 获取任务结果以提取错误信息
            task_result = await task_manager.get_task_result(task_id)
            error_info = {}
            error_message = "任务执行失败"

            if task_result and hasattr(task_result, 'summary') and task_result.summary:
                summary = task_result.summary
                if hasattr(summary, 'error') and summary.error:
                    error_info = summary.error
                    error_message = error_info.get('message', error_message)

            # 返回包含错误信息的响应
            return {
                "success": False,
                "task_id": task_id,
                "status": TaskStatus.FAILED,
                "tables": [],
                "error": {
                    "type": error_info.get('type', 'TaskExecutionError'),
                    "message": error_message,
                    "details": error_info.get('details', {})
                }
            }

        # 直接使用TaskManager的get_task_tables方法获取表级进度
        tables_data = await task_manager.get_task_tables(task_id)
        if not tables_data:
            # 这里任务存在但无法获取表级数据，这通常表示内部错误
            logger.error(f"无法获取任务表级进度: task_id={task_id}")
            return {
                "success": False,
                "task_id": task_id,
                "status": task_status.status,
                "tables": [],
                "message": "无法获取表级进度数据"
            }

        return tables_data
    except Exception as e:
        # 捕获并记录所有其他异常
        logger.exception(f"获取表级进度时发生错误: task_id={task_id}, error={str(e)}")
        # 不使用raise，改为返回JSONResponse
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": f"获取表级进度失败: {str(e)}",
                "task_id": task_id,
                "error": {
                    "type": type(e).__name__,
                    "message": str(e)
                }
            }
        )