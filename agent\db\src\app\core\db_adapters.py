"""
数据库适配器模块

为各类数据库实现统一的访问接口，允许异步代码调用同步数据库驱动。
"""
import asyncio
import traceback
import re
from typing import Optional, List, Dict, Any, Tuple, Union

from app.models.logger import logger
# 避免循环导入，在需要时动态导入
# from app.core.error_utils import is_fatal_error
# from app.core.error_reporter import report_system_error

class _ErrorSignal:
    """用于在同步方法中向异步调用者传递错误信号的内部类"""
    def __init__(self, error_type: str, message: str, query: str):
        self.error_type = error_type
        self.message = message
        self.query = query

class DB2ConnectionWrapper:
    """IBM DB2数据库连接的异步包装器"""

    def __init__(self, connection):
        """初始化DB2连接包装器"""
        self.connection = connection
        self._acquire_lock = asyncio.Lock()

    async def acquire(self):
        """获取数据库连接"""
        # 获取锁，确保连接操作的互斥性
        await self._acquire_lock.acquire()
        return self

    def release(self):
        """释放连接（解锁）"""
        if self._acquire_lock.locked():
            self._acquire_lock.release()

    async def __aenter__(self):
        """异步上下文管理器的进入方法，返回自身作为连接"""
        # 直接返回self，不再调用acquire()，避免协程未等待问题
        # 因为在comparison.py中使用了async with source_engine.acquire() as conn:
        # 这里的acquire()已经是一个协程，会在async with中被正确等待
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器的退出方法，释放锁"""
        self.release()
        # 始终返回False，让异常向上传播，不在这里处理
        return False

    async def fetch(self, query: str, *params) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
        """执行查询并获取所有结果"""
        try:
            # 在线程池中执行同步查询
            result = await asyncio.to_thread(self._fetch_sync, query, *params)
            return result
        except Exception as e:
            # 提取SQL错误代码和状态（如果存在）
            error_code = None
            sql_state = None
            error_msg = str(e).lower()

            # 尝试从错误消息中提取SQL错误代码
            if "sql" in error_msg and "n" in error_msg:
                import re
                code_match = re.search(r'sql(\d+)n', error_msg)
                if code_match:
                    error_code = code_match.group(1)

            # 尝试从错误消息中提取SQLSTATE
            if "sqlstate=" in error_msg:
                import re
                state_match = re.search(r'sqlstate=(\w+)', error_msg)
                if state_match:
                    sql_state = state_match.group(1)

            # 构建详细的错误消息
            detailed_error = f"DB2查询执行失败: {str(e)}"
            if error_code:
                detailed_error += f", 错误代码: SQL{error_code}N"
            if sql_state:
                detailed_error += f", SQLSTATE: {sql_state}"

            logger.error(detailed_error)
            logger.error(f"问题查询: {query}")
            logger.error(f"查询参数: {params}")
            logger.error(traceback.format_exc())

            # 检查是否为致命错误
            from app.core.error_utils import is_fatal_error
            fatal = is_fatal_error(e)

            # 对于SQL错误，强制设置为致命错误
            if "sql" in error_msg.lower() and any(code in error_msg.lower() for code in ["0206", "0104", "0901"]):
                fatal = True
                logger.critical(f"检测到SQL语法错误，将其视为致命错误: {str(e)}")

            if fatal:
                logger.critical(f"检测到致命数据库错误: {str(e)}")
                # 异步报告系统错误，不阻塞当前函数
                from app.core.error_reporter import report_system_error
                context = {
                    "query": query,
                    "params": str(params),
                    "operation": "fetch",
                    "is_fatal": True,
                    "error_code": error_code,
                    "sql_state": sql_state
                }
                asyncio.create_task(report_system_error(e, context))

                # 尝试获取当前任务ID并通知任务管理器
                try:
                    import inspect
                    frame = inspect.currentframe()
                    while frame:
                        if 'task_id' in frame.f_locals:
                            task_id = frame.f_locals['task_id']
                            from app.core.task_manager import get_task_manager
                            asyncio.create_task(self._notify_task_manager(task_id, e, context))
                            break
                        frame = frame.f_back
                except Exception as notify_error:
                    logger.error(f"尝试通知任务管理器失败: {str(notify_error)}")

            # 返回错误信息而不是抛出异常
            return {
                "success": False,
                "error": {
                    "type": type(e).__name__,
                    "message": str(e),
                    "query": query,
                    "is_fatal": fatal,
                    "error_code": error_code,
                    "sql_state": sql_state
                }
            }

    async def _notify_task_manager(self, task_id: str, error: Exception, context: dict):
        """通知任务管理器处理致命错误"""
        try:
            from app.core.task_manager import get_task_manager
            task_manager = await get_task_manager()
            await task_manager.handle_fatal_error(task_id, error, context)
            logger.info(f"已通知任务管理器处理任务 {task_id} 的致命错误")
        except Exception as e:
            logger.error(f"通知任务管理器处理致命错误失败: {str(e)}")

    def _fetch_sync(self, query: str, *params) -> List[Dict[str, Any]]:
        """同步执行查询并获取所有结果"""
        cursor = self.connection.cursor()
        try:
            cursor.execute(query, params)
            # 安全检查cursor.description是否为None
            if cursor.description is None:
                return []
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        finally:
            cursor.close()

    async def fetchval(self, query: str, *params) -> Any:
        """执行查询并获取第一行第一列的值"""
        try:
            # 在线程池中执行同步查询
            result = await asyncio.to_thread(self._fetchval_sync, query, *params)
            if isinstance(result, _ErrorSignal):
                # 如果返回的是_ErrorSignal实例，则格式化为错误字典
                # 提取SQL错误代码和状态（如果存在）
                error_code = None
                sql_state = None
                error_msg = result.message.lower()

                # 尝试从错误消息中提取SQL错误代码
                if "sql" in error_msg and "n" in error_msg:
                    import re
                    code_match = re.search(r'sql(\d+)n', error_msg)
                    if code_match:
                        error_code = code_match.group(1)

                # 尝试从错误消息中提取SQLSTATE
                if "sqlstate=" in error_msg:
                    import re
                    state_match = re.search(r'sqlstate=(\w+)', error_msg)
                    if state_match:
                        sql_state = state_match.group(1)

                # 构建详细的错误消息
                detailed_error = f"DB2 fetchval 执行失败: {result.message}"
                if error_code:
                    detailed_error += f", 错误代码: SQL{error_code}N"
                if sql_state:
                    detailed_error += f", SQLSTATE: {sql_state}"

                logger.error(detailed_error)
                logger.error(f"问题查询: {query}")
                logger.error(f"查询参数: {params}")

                # 检查是否为致命错误
                from app.core.error_utils import is_fatal_error
                error = Exception(result.message)
                fatal = is_fatal_error(error)

                # 对于SQL错误，强制设置为致命错误
                if "sql" in error_msg and any(code in error_msg for code in ["0206", "0104", "0901"]):
                    fatal = True
                    logger.critical(f"检测到SQL语法错误，将其视为致命错误: {result.message}")

                if fatal:
                    logger.critical(f"检测到致命数据库错误: {result.message}")
                    # 异步报告系统错误，不阻塞当前函数
                    from app.core.error_reporter import report_system_error
                    context = {
                        "query": query,
                        "params": str(params),
                        "operation": "fetchval",
                        "is_fatal": True,
                        "error_code": error_code,
                        "sql_state": sql_state
                    }
                    asyncio.create_task(report_system_error(error, context))

                    # 尝试获取当前任务ID并通知任务管理器
                    try:
                        import inspect
                        frame = inspect.currentframe()
                        while frame:
                            if 'task_id' in frame.f_locals:
                                task_id = frame.f_locals['task_id']
                                from app.core.task_manager import get_task_manager
                                asyncio.create_task(self._notify_task_manager(task_id, error, context))
                                break
                            frame = frame.f_back
                    except Exception as notify_error:
                        logger.error(f"尝试通知任务管理器失败: {str(notify_error)}")

                return {
                    "success": False,
                    "error": {
                        "type": result.error_type,
                        "message": result.message,
                        "query": result.query,
                        "is_fatal": fatal,
                        "error_code": error_code,
                        "sql_state": sql_state
                    }
                }
            return result
        except Exception as e:
            # 这个except块处理asyncio.to_thread本身的错误或其他未预料的异步错误
            error_msg = f"DB2 fetchval 异步执行失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())

            # 检查是否为致命错误
            from app.core.error_utils import is_fatal_error
            fatal = is_fatal_error(e)
            if fatal:
                logger.critical(f"检测到致命数据库错误: {str(e)}")
                # 异步报告系统错误，不阻塞当前函数
                from app.core.error_reporter import report_system_error
                context = {
                    "query": query,
                    "params": str(params),
                    "operation": "fetchval",
                    "is_fatal": True
                }
                asyncio.create_task(report_system_error(e, context))

                # 尝试获取当前任务ID并通知任务管理器
                try:
                    import inspect
                    frame = inspect.currentframe()
                    while frame:
                        if 'task_id' in frame.f_locals:
                            task_id = frame.f_locals['task_id']
                            from app.core.task_manager import get_task_manager
                            asyncio.create_task(self._notify_task_manager(task_id, e, context))
                            break
                        frame = frame.f_back
                except Exception as notify_error:
                    logger.error(f"尝试通知任务管理器失败: {str(notify_error)}")

            return {
                "success": False,
                "error": {
                    "type": type(e).__name__,
                    "message": str(e),
                    "query": query,
                    "is_fatal": fatal
                }
            }

    def _fetchval_sync(self, query: str, *params) -> Any:
        """同步执行查询并获取第一行第一列的值"""
        cursor = self.connection.cursor()
        try:
            cursor.execute(query, params)
            row = cursor.fetchone()
            return row[0] if row else None
        except Exception as e:
            logger.error(f"Error executing query in _fetchval_sync. Query: {query}, Params: {params}")
            logger.error(traceback.format_exc()) # 添加堆栈跟踪日志
            return _ErrorSignal(type(e).__name__, str(e), query)  # 返回错误信号对象
        finally:
            cursor.close()

    async def execute(self, query: str, *params) -> Union[int, Dict[str, Any]]:
        """执行SQL命令并返回影响的行数"""
        try:
            # 在线程池中执行同步命令
            result = await asyncio.to_thread(self._execute_sync, query, *params)
            return result
        except Exception as e:
            # 提取SQL错误代码和状态（如果存在）
            error_code = None
            sql_state = None
            error_msg = str(e).lower()

            # 尝试从错误消息中提取SQL错误代码
            if "sql" in error_msg and "n" in error_msg:
                import re
                code_match = re.search(r'sql(\d+)n', error_msg)
                if code_match:
                    error_code = code_match.group(1)

            # 尝试从错误消息中提取SQLSTATE
            if "sqlstate=" in error_msg:
                import re
                state_match = re.search(r'sqlstate=(\w+)', error_msg)
                if state_match:
                    sql_state = state_match.group(1)

            # 构建详细的错误消息
            detailed_error = f"DB2命令执行失败: {str(e)}"
            if error_code:
                detailed_error += f", 错误代码: SQL{error_code}N"
            if sql_state:
                detailed_error += f", SQLSTATE: {sql_state}"

            logger.error(detailed_error)
            logger.error(f"问题查询: {query}")
            logger.error(f"查询参数: {params}")
            logger.error(traceback.format_exc())

            # 检查是否为致命错误
            from app.core.error_utils import is_fatal_error
            fatal = is_fatal_error(e)

            # 对于SQL错误，强制设置为致命错误
            if "sql" in error_msg and any(code in error_msg for code in ["0206", "0104", "0901"]):
                fatal = True
                logger.critical(f"检测到SQL语法错误，将其视为致命错误: {str(e)}")

            if fatal:
                logger.critical(f"检测到致命数据库错误: {str(e)}")
                # 异步报告系统错误，不阻塞当前函数
                from app.core.error_reporter import report_system_error
                context = {
                    "query": query,
                    "params": str(params),
                    "operation": "execute",
                    "is_fatal": True,
                    "error_code": error_code,
                    "sql_state": sql_state
                }
                asyncio.create_task(report_system_error(e, context))

                # 尝试获取当前任务ID并通知任务管理器
                try:
                    import inspect
                    frame = inspect.currentframe()
                    while frame:
                        if 'task_id' in frame.f_locals:
                            task_id = frame.f_locals['task_id']
                            from app.core.task_manager import get_task_manager
                            asyncio.create_task(self._notify_task_manager(task_id, e, context))
                            break
                        frame = frame.f_back
                except Exception as notify_error:
                    logger.error(f"尝试通知任务管理器失败: {str(notify_error)}")

            # 返回错误信息而不是抛出异常
            return {
                "success": False,
                "error": {
                    "type": type(e).__name__,
                    "message": str(e),
                    "query": query,
                    "is_fatal": fatal,
                    "error_code": error_code,
                    "sql_state": sql_state
                }
            }

    def _execute_sync(self, query: str, *params) -> int:
        """同步执行SQL命令并返回影响的行数"""
        cursor = self.connection.cursor()
        try:
            cursor.execute(query, params)
            return cursor.rowcount
        finally:
            cursor.close()

    async def close(self):
        """关闭连接"""
        try:
            # 在线程池中执行同步关闭
            await asyncio.to_thread(self._close_sync)
        except Exception as e:
            logger.warning(f"关闭DB2连接失败: {str(e)}")

    def _close_sync(self):
        """同步关闭连接"""
        try:
            self.connection.close()
        except Exception as e:
            logger.warning(f"关闭连接时出错: {str(e)}")
        finally:
            # 确保锁总是被释放
            self.release()