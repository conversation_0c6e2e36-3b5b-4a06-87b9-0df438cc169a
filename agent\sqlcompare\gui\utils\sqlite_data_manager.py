# gui/utils/sqlite_data_manager.py
"""
SQLite数据管理器 - 为GUI提供数据访问接口
支持实时查询比对结果数据库
v4.0架构兼容版本
"""
import sqlite3
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# v4.0架构导入
try:
    from services.sqlalchemy_service import SQLAlchemyComparisonService
    V4_AVAILABLE = True
except ImportError:
    V4_AVAILABLE = False

logger = logging.getLogger(__name__)

class SqliteDataManager:
    """SQLite数据管理器，专为GUI查询优化"""
    
    def __init__(self, db_path: str = 'diff_results.db'):
        self.db_path = db_path
        self.conn = None
        self.table_name = 'comparison_results'
        
    def connect(self) -> bool:
        """连接到数据库"""
        try:
            if not os.path.exists(self.db_path):
                logger.warning(f"数据库文件不存在: {self.db_path}")
                return False
                
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            return True
        except sqlite3.Error as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            try:
                self.conn.close()
            except Exception as e:
                logger.warning(f"关闭数据库连接时出错: {e}")
            finally:
                self.conn = None
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.conn is not None
    
    def get_available_tables(self) -> List[str]:
        """获取可用的比对表列表"""
        if not self.conn:
            return []
            
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT DISTINCT table_name FROM {self.table_name} ORDER BY table_name")
            return [row[0] for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"获取表列表失败: {e}")
            return []
    
    def get_table_statistics(self, table_name: str) -> Dict[str, int]:
        """获取表的统计信息"""
        if not self.conn:
            return {}
            
        try:
            cursor = self.conn.cursor()
            
            # 获取各状态的记录数
            stats_sql = f"""
            SELECT status, COUNT(*) as count
            FROM {self.table_name}
            WHERE table_name = ?
            GROUP BY status
            """
            cursor.execute(stats_sql, (table_name,))
            
            stats = {'total': 0}
            for row in cursor.fetchall():
                stats[row[0]] = row[1]
                stats['total'] += row[1]
                
            return stats
        except sqlite3.Error as e:
            logger.error(f"获取表统计失败: {e}")
            return {}
    
    def get_comparison_data(self, table_name: str, page: int = 1, page_size: int = 100,
                          status_filter: Optional[str] = None, search_term: Optional[str] = None) -> Tuple[List[Dict], int]:
        """
        获取比对数据的分页结果
        
        Args:
            table_name: 表名
            page: 页码（从1开始）
            page_size: 每页记录数
            status_filter: 状态过滤器
            search_term: 搜索关键词
            
        Returns:
            (数据列表, 总记录数)
        """
        if not self.conn:
            return [], 0
            
        try:
            cursor = self.conn.cursor()
            
            # 构建查询条件
            where_conditions = ["table_name = ?"]
            params = [table_name]
            
            if status_filter:
                where_conditions.append("status = ?")
                params.append(status_filter)
                
            if search_term:
                search_pattern = f"%{search_term}%"
                where_conditions.append("""
                (record_key LIKE ? OR value_a LIKE ? OR value_b LIKE ? OR field_name LIKE ?)
                """)
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总记录数
            count_sql = f"SELECT COUNT(*) FROM {self.table_name} WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total_count = cursor.fetchone()[0]
            
            # 获取分页数据
            offset = (page - 1) * page_size
            data_sql = f"""
            SELECT id, record_key, status, field_name, value_a, value_b, created_at
            FROM {self.table_name}
            WHERE {where_clause}
            ORDER BY id
            LIMIT ? OFFSET ?
            """
            params.extend([page_size, offset])
            cursor.execute(data_sql, params)
            
            # 转换为字典列表
            results = []
            for row in cursor.fetchall():
                results.append({
                    'id': row['id'],
                    'record_key': row['record_key'],
                    'status': row['status'],
                    'field_name': row['field_name'],
                    'value_a': row['value_a'],
                    'value_b': row['value_b'],
                    'created_at': row['created_at']
                })
            
            return results, total_count
            
        except sqlite3.Error as e:
            logger.error(f"查询比对数据失败: {e}")
            return [], 0
    
    def get_record_details(self, table_name: str, record_key: str) -> List[Dict]:
        """获取特定记录的详细差异信息"""
        if not self.conn:
            return []
            
        try:
            cursor = self.conn.cursor()
            detail_sql = f"""
            SELECT id, status, field_name, value_a, value_b, created_at
            FROM {self.table_name}
            WHERE table_name = ? AND record_key = ?
            ORDER BY field_name
            """
            cursor.execute(detail_sql, (table_name, record_key))
            
            details = []
            for row in cursor.fetchall():
                details.append({
                    'id': row['id'],
                    'status': row['status'],
                    'field_name': row['field_name'],
                    'value_a': row['value_a'],
                    'value_b': row['value_b'],
                    'created_at': row['created_at']
                })
            
            return details
            
        except sqlite3.Error as e:
            logger.error(f"获取记录详情失败: {e}")
            return []
    
    def export_table_data(self, table_name: str, output_path: str, status_filter: Optional[str] = None) -> bool:
        """导出表数据到CSV文件"""
        if not self.conn:
            return False
            
        try:
            import csv
            
            cursor = self.conn.cursor()
            
            # 构建查询
            where_clause = "table_name = ?"
            params = [table_name]
            
            if status_filter:
                where_clause += " AND status = ?"
                params.append(status_filter)
            
            export_sql = f"""
            SELECT record_key, status, field_name, value_a, value_b, created_at
            FROM {self.table_name}
            WHERE {where_clause}
            ORDER BY record_key, field_name
            """
            cursor.execute(export_sql, params)
            
            # 写入CSV文件
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['记录键', '状态', '字段名', '源值', '目标值', '创建时间'])
                
                for row in cursor.fetchall():
                    writer.writerow([
                        row['record_key'],
                        row['status'],
                        row['field_name'] or '',
                        row['value_a'] or '',
                        row['value_b'] or '',
                        row['created_at']
                    ])
            
            logger.info(f"数据已导出到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False
    
    def clear_table_data(self, table_name: str) -> bool:
        """清除指定表的数据"""
        if not self.conn:
            return False
            
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"DELETE FROM {self.table_name} WHERE table_name = ?", (table_name,))
            self.conn.commit()
            logger.info(f"已清除表 {table_name} 的数据")
            return True
        except sqlite3.Error as e:
            logger.error(f"清除表数据失败: {e}")
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

    # ==================== v4.0架构支持方法 ====================

    def get_results_from_v4_service(self, task_id: str, table_name: str = None,
                                   limit: int = 100, offset: int = 0) -> List[Dict]:
        """从v4.0架构服务获取比对结果"""
        if not V4_AVAILABLE:
            logger.warning("v4.0架构服务不可用，回退到SQLite查询")
            return self.get_comparison_results(table_name, limit, offset)

        try:
            # 获取SQLAlchemy服务实例
            service = SQLAlchemyComparisonService.get_instance()

            # 获取比对结果
            results = service.get_comparison_results(
                task_id=task_id,
                table_name=table_name,
                limit=limit,
                offset=offset
            )

            # 转换为字典格式
            result_list = []
            for result in results:
                result_dict = {
                    'record_key': result.record_key,
                    'status': result.status,
                    'field_name': result.field_name,
                    'source_value': result.source_value,
                    'target_value': result.target_value,
                    'table_name': result.table_name,
                    'create_time': result.create_time.isoformat() if result.create_time else None
                }
                result_list.append(result_dict)

            return result_list

        except Exception as e:
            logger.error(f"从v4.0服务获取结果失败: {e}")
            # 回退到SQLite查询
            return self.get_comparison_results(table_name, limit, offset)

    def get_task_info_from_v4_service(self, task_id: str) -> Optional[Dict]:
        """从v4.0架构服务获取任务信息"""
        if not V4_AVAILABLE:
            return None

        try:
            # 获取SQLAlchemy服务实例
            service = SQLAlchemyComparisonService.get_instance()

            # 获取任务执行信息
            execution_info = service.get_task_execution_info(task_id)
            if execution_info:
                return {
                    'task_id': execution_info.task_id,
                    'task_name': execution_info.task_name,
                    'status': execution_info.status,
                    'progress_pct': execution_info.progress_pct,
                    'current_step': execution_info.current_step,
                    'processed_records': execution_info.processed_records,
                    'total_records': execution_info.total_records,
                    'diff_records': execution_info.diff_records,
                    'source_only': execution_info.source_only,
                    'target_only': execution_info.target_only,
                    'create_time': execution_info.create_time.isoformat() if execution_info.create_time else None,
                    'exec_time': execution_info.exec_time
                }
            return None

        except Exception as e:
            logger.error(f"从v4.0服务获取任务信息失败: {e}")
            return None

    def is_v4_available(self) -> bool:
        """检查v4.0架构是否可用"""
        return V4_AVAILABLE

    def get_hybrid_results(self, task_id: str = None, table_name: str = None,
                          limit: int = 100, offset: int = 0) -> List[Dict]:
        """混合模式获取结果：优先使用v4.0服务，回退到SQLite"""
        if task_id and V4_AVAILABLE:
            # 尝试从v4.0服务获取
            results = self.get_results_from_v4_service(task_id, table_name, limit, offset)
            if results:
                return results

        # 回退到SQLite查询
        return self.get_comparison_results(table_name, limit, offset)
