"""
API版本检测和兼容性中间件

提供API版本检测、废弃警告和使用情况监控功能
"""
import os
import sys
# 获取当前脚本的目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 将项目根目录添加到Python路径中，使得backend和comparison模块可以被正确导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)
import time
import logging
from typing import Dict, Any, Optional, List
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter, Histogram, Gauge

logger = logging.getLogger(__name__)

# Prometheus监控指标
api_requests_total = Counter(
    'api_requests_total',
    'Total API requests',
    ['endpoint', 'method', 'version', 'status']
)

api_response_time = Histogram(
    'api_response_time_seconds',
    'API response time in seconds',
    ['endpoint', 'version']
)

legacy_api_usage = Counter(
    'legacy_api_usage_total',
    'Total legacy API usage',
    ['endpoint', 'client_ip', 'user_agent']
)

active_legacy_clients = Gauge(
    'active_legacy_clients',
    'Number of active legacy API clients'
)


class APIVersionMiddleware(BaseHTTPMiddleware):
    """API版本检测和兼容性中间件"""
    
    def __init__(self, app, enable_legacy: bool = True, deprecation_date: str = "2024-06-01"):
        super().__init__(app)
        self.enable_legacy = enable_legacy
        self.deprecation_date = deprecation_date
        self.legacy_clients = set()  # 跟踪Legacy API客户端
        
        # 从环境变量读取配置
        self.enable_legacy = os.getenv("ENABLE_LEGACY_API", "true").lower() == "true"
        self.deprecation_warning = os.getenv("LEGACY_API_DEPRECATION_WARNING", "true").lower() == "true"
        self.migration_guide_url = os.getenv("MIGRATION_GUIDE_URL", "https://docs.example.com/migration")
        
        logger.info(f"API版本中间件初始化 - Legacy API: {'启用' if self.enable_legacy else '禁用'}")
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并添加版本检测"""
        start_time = time.time()
        
        # 检测API版本
        api_version = self._detect_api_version(request)
        is_legacy = api_version == "legacy"
        
        # 检查Legacy API是否启用
        if is_legacy and not self.enable_legacy:
            return Response(
                content='{"error": "Legacy API已禁用，请使用新版本API /api/v1/"}',
                status_code=410,  # Gone
                media_type="application/json"
            )
        
        # 记录Legacy API使用情况
        if is_legacy:
            self._track_legacy_usage(request)
        
        # 执行请求
        response = await call_next(request)
        
        # 计算响应时间
        response_time = time.time() - start_time
        
        # 添加版本相关的响应头
        self._add_version_headers(response, api_version, is_legacy)
        
        # 记录监控指标
        self._record_metrics(request, response, api_version, response_time)
        
        # 记录Legacy API使用日志
        if is_legacy:
            self._log_legacy_usage(request, response, response_time)
        
        return response
    
    def _detect_api_version(self, request: Request) -> str:
        """检测API版本"""
        path = request.url.path
        
        # 检查路径前缀
        if path.startswith("/api/v1/"):
            return "v1"
        elif path.startswith("/api/v2/"):
            return "v2"
        elif path.startswith("/api/") and not path.startswith("/api/v"):
            return "legacy"
        
        # 检查请求头
        api_version_header = request.headers.get("X-API-Version", "").lower()
        if api_version_header in ["v1", "1.0"]:
            return "v1"
        elif api_version_header in ["legacy", "0.9"]:
            return "legacy"
        
        # 默认版本
        return "unknown"
    
    def _add_version_headers(self, response: Response, api_version: str, is_legacy: bool):
        """添加版本相关的响应头"""
        response.headers["X-API-Version"] = api_version
        response.headers["X-API-Server"] = "DB-Comparer-v2.0"
        
        if is_legacy and self.deprecation_warning:
            response.headers["X-API-Deprecated"] = "true"
            response.headers["X-API-Deprecation-Date"] = self.deprecation_date
            response.headers["X-API-Migration-Guide"] = self.migration_guide_url
            response.headers["X-API-Sunset-Warning"] = f"此API将在 {self.deprecation_date} 后停止支持"
            response.headers["X-Recommended-Version"] = "v1"
            
            # 添加废弃警告到响应体（如果是JSON）
            if response.headers.get("content-type", "").startswith("application/json"):
                response.headers["X-Legacy-Warning"] = "请尽快迁移到新版本API"
    
    def _track_legacy_usage(self, request: Request):
        """跟踪Legacy API使用情况"""
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("User-Agent", "unknown")
        
        # 记录客户端
        client_key = f"{client_ip}:{user_agent}"
        self.legacy_clients.add(client_key)
        
        # 更新活跃客户端数量
        active_legacy_clients.set(len(self.legacy_clients))
        
        # 记录使用计数
        legacy_api_usage.labels(
            endpoint=request.url.path,
            client_ip=client_ip,
            user_agent=user_agent[:50]  # 限制长度
        ).inc()
    
    def _record_metrics(self, request: Request, response: Response, api_version: str, response_time: float):
        """记录监控指标"""
        # 记录请求总数
        api_requests_total.labels(
            endpoint=request.url.path,
            method=request.method,
            version=api_version,
            status=response.status_code
        ).inc()
        
        # 记录响应时间
        api_response_time.labels(
            endpoint=request.url.path,
            version=api_version
        ).observe(response_time)
    
    def _log_legacy_usage(self, request: Request, response: Response, response_time: float):
        """记录Legacy API使用日志"""
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("User-Agent", "unknown")
        
        logger.warning(
            f"Legacy API使用 - "
            f"路径: {request.method} {request.url.path}, "
            f"客户端: {client_ip}, "
            f"User-Agent: {user_agent[:100]}, "
            f"状态码: {response.status_code}, "
            f"响应时间: {response_time:.3f}s"
        )
        
        # 如果响应时间过长，记录性能警告
        if response_time > 5.0:
            logger.warning(
                f"Legacy API性能警告 - "
                f"路径: {request.url.path}, "
                f"响应时间: {response_time:.3f}s 超过阈值"
            )


class LegacyAPIMonitor:
    """Legacy API监控工具"""
    
    def __init__(self):
        self.usage_stats = {}
        self.client_stats = {}
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """获取使用情况摘要"""
        return {
            "total_legacy_requests": sum(self.usage_stats.values()),
            "unique_endpoints": len(self.usage_stats),
            "active_clients": len(self.client_stats),
            "top_endpoints": sorted(
                self.usage_stats.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
        }
    
    def get_migration_recommendations(self) -> List[Dict[str, Any]]:
        """获取迁移建议"""
        recommendations = []
        
        # 基于使用频率的建议
        for endpoint, count in self.usage_stats.items():
            if count > 100:  # 高频使用的端点
                recommendations.append({
                    "endpoint": endpoint,
                    "usage_count": count,
                    "priority": "high",
                    "recommendation": f"优先迁移此高频使用的端点: {endpoint}"
                })
        
        return recommendations


# 全局监控实例
legacy_monitor = LegacyAPIMonitor()


def get_legacy_api_stats() -> Dict[str, Any]:
    """获取Legacy API统计信息"""
    return {
        "monitor_summary": legacy_monitor.get_usage_summary(),
        "migration_recommendations": legacy_monitor.get_migration_recommendations(),
        "deprecation_info": {
            "deprecation_date": "2024-06-01",
            "migration_guide": "https://docs.example.com/migration",
            "support_contact": "<EMAIL>"
        }
    }
