"""
数据库连接相关模型
定义数据库连接请求和响应的数据结构。
"""
from enum import Enum
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field

class DatabaseType(str, Enum):
    """支持的数据库类型枚举（与DataSourceType保持一致）"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    DB2 = "db2"
    GAUSSDB = "gaussdb"
    MONGODB = "mongodb"


class ConnectionParameters(BaseModel):
    """数据库连接的额外参数"""
    charset: Optional[str] = None
    service_name: Optional[str] = None
    sid: Optional[str] = None
    ssl_mode: Optional[str] = None
    db_schema: Optional[str] = None

    # 允许其他字段
    model_config = {
        "extra": "allow"
    }


class DatabaseConnection(BaseModel):
    """数据库连接请求模型"""
    type: DatabaseType
    host: str
    port: int
    username: str
    password: str = Field(..., description="数据库密码")
    database: str = Field(..., description="数据库名称或服务名")
    parameters: Optional[ConnectionParameters] = Field(
        default_factory=ConnectionParameters,
        description="数据库特定的连接参数"
    )

    @property
    def db_type(self) -> DatabaseType:
        """获取数据库类型（兼容性属性）"""
        return self.type

    model_config = {
        "json_schema_extra": {
            "example": {
                "type": "mysql",
                "host": "localhost",
                "port": 3306,
                "username": "user",
                "password": "password",
                "database": "testdb",
                "parameters": {
                    "charset": "utf8mb4"
                }
            }
        }
    }


class ConnectionTestResponse(BaseModel):
    """连接测试响应模型"""
    success: bool = Field(..., description="连接测试是否成功")
    message: str = Field(..., description="连接测试结果消息")
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="成功时可能包含额外的连接信息(如服务器版本)"
    )