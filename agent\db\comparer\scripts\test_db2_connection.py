#!/usr/bin/env python3
"""
DB2连接测试脚本
快速测试DB2连接配置和诊断问题
"""
import os
import sys
import asyncio
import argparse
import logging
from datetime import datetime

# 添加项目根目录到路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
sys.path.insert(0, PROJECT_ROOT)

from comparison.connectors.database_connector import DB2Connector
from comparison.connectors.db2_driver_manager import db2_driver_manager
from tools.db2_diagnostic_tool import DB2DiagnosticTool, DB2ConnectionInfo


def setup_logging(verbose=False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'db2_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("           DB2连接测试工具")
    print("=" * 60)
    print()


def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*20} {title} {'='*20}")


async def test_driver_status():
    """测试驱动状态"""
    print_section("驱动状态检查")
    
    try:
        # 检查驱动要求
        requirements = DB2Connector.check_driver_requirements()
        
        print(f"驱动要求满足: {'✅' if requirements['requirements_met'] else '❌'}")
        print(f"驱动状态: {requirements['status']}")
        
        if requirements.get('python_driver_version'):
            print(f"Python驱动版本: {requirements['python_driver_version']}")
        
        if requirements.get('cli_driver_path'):
            print(f"CLI驱动路径: {requirements['cli_driver_path']}")
        
        if requirements.get('missing_components'):
            print(f"缺失组件: {', '.join(requirements['missing_components'])}")
        
        if requirements.get('error_message'):
            print(f"错误信息: {requirements['error_message']}")
        
        return requirements['requirements_met']
        
    except Exception as e:
        print(f"❌ 驱动状态检查失败: {e}")
        return False


async def test_environment():
    """测试环境配置"""
    print_section("环境配置检查")
    
    # 检查关键环境变量
    env_vars = ['IBM_DB_HOME', 'PATH', 'LD_LIBRARY_PATH']
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            # 截断长路径以便显示
            display_value = value[:80] + "..." if len(value) > 80 else value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: 未设置")
    
    # 检查IBM_DB_HOME路径
    ibm_db_home = os.environ.get('IBM_DB_HOME')
    if ibm_db_home:
        if os.path.exists(ibm_db_home):
            print(f"✅ IBM_DB_HOME路径存在: {ibm_db_home}")
            
            # 检查关键子目录
            subdirs = ['bin', 'lib', 'include']
            for subdir in subdirs:
                subdir_path = os.path.join(ibm_db_home, subdir)
                status = "✅" if os.path.exists(subdir_path) else "❌"
                print(f"  {status} {subdir}/")
        else:
            print(f"❌ IBM_DB_HOME路径不存在: {ibm_db_home}")
    
    print(f"\n系统信息:")
    print(f"  操作系统: {os.name}")
    print(f"  Python版本: {sys.version}")


async def test_connection(connection_info):
    """测试数据库连接"""
    print_section("数据库连接测试")
    
    connector = DB2Connector()
    
    try:
        print("正在尝试连接DB2数据库...")
        print(f"主机: {connection_info.host}")
        print(f"端口: {connection_info.port}")
        print(f"数据库: {connection_info.database}")
        print(f"用户: {connection_info.username}")
        
        await connector.connect(connection_info)
        print("✅ 数据库连接成功!")
        
        # 执行测试查询
        print("\n执行测试查询...")
        result = connector._execute_sql("SELECT 1 FROM SYSIBM.SYSDUMMY1")
        if result and result[0].get('1') == 1:
            print("✅ 测试查询执行成功!")
        else:
            print("❌ 测试查询结果异常")
        
        # 获取DB2版本信息
        try:
            version_result = connector._execute_sql("SELECT SERVICE_LEVEL FROM SYSIBMADM.ENV_INST_INFO")
            if version_result:
                print(f"✅ DB2版本: {version_result[0]}")
        except Exception as e:
            print(f"⚠️ 无法获取DB2版本信息: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        
        # 分析错误类型
        error_msg = str(e)
        if "SQL1042C" in error_msg:
            print("\n🔍 检测到SQL1042C系统错误")
            print("这通常是由以下原因引起的:")
            print("  - IBM DB2 CLI Driver配置问题")
            print("  - 环境变量设置错误")
            print("  - 系统资源不足")
            print("  - 网络连接问题")
        elif "SQL30082N" in error_msg:
            print("\n🔍 检测到认证错误")
            print("请检查用户名和密码是否正确")
        
        return False
        
    finally:
        try:
            await connector.disconnect()
        except:
            pass


async def run_comprehensive_diagnostic(connection_info):
    """运行全面诊断"""
    print_section("全面诊断")
    
    try:
        tool = DB2DiagnosticTool()
        result = await tool.run_comprehensive_diagnostic(connection_info)
        
        print(f"诊断完成时间: {result['timestamp']}")
        print(f"连接状态: {result['connection_test']['final_status']}")
        
        if result.get('recommendations'):
            print("\n📋 建议:")
            for i, rec in enumerate(result['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        return result
        
    except Exception as e:
        print(f"❌ 全面诊断失败: {e}")
        return None


def get_connection_info_from_args(args):
    """从命令行参数获取连接信息"""
    return DB2ConnectionInfo(
        host=args.host,
        port=args.port,
        database=args.database,
        username=args.username,
        password=args.password,
        schema=args.schema
    )


def get_connection_info_interactive():
    """交互式获取连接信息"""
    print("请输入DB2连接信息:")
    
    host = input("主机地址: ").strip()
    port = int(input("端口 (默认50000): ").strip() or "50000")
    database = input("数据库名: ").strip()
    username = input("用户名: ").strip()
    password = input("密码: ").strip()
    schema = input("模式 (可选): ").strip() or None
    
    return DB2ConnectionInfo(
        host=host,
        port=port,
        database=database,
        username=username,
        password=password,
        schema=schema
    )


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DB2连接测试工具")
    parser.add_argument("--host", help="DB2主机地址")
    parser.add_argument("--port", type=int, default=50000, help="DB2端口")
    parser.add_argument("--database", help="数据库名")
    parser.add_argument("--username", help="用户名")
    parser.add_argument("--password", help="密码")
    parser.add_argument("--schema", help="模式")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--driver-only", action="store_true", help="仅检查驱动状态")
    parser.add_argument("--diagnostic", action="store_true", help="运行全面诊断")
    
    args = parser.parse_args()
    
    setup_logging(args.verbose)
    print_banner()
    
    # 检查驱动状态
    driver_ok = await test_driver_status()
    
    # 检查环境配置
    await test_environment()
    
    if args.driver_only:
        return
    
    # 获取连接信息
    if all([args.host, args.database, args.username, args.password]):
        connection_info = get_connection_info_from_args(args)
    else:
        try:
            connection_info = get_connection_info_interactive()
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return
    
    # 测试连接
    if driver_ok:
        connection_ok = await test_connection(connection_info)
        
        # 运行全面诊断（如果请求）
        if args.diagnostic:
            await run_comprehensive_diagnostic(connection_info)
    else:
        print("\n⚠️ 驱动配置有问题，跳过连接测试")
        print("请先解决驱动配置问题")
    
    print("\n" + "="*60)
    print("测试完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
