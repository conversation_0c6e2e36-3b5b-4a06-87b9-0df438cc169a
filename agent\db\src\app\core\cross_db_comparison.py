"""
跨数据库比对模块

提供跨数据库比对功能，处理不同数据库之间的类型差异。
"""
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set

from app.models.db import DatabaseConnection
from app.models.task import (
    CompareConfig, TaskStats, ComparisonSummary,
    TaskStatus, AlgorithmType
)
from app.core.comparison_base import BaseComparisonEngine
from app.core.data_source import RelationalDBAdapter
# 算法相关导入已移至基类
from app.core.smart_partitioning import SmartPartitioningStrategy
from app.core.diff_analyzer import DifferenceAnalyzer
from app.core.type_mapping import get_standard_type, normalize_value, StandardType
from app.core.query_adapter import QueryAdapter
from app.models.logger import logger


class CrossDBComparisonEngine(BaseComparisonEngine):
    """跨数据库比对引擎"""

    def __init__(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig,
        progress_callback: Optional[callable] = None
    ):
        """
        初始化跨数据库比对引擎

        Args:
            source_conn: 源数据库连接信息
            target_conn: 目标数据库连接信息
            config: 比对配置
            progress_callback: 进度回调函数
        """
        super().__init__(source_conn, target_conn, config, progress_callback)

        # 确定数据库类型
        self.source_db_type = source_conn.type.lower() if hasattr(source_conn, 'type') and source_conn.type else "db2"
        self.target_db_type = target_conn.type.lower() if hasattr(target_conn, 'type') and target_conn.type else "db2"

        # 初始化数据源适配器
        self.source_adapter = RelationalDBAdapter(source_conn)
        self.target_adapter = RelationalDBAdapter(target_conn)

        # 初始化查询适配器
        self.query_adapter = QueryAdapter(self.source_db_type, self.target_db_type)

        # 算法初始化已在基类中完成，这里不需要重复

        # 初始化智能分区策略
        self.partitioning_strategy = SmartPartitioningStrategy()

        # 初始化差异分析器
        self.diff_analyzer = DifferenceAnalyzer()

        # 批处理大小
        self.batch_size = config.batch_size if config.batch_size else 1000

        # 查询缓存
        self._query_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0
        self._cache_enabled = True  # 可以通过配置控制是否启用缓存
        self._max_cache_size = 100  # 最大缓存条目数

        # 字段类型映射
        self._field_types = {}

        # 是否是跨数据库比对
        self.is_cross_db = self.source_db_type != self.target_db_type

        logger.info(f"初始化跨数据库比对引擎: {self.source_db_type} -> {self.target_db_type}")

    async def initialize(self) -> bool:
        """
        初始化比对引擎

        Returns:
            bool: 初始化是否成功
        """
        await super().initialize()

        try:
            # 验证比对配置
            if not self._validate_config():
                logger.error("比对配置验证失败，终止比对")
                self.status = TaskStatus.FAILED
                return False

            # 连接数据源
            source_connected = await self.source_adapter.connect()
            if not source_connected:
                logger.error(f"无法连接到源数据库: {self.source_conn.host}:{self.source_conn.port}")
                self.status = TaskStatus.FAILED
                return False

            target_connected = await self.target_adapter.connect()
            if not target_connected:
                logger.error(f"无法连接到目标数据库: {self.target_conn.host}:{self.target_conn.port}")
                self.status = TaskStatus.FAILED
                return False

            # 获取源和目标表的记录总数（用于进度计算）
            self.stats.source_count = await self._get_total_count(True)

            self.stats.target_count = await self._get_total_count(False)

            # 检查是否获取记录总数失败
            if self.stats.source_count == -1 or self.stats.target_count == -1:
                logger.error("获取记录总数失败，终止比对")
                self.status = TaskStatus.FAILED
                return False

            logger.info(f"源记录数: {self.stats.source_count}, 目标记录数: {self.stats.target_count}")

            # 如果是跨数据库比对，获取字段类型信息
            if self.is_cross_db:
                logger.info("检测到跨数据库比对，正在获取字段类型信息...")
                await self._get_field_types()

            # 计算进度阈值
            source_count = max(0, self.stats.source_count)
            target_count = max(0, self.stats.target_count)
            total_records = max(source_count, target_count)
            self.progress_threshold = min(max(int(total_records / 100), 10), 1000)

            # 初始化比对结果存储
            self._comparison_results = {
                "matched": [],
                "diff": [],
                "source_only": [],
                "target_only": []
            }

            return True
        except Exception as e:
            logger.exception(f"初始化比对引擎失败: {str(e)}")
            self.status = TaskStatus.FAILED
            return False

    async def _get_field_types(self) -> None:
        """
        获取字段类型信息

        Returns:
            None
        """
        try:
            # 获取源表字段类型
            source_fields = await self._get_table_fields(True)

            # 获取目标表字段类型
            target_fields = await self._get_table_fields(False)

            # 合并字段类型信息
            self._field_types = {
                "source": source_fields,
                "target": target_fields
            }

            # 记录字段类型信息
            logger.info(f"获取到源表字段类型: {len(source_fields)} 个")
            logger.info(f"获取到目标表字段类型: {len(target_fields)} 个")

            # 检查比对字段是否存在
            if hasattr(self.config, 'compare_fields') and self.config.compare_fields:
                for field in self.config.compare_fields:
                    if field not in source_fields:
                        logger.warning(f"源表中不存在比对字段: {field}")
                    if field not in target_fields:
                        logger.warning(f"目标表中不存在比对字段: {field}")
        except Exception as e:
            logger.warning(f"获取字段类型信息失败: {str(e)}")

    async def _get_table_fields(self, is_source: bool) -> Dict[str, str]:
        """
        获取表字段类型

        Args:
            is_source: 是否是源表

        Returns:
            Dict[str, str]: 字段类型字典，键为字段名，值为字段类型
        """
        db_type = self.source_db_type if is_source else self.target_db_type
        adapter = self.source_adapter if is_source else self.target_adapter

        # 如果是查询模式，无法获取字段类型
        if self.config.mode == "query":
            return {}

        # 获取表名
        table_name = self.config.source_table if is_source else self.config.target_table

        # 根据数据库类型构建查询语句
        if db_type == "db2":
            query = f"""
            SELECT COLNAME AS COLUMN_NAME, TYPENAME AS DATA_TYPE
            FROM SYSCAT.COLUMNS
            WHERE TABNAME = '{table_name}'
            """
        elif db_type == "oracle":
            query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM ALL_TAB_COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            """
        elif db_type == "gaussdb" or db_type == "postgresql":
            query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            """
        elif db_type == "sqlserver":
            query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            """
        elif db_type == "mysql":
            query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            """
        else:
            logger.warning(f"不支持的数据库类型: {db_type}")
            return {}

        # 执行查询
        try:
            result = await adapter.execute_query(query)

            # 处理查询结果
            fields = {}
            for row in result:
                column_name = row.get("COLUMN_NAME", "").upper()
                data_type = row.get("DATA_TYPE", "").upper()
                fields[column_name] = data_type

            return fields
        except Exception as e:
            logger.warning(f"获取表字段类型失败: {str(e)}")
            return {}

    async def compare_data(self, source_data: List[Dict[str, Any]], target_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        比对数据

        Args:
            source_data: 源数据列表
            target_data: 目标数据列表

        Returns:
            Dict[str, Any]: 比对结果
        """
        # 性能监控：记录比对开始时间
        comparison_start_time = time.time()

        # 如果是跨数据库比对，需要进行类型转换
        if self.is_cross_db:
            # 标准化源数据和目标数据
            source_data = self._standardize_data(source_data, True)
            target_data = self._standardize_data(target_data, False)

        # 使用选择的算法进行比对
        comparison_results = await self.algorithm.compare(source_data, target_data, self.config)

        # 更新性能指标
        comparison_end_time = time.time()
        self._performance_metrics['comparison_time'] += (comparison_end_time - comparison_start_time)

        return comparison_results

    def _standardize_data(self, data: List[Dict[str, Any]], is_source: bool) -> List[Dict[str, Any]]:
        """
        标准化数据，用于跨数据库比较

        Args:
            data: 原始数据列表
            is_source: 是否是源数据

        Returns:
            List[Dict[str, Any]]: 标准化后的数据列表
        """
        # 如果没有字段类型信息，无法进行标准化
        if not self._field_types:
            return data

        # 获取字段类型信息
        field_types = self._field_types["source"] if is_source else self._field_types["target"]
        db_type = self.source_db_type if is_source else self.target_db_type

        # 标准化数据
        standardized_data = []
        for record in data:
            standardized_record = {}
            for field, value in record.items():
                # 获取字段类型
                field_type = field_types.get(field.upper(), "")

                # 获取标准类型
                standard_type = get_standard_type(db_type, field_type)

                # 标准化值
                standardized_value = normalize_value(value, standard_type)

                # 保存标准化后的值
                standardized_record[field] = standardized_value

            standardized_data.append(standardized_record)

        return standardized_data

    def _standardize_diff_record(self, diff: Dict[str, Any]) -> Dict[str, Any]:
        """
        将差异记录标准化为统一格式

        Args:
            diff: 差异记录

        Returns:
            Dict[str, Any]: 标准化后的差异记录
        """
        # 如果已有格式化的差异字段，直接返回
        if 'diff_fields' in diff:
            return diff

        # 需要将原始差异格式转换为标准格式
        fields_diff = {}

        # 根据不同算法的输出格式进行适配
        if 'source' in diff and 'target' in diff:
            # 哈希比对算法格式
            source_record = diff.get('source', {})
            target_record = diff.get('target', {})
        elif 'source_record' in diff and 'target_record' in diff:
            # 标准比对算法格式
            source_record = diff.get('source_record', {})
            target_record = diff.get('target_record', {})
        else:
            # 未知格式，返回空记录
            return {
                'key': diff.get('key', {}),
                'diff_fields': {},
                'source_record': {},
                'target_record': {}
            }

        # 找出所有不同的字段
        all_fields = set(source_record.keys()) | set(target_record.keys())
        for field in all_fields:
            source_value = source_record.get(field)
            target_value = target_record.get(field)

            # 如果是跨数据库比对，需要进行类型转换后再比较
            if self.is_cross_db and self._field_types:
                # 获取字段类型
                source_field_type = self._field_types.get("source", {}).get(field.upper(), "")
                target_field_type = self._field_types.get("target", {}).get(field.upper(), "")

                # 获取标准类型
                source_standard_type = get_standard_type(self.source_db_type, source_field_type)
                target_standard_type = get_standard_type(self.target_db_type, target_field_type)

                # 使用更严格的标准类型
                standard_type = source_standard_type if source_standard_type != StandardType.OTHER else target_standard_type

                # 标准化值
                source_value = normalize_value(source_value, standard_type)
                target_value = normalize_value(target_value, standard_type)

            # 比较值是否相等
            if source_value != target_value:
                fields_diff[field] = {
                    'source': source_value,
                    'target': target_value
                }

        # 返回标准化的差异记录
        return {
            'key': diff.get('key', {}),
            'diff_fields': fields_diff,
            'source_record': source_record,
            'target_record': target_record
        }
