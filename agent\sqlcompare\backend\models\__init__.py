#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Backend数据模型包 - 只保留API响应基类，其他模型使用主项目的
"""

from .base import (
    BaseModel, TimestampMixin, PaginationParams, PaginationResponse,
    APIResponse, ListResponse, HealthResponse, ErrorDetail
)

# 从主项目导入数据模型
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from models.pydantic_models import (
    ConnectionCreate, ConnectionResponse, ModelCreate, ModelResponse,
    TableRuleCreate, TableRuleResponse, TaskCreateFromModel, TaskCreateDirect,
    TaskResponse, TaskProgressResponse, ComparisonResultResponse, TaskStatistics
)

from models.sqlalchemy_models import (
    TaskStatus, UserRole, DatabaseType, DifferenceStatus, DifferenceType
)

__all__ = [
    # Base models (backend specific)
    "BaseModel", "TimestampMixin", "PaginationParams", "PaginationResponse",
    "APIResponse", "ListResponse", "HealthResponse", "ErrorDetail",

    # Main project models
    "ConnectionCreate", "ConnectionResponse", "ModelCreate", "ModelResponse",
    "TableRuleCreate", "TableRuleResponse", "TaskCreateFromModel", "TaskCreateDirect",
    "TaskResponse", "TaskProgressResponse", "ComparisonResultResponse", "TaskStatistics",

    # Enums
    "TaskStatus", "UserRole", "DatabaseType", "DifferenceStatus", "DifferenceType",
]
