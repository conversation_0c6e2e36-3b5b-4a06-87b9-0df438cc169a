"""
统一的消息处理机制
处理前后端与Agent之间的所有消息通信
"""
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import asyncio
import json
from datetime import datetime

from app.models.logger import logger
from app.models.response import WebSocketMessage, BaseResponse


class MessageType(Enum):
    """消息类型枚举"""
    TASK_STATUS = "task_status"
    TASK_PROGRESS = "task_progress"
    TASK_ERROR = "task_error"
    TASK_COMPLETE = "task_complete"
    SYSTEM_ERROR = "system_error"
    HEARTBEAT = "heartbeat"
    NOTIFICATION = "notification"


class MessagePriority(Enum):
    """消息优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


class Message:
    """消息对象"""
    
    def __init__(
        self,
        message_type: MessageType,
        data: Dict[str, Any],
        task_id: Optional[str] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
        retry_count: int = 0,
        max_retries: int = 3
    ):
        self.id = self._generate_message_id()
        self.type = message_type
        self.data = data
        self.task_id = task_id
        self.priority = priority
        self.retry_count = retry_count
        self.max_retries = max_retries
        self.created_at = datetime.now()
        self.last_attempt = None
        
    def _generate_message_id(self) -> str:
        """生成消息ID"""
        import uuid
        return str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'type': self.type.value,
            'data': self.data,
            'task_id': self.task_id,
            'priority': self.priority.value,
            'created_at': self.created_at.isoformat(),
            'retry_count': self.retry_count
        }
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self, handler_name: str):
        self.handler_name = handler_name
        self.supported_types: List[MessageType] = []
    
    async def handle(self, message: Message) -> bool:
        """处理消息"""
        try:
            if message.type not in self.supported_types:
                logger.warning(f"处理器 {self.handler_name} 不支持消息类型 {message.type}")
                return False
            
            return await self._process_message(message)
        except Exception as e:
            logger.error(f"消息处理失败: {self.handler_name}, 错误: {str(e)}")
            return False
    
    async def _process_message(self, message: Message) -> bool:
        """处理具体消息，子类需要实现"""
        raise NotImplementedError


class TaskStatusHandler(MessageHandler):
    """任务状态消息处理器"""
    
    def __init__(self):
        super().__init__("TaskStatusHandler")
        self.supported_types = [
            MessageType.TASK_STATUS,
            MessageType.TASK_PROGRESS,
            MessageType.TASK_ERROR,
            MessageType.TASK_COMPLETE
        ]
    
    async def _process_message(self, message: Message) -> bool:
        """处理任务状态消息"""
        try:
            task_id = message.task_id
            if not task_id:
                logger.warning("任务状态消息缺少task_id")
                return False
            
            # 根据消息类型处理
            if message.type == MessageType.TASK_STATUS:
                return await self._handle_status_update(task_id, message.data)
            elif message.type == MessageType.TASK_PROGRESS:
                return await self._handle_progress_update(task_id, message.data)
            elif message.type == MessageType.TASK_ERROR:
                return await self._handle_error(task_id, message.data)
            elif message.type == MessageType.TASK_COMPLETE:
                return await self._handle_completion(task_id, message.data)
            
            return False
        except Exception as e:
            logger.error(f"处理任务状态消息失败: {str(e)}")
            return False
    
    async def _handle_status_update(self, task_id: str, data: Dict[str, Any]) -> bool:
        """处理状态更新"""
        logger.info(f"任务状态更新: {task_id}, 状态: {data.get('status')}")
        # 这里可以添加具体的状态更新逻辑
        return True
    
    async def _handle_progress_update(self, task_id: str, data: Dict[str, Any]) -> bool:
        """处理进度更新"""
        progress = data.get('progress', 0)
        logger.info(f"任务进度更新: {task_id}, 进度: {progress}%")
        # 这里可以添加具体的进度更新逻辑
        return True
    
    async def _handle_error(self, task_id: str, data: Dict[str, Any]) -> bool:
        """处理错误"""
        error_message = data.get('error', '未知错误')
        logger.error(f"任务错误: {task_id}, 错误: {error_message}")
        # 这里可以添加具体的错误处理逻辑
        return True
    
    async def _handle_completion(self, task_id: str, data: Dict[str, Any]) -> bool:
        """处理完成"""
        logger.info(f"任务完成: {task_id}")
        # 这里可以添加具体的完成处理逻辑
        return True


class MessageQueue:
    """消息队列"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.queue = asyncio.PriorityQueue(maxsize=max_size)
        self.handlers: Dict[MessageType, List[MessageHandler]] = {}
        self.running = False
        self.worker_task = None
    
    def register_handler(self, message_type: MessageType, handler: MessageHandler):
        """注册消息处理器"""
        if message_type not in self.handlers:
            self.handlers[message_type] = []
        self.handlers[message_type].append(handler)
        logger.info(f"注册消息处理器: {message_type.value} -> {handler.handler_name}")
    
    async def send_message(self, message: Message) -> bool:
        """发送消息"""
        try:
            # 使用优先级作为排序键（数值越大优先级越高）
            priority_key = -message.priority.value
            await self.queue.put((priority_key, message.created_at, message))
            logger.debug(f"消息已加入队列: {message.type.value}, ID: {message.id}")
            return True
        except asyncio.QueueFull:
            logger.error(f"消息队列已满，丢弃消息: {message.type.value}")
            return False
    
    async def start_worker(self):
        """启动消息处理工作线程"""
        if self.running:
            return
        
        self.running = True
        self.worker_task = asyncio.create_task(self._worker_loop())
        logger.info("消息处理工作线程已启动")
    
    async def stop_worker(self):
        """停止消息处理工作线程"""
        self.running = False
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        logger.info("消息处理工作线程已停止")
    
    async def _worker_loop(self):
        """消息处理工作循环"""
        while self.running:
            try:
                # 等待消息
                _, _, message = await asyncio.wait_for(self.queue.get(), timeout=1.0)
                
                # 处理消息
                success = await self._process_message(message)
                
                # 如果处理失败且可以重试，重新加入队列
                if not success and message.can_retry():
                    message.retry_count += 1
                    message.last_attempt = datetime.now()
                    await asyncio.sleep(2 ** message.retry_count)  # 指数退避
                    await self.send_message(message)
                    logger.warning(f"消息处理失败，重试: {message.id}, 重试次数: {message.retry_count}")
                
                self.queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"消息处理工作循环异常: {str(e)}")
    
    async def _process_message(self, message: Message) -> bool:
        """处理单个消息"""
        handlers = self.handlers.get(message.type, [])
        if not handlers:
            logger.warning(f"没有找到消息类型 {message.type.value} 的处理器")
            return False
        
        # 尝试所有处理器
        success = False
        for handler in handlers:
            try:
                if await handler.handle(message):
                    success = True
            except Exception as e:
                logger.error(f"处理器 {handler.handler_name} 处理消息失败: {str(e)}")
        
        return success


# 全局消息队列实例
message_queue = MessageQueue()

# 注册默认处理器
task_status_handler = TaskStatusHandler()
message_queue.register_handler(MessageType.TASK_STATUS, task_status_handler)
message_queue.register_handler(MessageType.TASK_PROGRESS, task_status_handler)
message_queue.register_handler(MessageType.TASK_ERROR, task_status_handler)
message_queue.register_handler(MessageType.TASK_COMPLETE, task_status_handler)
