"""
数据比对平台 - FastAPI主应用
"""
import os
import sys
import uvicorn
import logging
# 获取当前脚本的目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 将项目根目录添加到Python路径中，使得backend和comparison模块可以被正确导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(SCRIPT_DIR))
sys.path.insert(0, PROJECT_ROOT)
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager

from backend.core.config import settings
from backend.core.database import init_db
from backend.api.v1.router import api_router
from backend.api.legacy.router import legacy_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    init_db()
    logging.info("数据比对平台启动成功")
    
    yield
    
    # 关闭时执行
    logging.info("数据比对平台正在关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="数据比对平台",
    description="高性能、高一致性的数据比对平台",
    version=settings.VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 添加API版本检测中间件
from backend.middleware.api_version import APIVersionMiddleware
app.add_middleware(APIVersionMiddleware)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 注册Legacy API路由（兼容性支持）
from backend.api.legacy.router import legacy_router
app.include_router(legacy_router, prefix="/api", tags=["Legacy API - 兼容性支持"])


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "数据比对平台运行正常",
        "version": settings.VERSION,
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z"
    }


@app.get("/api/legacy/stats")
async def get_legacy_api_stats():
    """获取Legacy API使用统计"""
    from backend.middleware.api_version import get_legacy_api_stats
    return get_legacy_api_stats()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
