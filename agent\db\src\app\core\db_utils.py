import os
import sys
import site
import asyncio
import asyncpg
import aiomysql
from typing import Dict, Any
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_DIR = os.path.dirname(SCRIPT_DIR)
sys.path.insert(0, os.path.dirname(PROJECT_DIR))
from app.models.task import DatabaseConfig
from app.models.db import DatabaseConnection
from app.core.config import settings
from app.models.logger import logger

def configure_db2_driver():
    # 可能的clidriver目录位置
    possible_paths = []

    # 使用环境变量的clidriver
    if os.environ.get('IBM_DB_HOME'):
        db2_home = os.environ.get('IBM_DB_HOME')
        possible_paths.append(os.path.join(db2_home, 'clidriver'))

    # 1. site-packages中的clidriver
    for site_dir in site.getsitepackages():
        possible_paths.append(os.path.join(site_dir, 'clidriver'))
        possible_paths.append(os.path.join(site_dir, 'ibm_db', 'clidriver'))

    # 2. 当前工作目录
    possible_paths.append(os.path.join(os.getcwd(), 'clidriver'))

    # 3. 项目根目录
    script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    possible_paths.append(os.path.join(script_dir, 'clidriver'))

    # 4. Windows常见安装位置
    if sys.platform.startswith('win'):
        for drive in ['C:', 'D:', 'E:']:
            possible_paths.append(f"{drive}\\IBM\\clidriver")
            possible_paths.append(f"{drive}\\Program Files\\IBM\\clidriver")
            possible_paths.append(f"{drive}\\Program Files (x86)\\IBM\\clidriver")
    # 5. Linux/Mac常见安装位置
    else:
        possible_paths.extend([
            '/opt/ibm/clidriver',
            '/opt/IBM/clidriver',
            '/usr/local/clidriver',
            os.path.expanduser('~/clidriver')
        ])

    # 检查每个可能的路径
    for path in possible_paths:
        if os.path.isdir(path) and os.path.isfile(os.path.join(path, 'bin', 'db2cli' + ('.exe' if sys.platform.startswith('win') else ''))):
            # 找到了有效的clidriver目录
            os.environ['IBM_DB_HOME'] = path

            # 更新PATH或LD_LIBRARY_PATH
            if sys.platform.startswith('win'):
                os.environ['PATH'] = f"{os.path.join(path, 'bin')};{os.path.join(path, 'lib')};{os.environ.get('PATH', '')}"
            else:
                ld_path = os.environ.get('LD_LIBRARY_PATH', '')
                os.environ['LD_LIBRARY_PATH'] = f"{os.path.join(path, 'lib')}:{ld_path}" if ld_path else os.path.join(path, 'lib')

            clidriver_bin_path = os.path.join(path, 'bin')
            try:
                os.add_dll_directory(clidriver_bin_path)
            except FileNotFoundError:
                logger.warning(f"无效的CLIDRIVER目录: {clidriver_bin_path}")
            except AttributeError:
                logger.warning("当前 Python 版本不支持 os.add_dll_directory (需要 Python 3.8+)")
            except Exception as dll_err:
                logger.warning(f"添加 DLL 目录时出错 ({clidriver_bin_path}): {dll_err}")

            logger.info(f"IBM DB2 driver found at: {path}")
            return True

    logger.warning("IBM DB2 driver not found. Please install it manually.")
    return False

async def get_db_connection(db_config: DatabaseConnection):
    """
    根据数据库配置创建数据库连接

    Args:
        db_config: 数据库连接配置

    Returns:
        数据库连接引擎/客户端，失败时返回None
    """
    if not db_config:
        logger.error("数据库配置为空")
        return None

    db_type = db_config.type.lower()
    # 生成连接ID和可读的连接字符串
    connection_id = f"{db_type}_{db_config.host}_{db_config.port}_{db_config.database}_{db_config.username}"
    connection_str = f"{db_config.host}:{db_config.port}/{db_config.database}"

    # 检查是否已有连接池
    if connection_id in _connection_pools:
        logger.info(f"使用现有连接池: {connection_str}")
        # 返回现有连接池，但不记录连接成功日志，避免混淆
        return _connection_pools[connection_id]

    try:
        # 使用数据库引擎工厂创建引擎实例
        from app.core.db_engine_factory import create_database_engine
        engine = await create_database_engine(db_config)

        if engine:
            logger.info(f"数据库连接成功: 类型={db_type}, 连接={connection_str}")
            # 保存到连接池字典
            _connection_pools[connection_id] = engine
            return engine

        # 如果引擎创建失败，回退到旧的连接方式
        logger.warning(f"使用DatabaseEngine创建失败，回退到旧的连接方式: 类型={db_type}")

        if db_type == "mysql":
            # 创建MySQL连接池
            try:
                pool = await aiomysql.create_pool(
                    host=db_config.host,
                    port=db_config.port or 3306,
                    user=db_config.username,
                    password=db_config.password,
                    db=db_config.database,
                    charset='utf8mb4',
                    autocommit=True,
                    connect_timeout=settings.DB_CONNECTION_TIMEOUT
                )
                # 保存到连接池字典
                _connection_pools[connection_id] = pool
                return pool
            except Exception as e:
                logger.error(f"MySQL连接失败: {db_config.host}:{db_config.port}/{db_config.database}, 错误: {str(e)}")
                return None

        elif db_type == "postgresql":
            # 创建PostgreSQL连接池
            try:
                pool = await asyncpg.create_pool(
                    host=db_config.host,
                    port=db_config.port or 5432,
                    user=db_config.username,
                    password=db_config.password,
                    database=db_config.database,
                    timeout=settings.DB_CONNECTION_TIMEOUT
                )
                # 保存到连接池字典
                _connection_pools[connection_id] = pool
                return pool
            except Exception as e:
                logger.error(f"PostgreSQL连接失败: {db_config.host}:{db_config.port}/{db_config.database}, 错误: {str(e)}")
                return None

        elif db_type == "db2":
            # DB2使用专用连接包装器
            try:
                import ibm_db
                import ibm_db_dbi
                from app.core.db_adapters import DB2ConnectionWrapper

                # 构建连接字符串
                conn_str = (
                    f"DATABASE={db_config.database};"
                    f"HOSTNAME={db_config.host};"
                    f"PORT={db_config.port or 50000};"
                    f"PROTOCOL=TCPIP;"
                    f"UID={db_config.username};"
                    f"PWD={db_config.password};"
                    f"AUTHENTICATION=SERVER;"
                    f"CONNECTTIMEOUT={settings.DB_CONNECTION_TIMEOUT};"
                )

                # 创建连接包装器
                try:
                    connection = await asyncio.to_thread(ibm_db_dbi.connect, conn_str)
                    wrapper = DB2ConnectionWrapper(connection)
                    # 保存到连接池字典
                    _connection_pools[connection_id] = wrapper
                    return wrapper
                except Exception as e:
                    logger.error(f"DB2连接失败: {db_config.host}:{db_config.port}/{db_config.database}, 错误: {str(e)}")
                    return None
            except ImportError as e:
                logger.error(f"DB2驱动导入失败: {str(e)}")
                return None

        else:
            # 其他数据库类型暂未实现
            logger.error(f"不支持的数据库类型: {db_type}")
            return None

    except Exception as e:
        logger.exception(f"创建数据库连接失败: 类型={db_type}, 主机={db_config.host}, 端口={db_config.port}, 数据库={db_config.database}, 错误: {str(e)}")
        return None

async def test_connection(config: DatabaseConfig, timeout: int = None) -> Dict[str, Any]:
    """
    测试数据库连接

    根据提供的连接参数测试与数据库的连接，返回连接结果、数据库版本和其他相关信息。

    Args:
        config: 数据库连接配置
        timeout: 连接超时时间（秒）

    Returns:
        包含连接测试结果的字典：
        {
            "success": bool,  # 连接是否成功
            "message": str,   # 连接结果消息或错误原因
            "version": str,   # 数据库版本（如果能获取）
            "metadata": dict  # 其他元数据信息
        }
    """
    # 使用配置的超时时间或默认值
    timeout = timeout or settings.DB_CONNECTION_TIMEOUT

    try:
        # 使用asyncio的超时控制
        return await asyncio.wait_for(
            _test_connection_by_type(config),
            timeout=timeout
        )
    except asyncio.TimeoutError:
        logger.warning(f"连接测试超时 - 类型: {config.type}, 主机: {config.host}, 超时: {timeout}秒")
        return {
            "success": False,
            "message": f"连接超时（{timeout}秒）",
            "version": None,
            "metadata": None
        }
    except Exception as e:
        logger.exception(f"连接测试异常 - 类型: {config.type}, 错误: {str(e)}")
        return {
            "success": False,
            "message": f"连接异常: {str(e)}",
            "version": None,
            "metadata": None
        }

async def _test_connection_by_type(config: DatabaseConfig) -> Dict[str, Any]:
    """根据数据库类型调用对应的连接测试函数"""
    db_type = config.type.lower()

    if db_type == "mysql":
        return await _test_mysql_connection(config)
    elif db_type == "postgresql":
        return await _test_postgresql_connection(config)
    elif db_type == "oracle":
        return await _test_oracle_connection(config)
    elif db_type == "sqlserver":
        return await _test_sqlserver_connection(config)
    elif db_type == "db2":
        return await _test_db2_connection(config)
    elif db_type == "gaussdb":
        return await _test_gaussdb_connection(config)
    else:
        return {
            "success": False,
            "message": f"不支持的数据库类型: {db_type}",
            "version": None,
            "metadata": None
        }


async def _test_mysql_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试MySQL数据库连接"""
    conn = None
    try:
        # 创建MySQL连接
        conn = await aiomysql.connect(
            host=config.host,
            port=config.port or 3306,
            user=config.username,
            password=config.password,
            db=config.database,
            charset='utf8mb4',
            connect_timeout=5  # 内部连接超时5秒
        )

        # 执行简单查询获取版本信息
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT VERSION() as version")
            version_row = await cursor.fetchone()
            version = version_row[0] if version_row else "未知"

            # 获取数据库列表
            await cursor.execute("SHOW DATABASES")
            databases = [row[0] for row in await cursor.fetchall()]

            # 获取当前数据库的表数量
            if config.database:
                await cursor.execute(f"USE {config.database}")
                await cursor.execute("SHOW TABLES")
                table_count = len(await cursor.fetchall())
            else:
                table_count = 0

        return {
            "success": True,
            "message": "连接成功",
            "version": version,
            "metadata": {
                "database_count": len(databases),
                "table_count": table_count,
                "databases": databases[:10]  # 最多返回10个数据库名
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"MySQL连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }
    finally:
        if conn:
            conn.close()


async def _test_postgresql_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试PostgreSQL数据库连接"""
    conn = None
    try:
        # 创建PostgreSQL连接
        conn = await asyncpg.connect(
            host=config.host,
            port=config.port or 5432,
            user=config.username,
            password=config.password,
            database=config.database
        )

        # 获取版本信息
        version_row = await conn.fetchrow("SELECT version()")
        version = version_row['version'] if version_row else "未知"

        # 获取数据库列表
        databases = await conn.fetch(
            "SELECT datname FROM pg_database WHERE datistemplate = false"
        )
        database_list = [row['datname'] for row in databases]

        # 获取当前数据库的表数量
        tables = await conn.fetch(
            "SELECT count(*) FROM information_schema.tables "
            "WHERE table_schema NOT IN ('pg_catalog', 'information_schema')"
        )
        table_count = tables[0]['count'] if tables else 0

        return {
            "success": True,
            "message": "连接成功",
            "version": version,
            "metadata": {
                "database_count": len(database_list),
                "table_count": table_count,
                "databases": database_list[:10]  # 最多返回10个数据库名
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"PostgreSQL连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }
    finally:
        if conn:
            await conn.close()


async def _test_oracle_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试Oracle数据库连接"""
    try:
        # 注意：这里需要导入oracledb库，需要在项目依赖中添加
        import oracledb

        # 创建异步执行上下文
        async with oracledb.create_pool(
            user=config.username,
            password=config.password,
            dsn=f"{config.host}:{config.port or 1521}/{config.database}",
            min=1, max=2,  # 连接池最小最大连接数
            timeout=5      # 获取连接超时时间
        ) as pool:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 获取版本信息
                    await cursor.execute("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1")
                    version_row = await cursor.fetchone()
                    version = version_row[0] if version_row else "未知"

                    # 获取表空间信息
                    await cursor.execute("SELECT COUNT(*) FROM USER_TABLES")
                    table_count = await cursor.fetchone()

                    # 获取当前用户可见的所有表空间
                    await cursor.execute("SELECT TABLESPACE_NAME FROM USER_TABLESPACES")
                    tablespaces = [row[0] for row in await cursor.fetchall()]

        return {
            "success": True,
            "message": "连接成功",
            "version": version,
            "metadata": {
                "table_count": table_count[0] if table_count else 0,
                "tablespaces": tablespaces[:10]  # 最多返回10个表空间
            }
        }
    except ImportError:
        return {
            "success": False,
            "message": "Oracle驱动未安装，请安装oracledb库",
            "version": None,
            "metadata": None
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Oracle连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }


async def _test_sqlserver_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试SQLServer数据库连接"""
    try:
        # 注意：这里需要导入aioodbc库，需要在项目依赖中添加
        import aioodbc

        # 构建连接字符串
        dsn = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={config.host},{config.port or 1433};"
            f"DATABASE={config.database};"
            f"UID={config.username};"
            f"PWD={config.password}"
        )

        # 创建连接
        conn = await aioodbc.connect(dsn=dsn, timeout=5)
        try:
            # 创建游标并执行查询
            async with conn.cursor() as cursor:
                # 获取版本信息
                await cursor.execute("SELECT @@VERSION")
                version_row = await cursor.fetchone()
                version = version_row[0] if version_row else "未知"

                # 获取数据库信息
                await cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES")
                table_count = await cursor.fetchone()

                # 获取数据库列表
                await cursor.execute("SELECT name FROM sys.databases WHERE database_id > 4")  # 排除系统数据库
                databases = [row[0] for row in await cursor.fetchall()]

            return {
                "success": True,
                "message": "连接成功",
                "version": version,
                "metadata": {
                    "table_count": table_count[0] if table_count else 0,
                    "databases": databases[:10]  # 最多返回10个数据库
                }
            }
        finally:
            await conn.close()
    except ImportError:
        return {
            "success": False,
            "message": "SQLServer驱动未安装，请安装aioodbc库和ODBC驱动",
            "version": None,
            "metadata": None
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"SQLServer连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }


async def _test_db2_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试IBM DB2数据库连接"""
    # 重新验证驱动程序配置
    driver_configured = configure_db2_driver()

    try:
        if not driver_configured:
            return {
                "success": False,
                "message": "DB2驱动未找到，请检查安装或手动配置IBM_DB_HOME环境变量",
                "version": None,
                "metadata": None
            }

        # 尝试导入ibm_db和ibm_db_dbi库
        try:
            import ibm_db
            import ibm_db_dbi
            logger.info("成功导入IBM DB2驱动库")
        except ImportError as e:
            logger.error(f"导入IBM DB2驱动库失败: {str(e)}")
            # 尝试提供更详细的错误信息
            error_details = ""
            if "DLL load failed" in str(e):
                error_details = " (DLL加载失败，可能是驱动程序路径不正确或缺少依赖项)"
            elif "No module named" in str(e):
                error_details = " (模块未找到，请确认已通过pip安装ibm_db)"

            return {
                "success": False,
                "message": f"DB2驱动导入失败: {str(e)}{error_details}",
                "version": None,
                "metadata": None
            }

        # 构建连接字符串
        conn_str = (
            f"DATABASE={config.database};"
            f"HOSTNAME={config.host};"
            f"PORT={config.port or 50000};"
            f"PROTOCOL=TCPIP;"
            f"UID={config.username};"
            f"PWD={config.password};"
            f"AUTHENTICATION=SERVER;"
        )

        logger.debug(f"DB2连接字符串(敏感信息已隐藏): DATABASE={config.database};HOSTNAME={config.host};PORT={config.port or 50000};PROTOCOL=TCPIP;UID={config.username};PWD=******")

        # 在线程池中执行同步DB2连接
        return await asyncio.to_thread(_db2_connect_sync, conn_str)
    except Exception as e:
        logger.exception(f"DB2连接测试异常: {str(e)}")
        return {
            "success": False,
            "message": f"DB2连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }

def _db2_connect_sync(conn_str: str) -> Dict[str, Any]:
    """在同步线程中执行DB2连接测试"""
    import ibm_db_dbi  # 导入应该在此函数内部，确保在配置驱动后执行

    conn = None
    try:
        # 尝试建立连接
        conn = ibm_db_dbi.connect(conn_str)
        cursor = conn.cursor()

        # 获取版本信息
        try:
            cursor.execute("SELECT SERVICE_LEVEL FROM SYSIBMADM.ENV_INST_INFO")
            version_row = cursor.fetchone()
            version = version_row[0] if version_row else "未知"
        except Exception as e:
            logger.warning(f"无法获取DB2版本信息: {str(e)}")
            version = "未知（无法访问SYSIBMADM.ENV_INST_INFO）"

        # 获取表信息
        try:
            cursor.execute("SELECT COUNT(*) FROM SYSCAT.TABLES WHERE TABSCHEMA NOT IN ('SYSCAT', 'SYSIBM', 'SYSIBMADM')")
            table_count = cursor.fetchone()
        except Exception as e:
            logger.warning(f"无法获取DB2表信息: {str(e)}")
            table_count = [0]

        # 获取schema列表
        try:
            cursor.execute("SELECT SCHEMANAME FROM SYSCAT.SCHEMATA WHERE DEFINER <> 'SYSIBM'")
            schemas = [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.warning(f"无法获取DB2 schema列表: {str(e)}")
            schemas = []

        return {
            "success": True,
            "message": "连接成功",
            "version": version,
            "metadata": {
                "table_count": table_count[0] if table_count else 0,
                "schemas": schemas[:10]  # 最多返回10个schema
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"DB2连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }
    finally:
        if conn:
            try:
                conn.close()
            except Exception as e:
                logger.warning(f"关闭DB2连接时发生异常: {str(e)}")


async def _test_gaussdb_connection(config: DatabaseConfig) -> Dict[str, Any]:
    """测试华为GaussDB数据库连接 (PostgreSQL兼容模式)"""
    try:
        # GaussDB的PostgreSQL接口与标准PostgreSQL兼容
        return await _test_postgresql_connection(config)
    except Exception as e:
        return {
            "success": False,
            "message": f"GaussDB连接失败: {str(e)}",
            "version": None,
            "metadata": None
        }

# 连接池管理
_connection_pools = {}

async def close_connection_pool(connection_id: str) -> bool:
    """
    关闭并清理指定的数据库连接池

    Args:
        connection_id: 连接池ID

    Returns:
        bool: 是否成功关闭连接池
    """
    if connection_id not in _connection_pools:
        logger.warning(f"尝试关闭不存在的连接池: {connection_id}")
        return False

    try:
        pool = _connection_pools[connection_id]

        # 根据不同类型的连接池调用不同的关闭方法
        if hasattr(pool, 'close'):
            # PostgreSQL, SQLite等
            await pool.close()
        elif hasattr(pool, 'terminate'):
            # 某些连接池使用terminate
            await pool.terminate()
        elif hasattr(pool, 'end'):
            # MySQL等
            pool.end()
        elif hasattr(pool, 'dispose'):
            # SQLAlchemy
            await pool.dispose()

        # 从连接池字典中移除
        del _connection_pools[connection_id]
        logger.info(f"成功关闭连接池: {connection_id}")
        return True
    except Exception as e:
        logger.error(f"关闭连接池 {connection_id} 时出错: {str(e)}")
        return False