# core/strategies/hash_strategy.py
"""
哈希比对策略
适用于小到中等规模的数据集，不需要排序，内存占用相对较高
"""
import time
import logging
from typing import Dict, Any, Optional, Callable
from collections import defaultdict

import sys
import os
# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(os.path.dirname(current_dir)))

from .base_strategy import ComparisonStrategy
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter
from core.models import DiffResult
from core.engine import compare_values

logger = logging.getLogger(__name__)


class HashComparisonStrategy(ComparisonStrategy):
    """哈希比对策略实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.batch_size = self.config.get('batch_size', 50000)  # 批处理大小
    
    def get_strategy_name(self) -> str:
        return "Hash Comparison Strategy"
    
    def requires_sorting(self) -> bool:
        return False
    
    def estimate_memory_usage(self, record_count_a: int, record_count_b: int) -> int:
        """
        估算内存使用量
        假设每条记录平均占用200字节（KEY + VALUE + 哈希表开销）
        """
        avg_record_size = 200
        return (record_count_a + record_count_b) * avg_record_size
    
    def compare(
        self,
        source_a: BaseConnector,
        source_b: BaseConnector,
        reporter: BaseReporter,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        使用哈希表进行数据比对
        
        算法流程：
        1. 将数据源A的所有记录加载到哈希表
        2. 遍历数据源B，与哈希表中的记录比对
        3. 标记匹配和差异记录
        4. 报告所有差异
        """
        logger.info(f"开始使用{self.get_strategy_name()}进行数据比对...")
        start_time = time.perf_counter()
        
        # 统计信息
        stats = {
            'total_records_a': 0,
            'total_records_b': 0,
            'differences': 0,
            'load_time_a': 0.0,
            'load_time_b': 0.0,
            'compare_time': 0.0,
            'report_time': 0.0
        }
        
        try:
            with source_a, source_b, reporter:
                # 阶段1：加载数据源A到哈希表
                logger.info("阶段1：加载数据源A到内存...")
                load_start = time.perf_counter()
                
                hash_table_a = {}
                for record in source_a:
                    hash_table_a[record.key] = record.value
                    stats['total_records_a'] += 1
                    
                    # 进度回调
                    if progress_callback and stats['total_records_a'] % self.batch_size == 0:
                        progress_callback(f"已加载A源 {stats['total_records_a']} 条记录")
                
                stats['load_time_a'] = time.perf_counter() - load_start
                logger.info(f"数据源A加载完成：{stats['total_records_a']} 条记录，耗时 {stats['load_time_a']:.2f} 秒")
                
                # 阶段2：遍历数据源B并比对
                logger.info("阶段2：遍历数据源B并进行比对...")
                load_start = time.perf_counter()
                
                matched_keys = set()  # 记录已匹配的KEY
                
                for record_b in source_b:
                    stats['total_records_b'] += 1
                    
                    compare_start = time.perf_counter()
                    
                    if record_b.key in hash_table_a:
                        # KEY存在于A源，比较值
                        matched_keys.add(record_b.key)
                        value_a = hash_table_a[record_b.key]
                        
                        if not compare_values(value_a, record_b.value):
                            # 值不同
                            report_start = time.perf_counter()
                            diff = DiffResult(
                                key=record_b.key,
                                value_a=value_a,
                                value_b=record_b.value,
                                status='DIFFERENT'
                            )
                            reporter.report_diff(diff)
                            stats['differences'] += 1
                            stats['report_time'] += time.perf_counter() - report_start
                    else:
                        # KEY只存在于B源
                        report_start = time.perf_counter()
                        diff = DiffResult(
                            key=record_b.key,
                            value_b=record_b.value,
                            status='IN_B_ONLY'
                        )
                        reporter.report_diff(diff)
                        stats['differences'] += 1
                        stats['report_time'] += time.perf_counter() - report_start
                    
                    stats['compare_time'] += time.perf_counter() - compare_start
                    
                    # 进度回调
                    if progress_callback and stats['total_records_b'] % self.batch_size == 0:
                        progress_callback(f"已比对B源 {stats['total_records_b']} 条记录，发现 {stats['differences']} 个差异")
                
                stats['load_time_b'] = time.perf_counter() - load_start
                logger.info(f"数据源B处理完成：{stats['total_records_b']} 条记录")
                
                # 阶段3：处理A源独有的记录
                logger.info("阶段3：处理A源独有的记录...")
                report_start = time.perf_counter()
                
                a_only_count = 0
                for key_a, value_a in hash_table_a.items():
                    if key_a not in matched_keys:
                        # KEY只存在于A源
                        diff = DiffResult(
                            key=key_a,
                            value_a=value_a,
                            status='IN_A_ONLY'
                        )
                        reporter.report_diff(diff)
                        stats['differences'] += 1
                        a_only_count += 1
                
                stats['report_time'] += time.perf_counter() - report_start
                logger.info(f"A源独有记录：{a_only_count} 条")
                
        except Exception as e:
            logger.error(f"哈希比对过程中发生错误: {e}", exc_info=True)
            raise
        finally:
            end_time = time.perf_counter()
            total_time = end_time - start_time
            
            # 输出统计信息
            logger.info("=" * 50)
            logger.info("哈希比对完成")
            logger.info("=" * 50)
            logger.info(f"总耗时: {total_time:.2f} 秒")
            logger.info(f"A源记录数: {stats['total_records_a']}")
            logger.info(f"B源记录数: {stats['total_records_b']}")
            logger.info(f"总差异数: {stats['differences']}")
            
            if total_time > 0:
                total_records = stats['total_records_a'] + stats['total_records_b']
                rate = total_records / total_time
                logger.info(f"平均处理速率: {rate:.2f} 条/秒")
            
            logger.info("时间分布:")
            logger.info(f"  A源加载: {stats['load_time_a']:.2f} 秒")
            logger.info(f"  B源处理: {stats['load_time_b']:.2f} 秒")
            logger.info(f"  数据比较: {stats['compare_time']:.2f} 秒")
            logger.info(f"  差异报告: {stats['report_time']:.2f} 秒")
            
            # 内存使用估算
            estimated_memory = self.estimate_memory_usage(
                stats['total_records_a'], 
                stats['total_records_b']
            )
            logger.info(f"估算内存使用: {estimated_memory / 1024 / 1024:.1f} MB")
            
            stats['total_time'] = total_time
            stats['processing_rate'] = (stats['total_records_a'] + stats['total_records_b']) / total_time if total_time > 0 else 0
            
            return stats
