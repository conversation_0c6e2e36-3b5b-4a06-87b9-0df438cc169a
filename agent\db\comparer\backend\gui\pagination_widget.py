#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页控制组件
提供用户友好的分页导航界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Callable, Optional

logger = logging.getLogger(__name__)


class PaginationWidget:
    """分页控制组件
    
    特点：
    - 完整的分页导航功能
    - 页码跳转和每页记录数配置
    - 状态显示和用户友好的交互
    - 回调机制支持数据刷新
    """
    
    def __init__(self, parent, on_page_changed: Callable[[int, int], None]):
        """初始化分页组件
        
        Args:
            parent: 父容器
            on_page_changed: 页面变化回调函数 (page, page_size) -> None
        """
        self.parent = parent
        self.on_page_changed = on_page_changed
        
        # 分页状态
        self.current_page = 1
        self.page_size = 100
        self.total_records = 0
        self.total_pages = 0
        
        # 创建分页控制界面
        self._create_pagination_ui()
        
        logger.debug("分页控制组件初始化完成")
    
    def _create_pagination_ui(self):
        """创建分页控制界面"""
        # 主框架
        self.pagination_frame = ttk.Frame(self.parent)
        self.pagination_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 左侧：记录信息
        left_frame = ttk.Frame(self.pagination_frame)
        left_frame.pack(side=tk.LEFT)
        
        self.info_label = ttk.Label(left_frame, text="共 0 条记录")
        self.info_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # 中间：分页控制
        center_frame = ttk.Frame(self.pagination_frame)
        center_frame.pack(side=tk.LEFT, expand=True)
        
        # 首页按钮
        self.first_btn = ttk.Button(center_frame, text="首页", width=6, 
                                   command=self._go_first_page, state=tk.DISABLED)
        self.first_btn.pack(side=tk.LEFT, padx=2)
        
        # 上一页按钮
        self.prev_btn = ttk.Button(center_frame, text="上一页", width=6, 
                                  command=self._go_prev_page, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=2)
        
        # 页码信息
        ttk.Label(center_frame, text="第").pack(side=tk.LEFT, padx=(10, 2))
        
        # 页码输入框
        self.page_var = tk.StringVar(value="1")
        self.page_entry = ttk.Entry(center_frame, textvariable=self.page_var, width=6, justify=tk.CENTER)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        self.page_entry.bind('<Return>', self._on_page_entry_return)
        
        ttk.Label(center_frame, text="页").pack(side=tk.LEFT, padx=(2, 2))
        
        self.page_info_label = ttk.Label(center_frame, text="/ 共 0 页")
        self.page_info_label.pack(side=tk.LEFT, padx=(2, 10))
        
        # 跳转按钮
        self.goto_btn = ttk.Button(center_frame, text="跳转", width=6, 
                                  command=self._goto_page, state=tk.DISABLED)
        self.goto_btn.pack(side=tk.LEFT, padx=2)
        
        # 下一页按钮
        self.next_btn = ttk.Button(center_frame, text="下一页", width=6, 
                                  command=self._go_next_page, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=2)
        
        # 末页按钮
        self.last_btn = ttk.Button(center_frame, text="末页", width=6, 
                                  command=self._go_last_page, state=tk.DISABLED)
        self.last_btn.pack(side=tk.LEFT, padx=2)
        
        # 右侧：每页记录数配置
        right_frame = ttk.Frame(self.pagination_frame)
        right_frame.pack(side=tk.RIGHT)
        
        ttk.Label(right_frame, text="每页显示:").pack(side=tk.LEFT, padx=(20, 5))
        
        # 每页记录数下拉框
        self.page_size_var = tk.StringVar(value="100")
        self.page_size_combo = ttk.Combobox(right_frame, textvariable=self.page_size_var, 
                                           values=["50", "100", "200", "500"], 
                                           width=8, state="readonly")
        self.page_size_combo.pack(side=tk.LEFT, padx=2)
        self.page_size_combo.bind('<<ComboboxSelected>>', self._on_page_size_changed)
        
        ttk.Label(right_frame, text="条").pack(side=tk.LEFT, padx=(2, 0))
    
    def update_pagination_info(self, current_page: int, total_records: int, page_size: int):
        """更新分页信息
        
        Args:
            current_page: 当前页码
            total_records: 总记录数
            page_size: 每页记录数
        """
        self.current_page = current_page
        self.total_records = total_records
        self.page_size = page_size
        self.total_pages = max(1, (total_records + page_size - 1) // page_size)
        
        # 更新界面显示
        self._update_ui_state()
        
        logger.debug(f"分页信息更新: 第{current_page}页, 共{total_records}条记录, {self.total_pages}页")
    
    def _update_ui_state(self):
        """更新界面状态"""
        # 更新信息标签
        start_record = (self.current_page - 1) * self.page_size + 1
        end_record = min(self.current_page * self.page_size, self.total_records)
        
        if self.total_records > 0:
            info_text = f"共 {self.total_records} 条记录，显示第 {start_record}-{end_record} 条"
        else:
            info_text = "共 0 条记录"
        
        self.info_label.config(text=info_text)
        
        # 更新页码信息
        self.page_var.set(str(self.current_page))
        self.page_info_label.config(text=f"/ 共 {self.total_pages} 页")
        
        # 更新按钮状态
        has_data = self.total_records > 0
        has_prev = self.current_page > 1
        has_next = self.current_page < self.total_pages
        
        self.first_btn.config(state=tk.NORMAL if has_prev else tk.DISABLED)
        self.prev_btn.config(state=tk.NORMAL if has_prev else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if has_next else tk.DISABLED)
        self.last_btn.config(state=tk.NORMAL if has_next else tk.DISABLED)
        self.goto_btn.config(state=tk.NORMAL if has_data else tk.DISABLED)
    
    def _go_first_page(self):
        """跳转到首页"""
        if self.current_page != 1:
            self._change_page(1)
    
    def _go_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self._change_page(self.current_page - 1)
    
    def _go_next_page(self):
        """跳转到下一页"""
        if self.current_page < self.total_pages:
            self._change_page(self.current_page + 1)
    
    def _go_last_page(self):
        """跳转到末页"""
        if self.current_page != self.total_pages:
            self._change_page(self.total_pages)
    
    def _goto_page(self):
        """跳转到指定页"""
        try:
            target_page = int(self.page_var.get())
            if 1 <= target_page <= self.total_pages:
                self._change_page(target_page)
            else:
                messagebox.showwarning("页码错误", f"页码必须在 1 到 {self.total_pages} 之间")
                self.page_var.set(str(self.current_page))  # 恢复当前页码
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的页码数字")
            self.page_var.set(str(self.current_page))  # 恢复当前页码
    
    def _on_page_entry_return(self, event):
        """页码输入框回车事件"""
        self._goto_page()
    
    def _on_page_size_changed(self, event):
        """每页记录数变化事件"""
        try:
            new_page_size = int(self.page_size_var.get())
            if new_page_size != self.page_size:
                # 计算新的页码，尽量保持当前显示的记录位置
                current_record = (self.current_page - 1) * self.page_size + 1
                new_page = max(1, (current_record + new_page_size - 1) // new_page_size)
                
                self.page_size = new_page_size
                self._change_page(new_page)
        except ValueError:
            logger.error(f"无效的每页记录数: {self.page_size_var.get()}")
    
    def _change_page(self, new_page: int):
        """切换页面
        
        Args:
            new_page: 新页码
        """
        if new_page != self.current_page:
            self.current_page = new_page
            self._update_ui_state()
            
            # 触发回调
            try:
                self.on_page_changed(self.current_page, self.page_size)
            except Exception as e:
                logger.error(f"页面变化回调失败: {e}")
                messagebox.showerror("错误", f"加载页面数据失败: {e}")
    
    def reset(self):
        """重置分页状态"""
        self.current_page = 1
        self.total_records = 0
        self.total_pages = 0
        self.page_size = int(self.page_size_var.get())
        self._update_ui_state()
        
        logger.debug("分页状态已重置")
    
    def set_loading_state(self, loading: bool):
        """设置加载状态
        
        Args:
            loading: 是否正在加载
        """
        state = tk.DISABLED if loading else tk.NORMAL
        
        self.first_btn.config(state=state)
        self.prev_btn.config(state=state)
        self.next_btn.config(state=state)
        self.last_btn.config(state=state)
        self.goto_btn.config(state=state)
        self.page_entry.config(state=state)
        self.page_size_combo.config(state=state)
        
        if loading:
            self.info_label.config(text="正在加载...")
        else:
            self._update_ui_state()

    def update_data(self, data_list):
        """更新数据并刷新分页显示

        Args:
            data_list: 新的数据列表
        """
        try:
            self.total_records = len(data_list) if data_list else 0
            self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)

            # 如果当前页超出范围，重置到第一页
            if self.current_page > self.total_pages:
                self.current_page = 1

            self._update_ui_state()

            # 触发页面变化回调
            if self.on_page_changed:
                self.on_page_changed(self.current_page, self.page_size)

        except Exception as e:
            logger.error(f"更新分页数据失败: {e}")

    def reset_to_first_page(self):
        """重置到第一页"""
        try:
            self.current_page = 1
            self._update_ui_state()
        except Exception as e:
            logger.error(f"重置到第一页失败: {e}")
