"""
重构后的比对服务使用示例
展示配置管理层和执行管理层分离的架构使用方式
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from models.sqlalchemy_models import (
    Base, User, ComparisonConnection, ComparisonModel, ComparisonTableRule,
    ComparisonTask, TaskStatus,
    create_database_engine, create_tables, get_session_factory
)


class RefactoredComparisonService:
    """重构后的比对服务类"""
    
    def __init__(self, database_url: str = "sqlite:///refactored_comparison.db"):
        self.engine = create_database_engine(database_url)
        create_tables(self.engine)
        self.SessionLocal = get_session_factory(self.engine)
    
    # 配置管理层方法
    
    def create_connection(self, connection_data: Dict[str, Any]) -> int:
        """创建数据库连接配置"""
        with self.SessionLocal() as session:
            connection = ComparisonConnection(
                name=connection_data['name'],
                type=connection_data['type'],
                host=connection_data['host'],
                port=connection_data['port'],
                username=connection_data['username'],
                password=connection_data['password'],  # 实际应用中需要加密
                database=connection_data['database'],
                params=connection_data.get('params', {})
            )
            session.add(connection)
            session.commit()
            return connection.id
    
    def create_model(self, model_data: Dict[str, Any]) -> int:
        """创建比对模型"""
        with self.SessionLocal() as session:
            model = ComparisonModel(
                name=model_data['name'],
                description=model_data.get('description'),
                source_connid=model_data['source_connid'],
                target_connid=model_data['target_connid'],
                cmp_type=model_data.get('cmp_type', 'content'),
                global_config=model_data.get('global_config', {})
            )
            session.add(model)
            session.commit()
            return model.id
    
    def add_table_rule(self, model_id: int, rule_data: Dict[str, Any]) -> int:
        """添加表规则"""
        with self.SessionLocal() as session:
            rule = ComparisonTableRule(
                model_id=model_id,
                table_id=rule_data['table_id'],
                table_name=rule_data['table_name'],
                sql_1=rule_data['sql_1'],
                sql_2=rule_data['sql_2'],
                remark=rule_data.get('remark'),
                primary_keys=rule_data.get('primary_keys'),
                ignore_fields=rule_data.get('ignore_fields'),
                field_mappings=rule_data.get('field_mappings')
            )
            session.add(rule)
            session.commit()
            return rule.id
    
    def get_model_with_rules(self, model_id: int) -> Dict[str, Any]:
        """获取模型及其规则"""
        with self.SessionLocal() as session:
            model = session.query(ComparisonModel).filter(
                ComparisonModel.id == model_id
            ).first()
            
            if not model:
                return {"error": "Model not found"}
            
            rules = session.query(ComparisonTableRule).filter(
                ComparisonTableRule.model_id == model_id,
                ComparisonTableRule.is_active == True
            ).all()
            
            return {
                "model": {
                    "id": model.id,
                    "name": model.name,
                    "description": model.description,
                    "source_connid": model.source_connid,
                    "target_connid": model.target_connid,
                    "cmp_type": model.cmp_type,
                    "status": model.status
                },
                "rules": [
                    {
                        "id": rule.id,
                        "table_id": rule.table_id,
                        "table_name": rule.table_name,
                        "sql_1": rule.sql_1,
                        "sql_2": rule.sql_2,
                        "remark": rule.remark
                    }
                    for rule in rules
                ]
            }
    
    # 执行管理层方法
    
    def create_task_from_model(self, user_id: str, task_data: Dict[str, Any]) -> str:
        """基于模型创建任务"""
        with self.SessionLocal() as session:
            task_id = str(uuid.uuid4())
            
            # 创建主任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                model_id=task_data['model_id'],
                task_name=task_data.get('task_name'),
                description=task_data.get('description'),
                batch_size=task_data.get('batch_size', 20000),
                timeout_seconds=task_data.get('timeout_seconds', 3600),
                priority=task_data.get('priority', 5)
            )
            session.add(task)
            session.flush()
            
            # 获取模型的表规则
            rules = session.query(ComparisonTableRule).filter(
                ComparisonTableRule.model_id == task_data['model_id'],
                ComparisonTableRule.is_active == True
            ).all()
            
            # 过滤选择的表（如果指定）
            selected_table_ids = task_data.get('selected_table_ids')
            if selected_table_ids:
                rules = [rule for rule in rules if rule.table_id in selected_table_ids]
            
            # 为每个表规则创建任务记录（单表设计）
            for rule in rules:
                task = ComparisonTask(
                    task_id=task_id,
                    user_id=user_id,
                    model_id=model_id,
                    table_rule_id=rule.id,
                    task_name=task_name,
                    description=description,
                    status=TaskStatus.PENDING.value
                )
                session.add(task)
            
            session.commit()
            return task_id
    
    def get_task_summary(self, task_id: str) -> Dict[str, Any]:
        """获取任务汇总信息（单表设计）"""
        with self.SessionLocal() as session:
            # 使用ComparisonTask的汇总方法
            return ComparisonTask.get_task_summary(session, task_id)
    
    async def execute_task(self, task_id: str) -> bool:
        """执行任务"""
        try:
            with self.SessionLocal() as session:
                # 获取任务和执行记录
                task = session.query(ComparisonTask).filter(
                    ComparisonTask.task_id == task_id
                ).first()
                
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                executions = session.query(ComparisonTaskExecution).filter(
                    ComparisonTaskExecution.task_id == task_id
                ).order_by(ComparisonTaskExecution.priority.desc()).all()
                
                # 更新任务状态
                task.status = TaskStatus.RUNNING.value
                task.started_at = datetime.now()
                session.commit()
                
                # 执行每个表的比对
                completed_count = 0
                total_count = len(executions)
                
                for execution in executions:
                    try:
                        # 更新执行状态
                        execution.status = TaskStatus.RUNNING.value
                        execution.start_time = datetime.now()
                        session.commit()

                        # 模拟执行过程
                        success = await self._execute_single_table(session, execution)

                        if success:
                            execution.status = TaskStatus.COMPLETED.value
                            execution.complete_time = datetime.now()
                            execution.progress_pct = 100.0
                            completed_count += 1
                        else:
                            execution.status = TaskStatus.FAILED.value
                            execution.complete_time = datetime.now()
                        
                        # 更新任务整体进度
                        task.progress_pct = (completed_count / total_count) * 100
                        session.commit()

                    except Exception as e:
                        execution.status = TaskStatus.FAILED.value
                        execution.error_msg = str(e)
                        execution.complete_time = datetime.now()
                        session.commit()
                        print(f"执行失败: {e}")

                # 完成任务
                task.status = TaskStatus.COMPLETED.value if completed_count == total_count else TaskStatus.FAILED.value
                task.complete_time = datetime.now()
                task.progress_pct = 100.0
                session.commit()
                
                return completed_count == total_count
                
        except Exception as e:
            print(f"执行任务失败: {e}")
            return False
    
    async def _execute_single_table(self, session, execution: ComparisonTaskExecution) -> bool:
        """执行单表比对"""
        try:
            # 获取表规则
            rule = session.query(ComparisonTableRule).filter(
                ComparisonTableRule.id == execution.rule_id
            ).first()

            print(f"开始执行表: {rule.table_id}")

            # 模拟执行过程
            for progress in [20, 40, 60, 80, 100]:
                execution.progress_pct = progress
                execution.current_step = f"处理进度 {progress}%"
                session.commit()
                await asyncio.sleep(0.3)  # 模拟处理时间

            # 模拟结果统计
            execution.total_records = 5000
            execution.processed_records = 5000
            execution.diff_records = 50
            execution.source_only = 10
            execution.target_only = 15
            execution.exec_time = 2.5
            
            print(f"完成执行表: {rule.table_id}")
            return True
            
        except Exception as e:
            print(f"单表执行失败: {e}")
            return False


# 使用示例
async def main():
    """主函数示例"""
    service = RefactoredComparisonService()
    
    # 创建用户
    with service.SessionLocal() as session:
        try:
            user = User(
                user_id='refactored_user',
                username='重构测试用户',
                email='<EMAIL>',
                password='refactored_hash',
                role='user'
            )
            session.add(user)
            session.commit()
            print(f"创建用户成功: {user.user_id}")
        except Exception as e:
            print(f"用户可能已存在: {e}")
    
    # 1. 配置阶段：创建连接
    source_conn_id = service.create_connection({
        'name': '生产数据库',
        'type': 'db2',
        'host': 'prod-db.example.com',
        'port': 50000,
        'username': 'user',
        'password': 'password',
        'database': 'prod_db'
    })
    
    target_conn_id = service.create_connection({
        'name': '测试数据库',
        'type': 'db2',
        'host': 'test-db.example.com',
        'port': 50000,
        'username': 'user',
        'password': 'password',
        'database': 'test_db'
    })
    
    # 2. 配置阶段：创建比对模型
    model_id = service.create_model({
        'name': '核心业务表比对模型',
        'description': '用于核心业务表的数据一致性检查',
        'source_connid': source_conn_id,
        'target_connid': target_conn_id,
        'cmp_type': 'content'
    })
    
    # 3. 配置阶段：添加表规则
    service.add_table_rule(model_id, {
        'table_id': 'users',
        'table_name': 'users',
        'sql_1': 'SELECT USER_ID AS KEY, u.* FROM users u',
        'sql_2': 'SELECT USER_ID AS KEY, u.* FROM users u',
        'remark': '用户表比对'
    })
    
    service.add_table_rule(model_id, {
        'table_id': 'orders',
        'table_name': 'orders',
        'sql_1': 'SELECT ORDER_ID AS KEY, o.* FROM orders o',
        'sql_2': 'SELECT ORDER_ID AS KEY, o.* FROM orders o',
        'remark': '订单表比对'
    })
    
    # 查看模型配置
    model_info = service.get_model_with_rules(model_id)
    print(f"模型配置: {model_info}")
    
    # 4. 执行阶段：基于模型创建任务
    task_id = service.create_task_from_model('refactored_user', {
        'model_id': model_id,
        'task_name': '每日数据一致性检查',
        'description': '基于核心业务表模型的定期检查',
        'priority': 8
    })
    
    print(f"创建任务成功: {task_id}")
    
    # 5. 执行阶段：查看任务详情
    task_info = service.get_task_with_executions(task_id)
    print(f"任务详情: {task_info}")
    
    # 6. 执行阶段：执行任务
    print("开始执行任务...")
    success = await service.execute_task(task_id)
    print(f"任务执行结果: {'成功' if success else '失败'}")
    
    # 7. 查看最终结果
    final_info = service.get_task_with_executions(task_id)
    print(f"最终结果: {final_info}")


if __name__ == "__main__":
    asyncio.run(main())
