# core/models.py
from dataclasses import dataclass
from typing import Any, Optional

@dataclass
class Record:
    """代表一条数据记录，使用__slots__优化内存使用。"""
    __slots__ = ['key', 'value']
    key: str
    value: Any

@dataclass
class DiffResult:
    """代表一条比对差异的结果。"""
    key: str
    status: str  # e.g., 'IN_A_ONLY', 'IN_B_ONLY', 'DIFFERENT'
    value_a: Optional[Any] = None
    value_b: Optional[Any] = None