"""
数据源适配器接口和实现

提供统一的数据源访问接口，支持不同类型的数据源。
"""
import re
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple

from app.models.db import DatabaseConnection
from app.models.logger import logger


class DataSourceAdapter(ABC):
    """数据源适配器接口"""

    @abstractmethod
    async def connect(self) -> bool:
        """
        连接到数据源

        Returns:
            bool: 连接是否成功
        """
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        """
        断开数据源连接

        Returns:
            None
        """
        pass

    @abstractmethod
    async def execute_query(self, query: str) -> List[Dict[str, Any]]:
        """
        执行查询

        Args:
            query: 查询语句

        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        pass

    @abstractmethod
    async def execute_scalar(self, query: str) -> Any:
        """
        执行标量查询

        Args:
            query: 查询语句

        Returns:
            Any: 查询结果
        """
        pass

    @abstractmethod
    async def get_metadata(self) -> Dict[str, Any]:
        """
        获取数据源元数据

        Returns:
            Dict[str, Any]: 元数据信息
        """
        pass


class RelationalDBAdapter(DataSourceAdapter):
    """关系型数据库适配器"""

    def __init__(self, connection: DatabaseConnection):
        """
        初始化关系型数据库适配器

        Args:
            connection: 数据库连接信息
        """
        self.connection = connection
        self.engine = None
        self._connected = False

        # 确定数据库类型
        self.db_type = connection.type.lower() if hasattr(connection, 'type') and connection.type else "db2"

        # 初始化数据库特定的SQL方言处理器
        self._dialect = self._get_dialect(self.db_type)

        logger.debug(f"初始化关系型数据库适配器: {self.db_type}")

    async def connect(self) -> bool:
        """
        连接到数据库

        Returns:
            bool: 连接是否成功
        """
        try:
            # 生成连接ID，用于日志和调试
            connection_id = f"{self.connection.host}:{self.connection.port}/{self.connection.database}"

            # 记录连接开始
            logger.debug(f"开始连接 {self.db_type} 数据库: {connection_id}")

            from app.core.db_utils import get_db_connection
            self.engine = await get_db_connection(self.connection)
            self._connected = self.engine is not None

            if self._connected:
                # 使用connection_id确保日志中显示的是正确的连接信息
                logger.info(f"成功连接到 {self.db_type} 数据库: {connection_id}")
            else:
                logger.error(f"连接到 {self.db_type} 数据库失败: {connection_id}")

            return self._connected
        except Exception as e:
            logger.error(f"连接 {self.db_type} 数据库失败: {str(e)}")
            return False

    async def disconnect(self) -> None:
        """
        断开数据库连接

        Returns:
            None
        """
        # 大多数情况下，数据库连接由连接池管理，不需要显式关闭
        if self._connected:
            logger.debug(f"断开 {self.db_type} 数据库连接")

        self.engine = None
        self._connected = False

    def _get_dialect(self, db_type: str) -> Dict[str, Any]:
        """
        获取数据库特定的SQL方言处理器

        Args:
            db_type: 数据库类型

        Returns:
            Dict[str, Any]: 数据库方言处理器
        """
        # 默认方言处理器
        default_dialect = {
            "limit_clause": "LIMIT {limit}",
            "offset_clause": "OFFSET {offset}",
            "pagination_clause": "LIMIT {limit} OFFSET {offset}",
            "date_format": "TO_CHAR({column}, 'YYYY-MM-DD')",
            "timestamp_format": "TO_CHAR({column}, 'YYYY-MM-DD HH24:MI:SS')",
            "concat_operator": "||",
            "supports_window_functions": True,
            "supports_common_table_expressions": True,
            "supports_merge": True,
            "supports_upsert": False,
            "supports_returning": False,
            "supports_json": False,
            "supports_arrays": False,
            "supports_lateral_joins": False,
            "supports_full_outer_join": True,
            "supports_grouping_sets": True,
            "supports_recursive_cte": True,
            "supports_row_level_security": False,
            "supports_materialized_views": False,
            "supports_partitioning": False,
            "supports_parallel_query": False,
            "supports_spatial": False,
            "supports_xml": False,
            "supports_full_text_search": False,
            "supports_savepoints": True,
            "supports_sequences": True,
            "supports_triggers": True,
            "supports_stored_procedures": True,
            "supports_user_defined_functions": True,
            "supports_user_defined_types": True,
            "supports_user_defined_aggregates": False,
            "supports_user_defined_operators": False,
            "supports_user_defined_casts": False,
            "supports_user_defined_collations": False,
            "supports_user_defined_domains": False,
            "supports_user_defined_rules": False,
            "supports_user_defined_schemas": True,
            "supports_user_defined_tablespaces": False,
            "supports_user_defined_roles": True,
            "supports_user_defined_privileges": True,
            "supports_user_defined_constraints": True,
            "supports_user_defined_indexes": True,
            "supports_user_defined_views": True,
            "supports_user_defined_tables": True,
            "supports_user_defined_columns": True,
            "supports_user_defined_datatypes": True,
            "supports_user_defined_comments": True,
            "supports_user_defined_defaults": True,
            "supports_user_defined_nulls": True,
            "supports_user_defined_primary_keys": True,
            "supports_user_defined_foreign_keys": True,
            "supports_user_defined_unique_constraints": True,
            "supports_user_defined_check_constraints": True,
            "supports_user_defined_exclusion_constraints": False,
            "supports_user_defined_identity_columns": True,
            "supports_user_defined_generated_columns": True,
            "supports_user_defined_computed_columns": True,
            "supports_user_defined_virtual_columns": False,
            "supports_user_defined_stored_columns": True,
            "supports_user_defined_column_defaults": True,
            "supports_user_defined_column_nulls": True,
            "supports_user_defined_column_comments": True,
            "supports_user_defined_column_collations": False,
            "supports_user_defined_column_storage": False,
            "supports_user_defined_column_compression": False,
            "supports_user_defined_column_encryption": False,
            "supports_user_defined_column_masking": False,
            "supports_user_defined_column_security": False,
            "supports_user_defined_column_privileges": True,
            "supports_user_defined_column_constraints": True,
            "supports_user_defined_column_indexes": True,
            "supports_user_defined_column_statistics": True,
            "supports_user_defined_column_histograms": False,
            "supports_user_defined_column_distributions": False,
            "supports_user_defined_column_dependencies": False,
            "supports_user_defined_column_references": False,
            "supports_user_defined_column_usage": False,
            "supports_user_defined_column_privileges_usage": False,
            "supports_user_defined_column_constraints_usage": False,
            "supports_user_defined_column_indexes_usage": False,
            "supports_user_defined_column_statistics_usage": False,
            "supports_user_defined_column_histograms_usage": False,
            "supports_user_defined_column_distributions_usage": False,
            "supports_user_defined_column_dependencies_usage": False,
            "supports_user_defined_column_references_usage": False,
            "supports_user_defined_column_usage_usage": False,
            "supports_user_defined_column_privileges_usage_usage": False,
            "supports_user_defined_column_constraints_usage_usage": False,
            "supports_user_defined_column_indexes_usage_usage": False,
            "supports_user_defined_column_statistics_usage_usage": False,
            "supports_user_defined_column_histograms_usage_usage": False,
            "supports_user_defined_column_distributions_usage_usage": False,
            "supports_user_defined_column_dependencies_usage_usage": False,
            "supports_user_defined_column_references_usage_usage": False,
            "supports_user_defined_column_usage_usage_usage": False,
        }

        # 数据库特定的方言处理器
        dialects = {
            "db2": {
                # DB2特定的方言
                "limit_clause": "FETCH FIRST {limit} ROWS ONLY",
                "offset_clause": "OFFSET {offset} ROWS",
                "pagination_clause": "OFFSET {offset} ROWS FETCH FIRST {limit} ROWS ONLY",
                "date_format": "VARCHAR_FORMAT({column}, 'YYYY-MM-DD')",
                "timestamp_format": "VARCHAR_FORMAT({column}, 'YYYY-MM-DD HH24:MI:SS')",
                "concat_operator": "||",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": True,
                "supports_upsert": False,
                "supports_returning": False,
                "supports_json": True,
                "supports_arrays": False,
                "supports_lateral_joins": True,
                "supports_full_outer_join": True,
                "supports_grouping_sets": True,
                "supports_recursive_cte": True,
                "supports_row_level_security": False,
                "supports_materialized_views": True,
                "supports_partitioning": True,
                "supports_parallel_query": True,
                "supports_spatial": True,
                "supports_xml": True,
                "supports_full_text_search": True,
            },
            "oracle": {
                # Oracle特定的方言
                "limit_clause": "FETCH FIRST {limit} ROWS ONLY",
                "offset_clause": "OFFSET {offset} ROWS",
                "pagination_clause": "OFFSET {offset} ROWS FETCH FIRST {limit} ROWS ONLY",
                "date_format": "TO_CHAR({column}, 'YYYY-MM-DD')",
                "timestamp_format": "TO_CHAR({column}, 'YYYY-MM-DD HH24:MI:SS')",
                "concat_operator": "||",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": True,
                "supports_upsert": False,
                "supports_returning": True,
                "supports_json": True,
                "supports_arrays": False,
                "supports_lateral_joins": True,
                "supports_full_outer_join": True,
                "supports_grouping_sets": True,
                "supports_recursive_cte": True,
                "supports_row_level_security": True,
                "supports_materialized_views": True,
                "supports_partitioning": True,
                "supports_parallel_query": True,
                "supports_spatial": True,
                "supports_xml": True,
                "supports_full_text_search": True,
            },
            "gaussdb": {
                # GaussDB特定的方言（基于PostgreSQL）
                "limit_clause": "LIMIT {limit}",
                "offset_clause": "OFFSET {offset}",
                "pagination_clause": "LIMIT {limit} OFFSET {offset}",
                "date_format": "TO_CHAR({column}, 'YYYY-MM-DD')",
                "timestamp_format": "TO_CHAR({column}, 'YYYY-MM-DD HH24:MI:SS')",
                "concat_operator": "||",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": True,
                "supports_upsert": True,
                "supports_returning": True,
                "supports_json": True,
                "supports_arrays": True,
                "supports_lateral_joins": True,
                "supports_full_outer_join": True,
                "supports_grouping_sets": True,
                "supports_recursive_cte": True,
                "supports_row_level_security": True,
                "supports_materialized_views": True,
                "supports_partitioning": True,
                "supports_parallel_query": True,
                "supports_spatial": True,
                "supports_xml": True,
                "supports_full_text_search": True,
            },
            "sqlserver": {
                # SQL Server特定的方言
                "limit_clause": "TOP {limit}",
                "offset_clause": "OFFSET {offset} ROWS",
                "pagination_clause": "OFFSET {offset} ROWS FETCH NEXT {limit} ROWS ONLY",
                "date_format": "CONVERT(VARCHAR, {column}, 23)",
                "timestamp_format": "CONVERT(VARCHAR, {column}, 120)",
                "concat_operator": "+",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": True,
                "supports_upsert": False,
                "supports_returning": True,
                "supports_json": True,
                "supports_arrays": False,
                "supports_lateral_joins": True,
                "supports_full_outer_join": True,
                "supports_grouping_sets": True,
                "supports_recursive_cte": True,
                "supports_row_level_security": True,
                "supports_materialized_views": False,
                "supports_partitioning": True,
                "supports_parallel_query": True,
                "supports_spatial": True,
                "supports_xml": True,
                "supports_full_text_search": True,
            },
            "postgres": {
                # PostgreSQL特定的方言
                "limit_clause": "LIMIT {limit}",
                "offset_clause": "OFFSET {offset}",
                "pagination_clause": "LIMIT {limit} OFFSET {offset}",
                "date_format": "TO_CHAR({column}, 'YYYY-MM-DD')",
                "timestamp_format": "TO_CHAR({column}, 'YYYY-MM-DD HH24:MI:SS')",
                "concat_operator": "||",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": True,
                "supports_upsert": True,
                "supports_returning": True,
                "supports_json": True,
                "supports_arrays": True,
                "supports_lateral_joins": True,
                "supports_full_outer_join": True,
                "supports_grouping_sets": True,
                "supports_recursive_cte": True,
                "supports_row_level_security": True,
                "supports_materialized_views": True,
                "supports_partitioning": True,
                "supports_parallel_query": True,
                "supports_spatial": True,
                "supports_xml": True,
                "supports_full_text_search": True,
            },
            "mysql": {
                # MySQL特定的方言
                "limit_clause": "LIMIT {limit}",
                "offset_clause": "OFFSET {offset}",
                "pagination_clause": "LIMIT {offset}, {limit}",
                "date_format": "DATE_FORMAT({column}, '%Y-%m-%d')",
                "timestamp_format": "DATE_FORMAT({column}, '%Y-%m-%d %H:%i:%s')",
                "concat_operator": "CONCAT",
                "supports_window_functions": True,
                "supports_common_table_expressions": True,
                "supports_merge": False,
                "supports_upsert": True,
                "supports_returning": False,
                "supports_json": True,
                "supports_arrays": False,
                "supports_lateral_joins": False,
                "supports_full_outer_join": False,
                "supports_grouping_sets": False,
                "supports_recursive_cte": True,
                "supports_row_level_security": False,
                "supports_materialized_views": False,
                "supports_partitioning": True,
                "supports_parallel_query": False,
                "supports_spatial": True,
                "supports_xml": False,
                "supports_full_text_search": True,
            },
        }

        # 返回数据库特定的方言处理器，如果不存在则返回默认方言处理器
        return dialects.get(db_type, default_dialect)

    async def execute_query(self, query: str) -> List[Dict[str, Any]]:
        """
        执行查询

        Args:
            query: SQL查询语句

        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        if not self._connected or not self.engine:
            raise RuntimeError("数据库未连接")

        # 根据数据库类型处理查询语句
        query = self._process_query(query)

        # 记录查询语句
        logger.debug(f"执行 {self.db_type} 查询: {query}")

        try:
            # 检查是否为DatabaseEngine实例
            if hasattr(self.engine, 'execute_query'):
                # 使用DatabaseEngine接口
                return await self.engine.execute_query(query)
            else:
                # 使用旧版连接对象
                conn = await self.engine.acquire()
                try:
                    # 尝试使用不同的接口方法
                    if hasattr(conn, 'fetch'):
                        result = await conn.fetch(query)
                        # 检查查询是否成功
                        if isinstance(result, dict) and result.get('success') is False:
                            # 查询失败，处理错误
                            error_info = result.get('error', {})
                            error_msg = error_info.get('message', '未知错误')
                            is_fatal = error_info.get('is_fatal', False)

                            logger.error(f"{self.db_type} 数据查询失败: {error_msg}")

                            if is_fatal:
                                # 如果是致命错误，抛出异常以触发致命错误处理
                                from app.core.error_utils import DatabaseError
                                raise DatabaseError(f"{self.db_type} 数据查询致命错误: {error_msg}")
                            else:
                                # 非致命错误，返回空数据集
                                return []
                        else:
                            # 查询成功，处理结果
                            return [dict(row) for row in result]
                    elif hasattr(conn, 'cursor'):
                        # 使用cursor接口
                        cursor = await conn.cursor()
                        try:
                            await cursor.execute(query)
                            rows = await cursor.fetchall()

                            # 获取列名
                            columns = [col[0] for col in cursor.description]

                            # 转换为字典列表
                            result = []
                            for row in rows:
                                result.append(dict(zip(columns, row)))

                            return result
                        finally:
                            await cursor.close()
                    else:
                        # 不支持的连接对象类型
                        logger.error(f"不支持的 {self.db_type} 数据库连接对象类型")
                        return []
                finally:
                    conn.release()
        except Exception as e:
            logger.error(f"{self.db_type} 数据查询失败: {str(e)}")
            # 返回空数据集
            return []

    def _process_query(self, query: str) -> str:
        """
        处理查询语句，根据数据库类型进行适配

        Args:
            query: 原始SQL查询语句

        Returns:
            str: 处理后的SQL查询语句
        """
        # 如果查询为空，直接返回
        if not query:
            return query

        # 移除查询末尾的分号，避免某些数据库的语法错误
        query = query.strip()
        if query.endswith(';'):
            query = query[:-1]

        # 根据数据库类型进行特殊处理
        if self.db_type == "db2":
            # DB2特殊处理
            # 替换LIMIT子句
            if " LIMIT " in query.upper():
                # 提取LIMIT值
                limit_match = re.search(r'LIMIT\s+(\d+)', query, re.IGNORECASE)
                if limit_match:
                    limit_value = limit_match.group(1)
                    # 替换LIMIT子句为FETCH FIRST n ROWS ONLY
                    query = re.sub(r'LIMIT\s+\d+', f"FETCH FIRST {limit_value} ROWS ONLY", query, flags=re.IGNORECASE)
        elif self.db_type == "sqlserver":
            # SQL Server特殊处理
            # 替换LIMIT子句
            if " LIMIT " in query.upper():
                # 提取LIMIT值
                limit_match = re.search(r'LIMIT\s+(\d+)', query, re.IGNORECASE)
                if limit_match:
                    limit_value = limit_match.group(1)
                    # 检查是否有ORDER BY子句
                    if " ORDER BY " in query.upper():
                        # 如果有ORDER BY子句，使用OFFSET 0 ROWS FETCH NEXT n ROWS ONLY
                        query = re.sub(r'LIMIT\s+\d+', f"OFFSET 0 ROWS FETCH NEXT {limit_value} ROWS ONLY", query, flags=re.IGNORECASE)
                    else:
                        # 如果没有ORDER BY子句，使用TOP n
                        # 找到SELECT关键字的位置
                        select_pos = query.upper().find("SELECT")
                        if select_pos >= 0:
                            # 插入TOP子句
                            query = query[:select_pos+6] + f" TOP {limit_value} " + query[select_pos+6:]
                            # 移除LIMIT子句
                            query = re.sub(r'LIMIT\s+\d+', "", query, flags=re.IGNORECASE)
        elif self.db_type == "oracle":
            # Oracle特殊处理
            # 替换LIMIT子句
            if " LIMIT " in query.upper():
                # 提取LIMIT值
                limit_match = re.search(r'LIMIT\s+(\d+)', query, re.IGNORECASE)
                if limit_match:
                    limit_value = limit_match.group(1)
                    # 替换LIMIT子句为FETCH FIRST n ROWS ONLY
                    query = re.sub(r'LIMIT\s+\d+', f"FETCH FIRST {limit_value} ROWS ONLY", query, flags=re.IGNORECASE)

        # 返回处理后的查询语句
        return query

    async def execute_scalar(self, query: str) -> Any:
        """
        执行标量查询

        Args:
            query: SQL查询语句

        Returns:
            Any: 查询结果
        """
        if not self._connected or not self.engine:
            raise RuntimeError("数据库未连接")

        # 根据数据库类型处理查询语句
        query = self._process_query(query)

        # 记录查询语句
        logger.debug(f"执行 {self.db_type} 标量查询: {query}")

        try:
            # 检查是否为DatabaseEngine实例
            if hasattr(self.engine, 'execute_scalar'):
                # 使用DatabaseEngine接口
                return await self.engine.execute_scalar(query)
            else:
                # 使用旧版连接对象
                conn = await self.engine.acquire()
                try:
                    # 兼容不同的旧版连接对象接口
                    if hasattr(conn, 'fetchval'):
                        return await conn.fetchval(query)
                    elif hasattr(conn, 'fetch'):
                        rows = await conn.fetch(query)
                        return rows[0][0] if rows and len(rows) > 0 else 0
                    else:
                        # 尝试使用cursor接口
                        cursor = await conn.cursor()
                        try:
                            await cursor.execute(query)
                            row = await cursor.fetchone()
                            return row[0] if row else 0
                        finally:
                            await cursor.close()
                finally:
                    conn.release()
        except Exception as e:
            error_msg = str(e).lower()

            # 检查是否为表不存在错误
            if any(keyword in error_msg for keyword in [
                'sql0204n', 'undefined name', 'table or view does not exist',
                'table does not exist', 'relation does not exist', 'object name'
            ]):
                logger.error(f"{self.db_type} 表不存在错误: {str(e)}")
                from app.core.error_utils import TableNotFoundError
                raise TableNotFoundError(f"表不存在: {str(e)}")

            # 检查SQL语法错误
            if any(keyword in error_msg for keyword in [
                'sql0206n', 'sql0104n', 'sql0901n', 'syntax error',
                'sqlstate=42601', 'sqlstate=42703'
            ]):
                logger.error(f"{self.db_type} SQL语法错误: {str(e)}")
                from app.core.error_utils import SQLSyntaxError
                raise SQLSyntaxError(f"SQL语法错误: {str(e)}", query)

            logger.error(f"{self.db_type} 标量查询失败: {str(e)}")
            # 对于其他错误，也抛出异常而不是返回默认值
            raise RuntimeError(f"数据库查询失败: {str(e)}")

    async def get_metadata(self) -> Dict[str, Any]:
        """
        获取数据库元数据

        Returns:
            Dict[str, Any]: 元数据信息
        """
        metadata = {
            "type": "relational",
            "database_type": self.db_type,
            "host": self.connection.host,
            "port": self.connection.port,
            "database": self.connection.database,
            "schema": getattr(self.connection, 'schema', None),
            "username": getattr(self.connection, 'username', None),
            "dialect": self._dialect,
            "connected": self._connected
        }

        # 如果已连接，尝试获取更多元数据
        if self._connected:
            try:
                # 获取数据库版本
                version_query = self._get_version_query()
                version = await self.execute_scalar(version_query)
                metadata["version"] = version

                # 获取数据库时区
                timezone_query = self._get_timezone_query()
                timezone = await self.execute_scalar(timezone_query)
                metadata["timezone"] = timezone

                # 获取数据库字符集
                charset_query = self._get_charset_query()
                charset = await self.execute_scalar(charset_query)
                metadata["charset"] = charset
            except Exception as e:
                logger.warning(f"获取数据库元数据失败: {str(e)}")

        return metadata

    def _get_version_query(self) -> str:
        """
        获取数据库版本查询语句

        Returns:
            str: 查询语句
        """
        if self.db_type == "db2":
            return "SELECT service_level FROM TABLE(sysproc.env_get_inst_info()) as INSTANCEINFO"
        elif self.db_type == "oracle":
            return "SELECT banner FROM v$version WHERE banner LIKE 'Oracle%'"
        elif self.db_type == "gaussdb":
            return "SELECT version()"
        elif self.db_type == "sqlserver":
            return "SELECT @@VERSION"
        elif self.db_type == "postgres":
            return "SELECT version()"
        elif self.db_type == "mysql":
            return "SELECT version()"
        else:
            return "SELECT 'Unknown'"

    def _get_timezone_query(self) -> str:
        """
        获取数据库时区查询语句

        Returns:
            str: 查询语句
        """
        if self.db_type == "db2":
            return "SELECT CURRENT TIMEZONE FROM SYSIBM.SYSDUMMY1"
        elif self.db_type == "oracle":
            return "SELECT DBTIMEZONE FROM DUAL"
        elif self.db_type == "gaussdb":
            return "SHOW timezone"
        elif self.db_type == "sqlserver":
            return "SELECT CURRENT_TIMEZONE()"
        elif self.db_type == "postgres":
            return "SHOW timezone"
        elif self.db_type == "mysql":
            return "SELECT @@time_zone"
        else:
            return "SELECT 'Unknown'"

    def _get_charset_query(self) -> str:
        """
        获取数据库字符集查询语句

        Returns:
            str: 查询语句
        """
        if self.db_type == "db2":
            return "SELECT codepage FROM syscat.databases WHERE database_name = CURRENT DATABASE"
        elif self.db_type == "oracle":
            return "SELECT value FROM nls_database_parameters WHERE parameter = 'NLS_CHARACTERSET'"
        elif self.db_type == "gaussdb":
            return "SHOW server_encoding"
        elif self.db_type == "sqlserver":
            return "SELECT SERVERPROPERTY('Collation')"
        elif self.db_type == "postgres":
            return "SHOW server_encoding"
        elif self.db_type == "mysql":
            return "SELECT @@character_set_database"
        else:
            return "SELECT 'Unknown'"
