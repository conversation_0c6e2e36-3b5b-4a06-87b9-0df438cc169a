"""
增强的比对引擎模块

支持文件输出的数据比对，包括：
- 差异数据分离存储到独立文件
- 支持AS KEY语法的比对逻辑
- 流式处理减少内存占用
- 实时进度追踪和错误处理
"""

import asyncio
import time
import re
from typing import Dict, List, Optional, AsyncGenerator
from datetime import datetime
from dataclasses import dataclass

from app.models.task import CompareConfig, ComparisonSummary
from app.models.db import DatabaseConnection
from app.core.file_storage import ComparisonFileStorage
from app.core.enhanced_sql_parser import EnhancedSQLParser, ComparisonKeyConfig
from app.core.db_utils import get_db_connection
from app.models.logger import logger

@dataclass
class ComparisonStats:
    """比对统计信息"""
    source_count: int = 0
    target_count: int = 0
    matched_count: int = 0
    source_only_count: int = 0
    target_only_count: int = 0
    differences_count: int = 0
    processed_batches: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

class EnhancedComparisonEngine:
    """增强的比对引擎，支持文件输出"""

    def __init__(self, task_id: str, batch_size: int = 1000):
        """
        初始化比对引擎

        Args:
            task_id: 任务ID
            batch_size: 批处理大小
        """
        self.task_id = task_id
        self.batch_size = batch_size
        self.file_storage = ComparisonFileStorage()
        self.sql_parser = EnhancedSQLParser()
        self.workspace = self.file_storage.create_task_workspace(task_id)
        self.stats = ComparisonStats()

    async def run_comparison_with_file_output(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig
    ) -> ComparisonSummary:
        """
        执行比对并输出到文件

        Args:
            source_conn: 源数据库连接
            target_conn: 目标数据库连接
            config: 比对配置

        Returns:
            ComparisonSummary: 比对结果摘要
        """
        try:
            self.stats.start_time = datetime.now()
            logger.info(f"开始增强比对任务 - 任务ID: {self.task_id}")

            # 保存任务元数据
            await self._save_task_metadata(source_conn, target_conn, config)

            # 解析AS KEY语法
            key_config = await self._parse_comparison_keys(config)

            # 保存键值配置
            await self._save_key_config(key_config)

            # 执行流式比对
            await self._execute_streaming_comparison(
                source_conn, target_conn, config, key_config
            )

            # 生成统计摘要
            summary = await self._generate_summary()

            # 保存摘要文件
            await self._save_summary(summary)

            self.stats.end_time = datetime.now()
            logger.info(f"增强比对任务完成 - 任务ID: {self.task_id}")

            return summary

        except Exception as e:
            logger.error(f"增强比对任务失败 - 任务ID: {self.task_id}, 错误: {str(e)}")
            raise

    async def _save_task_metadata(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig
    ):
        """保存任务元数据"""
        try:
            task_info = {
                'task_id': self.task_id,
                'source_connection': {
                    'type': source_conn.type,
                    'host': source_conn.host,
                    'port': source_conn.port,
                    'database': source_conn.database
                },
                'target_connection': {
                    'type': target_conn.type,
                    'host': target_conn.host,
                    'port': target_conn.port,
                    'database': target_conn.database
                },
                'comparison_config': config.model_dump(),
                'batch_size': self.batch_size,
                'start_time': datetime.now().isoformat()
            }

            await self.file_storage.write_metadata(
                self.task_id, 'task_info', task_info
            )

            await self.file_storage.write_metadata(
                self.task_id, 'comparison_config', config.model_dump()
            )

            logger.debug(f"任务元数据已保存 - 任务ID: {self.task_id}")

        except Exception as e:
            logger.error(f"保存任务元数据失败: {str(e)}")
            raise

    async def _parse_comparison_keys(self, config: CompareConfig) -> Dict[str, ComparisonKeyConfig]:
        """解析比对键值配置"""
        try:
            key_configs = {}

            # 解析源查询的KEY配置
            if config.source_query:
                source_key_config = self.sql_parser.extract_comparison_key(config.source_query)
                key_configs['source'] = source_key_config
                logger.debug(f"源查询KEY配置: {source_key_config.key_fields}")

            # 解析目标查询的KEY配置
            if config.target_query:
                target_key_config = self.sql_parser.extract_comparison_key(config.target_query)
                key_configs['target'] = target_key_config
                logger.debug(f"目标查询KEY配置: {target_key_config.key_fields}")

            # 验证KEY配置一致性
            if 'source' in key_configs and 'target' in key_configs:
                self._validate_key_consistency(key_configs['source'], key_configs['target'])

            return key_configs

        except Exception as e:
            logger.error(f"解析比对键值配置失败: {str(e)}")
            raise

    def _validate_key_consistency(self, source_config: ComparisonKeyConfig,
                                 target_config: ComparisonKeyConfig):
        """验证源和目标的KEY配置一致性"""
        if len(source_config.key_fields) != len(target_config.key_fields):
            logger.warning(
                f"源和目标KEY字段数量不一致: "
                f"源={len(source_config.key_fields)}, 目标={len(target_config.key_fields)}"
            )

        if source_config.is_composite != target_config.is_composite:
            logger.warning("源和目标KEY复合性不一致")

    async def _save_key_config(self, key_configs: Dict[str, ComparisonKeyConfig]):
        """保存键值配置"""
        try:
            config_data = {}

            for conn_type, key_config in key_configs.items():
                config_data[conn_type] = {
                    'key_expression': key_config.key_expression,
                    'key_fields': key_config.key_fields,
                    'is_composite': key_config.is_composite,
                    'expression_type': key_config.expression_type.value,
                    'parsed_successfully': key_config.parsed_successfully,
                    'error_message': key_config.error_message
                }

            await self.file_storage.write_metadata(
                self.task_id, 'key_config', config_data
            )

            logger.debug(f"键值配置已保存 - 任务ID: {self.task_id}")

        except Exception as e:
            logger.error(f"保存键值配置失败: {str(e)}")
            raise

    async def _execute_streaming_comparison(
        self,
        source_conn: DatabaseConnection,
        target_conn: DatabaseConnection,
        config: CompareConfig,
        key_configs: Dict[str, ComparisonKeyConfig]
    ):
        """执行流式比对"""
        try:
            logger.info(f"开始流式比对 - 任务ID: {self.task_id}")

            # 获取数据库连接
            source_engine = await get_db_connection(source_conn)
            target_engine = await get_db_connection(target_conn)

            # 构建查询语句
            source_query = self._build_comparison_query(config.source_query, key_configs.get('source'))
            target_query = self._build_comparison_query(config.target_query, key_configs.get('target'))

            logger.debug(f"源查询: {source_query}")
            logger.debug(f"目标查询: {target_query}")

            # 流式获取数据并比对
            async for batch_result in self._stream_compare_data(
                source_engine, target_engine, source_query, target_query, key_configs
            ):
                # 写入差异文件
                await self._write_batch_results(batch_result)

                # 更新统计
                self._update_stats(batch_result)

                self.stats.processed_batches += 1

                if self.stats.processed_batches % 10 == 0:
                    logger.info(f"已处理 {self.stats.processed_batches} 个批次")

            logger.info(f"流式比对完成 - 任务ID: {self.task_id}")

        except Exception as e:
            logger.error(f"流式比对失败: {str(e)}")
            raise

    def _build_comparison_query(self, original_query: str,
                               key_config: Optional[ComparisonKeyConfig]) -> str:
        """构建比对查询语句"""
        if not key_config or not key_config.parsed_successfully:
            # 如果没有KEY配置，使用原始查询
            return original_query

        # 如果查询中已经包含AS KEY，直接使用
        if 'as key' in original_query.lower() or 'as KEY' in original_query:
            return original_query

        # 否则，尝试添加KEY表达式
        try:
            # 简单的SQL修改：在SELECT后添加KEY表达式
            key_expr = self.sql_parser.get_key_expression_sql(key_config)

            # 查找SELECT关键字
            select_match = re.search(r'select\s+', original_query, re.IGNORECASE)
            if select_match:
                insert_pos = select_match.end()
                modified_query = (
                    original_query[:insert_pos] +
                    key_expr + ', ' +
                    original_query[insert_pos:]
                )
                return modified_query

        except Exception as e:
            logger.warning(f"修改查询添加KEY表达式失败: {str(e)}")

        return original_query

    async def _stream_compare_data(
        self,
        source_engine,
        target_engine,
        source_query: str,
        target_query: str,
        key_configs: Dict[str, ComparisonKeyConfig]
    ) -> AsyncGenerator[Dict[str, List[Dict]], None]:
        """流式比对数据"""
        try:
            # 获取源数据和目标数据
            source_data = await self._fetch_all_data(source_engine, source_query)
            target_data = await self._fetch_all_data(target_engine, target_query)

            logger.info(f"源数据记录数: {len(source_data)}")
            logger.info(f"目标数据记录数: {len(target_data)}")

            self.stats.source_count = len(source_data)
            self.stats.target_count = len(target_data)

            # 构建索引
            source_index = self._build_data_index(source_data, key_configs.get('source'))
            target_index = self._build_data_index(target_data, key_configs.get('target'))

            # 比对数据
            batch_result = await self._compare_indexed_data(source_index, target_index)

            yield batch_result

        except Exception as e:
            logger.error(f"流式比对数据失败: {str(e)}")
            raise

    async def _fetch_all_data(self, engine, query: str) -> List[Dict]:
        """获取所有数据"""
        try:
            async with engine.acquire() as conn:
                result = await conn.fetch(query)
                return [dict(row) for row in result]

        except Exception as e:
            logger.error(f"获取数据失败: {str(e)}")
            raise

    def _build_data_index(self, data: List[Dict],
                         key_config: Optional[ComparisonKeyConfig]) -> Dict[str, Dict]:
        """构建数据索引"""
        index = {}

        for record in data:
            try:
                # 提取键值
                key = self._extract_record_key(record, key_config)
                if key:
                    index[key] = record

            except Exception as e:
                logger.warning(f"构建索引时跳过记录: {str(e)}")
                continue

        return index

    def _extract_record_key(self, record: Dict,
                           key_config: Optional[ComparisonKeyConfig]) -> Optional[str]:
        """从记录中提取键值"""
        try:
            # 如果有KEY字段，直接使用
            if 'KEY' in record:
                return str(record['KEY'])

            # 如果有key字段，使用小写版本
            if 'key' in record:
                return str(record['key'])

            # 如果有KEY配置，根据配置提取
            if key_config and key_config.key_fields:
                if len(key_config.key_fields) == 1:
                    field = key_config.key_fields[0]
                    if field in record:
                        return str(record[field])
                else:
                    # 复合键值
                    key_parts = []
                    for field in key_config.key_fields:
                        if field in record:
                            key_parts.append(str(record[field]))
                    if key_parts:
                        return '-'.join(key_parts)

            # 默认使用第一个字段
            if record:
                first_key = next(iter(record))
                return str(record[first_key])

            return None

        except Exception as e:
            logger.warning(f"提取记录键值失败: {str(e)}")
            return None

    async def _compare_indexed_data(self, source_index: Dict[str, Dict],
                                   target_index: Dict[str, Dict]) -> Dict[str, List[Dict]]:
        """比对索引数据"""
        try:
            result = {
                'source_only': [],
                'target_only': [],
                'differences': [],
                'matched': []
            }

            source_keys = set(source_index.keys())
            target_keys = set(target_index.keys())

            # 仅源存在的记录
            source_only_keys = source_keys - target_keys
            for key in source_only_keys:
                result['source_only'].append({
                    'key': key,
                    'record': source_index[key]
                })

            # 仅目标存在的记录
            target_only_keys = target_keys - source_keys
            for key in target_only_keys:
                result['target_only'].append({
                    'key': key,
                    'record': target_index[key]
                })

            # 两边都存在的记录
            common_keys = source_keys & target_keys
            for key in common_keys:
                source_record = source_index[key]
                target_record = target_index[key]

                # 比较记录内容
                diff_fields = self._compare_records(source_record, target_record)

                if diff_fields:
                    # 有差异
                    result['differences'].append({
                        'key': key,
                        'source_record': source_record,
                        'target_record': target_record,
                        'diff_fields': diff_fields
                    })
                else:
                    # 匹配
                    result['matched'].append({
                        'key': key,
                        'record': source_record
                    })

            return result

        except Exception as e:
            logger.error(f"比对索引数据失败: {str(e)}")
            raise

    def _compare_records(self, source_record: Dict, target_record: Dict) -> List[str]:
        """比较两条记录，返回差异字段列表"""
        diff_fields = []

        # 获取所有字段
        all_fields = set(source_record.keys()) | set(target_record.keys())

        for field in all_fields:
            # 跳过KEY字段（这是组合主键，不参与内容比较）
            if field.upper() == 'KEY':
                continue

            source_value = source_record.get(field)
            target_value = target_record.get(field)

            # 比较值，处理None值和类型转换
            if not self._values_equal(source_value, target_value):
                diff_fields.append(field)
                logger.debug(f"字段 {field} 有差异: 源值={source_value} ({type(source_value)}), 目标值={target_value} ({type(target_value)})")

        return diff_fields

    def _values_equal(self, value1, value2) -> bool:
        """比较两个值是否相等，处理类型转换和None值"""
        # 如果两个值都是None，认为相等
        if value1 is None and value2 is None:
            return True

        # 如果一个是None另一个不是，认为不相等
        if value1 is None or value2 is None:
            return False

        # 如果类型相同，直接比较
        if type(value1) == type(value2):
            return value1 == value2

        # 处理数字类型的比较
        try:
            if isinstance(value1, (int, float)) and isinstance(value2, (int, float)):
                return float(value1) == float(value2)
        except (ValueError, TypeError):
            pass

        # 处理字符串比较（去除前后空格）
        try:
            str1 = str(value1).strip() if value1 is not None else ""
            str2 = str(value2).strip() if value2 is not None else ""
            return str1 == str2
        except:
            pass

        # 默认情况下直接比较
        return value1 == value2

    async def _write_batch_results(self, batch_result: Dict[str, List[Dict]]):
        """写入批次结果到文件"""
        try:
            for diff_type, records in batch_result.items():
                if records:
                    await self.file_storage.write_differences(
                        self.task_id, diff_type, records
                    )

        except Exception as e:
            logger.error(f"写入批次结果失败: {str(e)}")
            raise

    def _update_stats(self, batch_result: Dict[str, List[Dict]]):
        """更新统计信息"""
        self.stats.source_only_count += len(batch_result.get('source_only', []))
        self.stats.target_only_count += len(batch_result.get('target_only', []))
        self.stats.differences_count += len(batch_result.get('differences', []))
        self.stats.matched_count += len(batch_result.get('matched', []))

    async def _generate_summary(self) -> ComparisonSummary:
        """生成比对摘要"""
        try:
            execution_time = 0
            if self.stats.start_time and self.stats.end_time:
                execution_time = (self.stats.end_time - self.stats.start_time).total_seconds()

            summary = ComparisonSummary(
                source_count=self.stats.source_count,
                target_count=self.stats.target_count,
                matched_count=self.stats.matched_count,
                diff_count=self.stats.differences_count,
                source_only_count=self.stats.source_only_count,
                target_only_count=self.stats.target_only_count
            )

            logger.info(f"比对摘要生成完成 - 任务ID: {self.task_id}")
            return summary

        except Exception as e:
            logger.error(f"生成比对摘要失败: {str(e)}")
            raise

    async def _save_summary(self, summary: ComparisonSummary):
        """保存比对摘要"""
        try:
            summary_data = {
                'source_count': summary.source_count,
                'target_count': summary.target_count,
                'matched_count': summary.matched_count,
                'differences_count': summary.diff_count,
                'source_only_count': summary.source_only_count,
                'target_only_count': summary.target_only_count,
                'processed_batches': self.stats.processed_batches,
                'execution_time_seconds': (
                    (self.stats.end_time - self.stats.start_time).total_seconds()
                    if self.stats.start_time and self.stats.end_time else 0
                )
            }

            await self.file_storage.write_summary(self.task_id, summary_data)

        except Exception as e:
            logger.error(f"保存比对摘要失败: {str(e)}")
            raise
