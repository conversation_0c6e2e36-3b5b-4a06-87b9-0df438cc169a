"""
安全的SQL构建器

防止SQL注入攻击，提供安全的SQL构建功能
"""
import re
import logging
from typing import List, Optional, Dict, Any

logger = logging.getLogger(__name__)


class SQLInjectionError(Exception):
    """SQL注入风险异常"""
    pass


class SecureSQLBuilder:
    """安全的SQL构建器"""
    
    # 危险的SQL关键字
    DANGEROUS_KEYWORDS = [
        'DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER',
        'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', '--', '/*', '*/',
        'TRUNCATE', 'GRANT', 'REVOKE', 'SHUTDOWN', 'BACKUP',
        'RESTORE', 'BULK', 'OPENROWSET', 'OPENDATASOURCE'
    ]
    
    # 允许的WHERE子句操作符
    ALLOWED_OPERATORS = [
        '=', '!=', '<>', '<', '>', '<=', '>=', 'LIKE', 'IN', 'NOT IN',
        'IS NULL', 'IS NOT NULL', 'BETWEEN', 'AND', 'OR', 'NOT'
    ]
    
    @staticmethod
    def build_comparison_sql(
        table_name: str,
        primary_key: str,
        columns: List[str],
        where_clause: Optional[str] = None,
        limit: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> str:
        """
        构建安全的比对SQL
        
        Args:
            table_name: 表名
            primary_key: 主键字段名
            columns: 要查询的字段列表
            where_clause: WHERE条件（可选）
            limit: 限制记录数（可选）
            order_by: 排序字段（可选）
            
        Returns:
            安全的SQL语句
            
        Raises:
            SQLInjectionError: 检测到SQL注入风险时抛出
        """
        try:
            # 验证表名
            SecureSQLBuilder._validate_identifier(table_name, "表名")
            
            # 验证主键字段名
            SecureSQLBuilder._validate_identifier(primary_key, "主键字段")
            
            # 验证所有字段名
            for col in columns:
                SecureSQLBuilder._validate_identifier(col, "字段名")
            
            # 验证排序字段
            if order_by:
                SecureSQLBuilder._validate_identifier(order_by, "排序字段")
            
            # 构建基础SQL
            columns_str = ", ".join(columns)
            sql_parts = [
                f"SELECT {primary_key} AS KEY, {columns_str}",
                f"FROM {table_name}"
            ]
            
            # 添加WHERE子句
            if where_clause:
                validated_where = SecureSQLBuilder._validate_where_clause(where_clause)
                sql_parts.append(f"WHERE {validated_where}")
            
            # 添加ORDER BY
            if order_by:
                sql_parts.append(f"ORDER BY {order_by}")
            
            # 添加LIMIT
            if limit:
                if not isinstance(limit, int) or limit <= 0:
                    raise SQLInjectionError(f"无效的LIMIT值: {limit}")
                sql_parts.append(f"LIMIT {limit}")
            
            sql = " ".join(sql_parts)
            
            logger.debug(f"构建的安全SQL: {sql}")
            return sql
            
        except Exception as e:
            logger.error(f"SQL构建失败: {str(e)}")
            raise SQLInjectionError(f"SQL构建失败: {str(e)}")
    
    @staticmethod
    def _validate_identifier(identifier: str, identifier_type: str) -> None:
        """
        验证SQL标识符是否安全
        
        Args:
            identifier: 要验证的标识符
            identifier_type: 标识符类型（用于错误消息）
            
        Raises:
            SQLInjectionError: 标识符不安全时抛出
        """
        if not identifier or not isinstance(identifier, str):
            raise SQLInjectionError(f"无效的{identifier_type}: {identifier}")
        
        # 检查长度
        if len(identifier) > 64:  # 大多数数据库的标识符长度限制
            raise SQLInjectionError(f"{identifier_type}过长: {identifier}")
        
        # 检查字符组成：只允许字母、数字、下划线，且不能以数字开头
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', identifier):
            raise SQLInjectionError(
                f"无效的{identifier_type}: {identifier}. "
                f"只允许字母、数字、下划线，且不能以数字开头"
            )
        
        # 检查是否为SQL关键字
        if identifier.upper() in SecureSQLBuilder.DANGEROUS_KEYWORDS:
            raise SQLInjectionError(f"{identifier_type}不能使用SQL关键字: {identifier}")
    
    @staticmethod
    def _validate_where_clause(where_clause: str) -> str:
        """
        验证WHERE子句是否安全
        
        Args:
            where_clause: WHERE条件
            
        Returns:
            验证后的WHERE子句
            
        Raises:
            SQLInjectionError: WHERE子句不安全时抛出
        """
        if not where_clause or not isinstance(where_clause, str):
            raise SQLInjectionError(f"无效的WHERE子句: {where_clause}")
        
        # 检查长度
        if len(where_clause) > 1000:
            raise SQLInjectionError("WHERE子句过长")
        
        # 检查危险关键字
        clause_upper = where_clause.upper()
        for keyword in SecureSQLBuilder.DANGEROUS_KEYWORDS:
            if keyword in clause_upper:
                raise SQLInjectionError(f"WHERE子句包含危险关键字: {keyword}")
        
        # 检查是否包含注释符号
        if '--' in where_clause or '/*' in where_clause or '*/' in where_clause:
            raise SQLInjectionError("WHERE子句不能包含注释符号")
        
        # 检查是否包含分号（防止多语句执行）
        if ';' in where_clause:
            raise SQLInjectionError("WHERE子句不能包含分号")
        
        # 基础的括号匹配检查
        if where_clause.count('(') != where_clause.count(')'):
            raise SQLInjectionError("WHERE子句括号不匹配")
        
        # 检查引号匹配
        single_quotes = where_clause.count("'")
        double_quotes = where_clause.count('"')
        if single_quotes % 2 != 0 or double_quotes % 2 != 0:
            raise SQLInjectionError("WHERE子句引号不匹配")
        
        return where_clause
    
    @staticmethod
    def build_parameterized_query(
        template: str,
        parameters: Dict[str, Any]
    ) -> tuple[str, Dict[str, Any]]:
        """
        构建参数化查询（推荐使用）
        
        Args:
            template: SQL模板，使用 :param_name 作为参数占位符
            parameters: 参数字典
            
        Returns:
            (SQL语句, 参数字典)
        """
        try:
            # 验证模板
            if not template or not isinstance(template, str):
                raise SQLInjectionError("无效的SQL模板")
            
            # 检查模板中的危险关键字
            template_upper = template.upper()
            dangerous_found = [
                keyword for keyword in SecureSQLBuilder.DANGEROUS_KEYWORDS
                if keyword in template_upper
            ]
            
            if dangerous_found:
                logger.warning(f"SQL模板包含潜在危险关键字: {dangerous_found}")
            
            # 验证参数
            validated_params = {}
            for key, value in parameters.items():
                # 验证参数名
                if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                    raise SQLInjectionError(f"无效的参数名: {key}")
                
                # 验证参数值
                if isinstance(value, str):
                    # 字符串参数不能包含危险字符
                    if any(char in value for char in [';', '--', '/*', '*/']):
                        raise SQLInjectionError(f"参数值包含危险字符: {key}={value}")
                
                validated_params[key] = value
            
            return template, validated_params
            
        except Exception as e:
            logger.error(f"参数化查询构建失败: {str(e)}")
            raise SQLInjectionError(f"参数化查询构建失败: {str(e)}")
    
    @staticmethod
    def escape_string_literal(value: str) -> str:
        """
        转义字符串字面量
        
        Args:
            value: 要转义的字符串
            
        Returns:
            转义后的字符串
        """
        if not isinstance(value, str):
            raise SQLInjectionError("只能转义字符串类型的值")
        
        # 转义单引号
        escaped = value.replace("'", "''")
        
        # 移除危险字符
        dangerous_chars = [';', '--', '/*', '*/']
        for char in dangerous_chars:
            if char in escaped:
                logger.warning(f"移除字符串中的危险字符: {char}")
                escaped = escaped.replace(char, '')
        
        return f"'{escaped}'"


class SQLValidationResult:
    """SQL验证结果"""
    
    def __init__(self, is_safe: bool, message: str, sql: Optional[str] = None):
        self.is_safe = is_safe
        self.message = message
        self.sql = sql
    
    def __bool__(self):
        return self.is_safe


def validate_sql_safety(sql: str) -> SQLValidationResult:
    """
    验证SQL语句的安全性
    
    Args:
        sql: 要验证的SQL语句
        
    Returns:
        验证结果
    """
    try:
        if not sql or not isinstance(sql, str):
            return SQLValidationResult(False, "无效的SQL语句")
        
        sql_upper = sql.upper().strip()
        
        # 检查是否为SELECT语句
        if not sql_upper.startswith('SELECT'):
            return SQLValidationResult(False, "只允许SELECT语句")
        
        # 检查危险关键字
        for keyword in SecureSQLBuilder.DANGEROUS_KEYWORDS:
            if keyword in sql_upper:
                return SQLValidationResult(
                    False, 
                    f"SQL包含危险关键字: {keyword}"
                )
        
        # 检查多语句
        if ';' in sql and not sql.strip().endswith(';'):
            return SQLValidationResult(False, "不允许多语句执行")
        
        # 检查注释
        if '--' in sql or '/*' in sql:
            return SQLValidationResult(False, "不允许包含注释")
        
        return SQLValidationResult(True, "SQL语句安全", sql)
        
    except Exception as e:
        return SQLValidationResult(False, f"SQL验证失败: {str(e)}")
