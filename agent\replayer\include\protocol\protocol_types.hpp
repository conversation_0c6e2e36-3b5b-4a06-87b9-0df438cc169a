#pragma once

#include "core/data_structures.hpp"
#include <string_view>
#include <variant>
#include <optional>

namespace arkreplay {
namespace protocol {

// ==================== 协议字段类型 ====================

/**
 * 协议字段值类型 - 使用variant支持多种类型
 */
using FieldValue = std::variant<
    std::string,
    int64_t,
    double,
    bool,
    std::vector<uint8_t>
>;

/**
 * 协议字段结构
 */
struct ProtocolField {
    uint32_t tag;                    // 字段标签
    std::string name;                // 字段名称
    FieldValue value;                // 字段值
    bool is_required;                // 是否必需
    
    ProtocolField(uint32_t t, const std::string& n, FieldValue v, bool req = false)
        : tag(t), name(n), value(std::move(v)), is_required(req) {}
};

/**
 * 协议消息结构
 */
class ProtocolMessage {
private:
    std::vector<ProtocolField> fields_;
    std::unordered_map<uint32_t, size_t> tag_index_; // 标签到索引的映射

public:
    ProtocolMessage() = default;
    
    void add_field(ProtocolField field) {
        tag_index_[field.tag] = fields_.size();
        fields_.emplace_back(std::move(field));
    }
    
    std::optional<const ProtocolField*> get_field(uint32_t tag) const {
        auto it = tag_index_.find(tag);
        if (it != tag_index_.end()) {
            return &fields_[it->second];
        }
        return std::nullopt;
    }
    
    const std::vector<ProtocolField>& get_all_fields() const { return fields_; }
    
    bool has_field(uint32_t tag) const {
        return tag_index_.find(tag) != tag_index_.end();
    }
    
    size_t field_count() const { return fields_.size(); }
    
    void clear() {
        fields_.clear();
        tag_index_.clear();
    }
};

// ==================== FIX协议特定结构 ====================

namespace fix {

/**
 * FIX消息类型
 */
enum class MsgType : char {
    HEARTBEAT = '0',
    TEST_REQUEST = '1',
    RESEND_REQUEST = '2',
    REJECT = '3',
    SEQUENCE_RESET = '4',
    LOGOUT = '5',
    LOGON = 'A',
    NEW_ORDER_SINGLE = 'D',
    EXECUTION_REPORT = '8',
    ORDER_CANCEL_REQUEST = 'F'
};

/**
 * FIX字段标签常量
 */
namespace tags {
    constexpr uint32_t BEGIN_STRING = 8;
    constexpr uint32_t BODY_LENGTH = 9;
    constexpr uint32_t MSG_TYPE = 35;
    constexpr uint32_t SENDER_COMP_ID = 49;
    constexpr uint32_t TARGET_COMP_ID = 56;
    constexpr uint32_t MSG_SEQ_NUM = 34;
    constexpr uint32_t SENDING_TIME = 52;
    constexpr uint32_t CHECKSUM = 10;
}

/**
 * FIX消息头
 */
struct FixHeader {
    std::string begin_string;        // BeginString(8)
    uint32_t body_length;           // BodyLength(9)
    MsgType msg_type;               // MsgType(35)
    std::string sender_comp_id;     // SenderCompID(49)
    std::string target_comp_id;     // TargetCompID(56)
    uint32_t msg_seq_num;           // MsgSeqNum(34)
    std::string sending_time;       // SendingTime(52)
    
    FixHeader() : body_length(0), msg_type(MsgType::HEARTBEAT), msg_seq_num(0) {}
};

} // namespace fix

// ==================== KCXP协议特定结构 ====================

namespace kcxp {

/**
 * KCXP消息类型
 */
enum class MessageType : uint16_t {
    UNKNOWN = 0x0000,
    LOGON = 0x0001,
    LOGOUT = 0x0002,
    HEARTBEAT = 0x0003,
    ORDER = 0x1001,
    CANCEL = 0x1002,
    MODIFY = 0x1003,
    EXECUTION = 0x2001,
    REJECT = 0x2002
};

/**
 * KCXP消息头
 */
struct alignas(8) KcxpHeader {
    uint32_t length;                // 消息长度
    uint16_t msg_type;              // 消息类型
    uint16_t version;               // 协议版本
    uint64_t sequence;              // 序列号
    uint64_t timestamp;             // 时间戳
    
    KcxpHeader() : length(0), msg_type(0), version(1), sequence(0), timestamp(0) {}
};

static_assert(sizeof(KcxpHeader) == 24, "KcxpHeader must be 24 bytes");

} // namespace kcxp

// ==================== MID协议特定结构 ====================

namespace mid {

/**
 * MID消息类型
 */
enum class MessageType : uint8_t {
    UNKNOWN = 0x00,
    LOGIN = 0x01,
    LOGOUT = 0x02,
    HEARTBEAT = 0x03,
    ORDER_ENTRY = 0x10,
    ORDER_CANCEL = 0x11,
    ORDER_MODIFY = 0x12,
    TRADE_REPORT = 0x20,
    ORDER_STATUS = 0x21
};

/**
 * MID消息头
 */
struct alignas(4) MidHeader {
    uint16_t length;                // 消息长度
    uint8_t msg_type;               // 消息类型
    uint8_t version;                // 协议版本
    uint32_t sequence;              // 序列号
    uint32_t timestamp;             // 时间戳（秒）
    uint16_t checksum;              // 校验和
    
    MidHeader() : length(0), msg_type(0), version(1), sequence(0), timestamp(0), checksum(0) {}
};

static_assert(sizeof(MidHeader) == 14, "MidHeader must be 14 bytes");

} // namespace mid

// ==================== 协议解析器接口 ====================

/**
 * 协议解析结果
 */
struct ParseResult {
    bool success;
    std::string error_message;
    size_t bytes_consumed;
    ProtocolMessage message;
    
    ParseResult() : success(false), bytes_consumed(0) {}
    
    static ParseResult success_result(size_t consumed, ProtocolMessage msg) {
        ParseResult result;
        result.success = true;
        result.bytes_consumed = consumed;
        result.message = std::move(msg);
        return result;
    }
    
    static ParseResult error_result(const std::string& error) {
        ParseResult result;
        result.success = false;
        result.error_message = error;
        return result;
    }
};

/**
 * 协议序列化结果
 */
struct SerializeResult {
    bool success;
    std::string error_message;
    std::vector<uint8_t> data;
    
    SerializeResult() : success(false) {}
    
    static SerializeResult success_result(std::vector<uint8_t> serialized_data) {
        SerializeResult result;
        result.success = true;
        result.data = std::move(serialized_data);
        return result;
    }
    
    static SerializeResult error_result(const std::string& error) {
        SerializeResult result;
        result.success = false;
        result.error_message = error;
        return result;
    }
};

/**
 * 协议解析器基类
 */
class ProtocolParser {
public:
    virtual ~ProtocolParser() = default;
    
    virtual ParseResult parse(const uint8_t* data, size_t length) = 0;
    virtual SerializeResult serialize(const ProtocolMessage& message) = 0;
    virtual core::ProtocolType get_protocol_type() const = 0;
    virtual std::string get_protocol_name() const = 0;
    virtual bool validate_message(const ProtocolMessage& message) const = 0;
};

} // namespace protocol
} // namespace arkreplay
