import os
import sys
import time
import uvicorn
import traceback
import asyncio
from typing import Dict, List, Any, Optional, Union
from contextlib import asynccontextmanager
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(SCRIPT_DIR))
from fastapi import FastAPI, Request, status, Depends, Header, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import APIKeyHeader

from app.core.config import settings
from app.models.logger import logger
from app.api.api_v1.api import api_router
from app.core.error_reporter import error_reporter, report_error, report_system_error
from app.core.server_notifier import server_notifier
from app.core.dependencies import initialize_services, get_task_manager

# 定义API密钥头
API_KEY = os.environ.get("DB_AGENT_API_KEY", "DB-DUMMY-AGENT-KEY")
api_key_header = APIKeyHeader(name="Authorization", auto_error=False)

# 添加配置
settings.SERVER_API_BASE_URL = os.environ.get("SERVER_API_URL", "http://localhost:8000")

# API密钥验证依赖项
async def verify_api_key(api_key: Optional[str] = Depends(api_key_header)):
    if not api_key:
        logger.warning("缺少API密钥")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供API密钥",
            headers={"WWW-Authenticate": "Bearer"},
        )

    expected_key = f"Bearer {API_KEY}"
    if api_key != expected_key:
        logger.warning("API密钥无效")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API密钥无效",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return True

# 定义生命周期管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 初始化服务
    try:
        logger.info(f"启动 {settings.PROJECT_NAME} 服务，版本: {settings.API_VERSION}")
        # 初始化全局服务实例
        await initialize_services()
        # 启动服务器通知器
        await server_notifier.start()
        # 启动错误报告器
        await error_reporter.start()
        logger.info("服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        logger.error(traceback.format_exc())

    yield  # 服务运行期间

    # 关闭事件
    try:
        # 关闭服务器通知器
        await server_notifier.stop()
        # 关闭错误报告器
        await error_reporter.close()
    except Exception as e:
        logger.error(f"关闭服务失败: {str(e)}")

    logger.info(f"关闭 {settings.PROJECT_NAME} 服务")

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="数据库连接管理与查询代理服务",
    version=settings.API_VERSION,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应配置为特定域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
)

# 注册API路由
app.include_router(api_router, prefix="/api")
#app.include_router(api_router, prefix="/api", dependencies=[Depends(verify_api_key)])

# 请求处理中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间中间件"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# 健康检查端点
@app.get("/health", tags=["健康检查"])
async def health_check() -> Dict[str, Any]:
    """健康检查接口，返回服务状态和版本信息"""
    return {
        "status": "ok",
        "version": settings.API_VERSION,
        "service": settings.PROJECT_NAME,
        "timestamp": time.time(),
    }

# ===== 新增的简单测试端点 =====
@app.get("/ping", tags=["测试"])
async def simple_ping():
    """一个非常简单的测试端点"""
    return {"message": "pong"}

@app.get("/ping_error", tags=["测试"])
async def simple_ping_error():
    """一个故意触发错误的简单测试端点"""
    raise HTTPException(status_code=400, detail="这是一个测试错误")
# ==============================

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception, background_tasks: BackgroundTasks = None):
    """全局异常处理器，记录异常并返回友好的错误响应，同时向Server报告错误"""
    # 获取错误类别和严重程度
    from app.core.error_reporter import ErrorProtocol, ErrorCategory, ErrorSeverity
    category = ErrorProtocol.categorize_error(exc)
    severity = ErrorProtocol.determine_severity(exc, category)

    logger.error(f"全局异常: {exc}, 类别: {category}, 严重程度: {severity}")
    logger.error(traceback.format_exc())

    # 提取请求路径和可能的任务ID
    path = request.url.path
    task_id = None

    # 尝试从URL路径中提取任务ID
    parts = path.split('/')
    for i, part in enumerate(parts):
        if part in ['tasks', 'task'] and i + 1 < len(parts):
            potential_task_id = parts[i + 1]
            if potential_task_id and potential_task_id not in ['error', 'compare', 'export']:  # 排除非ID路径
                task_id = potential_task_id
                break

    # 创建上下文信息
    context = {
        "path": str(request.url),
        "method": request.method,
        "client": f"{request.client.host}:{request.client.port}" if request.client else "unknown",
        "headers": dict(request.headers),
        "query_params": dict(request.query_params)
    }

    # 报告错误（在后台执行，避免阻塞响应）
    if background_tasks:
        if task_id:
            # 任务相关错误
            background_tasks.add_task(report_error, task_id, exc, context)
        else:
            # 系统级错误
            background_tasks.add_task(report_system_error, exc, context)
    else:
        # 创建一个任务来发送错误报告
        if task_id:
            asyncio.create_task(report_error(task_id, exc, context))
        else:
            asyncio.create_task(report_system_error(exc, context))

    # 根据错误严重程度确定状态码
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    if severity == ErrorSeverity.WARNING:
        status_code = status.HTTP_400_BAD_REQUEST
    elif severity == ErrorSeverity.INFO:
        status_code = status.HTTP_422_UNPROCESSABLE_ENTITY

    # 构建错误响应
    error_response = {
        "success": False,
        "message": str(exc) if settings.LOG_LEVEL == "DEBUG" else "服务器处理请求时发生错误",
        "error": {
            "type": type(exc).__name__,
            "category": str(category),
            "severity": str(severity)
        }
    }

    # 在调试模式下添加更多详细信息
    if settings.LOG_LEVEL == "DEBUG":
        error_response["error"]["detail"] = traceback.format_exc()

    # 如果有任务ID，添加到响应中
    if task_id:
        error_response["task_id"] = task_id

    return JSONResponse(
        status_code=status_code,
        content=error_response
    )

# 主程序入口
if __name__ == "__main__":
    # 使用uvicorn启动服务
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.PORT,
        workers=settings.MAX_WORKERS,
        reload=False
    )