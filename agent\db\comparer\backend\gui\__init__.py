#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI模块化重构包
将原有的app.py拆分为多个专门的模块，提高代码的可维护性和可扩展性
"""

from .main import MainGUI
from .config_manager import SmartConfigManager
from .comparison_interface import ComparisonInterface
from .direct_comparison import DirectComparisonMode
from .local_comparison import LocalComparisonMode
from .api_comparison import APIComparisonMode

__all__ = [
    'MainGUI',
    'SmartConfigManager',
    'ComparisonInterface',
    'DirectComparisonMode',
    'LocalComparisonMode',
    'APIComparisonMode'
]

__version__ = '2.0.0'
__author__ = 'DB-Agent Team'
__description__ = 'GUI模块化重构包'
