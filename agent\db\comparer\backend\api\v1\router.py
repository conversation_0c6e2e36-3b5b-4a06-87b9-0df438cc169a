"""
API路由主模块
"""
from fastapi import APIRouter

from .endpoints import (
    comparison, database, rules, tasks, results
)

# 创建API路由器
api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(
    comparison.router, 
    prefix="/comparison", 
    tags=["比对管理"]
)

api_router.include_router(
    database.router, 
    prefix="/database", 
    tags=["数据库管理"]
)

api_router.include_router(
    rules.router, 
    prefix="/rules", 
    tags=["规则管理"]
)

api_router.include_router(
    tasks.router, 
    prefix="/tasks", 
    tags=["任务管理"]
)

api_router.include_router(
    results.router, 
    prefix="/results", 
    tags=["结果管理"]
)
