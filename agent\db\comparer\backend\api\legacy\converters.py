"""
Legacy API数据转换器

负责在原版本API模型和新版本API模型之间进行数据转换
"""
import os
import sys
# 获取当前脚本的目录路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 将项目根目录添加到Python路径中，使得backend和comparison模块可以被正确导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(SCRIPT_DIR)))
sys.path.insert(0, PROJECT_ROOT)
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from .models import (
    CompareTaskRequest, DatabaseConnection, CompareConfig,
    TaskResponse, TaskStatusResponse, TaskResultResponse,
    ComparisonResponse, ConnectionTestRequest, ConnectionTestResponse,
    TaskStatus, ComparisonSummary, ErrorResponse
)
from backend.models.comparison_models import (
    ComparisonTaskCreate, ComparisonTaskResponse, ComparisonResultResponse
)

logger = logging.getLogger(__name__)


class LegacyConverter:
    """Legacy API数据转换器"""
    
    @staticmethod
    def convert_connection_to_new_format(legacy_conn: DatabaseConnection) -> Dict[str, Any]:
        """将Legacy连接配置转换为新版本格式"""
        return {
            "db_type": legacy_conn.type.value,
            "host": legacy_conn.host,
            "port": legacy_conn.port,
            "database": legacy_conn.database,
            "username": legacy_conn.username,
            "password": legacy_conn.password,
            **((legacy_conn.parameters.model_dump() if legacy_conn.parameters else {}))
        }
    
    @staticmethod
    def convert_task_request_to_new_format(legacy_request: CompareTaskRequest) -> Dict[str, Any]:
        """将Legacy任务请求转换为新版本格式"""
        config = legacy_request.config
        
        # 确定比对类型
        if config.mode.value == "table":
            comparison_type = "content"  # 表内容比对
        else:
            comparison_type = "content"  # SQL比对也归类为内容比对
        
        # 构建SQL规则
        sql_rules = None
        if config.source_sql and config.target_sql:
            sql_rules = {
                "sql_1": config.source_sql,
                "sql_2": config.target_sql
            }
        elif config.source_table and config.target_table:
            # 基于表名构建基础SQL
            primary_key = config.primary_keys[0] if config.primary_keys else "id"
            
            # 构建字段列表
            if config.compare_columns:
                columns = ", ".join(config.compare_columns)
            else:
                columns = "*"
            
            # 构建WHERE子句
            where_clause = f" WHERE {config.where_clause}" if config.where_clause else ""
            limit_clause = f" LIMIT {config.limit}" if config.limit else ""
            
            sql_rules = {
                "sql_1": f"SELECT {primary_key} AS KEY, {columns} FROM {config.source_table}{where_clause}{limit_clause}",
                "sql_2": f"SELECT {primary_key} AS KEY, {columns} FROM {config.target_table}{where_clause}{limit_clause}"
            }
        
        return {
            "comparison_type": comparison_type,
            "source_connection": LegacyConverter.convert_connection_to_new_format(legacy_request.source),
            "target_connection": LegacyConverter.convert_connection_to_new_format(legacy_request.target),
            "table_name": config.source_table or "unknown",
            "sql_rules": sql_rules,
            "batch_size": config.batch_size,
            "timeout": 3600  # 默认超时时间
        }
    
    @staticmethod
    def convert_new_task_response_to_legacy(new_response: ComparisonTaskResponse) -> TaskResponse:
        """将新版本任务响应转换为Legacy格式"""
        return TaskResponse(
            success=True,
            message="任务创建成功",
            code=200,
            task_id=new_response.task_id,
            status=new_response.status,
            progress=None,  # 新版本没有进度字段
            created_at=new_response.created_at.isoformat(),
            updated_at=new_response.started_at.isoformat() if new_response.started_at else None,
            data={
                "id": new_response.id,
                "comparison_type": new_response.comparison_type,
                "table_name": new_response.table_name
            }
        )
    
    @staticmethod
    def convert_new_status_to_legacy(new_response: ComparisonTaskResponse) -> TaskStatusResponse:
        """将新版本状态响应转换为Legacy格式"""
        # 状态映射
        status_mapping = {
            "pending": TaskStatus.PENDING,
            "running": TaskStatus.RUNNING,
            "completed": TaskStatus.COMPLETED,
            "failed": TaskStatus.FAILED,
            "cancelled": TaskStatus.CANCELLED
        }
        
        return TaskStatusResponse(
            success=True,
            message="任务状态查询成功",
            code=200,
            task_id=new_response.task_id,
            status=status_mapping.get(new_response.status, TaskStatus.PENDING),
            progress=None,  # 新版本没有进度字段
            created_at=new_response.created_at.isoformat(),
            started_at=new_response.started_at.isoformat() if new_response.started_at else None,
            completed_at=new_response.completed_at.isoformat() if new_response.completed_at else None,
            error_message=new_response.error_message
        )
    
    @staticmethod
    def convert_new_result_to_legacy(
        task_response: ComparisonTaskResponse, 
        result_response: Optional[ComparisonResultResponse]
    ) -> TaskResultResponse:
        """将新版本结果响应转换为Legacy格式"""
        
        summary = None
        details = None
        
        if result_response:
            summary = ComparisonSummary(
                total_records=result_response.total_records,
                matched_records=result_response.matched_records,
                different_records=result_response.different_records,
                source_only_records=result_response.source_only_records,
                target_only_records=result_response.target_only_records,
                execution_time=result_response.execution_time
            )
            
            details = result_response.differences or []
        
        return TaskResultResponse(
            success=True,
            message="任务结果查询成功",
            code=200,
            task_id=task_response.task_id,
            status=TaskStatus(task_response.status),
            summary=summary,
            details=details,
            created_at=task_response.created_at.isoformat(),
            completed_at=task_response.completed_at.isoformat() if task_response.completed_at else None
        )
    
    @staticmethod
    def convert_connection_test_request(legacy_request: ConnectionTestRequest) -> Dict[str, Any]:
        """转换连接测试请求"""
        return LegacyConverter.convert_connection_to_new_format(legacy_request.config)
    
    @staticmethod
    def convert_connection_test_response(new_response: Dict[str, Any]) -> ConnectionTestResponse:
        """转换连接测试响应"""
        return ConnectionTestResponse(
            success=new_response.get("success", False),
            message=new_response.get("message", ""),
            elapsed_time=new_response.get("elapsed_time", 0.0),
            version=new_response.get("version"),
            metadata=new_response.get("metadata")
        )
    
    @staticmethod
    def create_error_response(
        error_code: str, 
        message: str, 
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 400
    ) -> ErrorResponse:
        """创建错误响应"""
        return ErrorResponse(
            success=False,
            message=message,
            code=status_code,
            error_code=error_code,
            error_details=details
        )
    
    @staticmethod
    def convert_direct_comparison_result(result: Dict[str, Any]) -> ComparisonResponse:
        """转换直接比对结果"""
        summary_data = result.get("summary", {})
        
        summary = ComparisonSummary(
            total_records=summary_data.get("total_records", 0),
            matched_records=summary_data.get("matched_records", 0),
            different_records=summary_data.get("different_records", 0),
            source_only_records=summary_data.get("source_only_records", 0),
            target_only_records=summary_data.get("target_only_records", 0),
            execution_time=summary_data.get("execution_time", 0.0)
        )
        
        return ComparisonResponse(
            success=result.get("success", True),
            message=result.get("message", "比对完成"),
            code=200,
            summary=summary,
            details=result.get("details", []),
            execution_time=summary_data.get("execution_time", 0.0),
            data=result.get("data")
        )
