"""
任务管理模块
管理数据比对任务的创建、跟踪和管理。
支持通过WebSocket实时推送进度更新。
"""
import asyncio
import uuid
import time
import os
import glob
import shutil
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import WebSocket, WebSocketDisconnect

from app.core.config import settings
from app.models.logger import logger
from app.core.comparison_factory import ComparisonEngineFactory, infer_data_source_type
from app.models.task import TaskStatus, TaskStats
from app.models.task import CompareTaskRequest, TaskCreatedResponse
from app.models.task import TaskResultResponse, TaskStatusResponse
from app.models.task import DiffDetails, RecordDiff
from app.core.server_notifier import server_notifier
from app.core.error_reporter import report_error

class ConnectionManager:
    """WebSocket连接管理器，用于实时推送任务进度"""

    def __init__(self):
        # 存储活跃的WebSocket连接，按任务ID分组
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, task_id: str):
        """注册新的WebSocket连接"""
        await websocket.accept()
        if task_id not in self.active_connections:
            self.active_connections[task_id] = []
        self.active_connections[task_id].append(websocket)
        logger.info(f"WebSocket客户端连接到任务 {task_id}，当前连接数: {len(self.active_connections[task_id])}")

    def disconnect(self, websocket: WebSocket, task_id: str):
        """移除WebSocket连接"""
        if task_id in self.active_connections:
            if websocket in self.active_connections[task_id]:
                self.active_connections[task_id].remove(websocket)
            # 如果没有连接，移除任务ID键
            if not self.active_connections[task_id]:
                del self.active_connections[task_id]

    async def broadcast(self, task_id: str, message: Dict[str, Any]):
        """向特定任务的所有连接广播消息"""
        if task_id in self.active_connections:
            dead_connections = []
            for connection in self.active_connections[task_id]:
                try:
                    await connection.send_json(message)
                except WebSocketDisconnect:
                    dead_connections.append(connection)
                except Exception as e:
                    logger.error(f"广播消息至WebSocket时出错: {str(e)}")
                    dead_connections.append(connection)

            # 清理失效连接
            for dead in dead_connections:
                self.disconnect(dead, task_id)


class TaskManager:
    """
    任务管理器

    负责创建、跟踪、控制和管理数据比对任务。
    支持通过WebSocket实时推送进度和状态更新。
    """

    def __init__(self):
        """初始化任务管理器"""
        self.tasks = {}  # 任务状态内存缓存
        self.task_requests = {}  # 存储任务请求
        self.task_results = {}  # 存储任务结果
        self.task_queue = []  # 待处理任务队列
        self.ws_manager = ConnectionManager()  # WebSocket连接管理器

        # 运行中任务的取消事件
        self.cancel_events: Dict[str, asyncio.Event] = {}
        # 任务结果缓存 (仅保存最近N个任务的详细结果)
        self.result_cache_size = getattr(settings, "TASK_RESULT_CACHE_SIZE", 100)

    async def initialize(self):
        """初始化任务管理器"""
        logger.info("任务管理器初始化")

    async def create_task(self, request: CompareTaskRequest) -> TaskCreatedResponse:
        """
        创建新的数据比对任务

        参数:
            request: 数据比对任务请求

        返回:
            任务创建响应
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())

        # 创建任务状态记录
        task_status = {
            "task_id": task_id,
            "status": TaskStatus.PENDING,
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "started_at": None,
            "completed_at": None,
            "message": "任务已创建，等待执行",
            "stats": {
                "source_count": 0,
                "target_count": 0,
                "processed_count": 0,
                "diff_count": 0,
                "elapsed_time": 0,
                "estimated_remaining_time": None
            }
        }

        # 保存任务状态和请求到内存
        self.tasks[task_id] = task_status
        self.task_requests[task_id] = request

        # 将任务加入待处理队列
        self.task_queue.append(task_id)

        # 启动异步任务处理
        asyncio.create_task(self._process_task(task_id, request))

        # 返回任务创建响应
        base_url = getattr(settings, "API_BASE_URL", "").rstrip("/")
        return TaskCreatedResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="任务已创建，正在启动处理",
            created_at=datetime.now(),
            links={
                "status": f"{base_url}/tasks/{task_id}/status",
                "result": f"{base_url}/tasks/{task_id}/result",
                "cancel": f"{base_url}/tasks/{task_id}",
                "ws_status": f"{base_url}/ws/tasks/{task_id}/status"
            }
        )

    async def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """
        获取任务状态

        参数:
            task_id: 任务ID

        返回:
            任务状态响应或None(如果任务不存在)
        """
        # 从内存缓存获取
        task_data = self.tasks.get(task_id)
        if not task_data:
            return None

        # 格式化日期时间字段
        created_at = datetime.fromisoformat(task_data["created_at"])
        started_at = datetime.fromisoformat(task_data["started_at"]) if task_data.get("started_at") else None

        # 构建统计信息
        stats = TaskStats(**task_data["stats"]) if task_data.get("stats") else None

        # 构建错误信息（如果任务失败）
        error_info = None
        if task_data["status"] == TaskStatus.FAILED:
            # 尝试从任务结果中获取错误信息
            result_data = self.task_results.get(task_id, {})
            if result_data and "summary" in result_data:
                summary = result_data["summary"]
                if hasattr(summary, 'error') and summary.error:
                    error_info = summary.error
                elif isinstance(summary, dict) and "error" in summary:
                    error_info = summary["error"]

            # 如果没有从结果中获取到错误信息，尝试从任务数据中获取
            if not error_info and "error" in task_data:
                error_info = task_data["error"]

            # 如果仍然没有错误信息，创建一个基本的错误信息
            if not error_info:
                # 使用错误消息处理器来分析任务消息
                from app.core.error_message_processor import ErrorMessageProcessor

                task_message = task_data.get("message", "任务执行失败")
                processed_error = ErrorMessageProcessor.process_error_message(task_message)

                error_info = {
                    "type": processed_error.get("type", "TASK_EXECUTION_ERROR"),
                    "message": processed_error.get("message", task_message),
                    "is_fatal": True,
                    "suggestion": processed_error.get("suggestion", "请检查日志获取更多信息"),
                    "timestamp": datetime.now().timestamp()
                }

        # 构建任务状态响应
        base_url = getattr(settings, "API_BASE_URL", "").rstrip("/")
        return TaskStatusResponse(
            task_id=task_id,
            status=task_data["status"],
            progress=task_data["progress"],
            created_at=created_at,
            started_at=started_at,
            message=task_data["message"],
            stats=stats,
            error=error_info,
            links={
                "status": f"{base_url}/tasks/{task_id}/status",
                "result": f"{base_url}/tasks/{task_id}/result",
                "cancel": f"{base_url}/tasks/{task_id}",
                "ws_status": f"{base_url}/ws/tasks/{task_id}/status"
            }
        )

    async def get_task_result(self, task_id: str, page: int = 1, page_size: int = 20) -> Optional[TaskResultResponse]:
        """
        获取任务结果

        参数:
            task_id: 任务ID
            page: 差异详情的页码
            page_size: 每页记录数

        返回:
            任务结果响应或None(如果任务不存在)
        """
        # 获取任务状态
        task_status = await self.get_task_status(task_id)
        if not task_status:
            logger.warning(f"获取任务结果失败: 任务 {task_id} 不存在")
            return None

        # 如果任务未完成，仅返回状态部分
        if task_status.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            logger.debug(f"获取任务结果: 任务 {task_id} 未完成，当前状态为 {task_status.status}")
            return TaskResultResponse(
                task_id=task_id,
                status=task_status.status,
                summary=None,
                completed_at=None,
                execution_time=None,
                details=None
            )

        # 获取任务结果数据
        result_data = self.task_results.get(task_id)
        if not result_data:
            # 任务完成但没有结果数据
            logger.warning(f"任务 {task_id} 已完成但没有结果数据")
            return TaskResultResponse(
                task_id=task_id,
                status=task_status.status,
                summary=None,
                completed_at=None,
                execution_time=None,
                details=None,
                message="任务结果数据不可用"
            )

        # 构建空的DiffDetails对象，确保即使没有差异记录也能返回有效的结构
        details = None
        if result_data.get("details") is not None:
            # 如果结果中有details但不是DiffDetails类型，则需要手动构造
            try:
                if not isinstance(result_data.get("details"), DiffDetails):
                    raw_details = result_data.get("details", [])
                    total_items = len(raw_details)
                    total_pages = max(1, (total_items + page_size - 1) // page_size) if total_items > 0 else 1

                    # 验证页码
                    valid_page = max(1, min(page, total_pages))

                    # 获取当前页数据
                    start_idx = (valid_page - 1) * page_size
                    end_idx = min(start_idx + page_size, total_items)
                    page_items = raw_details[start_idx:end_idx] if start_idx < total_items else []

                    details = DiffDetails(
                        page=valid_page,
                        page_size=page_size,
                        total_pages=total_pages,
                        total_items=total_items,
                        items=page_items
                    )
                    logger.debug(f"已创建DiffDetails对象: 任务={task_id}, 页码={valid_page}/{total_pages}, 记录数={len(page_items)}/{total_items}")
                else:
                    details = result_data.get("details")
            except Exception as e:
                logger.exception(f"创建DiffDetails对象失败: {str(e)}")
                # 创建一个空的DiffDetails对象，避免返回None
                details = DiffDetails(
                    page=page,
                    page_size=page_size,
                    total_pages=1,
                    total_items=0,
                    items=[]
                )

        # 返回任务结果响应
        return TaskResultResponse(
            task_id=task_id,
            status=task_status.status,
            summary=result_data.get("summary"),
            details=details,
            completed_at=datetime.fromisoformat(result_data.get("completed_at")) if result_data.get("completed_at") else None,
            execution_time=result_data.get("execution_time"),
            message=result_data.get("message", "任务执行完成")
        )

    async def get_task_diff_details(self, task_id: str, page: int = 1, page_size: int = 20) -> Optional[DiffDetails]:
        """
        获取任务的差异详情（分页）

        参数:
            task_id: 任务ID
            page: 页码（从1开始）
            page_size: 每页记录数

        返回:
            差异详情对象或None
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.warning(f"获取差异详情失败: 任务 {task_id} 不存在")
                return None

            # 首先尝试从文件存储中读取差异详情
            try:
                from app.core.file_storage import ComparisonFileStorage
                file_storage = ComparisonFileStorage()

                # 尝试读取差异文件
                diff_items = await self._load_diff_details_from_files(file_storage, task_id, page, page_size)
                if diff_items is not None:
                    logger.info(f"从文件存储成功读取任务 {task_id} 的差异详情")
                    return diff_items

            except Exception as e:
                logger.warning(f"从文件存储读取差异详情失败: {str(e)}")

            # 如果文件存储失败，回退到内存数据
            task_data = self.tasks[task_id]
            result_data = task_data.get("result", {})

            # 检查是否有差异数据
            if "comparison_results" not in result_data:
                logger.debug(f"任务 {task_id} 没有比对结果数据")
                return None

            comparison_results = result_data["comparison_results"]

            # 收集所有差异记录
            all_diff_items = []

            # 处理有差异的记录
            for diff in comparison_results.get("diff", []):
                diff_item = self._convert_to_record_diff(diff, "value_diff")
                if diff_item:
                    all_diff_items.append(diff_item)

            # 处理仅源存在的记录
            for source_only in comparison_results.get("source_only", []):
                diff_item = self._convert_to_record_diff(source_only, "source_missing")
                if diff_item:
                    all_diff_items.append(diff_item)

            # 处理仅目标存在的记录
            for target_only in comparison_results.get("target_only", []):
                diff_item = self._convert_to_record_diff(target_only, "target_missing")
                if diff_item:
                    all_diff_items.append(diff_item)

            # 计算分页信息
            total_items = len(all_diff_items)
            total_pages = max(1, (total_items + page_size - 1) // page_size) if total_items > 0 else 1

            # 验证页码
            valid_page = max(1, min(page, total_pages))

            # 获取当前页数据
            start_idx = (valid_page - 1) * page_size
            end_idx = min(start_idx + page_size, total_items)
            page_items = all_diff_items[start_idx:end_idx] if start_idx < total_items else []

            # 创建差异详情对象
            diff_details = DiffDetails(
                page=valid_page,
                page_size=page_size,
                total_pages=total_pages,
                total_items=total_items,
                items=page_items
            )

            logger.debug(f"已获取任务 {task_id} 的差异详情: 第{valid_page}/{total_pages}页，当前页{len(page_items)}条，总计{total_items}条")
            return diff_details

        except Exception as e:
            logger.exception(f"获取任务差异详情失败: {task_id}, 错误: {str(e)}")
            return None

    async def _load_diff_details_from_files(self, file_storage, task_id: str, page: int, page_size: int) -> Optional[DiffDetails]:
        """从文件存储中加载差异详情"""
        try:
            import json
            from pathlib import Path

            # 构建文件路径
            workspace = file_storage.base_path / task_id / 'data'

            if not workspace.exists():
                logger.debug(f"任务 {task_id} 的文件存储工作区不存在")
                return None

            # 收集所有差异记录
            all_diff_items = []

            # 读取差异文件
            diff_files = {
                'differences': 'value_diff',
                'source_only': 'target_missing',
                'target_only': 'source_missing'
            }

            for file_name, diff_type in diff_files.items():
                file_path = workspace / f"{file_name}.jsonl"
                if file_path.exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                if line.strip():
                                    record = json.loads(line)
                                    diff_item = self._convert_file_record_to_diff(record, diff_type)
                                    if diff_item:
                                        all_diff_items.append(diff_item)
                    except Exception as e:
                        logger.warning(f"读取差异文件 {file_path} 失败: {str(e)}")

            # 计算分页信息
            total_items = len(all_diff_items)
            total_pages = max(1, (total_items + page_size - 1) // page_size) if total_items > 0 else 1

            # 验证页码
            valid_page = max(1, min(page, total_pages))

            # 获取当前页数据
            start_idx = (valid_page - 1) * page_size
            end_idx = min(start_idx + page_size, total_items)
            page_items = all_diff_items[start_idx:end_idx] if start_idx < total_items else []

            # 创建差异详情对象
            diff_details = DiffDetails(
                page=valid_page,
                page_size=page_size,
                total_pages=total_pages,
                total_items=total_items,
                items=page_items
            )

            logger.debug(f"从文件存储读取任务 {task_id} 的差异详情: 第{valid_page}/{total_pages}页，当前页{len(page_items)}条，总计{total_items}条")
            return diff_details

        except Exception as e:
            logger.warning(f"从文件存储加载差异详情失败: {str(e)}")
            return None

    def _convert_file_record_to_diff(self, record: Dict[str, Any], diff_type: str) -> Optional[RecordDiff]:
        """将文件中的记录转换为RecordDiff对象"""
        try:
            # 提取主键信息
            key = record.get("key", {})
            if isinstance(key, str):
                key = {"primary_key": key}
            elif not isinstance(key, dict):
                key = {"primary_key": str(key)}

            if diff_type == "value_diff":
                # 有差异的记录
                source_record = record.get("source_record", {})
                target_record = record.get("target_record", {})
                diff_fields = record.get("diff_fields", [])

                # 构建字段差异列表
                field_diffs = []
                for field in diff_fields:
                    field_diffs.append({
                        "field": field,
                        "source_value": source_record.get(field),
                        "target_value": target_record.get(field)
                    })

                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=field_diffs
                )

            elif diff_type == "source_missing":
                # 仅目标存在的记录
                target_record = record.get("record", {})
                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=None,
                    source_record=None,
                    target_record=target_record
                )

            elif diff_type == "target_missing":
                # 仅源存在的记录
                source_record = record.get("record", {})
                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=None,
                    source_record=source_record,
                    target_record=None
                )

            return None

        except Exception as e:
            logger.warning(f"转换文件记录失败: {str(e)}")
            return None

    def _convert_to_record_diff(self, diff_record: Dict[str, Any], diff_type: str) -> Optional[RecordDiff]:
        """
        将内部差异记录格式转换为API返回格式

        参数:
            diff_record: 内部差异记录
            diff_type: 差异类型

        返回:
            RecordDiff对象或None
        """
        try:
            # 提取主键信息
            key = diff_record.get("key", {})
            if isinstance(key, tuple):
                # 如果key是元组，转换为字典格式
                key = {"primary_key": str(key)}
            elif not isinstance(key, dict):
                key = {"primary_key": str(key)}

            # 根据差异类型处理不同的记录格式
            if diff_type == "value_diff":
                # 有差异的记录
                source_record = diff_record.get("source", diff_record.get("source_record", {}))
                target_record = diff_record.get("target", diff_record.get("target_record", {}))

                # 提取字段差异
                field_diffs = []
                if "diffs" in diff_record:
                    # 使用算法提供的差异信息
                    for field_diff in diff_record["diffs"]:
                        field_diffs.append({
                            "field": field_diff.get("field"),
                            "source_value": field_diff.get("source_value"),
                            "target_value": field_diff.get("target_value")
                        })
                elif "diff_fields" in diff_record:
                    # 使用标准化的差异字段
                    for field, values in diff_record["diff_fields"].items():
                        field_diffs.append({
                            "field": field,
                            "source_value": values.get("source"),
                            "target_value": values.get("target")
                        })
                else:
                    # 手动比较源和目标记录
                    all_fields = set(source_record.keys()) | set(target_record.keys())
                    for field in all_fields:
                        source_value = source_record.get(field)
                        target_value = target_record.get(field)
                        if source_value != target_value:
                            field_diffs.append({
                                "field": field,
                                "source_value": source_value,
                                "target_value": target_value
                            })

                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=field_diffs
                )

            elif diff_type == "source_missing":
                # 仅目标存在的记录（源缺失）
                target_record = diff_record.get("record", diff_record.get("target_record", diff_record.get("target", {})))

                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=None,
                    source_record=None,
                    target_record=target_record
                )

            elif diff_type == "target_missing":
                # 仅源存在的记录（目标缺失）
                source_record = diff_record.get("record", diff_record.get("source_record", diff_record.get("source", {})))

                return RecordDiff(
                    key=key,
                    diff_type=diff_type,
                    field_diffs=None,
                    source_record=source_record,
                    target_record=None
                )

            else:
                logger.warning(f"未知的差异类型: {diff_type}")
                return None

        except Exception as e:
            logger.exception(f"转换差异记录格式失败: {str(e)}")
            return None

    async def get_task_tables(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务的表级进度信息

        参数:
            task_id: 任务ID

        返回:
            包含表级进度的字典，如果任务不存在则返回None
        """
        # 从内存缓存获取任务状态
        task_data = self.tasks.get(task_id)
        if not task_data:
            logger.warning(f"获取表级进度失败: 任务 {task_id} 不存在")
            return None

        # 获取任务请求，用于获取表名信息
        task_request = self.task_requests.get(task_id)

        # 初始化表级进度数据结构
        table_progress = []

        # 根据不同比对模式处理
        if task_request:
            # 检查task_request是否有mode属性
            request_mode = getattr(task_request, 'mode', None)

            # 如果是表级比对模式
            if request_mode == "table" and hasattr(task_request, 'tables'):
                # 遍历表配置
                for table_config in task_request.tables:
                    source_table = table_config.source_table
                    target_table = table_config.target_table

                    # 从任务执行数据中获取该表的进度信息
                    table_data = next((t for t in task_data.get("table_stats", [])
                                    if t.get("source_table") == source_table and
                                       t.get("target_table") == target_table), None)

                    if not table_data:
                        # 如果没有找到该表的进度信息，使用默认值
                        table_data = {
                            "source_table": source_table,
                            "target_table": target_table,
                            "total_records": 0,
                            "processed": 0,
                            "matched": 0,
                            "different": 0,
                            "errors": 0,
                            "progress": 0
                        }

                    # 添加到表级进度列表
                    table_progress.append({
                        "tableName": f"{source_table} → {target_table}",
                        "sourceTable": source_table,
                        "targetTable": target_table,
                        "totalRecords": table_data.get("total_records", 0),
                        "processed": table_data.get("processed", 0),
                        "matched": table_data.get("matched", 0),
                        "different": table_data.get("different", 0),
                        "errors": table_data.get("errors", 0),
                        "progress": min(100, int(table_data.get("progress", 0)))
                    })
            # 如果是查询比对模式
            elif request_mode == "query":
                # 对于查询模式，通常只有一个"虚拟表"
                # 使用全局统计数据作为唯一表的数据
                stats = task_data.get("stats", {})
                table_progress.append({
                    "tableName": "自定义查询",
                    "sourceTable": "查询1",
                    "targetTable": "查询2",
                    "totalRecords": stats.get("source_count", 0),
                    "processed": stats.get("processed_count", 0),
                    "matched": stats.get("match_count", 0),
                    "different": stats.get("diff_count", 0),
                    "errors": stats.get("error_count", 0),
                    "progress": min(100, int(task_data.get("progress", 0)))
                })
            else:
                # 任务请求存在但没有mode属性或不是已知的模式
                # 使用统计数据构建一个通用表进度
                stats = task_data.get("stats", {})
                table_progress.append({
                    "tableName": "总计",
                    "totalRecords": stats.get("source_count", 0),
                    "processed": stats.get("processed_count", 0),
                    "matched": stats.get("match_count", 0) if "match_count" in stats else stats.get("matched_count", 0),
                    "different": stats.get("diff_count", 0),
                    "errors": 0,
                    "progress": min(100, int(task_data.get("progress", 0)))
                })
        else:
            # 如果没有任务请求信息，使用统计数据构建一个通用表进度
            stats = task_data.get("stats", {})
            table_progress.append({
                "tableName": "总计",
                "totalRecords": stats.get("source_count", 0),
                "processed": stats.get("processed_count", 0),
                "matched": stats.get("match_count", 0) if "match_count" in stats else stats.get("matched_count", 0),
                "different": stats.get("diff_count", 0),
                "errors": 0,
                "progress": min(100, int(task_data.get("progress", 0)))
            })

        # 返回包含表级进度的响应
        return {
            "success": True,
            "task_id": task_id,
            "status": task_data.get("status"),
            "tables": table_progress
        }

    async def list_tasks(self, status: Optional[TaskStatus] = None, limit: int = 100, offset: int = 0) -> List[TaskStatusResponse]:
        """
        获取任务列表

        参数:
            status: 可选的任务状态过滤
            limit: 返回结果数量限制
            offset: 分页偏移量

        返回:
            任务状态响应列表
        """
        # 过滤和分页任务
        filtered_tasks = []

        # 获取所有任务ID并排序（最新的任务排在前面）
        task_ids = sorted(self.tasks.keys(), key=lambda k: self.tasks[k]["created_at"], reverse=True)

        # 应用状态过滤
        if status:
            task_ids = [task_id for task_id in task_ids if self.tasks[task_id]["status"] == status]

        # 应用分页
        task_ids = task_ids[offset:offset+limit]

        # 获取任务状态
        for task_id in task_ids:
            try:
                task_status = await self.get_task_status(task_id)
                if task_status:
                    filtered_tasks.append(task_status)
            except Exception as e:
                logger.error(f"获取任务 {task_id} 状态时发生错误: {str(e)}")

        return filtered_tasks

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        参数:
            task_id: 任务ID

        返回:
            是否成功取消
        """
        # 检查任务是否存在
        if task_id not in self.tasks or task_id not in self.cancel_events:
            logger.warning(f"无法取消任务: 任务 {task_id} 不存在或无法取消")
            return False

        # 检查是否处于可取消状态
        task_data = self.tasks[task_id]
        if task_data["status"] in [TaskStatus.RUNNING, TaskStatus.PAUSED]:
            # 设置取消事件
            self.cancel_events[task_id].set()

            # 更新任务状态
            await self._update_task_status(
                task_id,
                status=TaskStatus.CANCELED,
                message="任务已取消",
                completed_at=datetime.now().isoformat()
            )

            return True

        # 其他状态下无法取消
        return False

    async def get_task_request(self, task_id: str) -> Optional[CompareTaskRequest]:
        """
        获取任务的原始请求数据

        参数:
            task_id: 任务ID

        返回:
            任务请求对象或None(如果任务不存在)
        """
        return self.task_requests.get(task_id)

    async def pause_task(self, task_id: str) -> bool:
        """
        暂停正在运行的任务

        参数:
            task_id: 任务ID

        返回:
            是否成功暂停
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            logger.warning(f"无法暂停任务: 任务 {task_id} 不存在")
            return False

        # 检查当前状态
        task_data = self.tasks[task_id]
        if task_data["status"] != TaskStatus.RUNNING:
            logger.warning(f"无法暂停任务: 任务 {task_id} 当前状态为 {task_data['status']}，不是运行中")
            return False

        # 更新任务状态
        await self._update_task_status(
            task_id,
            status=TaskStatus.PAUSED,
            message="任务已暂停"
        )

        # 调用任务执行器暂停任务
        if hasattr(self, 'executor') and task_id in getattr(self.executor, '_pause_events', {}):
            await self.executor.pause_task(task_id)

        return True

    async def cleanup_task_resources(self, task_id: str) -> bool:
        """
        清理任务相关资源

        参数:
            task_id: 任务ID

        返回:
            是否成功清理
        """
        logger.info(f"开始清理任务资源: {task_id}")

        try:
            # 1. 清理内存缓存
            if task_id in self.tasks:
                del self.tasks[task_id]
            if task_id in self.task_requests:
                del self.task_requests[task_id]
            if task_id in self.task_results:
                del self.task_results[task_id]
            if task_id in self.cancel_events:
                del self.cancel_events[task_id]

            # 2. 清理WebSocket连接
            if task_id in self.ws_manager.active_connections:
                connections = self.ws_manager.active_connections[task_id].copy()
                for connection in connections:
                    try:
                        await connection.close(code=1000, reason="任务已完成，关闭连接")
                    except Exception as e:
                        logger.warning(f"关闭WebSocket连接失败: {str(e)}")
                # 清空连接列表
                self.ws_manager.active_connections[task_id] = []

            # 3. 清理临时文件
            await self._cleanup_temp_files(task_id)

            # 4. 清理数据库连接
            await self._cleanup_db_connections(task_id)

            logger.info(f"任务资源清理完成: {task_id}")
            return True
        except Exception as e:
            logger.exception(f"清理任务资源失败: {str(e)}")
            return False

    async def _cleanup_temp_files(self, task_id: str) -> None:
        """
        清理任务相关的临时文件

        参数:
            task_id: 任务ID
        """
        try:
            # 获取临时文件目录
            temp_dir = os.path.join(getattr(settings, "TEMP_DIR", "temp"), task_id)

            # 检查目录是否存在
            if os.path.exists(temp_dir):
                # 删除目录及其内容
                shutil.rmtree(temp_dir, ignore_errors=True)
                logger.info(f"已删除任务临时目录: {temp_dir}")

            # 清理其他可能的临时文件
            patterns = [
                f"{task_id}_*.csv",
                f"{task_id}_*.json",
                f"{task_id}_*.log",
                f"{task_id}_*.tmp"
            ]

            for pattern in patterns:
                for file_path in glob.glob(os.path.join(getattr(settings, "TEMP_DIR", "temp"), pattern)):
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除临时文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {str(e)}")

    async def _cleanup_db_connections(self, task_id: str) -> None:
        """
        清理任务相关的数据库连接

        参数:
            task_id: 任务ID
        """
        try:
            # 获取任务请求
            task_request = self.task_requests.get(task_id)
            if not task_request:
                return

            # 尝试关闭数据库连接池
            # 这里需要根据实际的连接池管理方式进行调整
            from app.core.db_utils import close_connection_pool

            # 关闭源数据库连接池
            if hasattr(task_request, 'source') and hasattr(task_request.source, 'connection_id'):
                try:
                    await close_connection_pool(task_request.source.connection_id)
                    logger.info(f"已关闭源数据库连接池: {task_request.source.connection_id}")
                except Exception as e:
                    logger.warning(f"关闭源数据库连接池失败: {str(e)}")

            # 关闭目标数据库连接池
            if hasattr(task_request, 'target') and hasattr(task_request.target, 'connection_id'):
                try:
                    await close_connection_pool(task_request.target.connection_id)
                    logger.info(f"已关闭目标数据库连接池: {task_request.target.connection_id}")
                except Exception as e:
                    logger.warning(f"关闭目标数据库连接池失败: {str(e)}")
        except Exception as e:
            logger.warning(f"清理数据库连接时出错: {str(e)}")

    async def delete_task(self, task_id: str) -> bool:
        """
        删除任务

        参数:
            task_id: 任务ID

        返回:
            是否成功删除
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            logger.warning(f"无法删除任务: 任务 {task_id} 不存在")
            return False

        # 如果任务正在运行，先取消
        task_data = self.tasks[task_id]
        if task_data["status"] in [TaskStatus.RUNNING, TaskStatus.PAUSED]:
            await self.cancel_task(task_id)

        # 清理任务资源
        success = await self.cleanup_task_resources(task_id)
        if success:
            logger.info(f"任务 {task_id} 已被删除")
            return True
        else:
            logger.error(f"删除任务 {task_id} 失败: 资源清理失败")
            return False

    async def update_task_status(self, task_id: str, status: TaskStatus):
        """
        更新任务状态

        参数:
            task_id: 任务ID
            status: 新的任务状态
        """
        asyncio.create_task(self._update_task_status(
            task_id,
            status=status,
            message=f"任务状态已更新为 {status}"
        ))

    async def handle_ws_connection(self, websocket: WebSocket, task_id: str):
        """处理WebSocket连接"""
        # 接受WebSocket连接
        await self.ws_manager.connect(websocket, task_id)

        try:
            # 立即发送当前任务状态
            task_status = await self.get_task_status(task_id)
            if task_status:
                await websocket.send_json(task_status.dict())

            # 处理客户端消息
            async for message in websocket.iter_json():
                if message == "ping":
                    await websocket.send_json({"type": "pong"})
        except WebSocketDisconnect:
            self.ws_manager.disconnect(websocket, task_id)
        except Exception as e:
            logger.exception(f"WebSocket连接处理异常: {str(e)}")
            self.ws_manager.disconnect(websocket, task_id)

    # 内部方法

    async def _process_task(self, task_id: str, request: CompareTaskRequest):
        """处理数据比对任务"""
        # 安全地获取数据库连接信息，避免直接引用可能不存在的属性
        source_info = f"{getattr(request.source, 'host', 'unknown_host')}:{getattr(request.source, 'port', 'unknown_port')}"
        target_info = f"{getattr(request.target, 'host', 'unknown_host')}:{getattr(request.target, 'port', 'unknown_port')}"

        logger.info(
            f"开始执行任务 {task_id}, 源: {source_info}, "
            f"目标: {target_info}, 配置模式: {getattr(request.config, 'mode', 'unknown')}"
        )

        start_time = time.time()

        try:
            # 创建取消事件
            cancel_event = asyncio.Event()
            self.cancel_events[task_id] = cancel_event

            # 推断数据源类型
            source_type = infer_data_source_type(request.source)
            target_type = infer_data_source_type(request.target)

            logger.info(f"任务 {task_id} 数据源类型推断结果:")
            logger.info(f"  源数据库: {request.source.type} -> {source_type}")
            logger.info(f"  目标数据库: {request.target.type} -> {target_type}")
            logger.info(f"  数据源类型组合: {source_type} -> {target_type}")

            # 检查支持的组合
            supported_combinations = ComparisonEngineFactory.get_supported_combinations()
            logger.debug(f"支持的数据源类型组合: {list(supported_combinations.keys())}")

            # 使用工厂创建比对引擎
            engine = ComparisonEngineFactory.create_engine(
                source_type=source_type,
                target_type=target_type,
                source_conn=request.source,
                target_conn=request.target,
                config=request.config,
                progress_callback=lambda p, s: self._update_task_progress(task_id, p, s)
            )

            # 设置任务上下文，以便引擎能保存结果到任务中
            if hasattr(engine, 'set_task_context'):
                engine.set_task_context(task_id, self)
                logger.debug(f"已为引擎设置任务上下文: {task_id}")

            # 执行比对过程
            result = await engine.run()

            # 检查任务是否被取消
            if cancel_event.is_set():
                await self._update_task_status(
                    task_id,
                    status=TaskStatus.CANCELED,
                    message="任务已取消",
                    completed_at=datetime.now().isoformat()
                )
                return

            # 检查结果是否包含错误信息
            if result and hasattr(result, "error") and result.error:
                # 任务执行失败，更新状态并记录错误信息
                error_message = result.error.get('message', '未知错误')
                error_type = result.error.get('type', 'UnknownError')
                logger.error(f"任务 {task_id} 执行失败: [{error_type}] {error_message}")

                # 保存结果数据
                result_data = {
                    "summary": result.dict(),
                    "details": [],
                    "completed_at": datetime.now().isoformat(),
                    "execution_time": int(time.time() - start_time),
                    "message": f"任务执行失败: {error_message}",
                    "error": result.error
                }
                self.task_results[task_id] = result_data

                # 更新任务状态为失败
                await self._update_task_status(
                    task_id,
                    status=TaskStatus.FAILED,
                    progress=100,
                    message=f"任务执行失败: {error_message}",
                    completed_at=datetime.now().isoformat()
                )

                # 尝试将错误信息报告给服务器
                try:
                    from app.core.error_reporter import report_error
                    context = {
                        "source_db": {"host": request.source.host, "port": request.source.port, "type": str(getattr(request.source, 'type', 'unknown'))},
                        "target_db": {"host": request.target.host, "port": request.target.port, "type": str(getattr(request.target, 'type', 'unknown'))},
                        "task_id": task_id,
                        "error_details": result.error
                    }

                    # 创建一个模拟异常对象用于报告
                    class TaskExecutionError(Exception):
                        pass

                    error = TaskExecutionError(error_message)
                    await report_error(task_id, error, context)
                except Exception as report_error:
                    logger.error(f"报告错误失败: {str(report_error)}")

                return

            # 计算执行时间
            execution_time = int(time.time() - start_time)

            # 保存结果数据 - 确保格式与前端期望一致
            try:
                result_dict = result.model_dump() if hasattr(result, 'model_dump') else result.dict()
            except Exception:
                result_dict = result.dict() if hasattr(result, 'dict') else {}

            # 确保summary字段包含所有必要的统计信息
            summary = result_dict.copy()
            if 'summary' in result_dict:
                summary = result_dict['summary']

            # 标准化字段名，确保前端能正确解析
            normalized_summary = {
                'source_count': summary.get('source_count', 0),
                'target_count': summary.get('target_count', 0),
                'matched_count': summary.get('matched_count', 0),
                'diff_count': summary.get('diff_count', 0),
                'source_only_count': summary.get('source_only_count', 0),
                'target_only_count': summary.get('target_only_count', 0),
                'error_count': summary.get('error_count', 0),
                'processed_count': summary.get('processed_count', summary.get('matched_count', 0) + summary.get('diff_count', 0))
            }

            result_data = {
                "summary": normalized_summary,
                "details": result_dict.get('details', []),  # 详情数据
                "completed_at": datetime.now().isoformat(),
                "execution_time": execution_time,
                "message": "任务执行完成",
                "diff_type_counts": result_dict.get('diff_type_counts', {
                    'value_diff': normalized_summary['diff_count'],
                    'source_missing': 0,
                    'target_missing': 0,
                    'type_diff': 0
                })
            }

            self.task_results[task_id] = result_data
            logger.info(f"任务 {task_id} 结果已保存: {normalized_summary}")

            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                status=TaskStatus.COMPLETED,
                progress=100,
                message="任务执行完成",
                completed_at=datetime.now().isoformat()
            )

            # 如果配置了回调URL，发送回调
            if hasattr(request.config, 'callback_url') and request.config.callback_url:
                await self._send_callback(request.config.callback_url, task_id)

        except Exception as e:
            # 确保任何异常都会被捕获并正确处理
            logger.exception(f"任务 {task_id} 执行过程中发生异常: {str(e)}")

            # 构建丰富的错误上下文，确保包含足够的诊断信息
            error_context = {
                "source_db": {
                    "host": getattr(request.source, 'host', 'unknown'),
                    "port": getattr(request.source, 'port', 'unknown'),
                    "database": getattr(request.source, 'database', 'unknown'),
                    "type": str(getattr(request.source, 'type', 'unknown'))
                },
                "target_db": {
                    "host": getattr(request.target, 'host', 'unknown'),
                    "port": getattr(request.target, 'port', 'unknown'),
                    "database": getattr(request.target, 'database', 'unknown'),
                    "type": str(getattr(request.target, 'type', 'unknown'))
                },
                "task_id": task_id,
                "exception_type": type(e).__name__,
                "traceback": traceback.format_exc()
            }

            # 检查是否为致命错误
            from app.core.error_reporter import is_fatal_error
            is_fatal = is_fatal_error(e)

            # 如果是致命错误，使用专门的处理方法
            if is_fatal:
                logger.critical(f"任务 {task_id} 执行过程中发生致命错误: {str(e)}")
                await self.handle_fatal_error(task_id, e, error_context)
            else:
                # 普通错误处理流程
                # 更新任务状态为失败
                await self._update_task_status(
                    task_id,
                    status=TaskStatus.FAILED,
                        message=f"任务执行过程中发生异常: {str(e)}",
                    completed_at=datetime.now().isoformat()
                )

                # 保存错误信息到结果
                result_data = {
                    "summary": {
                        "source_count": 0,
                        "target_count": 0,
                        "matched_count": 0,
                        "diff_count": 0,
                        "source_only_count": 0,
                        "target_only_count": 0
                    },
                    "details": [],
                    "completed_at": datetime.now().isoformat(),
                    "execution_time": int(time.time() - start_time) if start_time else 0,
                    "message": f"任务执行失败: {str(e)}",
                    "error": {
                        "type": type(e).__name__,
                        "message": str(e),
                        "traceback": traceback.format_exc(),
                        "is_fatal": False,
                        "context": error_context
                    }
                }
                self.task_results[task_id] = result_data

                # 尝试将错误信息报告给服务器
                try:
                    from app.core.error_reporter import report_error
                    await report_error(task_id, e, error_context)
                except Exception as report_error:
                    logger.error(f"报告错误失败: {str(report_error)}")
        finally:
            # 清理取消事件
            if task_id in self.cancel_events:
                del self.cancel_events[task_id]

            # 任务完成后清理资源
            # 注意：这里不清理内存缓存，因为可能还需要访问任务状态和结果
            try:
                # 清理临时文件
                await self._cleanup_temp_files(task_id)

                # 清理数据库连接
                await self._cleanup_db_connections(task_id)

                logger.info(f"任务 {task_id} 完成后的资源清理已执行")
            except Exception as cleanup_error:
                logger.error(f"任务 {task_id} 完成后的资源清理失败: {str(cleanup_error)}")

    async def _update_task_status(self, task_id: str, **updates):
        """更新任务状态"""
        # 检查任务是否存在
        if task_id not in self.tasks:
            return

        # 获取当前任务数据
        task_data = self.tasks[task_id]

        # 记录状态是否变更
        status_changed = False
        old_status = task_data.get("status")

        # 更新提供的字段
        for key, value in updates.items():
            if key in task_data or key == "error":  # 允许添加error字段
                if key == "status" and task_data[key] != value:
                    status_changed = True
                task_data[key] = value

        # 保存更新后的任务数据
        self.tasks[task_id] = task_data

        # 获取更新后的任务状态对象
        task_status = await self.get_task_status(task_id)
        if not task_status:
            return

        # 通过WebSocket广播状态更新
        try:
            await self.ws_manager.broadcast(task_id, task_status.model_dump())
        except Exception as e:
            logger.error(f"WebSocket广播状态更新失败: {str(e)}")
            # 使用旧方法作为备选
            try:
                await self.ws_manager.broadcast(task_id, task_status.dict())
            except Exception:
                logger.error("WebSocket广播状态更新完全失败")

        # 如果状态发生变化，通知服务器
        if status_changed:
            try:
                # 获取新状态
                new_status = task_data.get("status")
                logger.info(f"任务状态变更: {task_id} {old_status} -> {new_status}")

                # 发送状态变更通知
                await server_notifier.notify_status_change(
                    task_id=task_id,
                    status=new_status,
                    task_response=task_status
                )
            except Exception as e:
                logger.error(f"发送状态变更通知失败: {str(e)}")

    async def _update_task_progress(self, task_id: str, progress: int, stats: TaskStats):
        """更新任务进度"""
        # 更新任务状态
        try:
            stats_dict = stats.model_dump()
        except Exception:
            # 兼容旧版本
            stats_dict = stats.dict()

        await self._update_task_status(
            task_id,
            progress=progress,
            stats=stats_dict
        )

        # 通知服务器进度更新
        try:
            # 只有进度变化较大时才发送通知，避免频繁通知
            if progress % 5 == 0 or progress == 100:  # 每5%发送一次，以及100%时必须发送
                await server_notifier.notify_task_progress(
                    task_id=task_id,
                    progress=progress,
                    stats=stats_dict
                )
        except Exception as e:
            logger.error(f"发送进度更新通知失败: {str(e)}")

    async def handle_fatal_error(self, task_id: str, error: Exception, context: Dict[str, Any] = None):
        """
        处理致命错误，停止任务并通知server和web

        Args:
            task_id: 任务ID
            error: 发生的异常
            context: 错误上下文信息
        """
        from app.core.error_reporter import ErrorProtocol, report_error

        # 检查任务是否存在
        if task_id not in self.tasks:
            logger.warning(f"无法处理致命错误: 任务 {task_id} 不存在")
            return

        # 获取当前任务状态
        task_data = self.tasks[task_id]
        current_status = task_data.get("status")

        # 如果任务已经是终止状态，则不需要再处理
        if current_status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELED]:
            logger.info(f"任务 {task_id} 已经处于终止状态 {current_status}，不需要再处理致命错误")
            return

        logger.critical(f"检测到致命错误，正在停止任务 {task_id}: {str(error)}")

        # 构建错误上下文（如果未提供）
        if context is None:
            context = {}

        # 添加错误是否致命的标记
        context["is_fatal_error"] = True

        # 设置取消事件（如果存在）
        if task_id in self.cancel_events:
            self.cancel_events[task_id].set()

        # 更新任务状态为失败
        error_message = f"致命错误导致任务终止: {str(error)}"
        await self._update_task_status(
            task_id,
            status=TaskStatus.FAILED,
            message=error_message,
            completed_at=datetime.now().isoformat()
        )

        # 保存错误信息到结果
        result_data = {
            "summary": {
                "source_count": task_data.get("stats", {}).get("source_count", 0),
                "target_count": task_data.get("stats", {}).get("target_count", 0),
                "matched_count": task_data.get("stats", {}).get("matched_count", 0),
                "diff_count": task_data.get("stats", {}).get("diff_count", 0),
                "source_only_count": 0,
                "target_only_count": 0
            },
            "details": [],
            "completed_at": datetime.now().isoformat(),
            "execution_time": 0,  # 无法准确计算执行时间
            "message": error_message,
            "error": {
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exc(),
                "is_fatal": True,
                "context": context
            }
        }
        self.task_results[task_id] = result_data

        # 向所有WebSocket客户端广播致命错误通知
        try:
            # 构建错误通知消息
            error_notification = {
                "event": "fatal_error",
                "task_id": task_id,
                "status": str(TaskStatus.FAILED),
                "message": error_message,
                "error": {
                    "type": type(error).__name__,
                    "message": str(error),
                    "is_fatal": True
                },
                "timestamp": datetime.now().isoformat()
            }

            # 广播给所有连接到该任务的WebSocket客户端
            await self.ws_manager.broadcast(task_id, error_notification)
            logger.info(f"已向WebSocket客户端广播任务 {task_id} 的致命错误通知")
        except Exception as ws_error:
            logger.error(f"向WebSocket客户端广播致命错误通知失败: {str(ws_error)}")

        # 报告错误
        try:
            await report_error(task_id, error, context)
        except Exception as report_error:
            logger.error(f"报告致命错误失败: {str(report_error)}")

        # 通知服务器任务状态变更
        try:
            # 构建状态通知
            status_notification = {
                "status": str(TaskStatus.FAILED),
                "progress": 100,  # 设置为100表示任务已结束
                "message": error_message,
                "completed_at": datetime.now().isoformat(),
                "is_fatal_error": True
            }

            # 发送状态通知
            await server_notifier.notify_status_change(
                task_id=task_id,
                status=TaskStatus.FAILED,
                task_response=status_notification
            )
            logger.info(f"已向服务器发送任务 {task_id} 的致命错误状态通知")
        except Exception as notify_error:
            logger.error(f"向服务器发送致命错误状态通知失败: {str(notify_error)}")

        # 记录日志
        logger.critical(f"任务 {task_id} 已因致命错误而终止")

    async def _send_callback(self, callback_url: str, task_id: str):
        """发送任务完成回调"""
        # 实现HTTP回调逻辑
        logger.info(f"发送任务 {task_id} 完成回调到 {callback_url}")
        # 实际实现可能需要使用aiohttp或httpx发送POST请求

# 创建全局任务管理器实例
task_manager = TaskManager()