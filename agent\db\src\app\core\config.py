"""
配置管理模块

集中管理应用的配置参数，从环境变量或配置文件加载。
"""
import os
from pydantic import field_validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类，管理所有配置参数"""

    # 基础配置
    API_VERSION: str = "0.1.0"
    PROJECT_NAME: str = "DB-Agent"
    PORT: int = int(os.getenv("PORT", 8001))

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 性能配置
    MAX_WORKERS: int = int(os.getenv("MAX_WORKERS", 1))
    MAX_CONNECTIONS: int = int(os.getenv("MAX_CONNECTIONS", 100))
    DEFAULT_TIMEOUT: int = int(os.getenv("DEFAULT_TIMEOUT", 30))

    # 缓存配置
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", 3600))  # 默认缓存1小时

    # 数据库默认连接超时
    DB_CONNECTION_TIMEOUT: int = int(os.getenv("DB_CONNECTION_TIMEOUT", 10))

    # 任务设置
    TASK_RESULT_TTL: int = int(os.getenv("TASK_RESULT_TTL", 86400))
    TASK_MAX_RETRIES: int = int(os.getenv("TASK_MAX_RETRIES", 3))
    TASK_RETRY_DELAY: int = int(os.getenv("TASK_RETRY_DELAY", 5))

    # 服务器API设置
    SERVER_API_BASE_URL: str = os.getenv("SERVER_API_URL", "http://localhost:8000")
    DB_AGENT_API_KEY: str = os.getenv("DB_AGENT_API_KEY", "DB-DUMMY-AGENT-KEY")

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """验证日志级别是有效的"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            return "INFO"
        return v.upper()

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"  # 忽略额外的环境变量
    }


# 创建全局配置实例
settings = Settings()