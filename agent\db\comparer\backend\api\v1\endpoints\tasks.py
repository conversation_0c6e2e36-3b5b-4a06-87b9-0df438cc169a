"""
任务管理API端点
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
import logging

from backend.services.comparison_service import ComparisonService
from backend.models.comparison_models import ComparisonTaskResponse

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[ComparisonTaskResponse])
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    comparison_service: ComparisonService = Depends()
):
    """获取任务列表"""
    try:
        tasks = await comparison_service.list_tasks(
            status=status, limit=limit, offset=offset
        )
        
        return tasks
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/{task_id}")
async def get_task_detail(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """获取任务详情"""
    try:
        task = await comparison_service.get_task_status(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")


@router.delete("/{task_id}")
async def cancel_task(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """取消任务"""
    try:
        success = await comparison_service.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": f"任务 {task_id} 已取消"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/statistics/summary")
async def get_task_statistics(
    comparison_service: ComparisonService = Depends()
):
    """获取任务统计信息"""
    try:
        # 获取所有任务
        all_tasks = await comparison_service.list_tasks(limit=1000)
        
        # 统计各状态任务数量
        stats = {
            "total_tasks": len(all_tasks),
            "pending_tasks": len([t for t in all_tasks if t.status == "pending"]),
            "running_tasks": len([t for t in all_tasks if t.status == "running"]),
            "completed_tasks": len([t for t in all_tasks if t.status == "completed"]),
            "failed_tasks": len([t for t in all_tasks if t.status == "failed"]),
            "cancelled_tasks": len([t for t in all_tasks if t.status == "cancelled"])
        }
        
        # 计算成功率
        total_finished = stats["completed_tasks"] + stats["failed_tasks"]
        if total_finished > 0:
            stats["success_rate"] = round(stats["completed_tasks"] / total_finished * 100, 2)
        else:
            stats["success_rate"] = 0.0
        
        return stats
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务统计失败: {str(e)}")


@router.get("/recent/latest")
async def get_recent_tasks(
    limit: int = 10,
    comparison_service: ComparisonService = Depends()
):
    """获取最近的任务"""
    try:
        tasks = await comparison_service.list_tasks(limit=limit, offset=0)
        
        return {
            "recent_tasks": tasks,
            "total": len(tasks)
        }
        
    except Exception as e:
        logger.error(f"获取最近任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近任务失败: {str(e)}")


@router.post("/{task_id}/retry")
async def retry_task(
    task_id: str,
    comparison_service: ComparisonService = Depends()
):
    """重试失败的任务"""
    try:
        # 获取原任务信息
        task_status = await comparison_service.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_status["status"] not in ["failed", "cancelled"]:
            raise HTTPException(status_code=400, detail="只能重试失败或已取消的任务")
        
        # TODO: 实现任务重试逻辑
        # 这里需要根据原任务配置创建新的任务
        
        return {"message": f"任务 {task_id} 重试功能待实现"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")
