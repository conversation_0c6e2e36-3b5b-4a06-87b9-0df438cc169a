#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内置比对适配器
基于现有的直接数据库比对逻辑，提供统一接口
"""

import time
import asyncio
from typing import Dict, Any, List

from .base_adapter import Comparison<PERSON>dapter, ErrorMapper
from .unified_models import (
    UnifiedResult, UnifiedDifference, UnifiedProgress, ComparisonMode,
    ProgressCallback, create_unified_difference
)


class DirectComparisonAdapter(ComparisonAdapter):
    """内置比对适配器"""
    
    def __init__(self, app_instance, config=None):
        super().__init__(app_instance, ComparisonMode.DIRECT, config)
        self.error_mapper = DirectModeErrorMapper()
    
    async def execute(self, config: Dict[str, Any], 
                     progress_callback: ProgressCallback) -> UnifiedResult:
        """执行内置比对"""
        try:
            tables = config.get('tables', [])
            total_tables = len(tables)
            differences = []
            completed_tables = 0
            
            self.logger.info(f"开始内置比对，共 {total_tables} 个表")
            
            for i, table_config in enumerate(tables):
                # 检查取消请求
                if self._check_cancellation():
                    return self._handle_cancellation()
                
                table_id = table_config['table_id']
                
                # 更新进度
                progress = self._create_progress(
                    current=i + 1,
                    total=total_tables,
                    message=f"正在比对表: {table_id}",
                    stage="direct_comparison"
                )
                progress_callback(progress)
                self._log_progress(progress)
                
                # 执行单表比对
                try:
                    table_diffs = await self._execute_table_comparison(table_config)
                    differences.extend(table_diffs)
                    completed_tables += 1
                    
                    self.logger.debug(f"表 {table_id} 比对完成，发现 {len(table_diffs)} 条差异")
                    
                except Exception as table_error:
                    self.logger.error(f"表 {table_id} 比对失败: {str(table_error)}")
                    # 继续处理其他表，不中断整个流程
                    continue
            
            # 创建成功结果
            return self._create_success_result(differences, total_tables, completed_tables)
            
        except Exception as e:
            return self._create_error_result(e)
    
    async def _execute_table_comparison(self, table_config: Dict[str, Any]) -> List[UnifiedDifference]:
        """执行单表比对"""
        table_id = table_config['table_id']
        sql_1 = table_config['sql_1']
        sql_2 = table_config['sql_2']
        
        # 更新性能指标
        self._update_performance_metrics(total_queries=2)
        
        # 查询源数据
        query_start = time.time()
        source_data = await self._execute_sql_query_async('DB1', sql_1, table_id, '源')
        if source_data is None:
            raise Exception(f"源数据查询失败: {table_id}")
        
        # 查询目标数据
        target_data = await self._execute_sql_query_async('DB2', sql_2, table_id, '目标')
        if target_data is None:
            raise Exception(f"目标数据查询失败: {table_id}")
        
        query_time = time.time() - query_start
        self._update_performance_metrics(average_query_time=query_time)
        
        # 比对数据
        raw_differences = self._compare_table_data(source_data, target_data, table_id)
        
        # 更新性能指标
        self._update_performance_metrics(
            total_records_processed=len(source_data) + len(target_data)
        )
        
        # 转换为统一格式
        unified_differences = []
        for diff in raw_differences:
            unified_diff = create_unified_difference(
                id=diff['id'],
                source_data=diff['source_data'],
                target_data=diff['target_data'],
                diff_fields=diff['diff_fields'],
                diff_type=diff['diff_type'],
                table_id=table_id,
                source='direct_comparison'
            )
            unified_differences.append(unified_diff)
        
        return unified_differences
    
    async def _execute_sql_query_async(self, db_key: str, sql: str, table_id: str, db_type: str):
        """异步执行SQL查询（包装同步方法）"""
        # 在线程池中执行同步的SQL查询方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.app._execute_sql_query, 
            db_key, sql, table_id, db_type
        )
    
    def _compare_table_data(self, source_data: list, target_data: list, table_id: str) -> list:
        """比对表数据（使用现有逻辑）"""
        try:
            # 直接调用app实例的比对方法
            return self.app._compare_table_data(source_data, target_data, table_id)
        except Exception as e:
            self.logger.error(f"表 {table_id} 数据比对异常: {str(e)}")
            return []
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        if 'tables' not in config:
            self.logger.error("配置中缺少 'tables' 字段")
            return False
        
        tables = config['tables']
        if not isinstance(tables, list) or len(tables) == 0:
            self.logger.error("tables 必须是非空列表")
            return False
        
        # 验证每个表配置
        for i, table in enumerate(tables):
            required_fields = ['table_id', 'sql_1', 'sql_2']
            for field in required_fields:
                if field not in table:
                    self.logger.error(f"表配置 {i} 缺少必要字段: {field}")
                    return False
                if not table[field] or not isinstance(table[field], str):
                    self.logger.error(f"表配置 {i} 字段 {field} 不能为空")
                    return False
        
        # 验证数据库连接配置
        db1_config = self.app.config_manager.get_database_config('DB1')
        db2_config = self.app.config_manager.get_database_config('DB2')
        
        if not db1_config or not db2_config:
            self.logger.error("数据库连接配置不完整")
            return False
        
        return True


class DirectModeErrorMapper(ErrorMapper):
    """内置模式错误映射器"""
    
    @staticmethod
    def map_error(error: Exception, mode: str = "direct") -> 'UnifiedError':
        """映射内置模式特定错误"""
        from .unified_models import create_unified_error
        
        error_msg = str(error)
        
        # DB2特定错误映射
        if "SQL30082N" in error_msg:
            return create_unified_error(
                error_code="DB2_AUTH_FAILED",
                error_type="AuthenticationError",
                message="DB2数据库认证失败，请检查用户名和密码",
                details={
                    'db_error_code': 'SQL30082N',
                    'original_error': error_msg,
                    'mode': mode
                },
                recoverable=False
            )
        elif "SQL1042C" in error_msg:
            return create_unified_error(
                error_code="DB2_SYSTEM_ERROR",
                error_type="SystemError",
                message="DB2数据库系统错误，建议重试",
                details={
                    'db_error_code': 'SQL1042C',
                    'original_error': error_msg,
                    'mode': mode
                },
                recoverable=True
            )
        elif "SQL0204N" in error_msg:
            return create_unified_error(
                error_code="DB2_OBJECT_NOT_FOUND",
                error_type="ObjectNotFoundError",
                message="DB2数据库对象不存在",
                details={
                    'db_error_code': 'SQL0204N',
                    'original_error': error_msg,
                    'mode': mode
                },
                recoverable=False
            )
        elif "SQL0911N" in error_msg or "SQL0913N" in error_msg:
            return create_unified_error(
                error_code="DB2_DEADLOCK",
                error_type="DeadlockError",
                message="DB2数据库死锁，建议重试",
                details={
                    'db_error_code': 'SQL0911N/SQL0913N',
                    'original_error': error_msg,
                    'mode': mode
                },
                recoverable=True
            )
        elif "ibm_db" in error_msg.lower():
            return create_unified_error(
                error_code="DB2_DRIVER_ERROR",
                error_type="DriverError",
                message="DB2驱动程序错误",
                details={
                    'original_error': error_msg,
                    'mode': mode,
                    'suggestion': '请检查IBM DB2驱动程序是否正确安装'
                },
                recoverable=False
            )
        else:
            # 使用基类的通用映射
            return ErrorMapper.map_error(error, mode)


# 性能优化的异步包装器
class AsyncDirectWrapper:
    """异步直接比对包装器"""
    
    def __init__(self, app_instance):
        self.app = app_instance
    
    async def execute_with_interruption_check(self, func, *args, **kwargs):
        """带中断检查的异步执行"""
        # 在线程池中执行，定期检查中断
        loop = asyncio.get_event_loop()
        
        # 创建一个可以被中断的任务
        task = loop.run_in_executor(None, func, *args, **kwargs)
        
        # 定期检查中断请求
        while not task.done():
            if getattr(self.app, 'cancel_requested', False):
                task.cancel()
                raise asyncio.CancelledError("用户取消操作")
            await asyncio.sleep(0.1)  # 100ms检查间隔
        
        return await task
